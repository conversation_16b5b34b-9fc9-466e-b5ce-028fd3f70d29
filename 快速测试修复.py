#!/usr/bin/env python3
"""
快速测试修复效果

验证主要的修复是否生效
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_image_generation_response():
    """测试ImageGenerationResponse的id字段"""
    try:
        from ai_gen_hub.core.models.responses import ImageGenerationResponse
        from ai_gen_hub.core.interfaces import ImageData
        
        # 测试创建ImageGenerationResponse
        response = ImageGenerationResponse(
            id="test-id",
            created=**********,
            data=[
                ImageData(
                    url="https://example.com/test.png",
                    b64_json=None,
                    revised_prompt=None
                )
            ],
            provider="test_provider",
            request_id="test-request",
            processing_time=1.0
        )
        
        print("✅ ImageGenerationResponse创建成功")
        print(f"   ID: {response.id}")
        print(f"   Provider: {response.provider}")
        return True
        
    except Exception as e:
        print(f"❌ ImageGenerationResponse测试失败: {e}")
        return False

def test_stream_config():
    """测试StreamConfig的验证"""
    try:
        from ai_gen_hub.core.models.requests import StreamConfig
        
        # 测试有效的chunk_size
        config = StreamConfig(
            enabled=True,
            chunk_size=512,
            include_usage=True
        )
        
        print("✅ StreamConfig创建成功")
        print(f"   Enabled: {config.enabled}")
        print(f"   Chunk Size: {config.chunk_size}")
        print(f"   Include Usage: {config.include_usage}")
        
        # 测试无效的chunk_size（应该失败）
        try:
            invalid_config = StreamConfig(
                enabled=True,
                chunk_size=2048,  # 超过1000的限制
                include_usage=True
            )
            print("❌ StreamConfig验证失败：应该拒绝chunk_size=2048")
            return False
        except Exception:
            print("✅ StreamConfig正确拒绝了无效的chunk_size")
        
        return True
        
    except Exception as e:
        print(f"❌ StreamConfig测试失败: {e}")
        return False

def test_basic_models():
    """测试基础模型"""
    try:
        from ai_gen_hub.core.models.base import Message, MessageRole
        
        # 测试Message创建
        message = Message(
            role=MessageRole.USER,
            content="Hello, world!"
        )
        
        print("✅ Message创建成功")
        print(f"   Role: {message.role}")
        print(f"   Content: {message.content}")
        return True
        
    except Exception as e:
        print(f"❌ Message测试失败: {e}")
        return False

def test_generation_config():
    """测试生成配置"""
    try:
        from ai_gen_hub.core.models.requests import GenerationConfig
        
        config = GenerationConfig(
            max_tokens=100,
            temperature=0.7,
            top_p=0.9
        )
        
        print("✅ GenerationConfig创建成功")
        print(f"   Max Tokens: {config.max_tokens}")
        print(f"   Temperature: {config.temperature}")
        print(f"   Top P: {config.top_p}")
        return True
        
    except Exception as e:
        print(f"❌ GenerationConfig测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始快速测试修复效果")
    print("=" * 40)
    
    tests = [
        ("ImageGenerationResponse", test_image_generation_response),
        ("StreamConfig", test_stream_config),
        ("基础模型", test_basic_models),
        ("生成配置", test_generation_config),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔧 测试 {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            if result:
                passed_tests += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 总结
    print("\n" + "=" * 40)
    print("📊 快速测试结果")
    print("=" * 40)
    
    print(f"📈 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 所有快速测试通过！主要修复生效")
        print("\n✅ 修复内容:")
        print("  • ImageGenerationResponse添加了id字段")
        print("  • StreamConfig的chunk_size验证正常")
        print("  • 基础模型创建正常")
        print("  • 生成配置正常")
        return True
    else:
        print(f"\n⚠️ {total_tests - passed_tests} 个测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)
