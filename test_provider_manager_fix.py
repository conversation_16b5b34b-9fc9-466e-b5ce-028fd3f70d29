#!/usr/bin/env python3
"""
测试 AIProviderManager 抽象方法修复

验证 AIProviderManager 类是否可以正常实例化，以及所有抽象方法是否已正确实现。
"""

import sys
import os
import asyncio

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_provider_manager_instantiation():
    """测试 AIProviderManager 实例化"""
    print("🧪 测试 AIProviderManager 实例化...")
    
    try:
        # 导入必要的模块
        from ai_gen_hub.services.provider_manager import AIProviderManager
        from ai_gen_hub.config.settings import Settings
        from ai_gen_hub.utils.key_manager import KeyManager
        from ai_gen_hub.core.interfaces import ProviderStatus
        
        print("✅ 模块导入成功")
        
        # 创建模拟的设置和密钥管理器
        class MockSettings:
            def __init__(self):
                self.openai = MockProviderConfig()
                self.google_ai = MockProviderConfig()
                self.anthropic = MockProviderConfig()
                self.dashscope = MockProviderConfig()
                self.monitoring = MockMonitoringConfig()
        
        class MockProviderConfig:
            def __init__(self):
                self.enabled = False
                self.api_keys = []
        
        class MockMonitoringConfig:
            def __init__(self):
                self.health_check_interval = 60
        
        class MockKeyManager:
            def get_key(self, provider: str):
                return "mock_key"
        
        # 创建模拟对象
        settings = MockSettings()
        key_manager = MockKeyManager()
        
        # 尝试实例化 AIProviderManager
        provider_manager = AIProviderManager(settings, key_manager)
        print("✅ AIProviderManager 实例化成功")
        
        # 测试抽象方法是否存在
        methods_to_test = [
            'get_provider',
            'register_provider', 
            'get_provider_status'
        ]
        
        for method_name in methods_to_test:
            if hasattr(provider_manager, method_name):
                method = getattr(provider_manager, method_name)
                if callable(method):
                    print(f"✅ 方法 {method_name} 存在且可调用")
                else:
                    print(f"❌ 方法 {method_name} 存在但不可调用")
                    return False
            else:
                print(f"❌ 方法 {method_name} 不存在")
                return False
        
        # 测试 get_provider_status 方法
        try:
            status_map = await provider_manager.get_provider_status()
            print(f"✅ get_provider_status 方法调用成功，返回: {status_map}")
        except Exception as e:
            print(f"✅ get_provider_status 方法存在（空供应商列表时的预期行为）: {e}")
        
        # 测试 register_provider 方法（使用模拟供应商）
        class MockProvider:
            def __init__(self):
                self.provider_name = "mock_provider"
                self.name = "mock_provider"
                self.status = ProviderStatus.HEALTHY
            
            async def initialize(self):
                pass
            
            async def health_check(self):
                return {"healthy": True}
        
        try:
            mock_provider = MockProvider()
            await provider_manager.register_provider(mock_provider)
            print("✅ register_provider 方法调用成功")
            
            # 验证供应商是否已注册
            status_map = await provider_manager.get_provider_status()
            if "mock_provider" in status_map:
                print("✅ 供应商注册验证成功")
            else:
                print("❌ 供应商注册验证失败")
                return False
                
        except Exception as e:
            print(f"❌ register_provider 方法调用失败: {e}")
            return False
        
        print("🎉 所有抽象方法测试通过！")
        return True
        
    except TypeError as e:
        if "abstract methods" in str(e):
            print(f"❌ 仍然存在未实现的抽象方法: {e}")
            return False
        else:
            print(f"❌ 类型错误: {e}")
            return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_application_startup():
    """测试应用启动相关组件"""
    print("\n🧪 测试应用启动相关组件...")
    
    try:
        # 测试关键组件的导入
        from ai_gen_hub.services.provider_manager import AIProviderManager
        from ai_gen_hub.utils.key_manager import KeyManager
        print("✅ 关键组件导入成功")
        
        # 测试是否可以创建基本的应用组件
        class MockSettings:
            def __init__(self):
                self.openai = MockProviderConfig()
                self.google_ai = MockProviderConfig()
                self.anthropic = MockProviderConfig()
                self.dashscope = MockProviderConfig()
                self.monitoring = MockMonitoringConfig()
                self.security = MockSecurityConfig()
        
        class MockProviderConfig:
            def __init__(self):
                self.enabled = False
                self.api_keys = []
        
        class MockMonitoringConfig:
            def __init__(self):
                self.health_check_interval = 60
        
        class MockSecurityConfig:
            def __init__(self):
                self.encryption_key = "mock_key"
        
        settings = MockSettings()
        
        # 测试密钥管理器
        try:
            key_manager = KeyManager(settings)
            print("✅ KeyManager 创建成功")
        except Exception as e:
            print(f"⚠️  KeyManager 创建失败（可能需要真实配置）: {e}")
            # 使用模拟的密钥管理器
            class MockKeyManager:
                def get_key(self, provider: str):
                    return "mock_key"
            key_manager = MockKeyManager()
            print("✅ 使用模拟 KeyManager")
        
        # 测试供应商管理器
        provider_manager = AIProviderManager(settings, key_manager)
        print("✅ AIProviderManager 创建成功")
        
        print("🎉 应用启动组件测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 应用启动组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("🚀 开始 AIProviderManager 修复验证测试")
    print("=" * 50)
    
    # 运行测试
    test1_result = await test_provider_manager_instantiation()
    test2_result = await test_application_startup()
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    tests = [
        ("AIProviderManager 实例化", test1_result),
        ("应用启动组件", test2_result),
    ]
    
    passed = 0
    for test_name, success in tests:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(tests)} 测试通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！AIProviderManager 修复成功！")
        return 0
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
