#!/usr/bin/env python3
"""
AI Gen Hub 服务器诊断工具

用于快速诊断服务器启动和运行问题，包括：
1. 配置验证
2. 服务初始化检查
3. 依赖服务状态检查
4. API端点可用性测试

使用方法:
    python diagnose_server.py
"""

import asyncio
import json
import logging
import os
import sys
import time
from typing import Dict, Any, Optional

import httpx

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ServerDiagnostic:
    """服务器诊断器"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def check_server_status(self) -> Dict[str, Any]:
        """检查服务器基本状态"""
        logger.info("=== 检查服务器基本状态 ===")
        
        try:
            # 尝试连接服务器
            response = await self.client.get(f"{self.base_url}/")
            logger.info(f"服务器响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                return {
                    "server_running": True,
                    "status_code": response.status_code,
                    "response_time": response.elapsed.total_seconds()
                }
            else:
                return {
                    "server_running": False,
                    "status_code": response.status_code,
                    "error": response.text
                }
                
        except httpx.ConnectError:
            logger.error("无法连接到服务器，请检查服务器是否启动")
            return {
                "server_running": False,
                "error": "连接失败"
            }
        except Exception as e:
            logger.error(f"服务器状态检查失败: {e}")
            return {
                "server_running": False,
                "error": str(e)
            }
    
    async def check_health_endpoint(self) -> Dict[str, Any]:
        """检查健康检查端点"""
        logger.info("=== 检查健康检查端点 ===")
        
        try:
            response = await self.client.get(f"{self.base_url}/health")
            logger.info(f"健康检查响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                health_data = response.json()
                logger.info(f"健康检查数据: {json.dumps(health_data, indent=2, ensure_ascii=False)}")
                return {
                    "health_check_available": True,
                    "health_data": health_data
                }
            else:
                logger.error(f"健康检查失败: {response.text}")
                return {
                    "health_check_available": False,
                    "error": response.text
                }
                
        except Exception as e:
            logger.error(f"健康检查端点访问失败: {e}")
            return {
                "health_check_available": False,
                "error": str(e)
            }
    
    async def check_docs_endpoint(self) -> Dict[str, Any]:
        """检查API文档端点"""
        logger.info("=== 检查API文档端点 ===")
        
        try:
            response = await self.client.get(f"{self.base_url}/docs")
            logger.info(f"API文档响应状态码: {response.status_code}")
            
            return {
                "docs_available": response.status_code == 200,
                "status_code": response.status_code
            }
            
        except Exception as e:
            logger.error(f"API文档端点访问失败: {e}")
            return {
                "docs_available": False,
                "error": str(e)
            }
    
    async def check_text_models_endpoint(self) -> Dict[str, Any]:
        """检查文本模型列表端点"""
        logger.info("=== 检查文本模型列表端点 ===")
        
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/text/models")
            logger.info(f"模型列表响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                models_data = response.json()
                logger.info(f"支持的模型: {json.dumps(models_data, indent=2, ensure_ascii=False)}")
                return {
                    "models_endpoint_available": True,
                    "models_data": models_data
                }
            else:
                logger.error(f"模型列表获取失败: {response.text}")
                return {
                    "models_endpoint_available": False,
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            logger.error(f"模型列表端点访问失败: {e}")
            return {
                "models_endpoint_available": False,
                "error": str(e)
            }
    
    async def check_simple_text_generation(self) -> Dict[str, Any]:
        """检查简单的文本生成请求"""
        logger.info("=== 检查简单文本生成请求 ===")

        request_data = {
            "model": "gemini-2.5-flash",
            "messages": [
                {
                    "role": "user",
                    "content": "请回答：1+1等于几？"
                }
            ],
            "stream": False,
            "max_tokens": 50
        }

        try:
            logger.info("发送简单文本生成请求...")
            start_time = time.time()

            # 增加超时时间到5分钟，并添加进度跟踪
            response = await self.client.post(
                f"{self.base_url}/api/v1/text/generate",
                json=request_data,
                timeout=300.0  # 5分钟超时
            )

            elapsed_time = time.time() - start_time
            logger.info(f"文本生成响应状态码: {response.status_code}")
            logger.info(f"响应时间: {elapsed_time:.2f}秒")

            if response.status_code == 200:
                response_data = response.json()
                logger.info("文本生成请求成功")

                # 提取生成的内容
                content = ""
                if "choices" in response_data and response_data["choices"]:
                    choice = response_data["choices"][0]
                    if "message" in choice and "content" in choice["message"]:
                        content = choice["message"]["content"]
                        logger.info(f"生成的内容: {content}")

                return {
                    "text_generation_working": True,
                    "response_time": elapsed_time,
                    "response_data": response_data,
                    "generated_content": content
                }
            else:
                logger.error(f"文本生成请求失败: {response.text}")
                return {
                    "text_generation_working": False,
                    "error": response.text,
                    "status_code": response.status_code,
                    "response_time": elapsed_time
                }

        except asyncio.TimeoutError:
            logger.error("文本生成请求超时（5分钟）")
            return {
                "text_generation_working": False,
                "error": "请求超时（5分钟）"
            }
        except httpx.TimeoutException:
            logger.error("HTTP请求超时")
            return {
                "text_generation_working": False,
                "error": "HTTP请求超时"
            }
        except Exception as e:
            logger.error(f"文本生成请求失败: {e}")
            return {
                "text_generation_working": False,
                "error": str(e)
            }

    async def check_streaming_text_generation(self) -> Dict[str, Any]:
        """检查流式文本生成请求"""
        logger.info("=== 检查流式文本生成请求 ===")

        request_data = {
            "model": "gemini-2.5-flash",
            "messages": [
                {
                    "role": "user",
                    "content": "请写一首关于春天的短诗，4行即可。"
                }
            ],
            "stream": True,
            "max_tokens": 200
        }

        try:
            logger.info("发送流式文本生成请求...")
            start_time = time.time()

            chunk_count = 0
            total_content = ""

            async with self.client.stream(
                "POST",
                f"{self.base_url}/api/v1/text/generate",
                json=request_data,
                timeout=300.0
            ) as response:
                logger.info(f"流式响应状态码: {response.status_code}")

                if response.status_code != 200:
                    error_text = await response.aread()
                    logger.error(f"流式请求失败: {error_text.decode()}")
                    return {
                        "streaming_working": False,
                        "error": error_text.decode(),
                        "status_code": response.status_code
                    }

                async for chunk in response.aiter_bytes():
                    chunk_count += 1
                    chunk_str = chunk.decode('utf-8')

                    # 解析SSE数据
                    for line in chunk_str.split('\n'):
                        line = line.strip()
                        if line.startswith('data: '):
                            data_str = line[6:]
                            if data_str and data_str != '[DONE]':
                                try:
                                    data = json.loads(data_str)
                                    # 提取内容
                                    if 'choices' in data and data['choices']:
                                        choice = data['choices'][0]
                                        if 'delta' in choice and 'content' in choice['delta']:
                                            content = choice['delta']['content']
                                            total_content += content
                                            logger.debug(f"收到内容: {content}")
                                except json.JSONDecodeError:
                                    continue

            elapsed_time = time.time() - start_time
            logger.info(f"流式请求完成，耗时: {elapsed_time:.2f}秒")
            logger.info(f"共收到 {chunk_count} 个数据块")
            logger.info(f"生成内容: {total_content}")

            return {
                "streaming_working": True,
                "elapsed_time": elapsed_time,
                "chunk_count": chunk_count,
                "content": total_content
            }

        except Exception as e:
            logger.error(f"流式请求失败: {e}")
            return {
                "streaming_working": False,
                "error": str(e)
            }
    
    def check_environment_config(self) -> Dict[str, Any]:
        """检查环境配置"""
        logger.info("=== 检查环境配置 ===")
        
        config_status = {}
        
        # 检查Google AI配置
        google_ai_keys = os.environ.get('GOOGLE_AI_API_KEYS', '')
        config_status['google_ai_configured'] = bool(google_ai_keys.strip())
        if config_status['google_ai_configured']:
            # 只显示密钥的前几个字符
            masked_keys = [key[:10] + '...' for key in google_ai_keys.split(',') if key.strip()]
            config_status['google_ai_keys_count'] = len(masked_keys)
            logger.info(f"Google AI API密钥已配置: {len(masked_keys)} 个")
        else:
            logger.warning("Google AI API密钥未配置")
        
        # 检查其他配置
        config_status['debug_mode'] = os.environ.get('DEBUG', '').lower() in ('true', '1', 'yes')
        config_status['log_level'] = os.environ.get('LOG_LEVEL', 'INFO')
        
        logger.info(f"调试模式: {config_status['debug_mode']}")
        logger.info(f"日志级别: {config_status['log_level']}")
        
        return config_status
    
    async def run_full_diagnostic(self) -> Dict[str, Any]:
        """运行完整诊断"""
        logger.info("开始服务器完整诊断...")
        
        results = {}
        
        # 1. 环境配置检查
        results["environment"] = self.check_environment_config()
        
        # 2. 服务器基本状态检查
        results["server_status"] = await self.check_server_status()
        
        if not results["server_status"].get("server_running", False):
            logger.error("服务器未运行，跳过后续检查")
            return results
        
        # 3. 健康检查端点
        results["health_check"] = await self.check_health_endpoint()
        
        # 4. API文档端点
        results["docs"] = await self.check_docs_endpoint()
        
        # 5. 模型列表端点
        results["models"] = await self.check_text_models_endpoint()
        
        # 6. 简单文本生成测试
        results["text_generation"] = await self.check_simple_text_generation()

        # 7. 流式文本生成测试
        results["streaming_generation"] = await self.check_streaming_text_generation()

        return results
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


async def main():
    """主函数"""
    diagnostic = ServerDiagnostic()
    
    try:
        results = await diagnostic.run_full_diagnostic()
        
        # 输出诊断结果摘要
        logger.info("=== 诊断结果摘要 ===")
        
        # 环境配置
        env_config = results.get("environment", {})
        logger.info(f"Google AI配置: {'✅ 已配置' if env_config.get('google_ai_configured') else '❌ 未配置'}")
        
        # 服务器状态
        server_status = results.get("server_status", {})
        logger.info(f"服务器运行: {'✅ 正常' if server_status.get('server_running') else '❌ 异常'}")
        
        # 健康检查
        health_check = results.get("health_check", {})
        logger.info(f"健康检查: {'✅ 正常' if health_check.get('health_check_available') else '❌ 异常'}")
        
        # 文本生成
        text_gen = results.get("text_generation", {})
        logger.info(f"文本生成: {'✅ 正常' if text_gen.get('text_generation_working') else '❌ 异常'}")

        # 流式生成
        streaming_gen = results.get("streaming_generation", {})
        logger.info(f"流式生成: {'✅ 正常' if streaming_gen.get('streaming_working') else '❌ 异常'}")
        
        # 问题诊断
        logger.info("=== 问题诊断 ===")
        
        if not env_config.get('google_ai_configured'):
            logger.error("🔧 修复建议: 请设置GOOGLE_AI_API_KEYS环境变量")
            logger.error("   export GOOGLE_AI_API_KEYS='your-api-key-here'")
        
        if not server_status.get('server_running'):
            logger.error("🔧 修复建议: 请启动AI Gen Hub服务器")
            logger.error("   python -m ai_gen_hub.main serve --debug")
        
        if not text_gen.get('text_generation_working'):
            error = text_gen.get('error', '未知错误')
            logger.error(f"🔧 文本生成问题: {error}")
            
            if 'timeout' in error.lower() or 'timeout' in str(text_gen.get('status_code', '')):
                logger.error("   可能是超时问题，检查网络连接和API密钥")
            elif 'unauthorized' in error.lower() or text_gen.get('status_code') == 401:
                logger.error("   可能是API密钥问题，检查密钥是否正确")
            elif 'not found' in error.lower() or text_gen.get('status_code') == 404:
                logger.error("   可能是路由问题，检查服务器配置")
        
        return results
        
    finally:
        await diagnostic.close()


if __name__ == "__main__":
    asyncio.run(main())
