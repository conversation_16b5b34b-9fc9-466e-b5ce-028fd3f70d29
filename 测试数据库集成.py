#!/usr/bin/env python3
"""
测试数据库集成功能

验证PostgreSQL集成、数据模型、Repository模式等功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

try:
    from ai_gen_hub.config.settings import DatabaseConfig
    from ai_gen_hub.database.connection import DatabaseManager, init_database
    from ai_gen_hub.database.migrations import init_database_with_migration
    from ai_gen_hub.database.repositories import UserRepository, APIKeyRepository, UsageRepository
    from ai_gen_hub.services.auth_service import AuthService
    from ai_gen_hub.core.models.auth import UserCreate, UserRole, Permission, APIKeyCreate
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)


async def test_database_connection():
    """测试数据库连接"""
    print("🔧 测试数据库连接")
    print("=" * 60)
    
    # 使用SQLite进行测试（不需要PostgreSQL服务器）
    config = DatabaseConfig(
        url="sqlite+aiosqlite:///./test_ai_gen_hub.db",
        pool_size=5,
        max_overflow=10,
        echo=True  # 显示SQL语句
    )
    
    try:
        # 创建数据库管理器
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        print("✅ 数据库连接初始化成功")
        
        # 创建表
        await db_manager.create_tables()
        print("✅ 数据库表创建成功")
        
        # 测试会话
        async with db_manager.get_session() as session:
            print("✅ 数据库会话获取成功")
        
        await db_manager.close()
        print("✅ 数据库连接关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_database_models():
    """测试数据库模型"""
    print("\n🔧 测试数据库模型和Repository")
    print("=" * 60)
    
    config = DatabaseConfig(
        url="sqlite+aiosqlite:///./test_ai_gen_hub.db",
        pool_size=5,
        max_overflow=10,
        echo=False
    )
    
    try:
        # 初始化数据库
        db_manager = await init_database(config)
        
        # 运行迁移
        await init_database_with_migration(db_manager)
        print("✅ 数据库迁移完成")
        
        # 测试用户Repository
        async with db_manager.get_session() as session:
            user_repo = UserRepository(session)
            
            # 创建测试用户
            user_data = UserCreate(
                username="testuser",
                email="<EMAIL>",
                password="password123",
                full_name="测试用户",
                role=UserRole.USER
            )
            
            # 创建认证服务来生成密码哈希
            auth_service = AuthService(
                jwt_secret_key="test-key",
                use_database=False  # 临时使用内存模式
            )
            password_hash = auth_service.password_manager.hash_password(user_data.password)
            
            # 创建用户
            db_user = await user_repo.create_user(user_data, password_hash)
            print(f"✅ 用户创建成功: {db_user.username}")
            
            # 查询用户
            found_user = await user_repo.get_user_by_username("testuser")
            assert found_user is not None
            assert found_user.email == "<EMAIL>"
            print("✅ 用户查询成功")
            
            # 测试API密钥Repository
            api_key_repo = APIKeyRepository(session)
            
            key_data = APIKeyCreate(
                name="测试密钥",
                permissions=[Permission.API_TEXT_GENERATE],
                rate_limit=100
            )
            
            # 生成API密钥
            import secrets
            import string
            api_key = "ak_" + "".join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))
            key_hash = auth_service.password_manager.hash_password(api_key)
            key_prefix = api_key[:8] + "..."
            
            # 创建API密钥
            db_api_key = await api_key_repo.create_api_key(
                db_user.id, key_data, key_hash, key_prefix
            )
            print(f"✅ API密钥创建成功: {db_api_key.name}")
            
            # 查询API密钥
            found_key = await api_key_repo.get_api_key_by_hash(key_hash)
            assert found_key is not None
            assert found_key.name == "测试密钥"
            print("✅ API密钥查询成功")
            
            # 测试使用量Repository
            usage_repo = UsageRepository(session)
            
            # 记录使用量
            usage = await usage_repo.record_usage(
                user_id=db_user.id,
                service_type="text_generation",
                model="gpt-3.5-turbo",
                provider="openai",
                tokens_used=100,
                cost=0.002
            )
            print(f"✅ 使用量记录成功: {usage.service_type}")
            
            # 查询使用量统计
            stats = await usage_repo.get_user_usage_stats(db_user.id)
            assert stats["total_requests"] == 1
            assert stats["total_tokens"] == 100
            print("✅ 使用量统计查询成功")
        
        await db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_auth_service_with_database():
    """测试认证服务数据库集成"""
    print("\n🔧 测试认证服务数据库集成")
    print("=" * 60)
    
    config = DatabaseConfig(
        url="sqlite+aiosqlite:///./test_ai_gen_hub.db",
        pool_size=5,
        max_overflow=10,
        echo=False
    )
    
    try:
        # 初始化数据库
        db_manager = await init_database(config)
        
        # 设置全局数据库管理器
        from ai_gen_hub.database.connection import set_database_manager
        set_database_manager(db_manager)
        
        # 创建认证服务（数据库模式）
        auth_service = AuthService(
            jwt_secret_key="test-secret-key",
            jwt_algorithm="HS256",
            use_database=True
        )
        
        # 测试用户创建
        user_data = UserCreate(
            username="dbuser",
            email="<EMAIL>",
            password="password123",
            role=UserRole.DEVELOPER
        )
        
        user = await auth_service.create_user(user_data)
        print(f"✅ 数据库模式用户创建成功: {user.username}")
        
        # 测试用户查询
        found_user = await auth_service.get_user_by_username("dbuser")
        assert found_user is not None
        assert found_user.role == UserRole.DEVELOPER
        print("✅ 数据库模式用户查询成功")
        
        # 测试用户认证
        authenticated_user = await auth_service.authenticate_user("dbuser", "password123")
        assert authenticated_user is not None
        print("✅ 数据库模式用户认证成功")
        
        # 测试JWT Token
        access_token = auth_service.create_access_token(user)
        payload = auth_service.verify_token(access_token)
        assert payload.sub == str(user.id)
        print("✅ JWT Token生成和验证成功")
        
        await db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ 认证服务数据库集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_database_performance():
    """测试数据库性能"""
    print("\n🔧 测试数据库性能")
    print("=" * 60)
    
    config = DatabaseConfig(
        url="sqlite+aiosqlite:///./test_ai_gen_hub.db",
        pool_size=10,
        max_overflow=20,
        echo=False
    )
    
    try:
        db_manager = await init_database(config)
        
        import time
        
        # 测试批量用户创建
        start_time = time.time()
        
        async with db_manager.get_session() as session:
            user_repo = UserRepository(session)
            auth_service = AuthService(
                jwt_secret_key="test-key",
                use_database=False
            )
            
            # 创建100个用户
            for i in range(100):
                user_data = UserCreate(
                    username=f"perfuser{i}",
                    email=f"perfuser{i}@example.com",
                    password="password123",
                    role=UserRole.USER
                )
                
                password_hash = auth_service.password_manager.hash_password(user_data.password)
                await user_repo.create_user(user_data, password_hash)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 批量创建100个用户耗时: {duration:.2f}秒")
        print(f"✅ 平均每个用户创建时间: {duration/100*1000:.2f}毫秒")
        
        # 测试批量查询
        start_time = time.time()
        
        async with db_manager.get_session() as session:
            user_repo = UserRepository(session)
            
            # 查询所有用户
            users = await user_repo.list_users(limit=200)
            
        end_time = time.time()
        query_duration = end_time - start_time
        
        print(f"✅ 查询{len(users)}个用户耗时: {query_duration:.2f}秒")
        
        await db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def cleanup_test_database():
    """清理测试数据库"""
    try:
        test_db_path = Path("./test_ai_gen_hub.db")
        if test_db_path.exists():
            test_db_path.unlink()
            print("✅ 测试数据库文件已清理")
    except Exception as e:
        print(f"⚠️ 清理测试数据库失败: {e}")


async def main():
    """主测试函数"""
    print("🚀 开始数据库集成测试")
    print()
    
    # 清理之前的测试数据库
    cleanup_test_database()
    
    tests = [
        ("数据库连接", test_database_connection),
        ("数据库模型和Repository", test_database_models),
        ("认证服务数据库集成", test_auth_service_with_database),
        ("数据库性能", test_database_performance),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed_tests += 1
            print(f"{'✅ 通过' if result else '❌ 失败'}: {test_name}")
        except Exception as e:
            print(f"❌ 异常: {test_name} - {e}")
    
    # 清理测试数据库
    cleanup_test_database()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 数据库集成测试结果总结")
    print("=" * 60)
    
    print(f"📈 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！数据库集成功能正常！")
        print("\n主要功能:")
        print("  • PostgreSQL/SQLite数据库连接")
        print("  • SQLAlchemy异步ORM模型")
        print("  • Repository模式数据访问层")
        print("  • 数据库迁移和种子数据")
        print("  • 认证服务数据库集成")
        print("  • 性能测试和优化")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查实现")
        return False


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        cleanup_test_database()
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        cleanup_test_database()
        sys.exit(1)
