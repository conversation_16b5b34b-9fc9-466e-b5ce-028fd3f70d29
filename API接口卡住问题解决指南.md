# AI Gen Hub API接口卡住问题解决指南

## 问题现象

调用AI Gen Hub API时，请求发送后服务器没有响应，客户端卡在等待响应头的阶段：
```
receive_response_headers.started request=<Request [b'POST']>
```

## 问题诊断步骤

### 第一步：运行诊断工具

```bash
python diagnose_server.py
```

这个工具会检查：
- 服务器是否正常运行
- 配置是否正确
- API端点是否可用
- 简单的文本生成是否工作

### 第二步：运行快速修复工具

```bash
python quick_fix_server.py
```

这个工具会：
- 检查和创建.env配置文件
- 验证模块导入
- 测试服务初始化
- 创建最小化测试服务器

### 第三步：检查服务器日志

启动服务器时使用调试模式：
```bash
python -m ai_gen_hub.main serve --debug --reload
```

观察启动日志，特别关注：
- 配置加载是否成功
- 供应商初始化是否成功
- 服务注入是否成功

## 常见问题和解决方案

### 1. Google AI API密钥未配置

**症状：**
- 诊断工具显示"Google AI配置: ❌ 未配置"
- 服务器日志显示"Google AI供应商初始化失败"

**解决方案：**
1. 创建或编辑`.env`文件：
```bash
# 设置Google AI API密钥
GOOGLE_AI_API_KEYS=your-actual-api-key-here
GOOGLE_AI_ENABLED=true
```

2. 重启服务器：
```bash
python -m ai_gen_hub.main serve --debug
```

### 2. 服务初始化失败

**症状：**
- 服务器启动时出现异常
- 依赖注入失败
- 文本生成服务未初始化

**解决方案：**
1. 检查依赖是否正确安装：
```bash
pip install -e .
```

2. 检查Python路径：
```bash
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
```

3. 使用最小化测试服务器验证基本功能：
```bash
python test_server.py
```

### 3. 异步处理阻塞

**症状：**
- 请求发送后长时间无响应
- 服务器日志显示请求开始但没有后续日志

**解决方案：**
1. 检查网络连接和防火墙设置
2. 验证Google AI API密钥是否有效
3. 增加超时时间：
```bash
export GOOGLE_AI_TIMEOUT=180
```

### 4. 依赖服务问题

**症状：**
- Redis连接失败
- 数据库连接问题

**解决方案：**
1. 禁用缓存（临时解决）：
```bash
export ENABLE_CACHING=false
```

2. 检查Redis服务：
```bash
redis-cli ping
```

## 调试技巧

### 1. 启用详细日志

在`.env`文件中设置：
```
DEBUG=true
LOG_LEVEL=DEBUG
```

### 2. 使用调试工具测试

直接测试Google AI API：
```bash
python debug_api.py --direct --api-key YOUR_API_KEY --provider google_ai --model gemini-2.5-flash --prompt "测试"
```

测试AI Gen Hub API：
```bash
python debug_api.py --provider google_ai --model gemini-2.5-flash --prompt "测试"
```

### 3. 检查进程状态

```bash
# 检查端口占用
lsof -i :8001

# 检查进程
ps aux | grep python
```

## 验证修复效果

### 1. 运行完整测试

```bash
python test_api_fix.py
```

### 2. 使用Swagger UI测试

访问 http://localhost:8001/docs 进行交互式测试

### 3. 使用curl测试

```bash
curl -X POST "http://localhost:8001/api/v1/text/generate" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "gemini-2.5-flash",
       "messages": [{"role": "user", "content": "你好"}],
       "stream": false
     }'
```

## 预防措施

### 1. 配置验证

在生产环境部署前，始终运行：
```bash
python diagnose_server.py
python quick_fix_server.py
```

### 2. 监控设置

设置适当的超时和重试：
```
GOOGLE_AI_TIMEOUT=120
GOOGLE_AI_MAX_RETRIES=3
REQUEST_TIMEOUT=300
```

### 3. 日志管理

在生产环境中适当调整日志级别：
```
LOG_LEVEL=INFO  # 生产环境
LOG_LEVEL=DEBUG # 调试环境
```

## 联系支持

如果问题仍然存在，请提供以下信息：

1. 诊断工具的完整输出
2. 服务器启动日志
3. 错误信息和堆栈跟踪
4. 环境配置（隐藏敏感信息）

## 常用命令速查

```bash
# 诊断问题
python diagnose_server.py

# 快速修复
python quick_fix_server.py

# 启动调试服务器
python -m ai_gen_hub.main serve --debug

# 测试API
python debug_api.py --provider google_ai --model gemini-2.5-flash --prompt "测试"

# 验证修复
python test_api_fix.py

# 最小化测试服务器
python test_server.py
```
