# 自定义 Provider 配置示例
# 
# 本文件展示如何为自定义 AI provider 配置各种参数
# 复制此文件并根据您的需求进行修改

# =============================================================================
# 自定义 Provider 基础配置
# =============================================================================

custom_provider:
  # 基础设置
  enabled: true                                    # 是否启用此 provider
  name: "custom_ai"                               # Provider 名称（用于内部标识）
  
  # API 配置
  api_keys:                                       # API 密钥列表（支持多个密钥轮换）
    - "${CUSTOM_AI_API_KEY_1}"                   # 主要密钥
    - "${CUSTOM_AI_API_KEY_2}"                   # 备用密钥
  
  base_url: "https://api.custom-ai.com/v1"       # API 基础 URL
  
  # 请求配置
  timeout: 120                                    # 请求超时时间（秒）
  max_retries: 3                                  # 最大重试次数
  retry_delay: 1.0                               # 重试延迟时间（秒）
  
  # 速率限制
  rate_limit: 1000                               # 每分钟最大请求数
  
  # 负载均衡
  priority: 1                                     # 优先级（1-10，数字越大优先级越高）
  weight: 1.0                                     # 负载均衡权重

# =============================================================================
# 高级配置选项
# =============================================================================

# 模型配置
models:
  custom_ai:
    # 支持的模型列表
    supported_models:
      - "custom-gpt-4"
      - "custom-gpt-3.5"
      - "custom-claude"
      - "custom-llama"
      - "custom-code-model"
    
    # 模型映射（将通用模型名映射到 provider 特定名称）
    model_mapping:
      "gpt-4": "custom-gpt-4"
      "gpt-3.5-turbo": "custom-gpt-3.5"
      "claude": "custom-claude"
      "llama": "custom-llama"
      "code": "custom-code-model"
    
    # 默认参数
    default_params:
      temperature: 0.7
      max_tokens: 2048
      top_p: 0.9
      frequency_penalty: 0.0
      presence_penalty: 0.0

# 监控和日志配置
monitoring:
  custom_ai:
    # 健康检查
    health_check:
      enabled: true
      interval: 300                              # 健康检查间隔（秒）
      timeout: 10                                # 健康检查超时（秒）
      endpoint: "/models"                        # 健康检查端点
    
    # 指标收集
    metrics:
      enabled: true
      collect_request_duration: true
      collect_token_usage: true
      collect_error_rates: true
    
    # 日志配置
    logging:
      level: "INFO"                              # 日志级别
      include_request_details: false             # 是否记录请求详情
      include_response_details: false            # 是否记录响应详情

# 错误处理配置
error_handling:
  custom_ai:
    # 重试策略
    retry_strategy:
      exponential_backoff: true                  # 是否使用指数退避
      max_backoff_time: 60                       # 最大退避时间（秒）
      jitter: true                               # 是否添加随机抖动
    
    # 错误映射
    error_mapping:
      "invalid_api_key": "AuthenticationError"
      "rate_limit_exceeded": "RateLimitError"
      "insufficient_quota": "QuotaExceededError"
      "model_not_found": "ModelNotFoundError"
    
    # 熔断器配置
    circuit_breaker:
      enabled: true
      failure_threshold: 5                       # 失败阈值
      recovery_timeout: 60                       # 恢复超时（秒）

# =============================================================================
# 环境特定配置
# =============================================================================

# 开发环境
development:
  custom_ai:
    base_url: "https://api-dev.custom-ai.com/v1"
    timeout: 60
    rate_limit: 100
    logging:
      level: "DEBUG"
      include_request_details: true

# 测试环境
testing:
  custom_ai:
    base_url: "https://api-test.custom-ai.com/v1"
    timeout: 30
    rate_limit: 50
    # 使用模拟响应进行测试
    mock_responses: true

# 生产环境
production:
  custom_ai:
    base_url: "https://api.custom-ai.com/v1"
    timeout: 180
    rate_limit: 2000
    # 生产环境安全配置
    security:
      validate_ssl: true
      require_https: true
    
    # 高可用配置
    high_availability:
      enable_failover: true
      backup_endpoints:
        - "https://api-backup.custom-ai.com/v1"
        - "https://api-fallback.custom-ai.com/v1"

# =============================================================================
# 集成配置
# =============================================================================

# 与其他 provider 的集成
integration:
  # 模型回退策略
  fallback_strategy:
    enabled: true
    fallback_providers:
      - "openai"                                 # 主要回退
      - "anthropic"                              # 次要回退
  
  # 负载分配
  load_distribution:
    strategy: "round_robin"                      # 分配策略
    weights:
      custom_ai: 40
      openai: 30
      anthropic: 20
      google_ai: 10

# 缓存配置
caching:
  custom_ai:
    enabled: true
    ttl: 3600                                    # 缓存生存时间（秒）
    max_size: 1000                               # 最大缓存条目数
    cache_key_strategy: "model_messages_hash"    # 缓存键策略

# =============================================================================
# 使用示例
# =============================================================================

# 示例：如何在代码中使用这些配置
usage_examples:
  # 基础使用
  basic_usage: |
    from ai_gen_hub.config.settings import get_settings
    from ai_gen_hub.providers.custom_provider import CustomProvider
    
    settings = get_settings()
    config = settings.get_provider_config("custom_ai")
    provider = CustomProvider(config, key_manager)
  
  # 环境特定配置
  environment_config: |
    # 设置环境变量
    export AI_GEN_HUB_ENVIRONMENT=production
    export CUSTOM_AI_API_KEY="your-production-key"
    
    # 配置会自动根据环境加载
    settings = get_settings()
  
  # 动态配置更新
  dynamic_config: |
    # 运行时更新配置
    provider.update_config({
        "timeout": 240,
        "rate_limit": 1500
    })

# =============================================================================
# 配置验证规则
# =============================================================================

validation:
  custom_ai:
    required_fields:
      - "api_keys"
      - "base_url"
    
    field_constraints:
      timeout:
        min: 10
        max: 600
      rate_limit:
        min: 1
        max: 10000
      priority:
        min: 1
        max: 10
    
    url_validation:
      base_url:
        schemes: ["https", "http"]
        require_https_in_production: true

# =============================================================================
# 注释和说明
# =============================================================================

# 配置说明：
# 1. 所有以 ${} 包围的值都是环境变量引用
# 2. 可以为不同环境设置不同的配置值
# 3. 配置会按照 default -> environment -> local 的顺序合并
# 4. 敏感信息（如 API 密钥）应该通过环境变量设置，不要直接写在配置文件中

# 环境变量设置示例：
# export CUSTOM_AI_API_KEY_1="your-primary-api-key"
# export CUSTOM_AI_API_KEY_2="your-backup-api-key"
# export AI_GEN_HUB_ENVIRONMENT="production"
# export AI_GEN_HUB_CONFIG_PATH="/path/to/this/config.yaml"
