# AI Gen Hub 供应商配置示例
# 包含新增的免费AI供应商配置

# =============================================================================
# 免费AI供应商配置
# =============================================================================

# Cohere 供应商配置
# 免费额度：每月1000次API调用
# 支持模型：Command R+, Command R, Command, Command Light
cohere:
  enabled: true
  api_key: "${COHERE_API_KEY}"  # 从环境变量获取
  base_url: "https://api.cohere.ai/v1"
  timeout: 60
  max_retries: 3
  retry_delay: 1.0
  
  # 支持的模型配置
  models:
    text_generation:
      - "command-r-plus"      # 最强大的模型，适合复杂推理
      - "command-r"           # 平衡性能和成本的最佳选择
      - "command"             # 标准对话模型
      - "command-light"       # 轻量级快速模型
  
  # 默认参数配置
  default_params:
    temperature: 0.7
    max_tokens: 1000
    top_p: 0.9
    top_k: 50
  
  # 模型映射配置
  model_mapping:
    "cohere-latest": "command-r-plus"
    "cohere": "command-r"
    "cohere-fast": "command-r"
    "cohere-lite": "command-light"
    "cohere-best": "command-r-plus"

# Hugging Face 供应商配置
# 免费额度：免费用户每月$0.10，PRO用户每月$2.00
# 支持模型：大量开源模型（Llama, Mistral, DeepSeek等）
huggingface:
  enabled: true
  api_key: "${HUGGINGFACE_API_KEY}"  # 格式：hf_****
  base_url: "https://router.huggingface.co/v1"
  timeout: 120  # 开源模型可能需要更长时间
  max_retries: 3
  retry_delay: 2.0
  
  # 支持的模型配置
  models:
    text_generation:
      # Llama 系列
      - "meta-llama/Llama-3.1-8B-Instruct"
      - "meta-llama/Llama-3.1-70B-Instruct"
      - "meta-llama/Llama-3.2-11B-Vision-Instruct"
      - "meta-llama/Llama-3.2-3B-Instruct"
      
      # DeepSeek 系列
      - "deepseek-ai/DeepSeek-V3-0324"
      - "deepseek-ai/deepseek-coder-33b-instruct"
      
      # Mistral 系列
      - "mistralai/Mistral-7B-Instruct-v0.3"
      - "mistralai/Mixtral-8x7B-Instruct-v0.1"
      
      # Qwen 系列
      - "Qwen/Qwen2.5-72B-Instruct"
      - "Qwen/Qwen2-VL-72B-Instruct"
    
    image_generation:
      # FLUX 系列
      - "black-forest-labs/FLUX.1-dev"
      - "black-forest-labs/FLUX.1-schnell"
      
      # Stable Diffusion 系列
      - "stabilityai/stable-diffusion-3.5-large"
      - "stabilityai/stable-diffusion-xl-base-1.0"
  
  # 默认参数配置
  default_params:
    temperature: 0.7
    max_tokens: 2048
    top_p: 0.9
    # 注意：Hugging Face不支持top_k
  
  # 模型映射配置
  model_mapping:
    "huggingface-latest": "meta-llama/Llama-3.1-70B-Instruct"
    "huggingface": "meta-llama/Llama-3.1-8B-Instruct"
    "hf": "meta-llama/Llama-3.1-8B-Instruct"
    "huggingface-fast": "meta-llama/Llama-3.2-3B-Instruct"
    "huggingface-best": "meta-llama/Llama-3.1-70B-Instruct"
    "huggingface-code": "deepseek-ai/deepseek-coder-33b-instruct"
    "huggingface-chinese": "deepseek-ai/DeepSeek-V3-0324"
    "huggingface-image": "black-forest-labs/FLUX.1-dev"
    "huggingface-vision": "meta-llama/Llama-3.2-11B-Vision-Instruct"

# =============================================================================
# 现有供应商配置（保持兼容）
# =============================================================================

# OpenAI 供应商配置
openai:
  enabled: true
  api_key: "${OPENAI_API_KEY}"
  base_url: "https://api.openai.com/v1"
  timeout: 60
  max_retries: 3
  retry_delay: 1.0
  
  models:
    text_generation:
      - "gpt-4o"
      - "gpt-4o-mini"
      - "gpt-4-turbo"
      - "gpt-3.5-turbo"
    image_generation:
      - "dall-e-3"
      - "dall-e-2"
  
  default_params:
    temperature: 0.7
    max_tokens: 4096
    top_p: 1.0

# Google AI 供应商配置
google_ai:
  enabled: true
  api_key: "${GOOGLE_AI_API_KEY}"
  base_url: "https://generativelanguage.googleapis.com/v1beta"
  timeout: 60
  max_retries: 3
  retry_delay: 1.0
  
  models:
    text_generation:
      - "gemini-2.5-pro"
      - "gemini-2.5-flash"
      - "gemini-2.0-flash-exp"
  
  default_params:
    temperature: 0.7
    maxOutputTokens: 2048
    topP: 0.9
    topK: 40

# Anthropic 供应商配置
anthropic:
  enabled: true
  api_key: "${ANTHROPIC_API_KEY}"
  base_url: "https://api.anthropic.com"
  timeout: 60
  max_retries: 3
  retry_delay: 1.0
  
  models:
    text_generation:
      - "claude-3-5-sonnet-20241022"
      - "claude-3-5-haiku-20241022"
      - "claude-3-opus-20240229"
  
  default_params:
    temperature: 0.7
    max_tokens: 8192
    top_p: 0.9
    top_k: 50

# =============================================================================
# 全局配置
# =============================================================================

# 供应商优先级配置（用于自动选择）
provider_priority:
  text_generation:
    - "openai"          # 优先使用OpenAI（稳定性好）
    - "google_ai"       # 其次Google AI（免费额度大）
    - "anthropic"       # 然后Anthropic（质量高）
    - "cohere"          # 接着Cohere（免费额度）
    - "huggingface"     # 最后Hugging Face（开源模型）
  
  image_generation:
    - "openai"          # 优先DALL-E
    - "huggingface"     # 其次Hugging Face（免费FLUX）

# 负载均衡配置
load_balancing:
  enabled: true
  strategy: "round_robin"  # round_robin, weighted, least_connections
  health_check_interval: 300  # 5分钟
  
  # 权重配置（仅在weighted策略下生效）
  weights:
    openai: 40
    google_ai: 30
    anthropic: 20
    cohere: 5
    huggingface: 5

# 兼容性配置
compatibility:
  # 是否启用自动参数适配
  auto_parameter_adaptation: true
  
  # 是否在不兼容时显示警告
  show_compatibility_warnings: true
  
  # 是否在响应中包含兼容性信息
  include_compatibility_info: false
  
  # 不支持的功能处理策略
  unsupported_feature_strategy: "ignore"  # ignore, warn, error

# 监控和日志配置
monitoring:
  # 是否启用详细的供应商调用日志
  detailed_provider_logging: true
  
  # 是否记录兼容性检查结果
  log_compatibility_checks: true
  
  # 是否启用性能监控
  performance_monitoring: true
  
  # 指标收集间隔（秒）
  metrics_collection_interval: 60

# 缓存配置
caching:
  # 是否启用模型列表缓存
  cache_model_lists: true
  
  # 模型列表缓存时间（秒）
  model_list_cache_ttl: 3600
  
  # 是否启用兼容性检查结果缓存
  cache_compatibility_checks: true
  
  # 兼容性检查缓存时间（秒）
  compatibility_cache_ttl: 1800
