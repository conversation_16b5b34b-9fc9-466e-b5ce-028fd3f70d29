# AI Gen Hub 认证系统配置示例
#
# 本文件展示如何配置完整的身份认证和授权系统
# 复制此文件并根据您的需求进行修改

# =============================================================================
# 基础认证配置
# =============================================================================

auth:
  # JWT配置
  jwt_secret_key: "${AUTH_JWT_SECRET_KEY}"           # JWT签名密钥（必须设置）
  jwt_algorithm: "HS256"                             # JWT算法
  jwt_access_token_expire_minutes: 30                # 访问令牌过期时间（分钟）
  jwt_refresh_token_expire_days: 7                   # 刷新令牌过期时间（天）
  
  # 会话配置
  session_expire_hours: 24                          # 会话过期时间（小时）
  session_cookie_name: "ai_gen_hub_session"         # 会话Cookie名称
  session_cookie_secure: true                       # 会话Cookie安全标志（生产环境必须为true）
  session_cookie_httponly: true                     # 会话Cookie HttpOnly标志
  
  # 安全策略
  password_min_length: 8                            # 密码最小长度
  password_require_uppercase: true                  # 密码是否需要大写字母
  password_require_lowercase: true                  # 密码是否需要小写字母
  password_require_numbers: true                    # 密码是否需要数字
  password_require_symbols: false                   # 密码是否需要符号
  
  # 频率限制
  rate_limit_enabled: true                          # 是否启用频率限制
  rate_limit_requests_per_minute: 100               # 每分钟请求限制
  rate_limit_burst_size: 10                         # 突发请求大小
  
  # 审计日志
  audit_log_enabled: true                           # 是否启用审计日志
  audit_log_retention_days: 90                      # 审计日志保留天数
  
  # 用户注册
  allow_registration: false                         # 是否允许用户注册
  require_email_verification: true                  # 是否需要邮箱验证
  
  # 默认身份提供商
  default_idp: "auth0"                              # 默认IdP（可选）

# =============================================================================
# 身份提供商配置
# =============================================================================

# Auth0配置
auth0:
  enabled: true                                     # 是否启用Auth0
  domain: "${AUTH0_DOMAIN}"                         # Auth0域名
  client_id: "${AUTH0_CLIENT_ID}"                   # Auth0客户端ID
  client_secret: "${AUTH0_CLIENT_SECRET}"           # Auth0客户端密钥
  audience: "${AUTH0_AUDIENCE}"                     # Auth0受众（可选）
  scopes:                                           # 权限范围
    - "openid"
    - "profile"
    - "email"
  
  # 用户字段映射
  user_mapping:
    id: "sub"
    username: "preferred_username"
    email: "email"
    full_name: "name"

# Keycloak配置
keycloak:
  enabled: false                                    # 是否启用Keycloak
  base_url: "${KEYCLOAK_BASE_URL}"                  # Keycloak基础URL
  realm: "${KEYCLOAK_REALM}"                        # Keycloak域
  client_id: "${KEYCLOAK_CLIENT_ID}"                # Keycloak客户端ID
  client_secret: "${KEYCLOAK_CLIENT_SECRET}"        # Keycloak客户端密钥
  scopes:
    - "openid"
    - "profile"
    - "email"
    - "roles"

# Azure AD配置
azure_ad:
  enabled: false                                    # 是否启用Azure AD
  tenant_id: "${AZURE_AD_TENANT_ID}"                # Azure AD租户ID
  client_id: "${AZURE_AD_CLIENT_ID}"                # Azure AD客户端ID
  client_secret: "${AZURE_AD_CLIENT_SECRET}"        # Azure AD客户端密钥
  scopes:
    - "openid"
    - "profile"
    - "email"
    - "User.Read"

# Google OAuth配置
google_oauth:
  enabled: false                                    # 是否启用Google OAuth
  client_id: "${GOOGLE_OAUTH_CLIENT_ID}"            # Google OAuth客户端ID
  client_secret: "${GOOGLE_OAUTH_CLIENT_SECRET}"    # Google OAuth客户端密钥
  scopes:
    - "openid"
    - "profile"
    - "email"

# GitHub配置
github:
  enabled: false                                    # 是否启用GitHub
  client_id: "${GITHUB_CLIENT_ID}"                  # GitHub客户端ID
  client_secret: "${GITHUB_CLIENT_SECRET}"          # GitHub客户端密钥
  scopes:
    - "user:email"
    - "read:user"

# GitLab配置
gitlab:
  enabled: false                                    # 是否启用GitLab
  base_url: "https://gitlab.com"                    # GitLab基础URL（自托管时修改）
  client_id: "${GITLAB_CLIENT_ID}"                  # GitLab客户端ID
  client_secret: "${GITLAB_CLIENT_SECRET}"          # GitLab客户端密钥
  scopes:
    - "openid"
    - "profile"
    - "email"
    - "read_user"

# =============================================================================
# 角色和权限配置
# =============================================================================

rbac:
  # 自定义角色定义
  custom_roles:
    - name: "api_developer"
      display_name: "API开发者"
      description: "专门用于API开发的角色"
      permissions:
        - "api:text:generate"
        - "api:image:generate"
        - "api:token:manage"
        - "console:access"
        - "console:analytics"
    
    - name: "content_manager"
      display_name: "内容管理员"
      description: "负责内容管理的角色"
      permissions:
        - "api:text:generate"
        - "console:access"
        - "console:analytics"
        - "user:read"
    
    - name: "system_monitor"
      display_name: "系统监控员"
      description: "负责系统监控的角色"
      permissions:
        - "system:monitor"
        - "console:access"
        - "console:analytics"
        - "user:read"
  
  # 默认用户角色分配
  default_user_role: "user"                         # 新用户默认角色
  
  # 角色继承关系
  role_inheritance:
    admin:
      - "developer"
      - "user"
    developer:
      - "user"

# =============================================================================
# 安全策略配置
# =============================================================================

security:
  # 密码策略
  password_policy:
    min_length: 8
    max_length: 128
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_symbols: false
    forbidden_passwords:                             # 禁用的常见密码
      - "password"
      - "123456"
      - "admin"
      - "root"
  
  # 账户锁定策略
  account_lockout:
    max_failed_attempts: 5                          # 最大失败尝试次数
    lockout_duration_minutes: 15                    # 锁定持续时间（分钟）
    reset_attempts_after_minutes: 60                # 重置失败计数时间（分钟）
  
  # 会话安全
  session_security:
    max_concurrent_sessions: 5                      # 最大并发会话数
    idle_timeout_minutes: 30                       # 空闲超时时间（分钟）
    absolute_timeout_hours: 8                      # 绝对超时时间（小时）
  
  # IP白名单（可选）
  ip_whitelist:
    enabled: false                                  # 是否启用IP白名单
    allowed_ips:                                    # 允许的IP地址
      - "127.0.0.1"
      - "::1"
      - "10.0.0.0/8"
      - "**********/12"
      - "***********/16"
  
  # 安全头部
  security_headers:
    content_security_policy: "default-src 'self'; script-src 'self' 'unsafe-inline'"
    x_frame_options: "DENY"
    x_content_type_options: "nosniff"
    x_xss_protection: "1; mode=block"
    strict_transport_security: "max-age=31536000; includeSubDomains"

# =============================================================================
# 监控和日志配置
# =============================================================================

monitoring:
  # 认证事件监控
  auth_monitoring:
    enabled: true
    alert_on_multiple_failures: true               # 多次失败时告警
    alert_threshold: 10                             # 告警阈值
    alert_window_minutes: 5                        # 告警时间窗口（分钟）
  
  # 权限变更监控
  permission_monitoring:
    enabled: true
    log_permission_changes: true                   # 记录权限变更
    alert_on_privilege_escalation: true           # 权限提升时告警
  
  # 异常活动检测
  anomaly_detection:
    enabled: true
    unusual_login_locations: true                  # 异常登录位置检测
    unusual_login_times: true                      # 异常登录时间检测
    suspicious_api_usage: true                     # 可疑API使用检测

# =============================================================================
# 环境特定配置
# =============================================================================

# 开发环境
development:
  auth:
    jwt_secret_key: "dev-secret-key-change-in-production"
    session_cookie_secure: false                   # 开发环境可以使用HTTP
    rate_limit_requests_per_minute: 1000           # 开发环境放宽限制
    audit_log_enabled: false                       # 开发环境可以关闭审计日志
  
  security:
    password_policy:
      min_length: 4                                # 开发环境简化密码要求
      require_symbols: false

# 测试环境
testing:
  auth:
    jwt_secret_key: "test-secret-key"
    jwt_access_token_expire_minutes: 5             # 测试环境短过期时间
    rate_limit_requests_per_minute: 10000          # 测试环境高限制
  
  # 使用模拟IdP进行测试
  mock_idp:
    enabled: true
    test_users:
      - username: "testuser"
        email: "<EMAIL>"
        role: "user"
      - username: "testadmin"
        email: "<EMAIL>"
        role: "admin"

# 生产环境
production:
  auth:
    session_cookie_secure: true                    # 生产环境必须使用HTTPS
    rate_limit_requests_per_minute: 100            # 生产环境严格限制
    audit_log_enabled: true                        # 生产环境必须启用审计日志
    require_email_verification: true               # 生产环境必须验证邮箱
  
  security:
    account_lockout:
      max_failed_attempts: 3                       # 生产环境更严格的锁定策略
      lockout_duration_minutes: 30
    
    ip_whitelist:
      enabled: true                                # 生产环境可以启用IP白名单
  
  monitoring:
    auth_monitoring:
      alert_threshold: 5                           # 生产环境更低的告警阈值
      alert_window_minutes: 1

# =============================================================================
# 使用说明
# =============================================================================

# 环境变量设置示例：
# export AUTH_JWT_SECRET_KEY="your-super-secret-jwt-key-here"
# export AUTH0_DOMAIN="your-auth0-domain.auth0.com"
# export AUTH0_CLIENT_ID="your-auth0-client-id"
# export AUTH0_CLIENT_SECRET="your-auth0-client-secret"
# export AUTH0_AUDIENCE="https://your-api.example.com"
# export AI_GEN_HUB_ENVIRONMENT="production"
# export AI_GEN_HUB_CONFIG_PATH="/path/to/this/config.yaml"

# 配置优先级：
# 1. 环境变量（最高优先级）
# 2. 配置文件中的环境特定配置
# 3. 配置文件中的默认配置
# 4. 代码中的默认值（最低优先级）

# 安全注意事项：
# 1. 所有密钥和敏感信息都应通过环境变量设置
# 2. 生产环境必须使用HTTPS
# 3. 定期轮换JWT密钥和IdP客户端密钥
# 4. 启用审计日志并定期审查
# 5. 配置适当的备份和恢复策略
