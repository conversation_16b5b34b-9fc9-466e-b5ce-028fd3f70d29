#!/usr/bin/env python3
"""
新增AI供应商测试脚本

测试Cohere和Hugging Face供应商的功能，包括：
- 基本文本生成
- v2接口兼容性
- 参数适配
- 错误处理

使用方法：
    python test_new_providers.py --provider cohere
    python test_new_providers.py --provider huggingface
    python test_new_providers.py --all
"""

import asyncio
import argparse
import json
import os
import sys
import time
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ai_gen_hub.core.interfaces import (
    OptimizedTextGenerationRequest,
    GenerationConfig,
    StreamConfig,
    Message,
    MessageRole
)
from ai_gen_hub.core.compatibility import ProviderCompatibilityManager
from ai_gen_hub.providers.cohere_provider import CohereProvider
from ai_gen_hub.providers.huggingface_provider import HuggingFaceProvider
from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.utils.key_manager import KeyManager


class NewProviderTester:
    """新供应商测试器"""
    
    def __init__(self):
        self.compatibility_manager = ProviderCompatibilityManager()
        self.results = {}
    
    async def test_cohere_provider(self) -> Dict[str, Any]:
        """测试Cohere供应商"""
        print("🧪 测试Cohere供应商...")
        
        # 检查API密钥
        api_key = os.getenv("COHERE_API_KEY")
        if not api_key:
            return {
                "success": False,
                "error": "未设置COHERE_API_KEY环境变量",
                "suggestion": "请设置: export COHERE_API_KEY='your-api-key'"
            }
        
        try:
            # 创建供应商实例
            config = ProviderConfig(
                name="cohere",
                enabled=True,
                api_key=api_key,
                base_url="https://api.cohere.ai/v1"
            )
            key_manager = KeyManager()
            provider = CohereProvider(config, key_manager)
            
            # 测试健康检查
            print("  ✓ 执行健康检查...")
            health_ok = await provider._perform_health_check(api_key)
            if not health_ok:
                return {
                    "success": False,
                    "error": "Cohere API健康检查失败",
                    "suggestion": "检查API密钥是否有效"
                }
            
            # 测试基本文本生成
            print("  ✓ 测试基本文本生成...")
            basic_request = OptimizedTextGenerationRequest(
                messages=[
                    Message(role=MessageRole.USER, content="用一句话解释什么是人工智能")
                ],
                model="command-r",
                generation=GenerationConfig(
                    temperature=0.7,
                    max_tokens=100,
                    top_p=0.9,
                    top_k=50
                )
            )
            
            response = await provider.generate_text_optimized(basic_request, api_key)
            if not response or not response.choices:
                return {
                    "success": False,
                    "error": "文本生成返回空响应"
                }
            
            # 测试兼容性检查
            print("  ✓ 测试兼容性检查...")
            compatibility_report = self.compatibility_manager.validate_request_compatibility(
                basic_request, "cohere"
            )
            
            # 测试流式生成
            print("  ✓ 测试流式生成...")
            stream_request = OptimizedTextGenerationRequest(
                messages=[
                    Message(role=MessageRole.USER, content="简单介绍一下机器学习")
                ],
                model="command-light",  # 使用轻量级模型
                generation=GenerationConfig(temperature=0.5, max_tokens=50),
                stream=StreamConfig(enabled=True)
            )
            
            stream_response = await provider.generate_text_optimized(stream_request, api_key)
            stream_chunks = []
            async for chunk in stream_response:
                stream_chunks.append(chunk)
                if len(stream_chunks) >= 3:  # 只收集前几个块
                    break
            
            return {
                "success": True,
                "health_check": health_ok,
                "basic_generation": {
                    "model": response.model,
                    "content": response.choices[0].message.content[:100] + "...",
                    "usage": {
                        "prompt_tokens": response.usage.prompt_tokens,
                        "completion_tokens": response.usage.completion_tokens
                    }
                },
                "compatibility": {
                    "compatible": compatibility_report["compatible"],
                    "warnings": len(compatibility_report["warnings"]),
                    "suggestions": len(compatibility_report["suggestions"])
                },
                "streaming": {
                    "chunks_received": len(stream_chunks),
                    "first_chunk_content": stream_chunks[0].choices[0].delta.content if stream_chunks else None
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"测试过程中发生异常: {str(e)}",
                "exception_type": type(e).__name__
            }
    
    async def test_huggingface_provider(self) -> Dict[str, Any]:
        """测试Hugging Face供应商"""
        print("🤗 测试Hugging Face供应商...")
        
        # 检查API密钥
        api_key = os.getenv("HUGGINGFACE_API_KEY")
        if not api_key:
            return {
                "success": False,
                "error": "未设置HUGGINGFACE_API_KEY环境变量",
                "suggestion": "请设置: export HUGGINGFACE_API_KEY='hf_your-token'"
            }
        
        try:
            # 创建供应商实例
            config = ProviderConfig(
                name="huggingface",
                enabled=True,
                api_key=api_key,
                base_url="https://router.huggingface.co/v1"
            )
            key_manager = KeyManager()
            provider = HuggingFaceProvider(config, key_manager)
            
            # 测试健康检查
            print("  ✓ 执行健康检查...")
            health_ok = await provider._perform_health_check(api_key)
            if not health_ok:
                return {
                    "success": False,
                    "error": "Hugging Face API健康检查失败",
                    "suggestion": "检查API密钥是否有效，或者服务是否可用"
                }
            
            # 测试基本文本生成
            print("  ✓ 测试基本文本生成...")
            basic_request = OptimizedTextGenerationRequest(
                messages=[
                    Message(role=MessageRole.USER, content="What is machine learning in one sentence?")
                ],
                model="meta-llama/Llama-3.2-3B-Instruct",  # 使用较小的模型
                generation=GenerationConfig(
                    temperature=0.7,
                    max_tokens=100,
                    top_p=0.9
                    # 注意：不设置top_k，因为Hugging Face不支持
                )
            )
            
            response = await provider.generate_text_optimized(basic_request, api_key)
            if not response or not response.choices:
                return {
                    "success": False,
                    "error": "文本生成返回空响应"
                }
            
            # 测试兼容性检查
            print("  ✓ 测试兼容性检查...")
            compatibility_report = self.compatibility_manager.validate_request_compatibility(
                basic_request, "huggingface"
            )
            
            # 测试中文模型
            print("  ✓ 测试中文模型...")
            chinese_request = OptimizedTextGenerationRequest(
                messages=[
                    Message(role=MessageRole.USER, content="用一句话解释深度学习")
                ],
                model="huggingface-chinese",  # 使用别名
                generation=GenerationConfig(temperature=0.5, max_tokens=50)
            )
            
            chinese_response = await provider.generate_text_optimized(chinese_request, api_key)
            
            return {
                "success": True,
                "health_check": health_ok,
                "basic_generation": {
                    "model": response.model,
                    "content": response.choices[0].message.content[:100] + "...",
                    "usage": {
                        "prompt_tokens": response.usage.prompt_tokens,
                        "completion_tokens": response.usage.completion_tokens
                    }
                },
                "compatibility": {
                    "compatible": compatibility_report["compatible"],
                    "warnings": len(compatibility_report["warnings"]),
                    "suggestions": len(compatibility_report["suggestions"])
                },
                "chinese_model": {
                    "model": chinese_response.model,
                    "content": chinese_response.choices[0].message.content[:100] + "..."
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"测试过程中发生异常: {str(e)}",
                "exception_type": type(e).__name__
            }
    
    async def test_compatibility_features(self) -> Dict[str, Any]:
        """测试兼容性功能"""
        print("🔧 测试兼容性功能...")
        
        try:
            # 创建测试请求
            test_request = OptimizedTextGenerationRequest(
                messages=[
                    Message(role=MessageRole.SYSTEM, content="你是一个有用的AI助手"),
                    Message(role=MessageRole.USER, content="Hello")
                ],
                model="test-model",
                generation=GenerationConfig(
                    temperature=0.8,
                    max_tokens=1000,
                    top_p=0.9,
                    top_k=50,
                    stop=["END", "STOP"]
                ),
                stream=StreamConfig(enabled=True, chunk_size=100)
            )
            
            # 测试所有供应商的兼容性
            compatibility_results = {}
            for provider_name in ["cohere", "huggingface", "openai", "anthropic"]:
                report = self.compatibility_manager.validate_request_compatibility(
                    test_request, provider_name
                )
                compatibility_results[provider_name] = {
                    "compatible": report["compatible"],
                    "warnings": report["warnings"],
                    "unsupported_features": report["unsupported_features"],
                    "suggestions": report["suggestions"]
                }
            
            # 测试参数适配
            cohere_adapted = self.compatibility_manager.adapt_request_for_provider(
                test_request, "cohere"
            )
            hf_adapted = self.compatibility_manager.adapt_request_for_provider(
                test_request, "huggingface"
            )
            
            return {
                "success": True,
                "compatibility_reports": compatibility_results,
                "parameter_adaptation": {
                    "cohere_params": list(cohere_adapted.keys()),
                    "huggingface_params": list(hf_adapted.keys()),
                    "cohere_has_preamble": "preamble" in cohere_adapted,
                    "hf_has_messages": "messages" in hf_adapted
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"兼容性测试失败: {str(e)}",
                "exception_type": type(e).__name__
            }
    
    def print_results(self, results: Dict[str, Any]) -> None:
        """打印测试结果"""
        print("\n" + "="*60)
        print("📊 测试结果汇总")
        print("="*60)
        
        for test_name, result in results.items():
            print(f"\n🔍 {test_name}:")
            if result["success"]:
                print("  ✅ 测试通过")
                # 打印详细信息
                for key, value in result.items():
                    if key != "success":
                        if isinstance(value, dict):
                            print(f"  📋 {key}:")
                            for sub_key, sub_value in value.items():
                                print(f"    - {sub_key}: {sub_value}")
                        else:
                            print(f"  📋 {key}: {value}")
            else:
                print("  ❌ 测试失败")
                print(f"  🚨 错误: {result['error']}")
                if "suggestion" in result:
                    print(f"  💡 建议: {result['suggestion']}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="测试新增AI供应商")
    parser.add_argument(
        "--provider",
        choices=["cohere", "huggingface", "compatibility"],
        help="指定要测试的供应商"
    )
    parser.add_argument(
        "--all",
        action="store_true",
        help="测试所有功能"
    )
    
    args = parser.parse_args()
    
    tester = NewProviderTester()
    results = {}
    
    if args.all or args.provider == "cohere":
        results["Cohere供应商"] = await tester.test_cohere_provider()
    
    if args.all or args.provider == "huggingface":
        results["Hugging Face供应商"] = await tester.test_huggingface_provider()
    
    if args.all or args.provider == "compatibility":
        results["兼容性功能"] = await tester.test_compatibility_features()
    
    if not results:
        print("请指定要测试的供应商或使用 --all 测试所有功能")
        parser.print_help()
        return
    
    tester.print_results(results)
    
    # 保存结果到文件
    with open("test_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细结果已保存到: test_results.json")


if __name__ == "__main__":
    asyncio.run(main())
