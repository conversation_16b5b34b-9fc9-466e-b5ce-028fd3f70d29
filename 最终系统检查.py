#!/usr/bin/env python3
"""
AI Gen Hub 最终系统检查

对整个系统进行全面检查，确保所有功能正常工作
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def check_project_structure():
    """检查项目结构"""
    print("🔍 检查项目结构")
    print("-" * 30)
    
    required_dirs = [
        "src/ai_gen_hub",
        "src/ai_gen_hub/api",
        "src/ai_gen_hub/core",
        "src/ai_gen_hub/services",
        "src/ai_gen_hub/providers",
        "src/ai_gen_hub/cache",
        "src/ai_gen_hub/database",
        "src/ai_gen_hub/monitoring",
        "src/ai_gen_hub/utils",
        "src/ai_gen_hub/web",
        "tests",
        "docs"
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
        else:
            print(f"✅ {dir_path}")
    
    if missing_dirs:
        print(f"❌ 缺少目录: {missing_dirs}")
        return False
    
    print("✅ 项目结构完整")
    return True

def check_core_files():
    """检查核心文件"""
    print("\n🔍 检查核心文件")
    print("-" * 30)
    
    core_files = [
        "src/ai_gen_hub/__init__.py",
        "src/ai_gen_hub/main.py",
        "src/ai_gen_hub/config/settings.py",
        "src/ai_gen_hub/core/models/base.py",
        "src/ai_gen_hub/core/models/requests.py",
        "src/ai_gen_hub/core/models/responses.py",
        "src/ai_gen_hub/services/text_generation.py",
        "src/ai_gen_hub/services/image_generation.py",
        "src/ai_gen_hub/services/workflow_service.py",
        "src/ai_gen_hub/services/collaboration_service.py",
        "src/ai_gen_hub/services/recommendation_service.py",
        "src/ai_gen_hub/services/enterprise_service.py",
        "requirements.txt",
        "pyproject.toml"
    ]
    
    missing_files = []
    for file_path in core_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    print("✅ 核心文件完整")
    return True

def check_documentation():
    """检查文档"""
    print("\n🔍 检查文档")
    print("-" * 30)
    
    doc_files = [
        "README.md",
        "docs/API文档.md",
        "docs/架构设计文档.md",
        "docs/部署指南.md",
        "docs/AI工作流编排系统说明.md",
        "docs/智能模型路由和A_B测试说明.md",
        "docs/实时协作和共享功能说明.md",
        "docs/AI内容个性化推荐说明.md",
        "docs/企业级部署和集成说明.md",
        "docs/项目完成总结.md"
    ]
    
    existing_docs = []
    missing_docs = []
    
    for doc_file in doc_files:
        if Path(doc_file).exists():
            existing_docs.append(doc_file)
            print(f"✅ {doc_file}")
        else:
            missing_docs.append(doc_file)
            print(f"⚠️ {doc_file} (可选)")
    
    print(f"✅ 文档完成度: {len(existing_docs)}/{len(doc_files)} ({len(existing_docs)/len(doc_files)*100:.1f}%)")
    return True

def check_test_files():
    """检查测试文件"""
    print("\n🔍 检查测试文件")
    print("-" * 30)
    
    test_files = [
        "tests/conftest.py",
        "tests/unit/test_core_models.py",
        "tests/unit/test_auth_system.py",
        "tests/integration/test_api.py",
        "测试AI工作流编排系统.py",
        "测试智能模型路由系统.py",
        "测试实时协作系统.py",
        "测试个性化推荐系统.py",
        "测试企业级部署系统.py"
    ]
    
    existing_tests = []
    missing_tests = []
    
    for test_file in test_files:
        if Path(test_file).exists():
            existing_tests.append(test_file)
            print(f"✅ {test_file}")
        else:
            missing_tests.append(test_file)
            print(f"⚠️ {test_file}")
    
    print(f"✅ 测试文件完成度: {len(existing_tests)}/{len(test_files)} ({len(existing_tests)/len(test_files)*100:.1f}%)")
    return True

def check_configuration():
    """检查配置文件"""
    print("\n🔍 检查配置")
    print("-" * 30)
    
    try:
        # 检查基本导入
        from ai_gen_hub.config.settings import Settings
        print("✅ 配置模块导入成功")
        
        # 检查设置实例化
        settings = Settings()
        print("✅ 设置实例化成功")
        
        # 检查关键配置
        if hasattr(settings, 'api_keys'):
            print("✅ API密钥配置存在")
        else:
            print("⚠️ API密钥配置缺失")
        
        if hasattr(settings, 'database'):
            print("✅ 数据库配置存在")
        else:
            print("⚠️ 数据库配置缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def check_core_models():
    """检查核心模型"""
    print("\n🔍 检查核心模型")
    print("-" * 30)
    
    try:
        # 检查基础模型
        from ai_gen_hub.core.models.base import Message, MessageRole
        from ai_gen_hub.core.models.requests import TextGenerationRequest
        from ai_gen_hub.core.models.responses import TextGenerationResponse
        
        print("✅ 基础模型导入成功")
        
        # 测试模型创建
        message = Message(role=MessageRole.USER, content="测试消息")
        print("✅ Message模型创建成功")
        
        request = TextGenerationRequest(
            messages=[message],
            model="gpt-3.5-turbo"
        )
        print("✅ TextGenerationRequest模型创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 核心模型检查失败: {e}")
        return False

def check_services():
    """检查服务模块"""
    print("\n🔍 检查服务模块")
    print("-" * 30)
    
    services = [
        ("文本生成服务", "ai_gen_hub.services.text_generation", "TextGenerationService"),
        ("图像生成服务", "ai_gen_hub.services.image_generation", "ImageGenerationService"),
        ("工作流服务", "ai_gen_hub.services.workflow_service", "WorkflowService"),
        ("协作服务", "ai_gen_hub.services.collaboration_service", "CollaborationService"),
        ("推荐服务", "ai_gen_hub.services.recommendation_service", "RecommendationService"),
        ("企业服务", "ai_gen_hub.services.enterprise_service", "EnterpriseService")
    ]
    
    success_count = 0
    
    for service_name, module_path, class_name in services:
        try:
            module = __import__(module_path, fromlist=[class_name])
            service_class = getattr(module, class_name)
            print(f"✅ {service_name} ({class_name})")
            success_count += 1
        except Exception as e:
            print(f"❌ {service_name}: {e}")
    
    print(f"✅ 服务模块完成度: {success_count}/{len(services)} ({success_count/len(services)*100:.1f}%)")
    return success_count == len(services)

def check_providers():
    """检查供应商模块"""
    print("\n🔍 检查供应商模块")
    print("-" * 30)
    
    providers = [
        ("OpenAI", "ai_gen_hub.providers.openai_provider", "OpenAIProvider"),
        ("Google AI", "ai_gen_hub.providers.google_ai_provider", "GoogleAIProvider"),
        ("Anthropic", "ai_gen_hub.providers.anthropic_provider", "AnthropicProvider")
    ]
    
    success_count = 0
    
    for provider_name, module_path, class_name in providers:
        try:
            module = __import__(module_path, fromlist=[class_name])
            provider_class = getattr(module, class_name)
            print(f"✅ {provider_name} ({class_name})")
            success_count += 1
        except Exception as e:
            print(f"❌ {provider_name}: {e}")
    
    print(f"✅ 供应商模块完成度: {success_count}/{len(providers)} ({success_count/len(providers)*100:.1f}%)")
    return success_count >= 2  # 至少2个供应商可用

def generate_system_report():
    """生成系统报告"""
    print("\n📊 系统检查报告")
    print("=" * 50)
    
    checks = [
        ("项目结构", check_project_structure),
        ("核心文件", check_core_files),
        ("文档", check_documentation),
        ("测试文件", check_test_files),
        ("配置", check_configuration),
        ("核心模型", check_core_models),
        ("服务模块", check_services),
        ("供应商模块", check_providers)
    ]
    
    passed_checks = 0
    total_checks = len(checks)
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            if result:
                passed_checks += 1
        except Exception as e:
            print(f"❌ {check_name} 检查异常: {e}")
    
    # 生成总结
    print(f"\n📈 系统检查结果: {passed_checks}/{total_checks} ({passed_checks/total_checks*100:.1f}%)")
    
    if passed_checks == total_checks:
        print("\n🎉 系统检查全部通过！")
        print("✨ AI Gen Hub 项目已完全就绪")
        print("🚀 可以进行生产环境部署")
        return True
    elif passed_checks >= total_checks * 0.8:
        print("\n✅ 系统检查基本通过")
        print("⚠️ 部分功能可能需要进一步完善")
        print("🚀 可以进行测试环境部署")
        return True
    else:
        print("\n⚠️ 系统检查发现重要问题")
        print("🔧 需要修复关键问题后再部署")
        return False

def main():
    """主函数"""
    print("🚀 AI Gen Hub 最终系统检查")
    print("=" * 50)
    
    try:
        result = generate_system_report()
        
        if result:
            print("\n🎯 下一步建议:")
            print("  1. 运行完整的测试套件")
            print("  2. 进行性能基准测试")
            print("  3. 准备生产环境部署")
            print("  4. 编写用户使用指南")
            print("  5. 制定运维监控方案")
        
        return result
        
    except Exception as e:
        print(f"\n❌ 系统检查过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 检查过程中发生错误: {e}")
        sys.exit(1)
