#!/usr/bin/env python3
"""
测试Pydantic验证错误修复

这个脚本测试StreamConfig相关的Pydantic验证问题是否已修复。
"""

import json
import logging
import httpx
import asyncio
from typing import Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_stream_config_validation():
    """测试StreamConfig验证修复"""
    logger.info("🧪 测试StreamConfig验证修复...")
    
    base_url = "http://localhost:8001"
    
    # 测试用例1: 传统格式（布尔值stream）
    test_case_1 = {
        "messages": [{"role": "user", "content": "Hello, test message 1"}],
        "model": "gemini-2.5-flash",
        "temperature": 0.7,
        "max_tokens": 50,
        "stream": False  # 布尔值
    }
    
    # 测试用例2: 包含StreamConfig对象的格式
    test_case_2 = {
        "messages": [{"role": "user", "content": "Hello, test message 2"}],
        "model": "gemini-2.5-flash",
        "temperature": 0.7,
        "max_tokens": 50,
        "stream": {  # StreamConfig对象格式
            "enabled": False,
            "chunk_size": 100,
            "include_usage": True
        }
    }
    
    # 测试用例3: 流式请求（布尔值）
    test_case_3 = {
        "messages": [{"role": "user", "content": "Hello, test message 3"}],
        "model": "gemini-2.5-flash",
        "temperature": 0.7,
        "max_tokens": 50,
        "stream": True  # 布尔值流式
    }
    
    # 测试用例4: 流式请求（StreamConfig对象）
    test_case_4 = {
        "messages": [{"role": "user", "content": "Hello, test message 4"}],
        "model": "gemini-2.5-flash",
        "temperature": 0.7,
        "max_tokens": 50,
        "stream": {  # StreamConfig对象格式流式
            "enabled": True,
            "chunk_size": 50,
            "include_usage": True
        }
    }
    
    test_cases = [
        ("传统格式（非流式）", test_case_1),
        ("StreamConfig对象格式（非流式）", test_case_2),
        ("传统格式（流式）", test_case_3),
        ("StreamConfig对象格式（流式）", test_case_4),
    ]
    
    results = {}
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        for test_name, test_data in test_cases:
            logger.info(f"\n📋 测试: {test_name}")
            logger.info(f"   请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
            
            try:
                # 发送请求到传统API端点
                response = await client.post(
                    f"{base_url}/api/v1/text/generate",
                    json=test_data,
                    headers={"Content-Type": "application/json"}
                )
                
                logger.info(f"   响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    # 检查是否是流式响应
                    content_type = response.headers.get("content-type", "")
                    if "text/event-stream" in content_type:
                        logger.info("   ✅ 流式响应成功")
                        # 读取一些流式数据
                        content = response.text[:200] + "..." if len(response.text) > 200 else response.text
                        logger.info(f"   流式内容预览: {content}")
                        results[test_name] = "✅ 成功（流式）"
                    else:
                        response_data = response.json()
                        logger.info("   ✅ 同步响应成功")
                        logger.info(f"   响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)[:200]}...")
                        results[test_name] = "✅ 成功（同步）"
                else:
                    error_data = response.json()
                    logger.error(f"   ❌ 请求失败: {error_data}")
                    results[test_name] = f"❌ 失败 ({response.status_code})"
                    
                    # 检查是否是Pydantic验证错误
                    error_detail = str(error_data.get("detail", ""))
                    if "validation error" in error_detail.lower() and "streamconfig" in error_detail.lower():
                        logger.error("   🔍 检测到StreamConfig验证错误！")
                        results[test_name] += " - StreamConfig验证错误"
                    
            except Exception as e:
                logger.error(f"   ❌ 请求异常: {e}")
                results[test_name] = f"❌ 异常: {str(e)}"
    
    return results

async def test_v2_api():
    """测试V2 API端点"""
    logger.info("\n🧪 测试V2 API端点...")
    
    base_url = "http://localhost:8001"
    
    # 优化版本格式
    optimized_request = {
        "messages": [{"role": "user", "content": "Hello from V2 API"}],
        "model": "gemini-2.5-flash",
        "generation": {
            "temperature": 0.7,
            "max_tokens": 50
        },
        "stream": {
            "enabled": False,
            "chunk_size": 100,
            "include_usage": True
        }
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{base_url}/api/v1/text/v2/generate",
                json=optimized_request,
                headers={"Content-Type": "application/json"}
            )
            
            logger.info(f"V2 API响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                logger.info("✅ V2 API测试成功")
                logger.info(f"响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)[:200]}...")
                return "✅ 成功"
            else:
                error_data = response.json()
                logger.error(f"❌ V2 API测试失败: {error_data}")
                return f"❌ 失败 ({response.status_code})"
                
    except Exception as e:
        logger.error(f"❌ V2 API测试异常: {e}")
        return f"❌ 异常: {str(e)}"

async def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("🧪 AI Gen Hub Pydantic验证修复测试")
    logger.info("=" * 60)
    
    # 检查服务器是否运行
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get("http://localhost:8001/health")
            if response.status_code != 200:
                logger.error("❌ 服务器健康检查失败")
                return False
            logger.info("✅ 服务器运行正常")
    except Exception as e:
        logger.error(f"❌ 无法连接到服务器: {e}")
        logger.info("请先启动服务器: python start_server_with_correct_cli.py")
        return False
    
    # 测试StreamConfig验证修复
    stream_results = await test_stream_config_validation()
    
    # 测试V2 API
    v2_result = await test_v2_api()
    
    # 输出测试结果摘要
    logger.info("\n" + "=" * 60)
    logger.info("📊 测试结果摘要")
    logger.info("=" * 60)
    
    logger.info("\n🔧 StreamConfig验证测试:")
    for test_name, result in stream_results.items():
        logger.info(f"  {test_name}: {result}")
    
    logger.info(f"\n🔧 V2 API测试: {v2_result}")
    
    # 计算成功率
    total_tests = len(stream_results) + 1
    successful_tests = sum(1 for result in stream_results.values() if "✅" in result)
    if "✅" in v2_result:
        successful_tests += 1
    
    success_rate = (successful_tests / total_tests) * 100
    logger.info(f"\n📈 总体成功率: {successful_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        logger.info("🎉 修复验证成功！大部分功能正常工作")
        return True
    else:
        logger.warning("⚠️  仍有问题需要修复")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\n👋 测试被用户取消")
        exit(0)
    except Exception as e:
        logger.error(f"❌ 测试脚本异常: {e}")
        exit(1)
