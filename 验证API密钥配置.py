#!/usr/bin/env python3
"""
AI Gen Hub API密钥配置验证脚本

此脚本用于验证AI供应商API密钥的配置是否正确，
并提供详细的诊断信息和修复建议。
"""

import os
import sys
import asyncio
import re
from typing import Dict, List, Tuple, Optional
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

try:
    from ai_gen_hub.config import get_settings
    from ai_gen_hub.services.provider_manager import AIProviderManager
    from ai_gen_hub.utils.key_manager import KeyManager
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)


class APIKeyValidator:
    """API密钥验证器"""
    
    def __init__(self):
        self.results = {}
        self.settings = None
        
    def print_header(self):
        """打印标题"""
        print("=" * 60)
        print("🔑 AI Gen Hub API密钥配置验证")
        print("=" * 60)
        print()
    
    def print_section(self, title: str):
        """打印章节标题"""
        print(f"\n📋 {title}")
        print("-" * 40)
    
    def print_success(self, message: str):
        """打印成功信息"""
        print(f"✅ {message}")
    
    def print_warning(self, message: str):
        """打印警告信息"""
        print(f"⚠️  {message}")
    
    def print_error(self, message: str):
        """打印错误信息"""
        print(f"❌ {message}")
    
    def print_info(self, message: str):
        """打印信息"""
        print(f"ℹ️  {message}")
    
    def check_env_file(self) -> bool:
        """检查.env文件是否存在"""
        self.print_section("环境配置文件检查")
        
        env_file = project_root / ".env"
        env_example = project_root / ".env.example"
        
        if not env_file.exists():
            self.print_error(f".env文件不存在: {env_file}")
            if env_example.exists():
                self.print_info(f"发现示例文件: {env_example}")
                self.print_info("请运行: cp .env.example .env")
            else:
                self.print_error("示例文件也不存在，请检查项目完整性")
            return False
        
        self.print_success(f".env文件存在: {env_file}")
        
        # 检查文件权限
        if not os.access(env_file, os.R_OK):
            self.print_error(".env文件不可读，请检查文件权限")
            return False
        
        self.print_success(".env文件可读")
        return True
    
    def load_settings(self) -> bool:
        """加载配置设置"""
        self.print_section("配置加载检查")
        
        try:
            self.settings = get_settings()
            self.print_success("配置加载成功")
            return True
        except Exception as e:
            self.print_error(f"配置加载失败: {e}")
            return False
    
    def validate_key_format(self, provider: str, key: str) -> Tuple[bool, str]:
        """验证API密钥格式"""
        patterns = {
            "openai": r"^sk-[a-zA-Z0-9]{48}$",
            "google_ai": r"^AIzaSy[a-zA-Z0-9_-]{33}$",
            "anthropic": r"^sk-ant-[a-zA-Z0-9_-]+$",
            "dashscope": r"^sk-[a-zA-Z0-9]+$"
        }
        
        if provider not in patterns:
            return True, "未知供应商，跳过格式验证"
        
        pattern = patterns[provider]
        if re.match(pattern, key):
            return True, "格式正确"
        else:
            return False, f"格式不正确，应匹配: {pattern}"
    
    def check_api_keys(self) -> Dict[str, List[str]]:
        """检查API密钥配置"""
        self.print_section("API密钥配置检查")
        
        if not self.settings:
            self.print_error("配置未加载，无法检查API密钥")
            return {}
        
        providers_config = {
            "OpenAI": "openai_api_keys",
            "Google AI": "google_ai_api_keys", 
            "Anthropic": "anthropic_api_keys",
            "DashScope": "dashscope_api_keys"
        }
        
        configured_providers = {}
        
        for provider_name, config_key in providers_config.items():
            try:
                # 尝试获取配置值
                keys = getattr(self.settings, config_key, None)
                
                if not keys:
                    self.print_warning(f"{provider_name}: 未配置API密钥")
                    continue
                
                # 解析密钥列表
                if isinstance(keys, str):
                    key_list = [k.strip() for k in keys.split(",") if k.strip()]
                else:
                    key_list = keys
                
                if not key_list:
                    self.print_warning(f"{provider_name}: API密钥为空")
                    continue
                
                # 验证每个密钥
                valid_keys = []
                for i, key in enumerate(key_list):
                    provider_key = provider_name.lower().replace(" ", "_")
                    is_valid, message = self.validate_key_format(provider_key, key)
                    
                    if is_valid:
                        self.print_success(f"{provider_name}: 密钥 {i+1} - {message}")
                        valid_keys.append(key)
                    else:
                        self.print_error(f"{provider_name}: 密钥 {i+1} - {message}")
                        self.print_info(f"  密钥内容: {key[:10]}...")
                
                if valid_keys:
                    configured_providers[provider_name] = valid_keys
                    self.print_success(f"{provider_name}: 共配置 {len(valid_keys)} 个有效密钥")
                
            except AttributeError:
                self.print_warning(f"{provider_name}: 配置项 {config_key} 不存在")
            except Exception as e:
                self.print_error(f"{provider_name}: 检查失败 - {e}")
        
        return configured_providers
    
    async def test_provider_connections(self, configured_providers: Dict[str, List[str]]):
        """测试供应商连接"""
        self.print_section("供应商连接测试")
        
        if not configured_providers:
            self.print_warning("没有配置的供应商，跳过连接测试")
            return
        
        try:
            # 初始化密钥管理器
            key_manager = KeyManager(self.settings)
            await key_manager.initialize()
            
            # 初始化供应商管理器
            provider_manager = AIProviderManager(self.settings, key_manager)
            await provider_manager.initialize()
            
            # 测试每个供应商
            for provider_name in configured_providers.keys():
                try:
                    provider_key = provider_name.lower().replace(" ", "_")
                    
                    # 执行健康检查
                    is_healthy = await provider_manager.health_check_provider(provider_key)
                    
                    if is_healthy:
                        self.print_success(f"{provider_name}: 连接测试通过")
                    else:
                        self.print_error(f"{provider_name}: 连接测试失败")
                        
                except Exception as e:
                    self.print_error(f"{provider_name}: 连接测试异常 - {e}")
            
            # 清理资源
            await provider_manager.cleanup()
            await key_manager.cleanup()
            
        except Exception as e:
            self.print_error(f"供应商连接测试失败: {e}")
    
    def check_basic_config(self):
        """检查基础配置"""
        self.print_section("基础配置检查")
        
        if not self.settings:
            self.print_error("配置未加载，无法检查基础配置")
            return
        
        # 检查必要的配置项
        required_configs = {
            "环境": "environment",
            "调试模式": "debug", 
            "API端口": "api_port",
            "JWT密钥": "jwt_secret_key"
        }
        
        for name, attr in required_configs.items():
            try:
                value = getattr(self.settings, attr, None)
                if value:
                    if attr == "jwt_secret_key" and value == "dev-secret-key-change-in-production":
                        self.print_warning(f"{name}: 使用默认值，生产环境请更改")
                    else:
                        self.print_success(f"{name}: 已配置")
                else:
                    self.print_warning(f"{name}: 未配置")
            except Exception as e:
                self.print_error(f"{name}: 检查失败 - {e}")
    
    def generate_recommendations(self, configured_providers: Dict[str, List[str]]):
        """生成配置建议"""
        self.print_section("配置建议")
        
        if not configured_providers:
            self.print_error("没有配置任何AI供应商，系统无法正常工作")
            self.print_info("建议配置步骤:")
            self.print_info("1. 获取OpenAI API密钥: https://platform.openai.com/api-keys")
            self.print_info("2. 在.env文件中添加: OPENAI_API_KEYS=sk-your-key-here")
            self.print_info("3. 重新运行此验证脚本")
            return
        
        self.print_success(f"已配置 {len(configured_providers)} 个AI供应商")
        
        # 推荐配置更多供应商
        all_providers = ["OpenAI", "Google AI", "Anthropic", "DashScope"]
        missing_providers = [p for p in all_providers if p not in configured_providers]
        
        if missing_providers:
            self.print_info("建议配置更多供应商以提高可用性:")
            for provider in missing_providers:
                if provider == "Google AI":
                    self.print_info(f"  - {provider}: https://makersuite.google.com/app/apikey")
                elif provider == "Anthropic":
                    self.print_info(f"  - {provider}: https://console.anthropic.com/")
                elif provider == "DashScope":
                    self.print_info(f"  - {provider}: https://dashscope.console.aliyun.com/")
        
        # 安全建议
        self.print_info("安全建议:")
        self.print_info("  - 定期轮换API密钥")
        self.print_info("  - 不要将密钥提交到版本控制")
        self.print_info("  - 生产环境使用强JWT密钥")
        self.print_info("  - 启用API访问限制")
    
    def print_summary(self, configured_providers: Dict[str, List[str]]):
        """打印总结"""
        self.print_section("验证总结")
        
        total_keys = sum(len(keys) for keys in configured_providers.values())
        
        if configured_providers:
            self.print_success(f"✅ 配置验证完成")
            self.print_success(f"✅ 已配置 {len(configured_providers)} 个供应商")
            self.print_success(f"✅ 总计 {total_keys} 个API密钥")
            
            print("\n🚀 下一步操作:")
            print("1. 启动服务器: python simple_start.py")
            print("2. 访问API文档: http://localhost:8001/docs")
            print("3. 使用调试页面: http://localhost:8001/debug")
            print("4. 测试API功能")
        else:
            self.print_error("❌ 配置验证失败")
            self.print_error("❌ 没有配置有效的API密钥")
            
            print("\n🔧 修复步骤:")
            print("1. 检查.env文件是否存在")
            print("2. 配置至少一个AI供应商的API密钥")
            print("3. 重新运行此验证脚本")
    
    async def run_validation(self):
        """运行完整的验证流程"""
        self.print_header()
        
        # 1. 检查环境文件
        if not self.check_env_file():
            return
        
        # 2. 加载配置
        if not self.load_settings():
            return
        
        # 3. 检查基础配置
        self.check_basic_config()
        
        # 4. 检查API密钥
        configured_providers = self.check_api_keys()
        
        # 5. 测试供应商连接
        await self.test_provider_connections(configured_providers)
        
        # 6. 生成建议
        self.generate_recommendations(configured_providers)
        
        # 7. 打印总结
        self.print_summary(configured_providers)


async def main():
    """主函数"""
    validator = APIKeyValidator()
    await validator.run_validation()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️ 验证被用户中断")
    except Exception as e:
        print(f"\n\n❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
