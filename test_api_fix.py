#!/usr/bin/env python3
"""
API修复效果测试脚本

用于验证API接口问题修复的效果，包括：
1. 日志记录是否正常
2. 超时配置是否生效
3. 错误处理是否完善
4. 流式响应是否正常

使用方法:
    python test_api_fix.py
"""

import asyncio
import json
import logging
import sys
import time
from typing import Dict, Any

import httpx

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class APITester:
    """API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=300.0)
    
    async def test_sync_request(self) -> Dict[str, Any]:
        """测试同步请求"""
        logger.info("=== 测试同步请求 ===")
        
        request_data = {
            "model": "gemini-2.5-flash",
            "messages": [
                {
                    "role": "user",
                    "content": "请简单介绍一下你自己，不超过50字。"
                }
            ],
            "stream": False,
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        try:
            start_time = time.time()
            logger.info("发送同步请求...")
            
            response = await self.client.post(
                f"{self.base_url}/api/v1/text/generate",
                json=request_data,
                headers={"Content-Type": "application/json"}
            )
            
            elapsed_time = time.time() - start_time
            logger.info(f"同步请求完成，耗时: {elapsed_time:.2f}秒")
            logger.info(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                logger.info("同步请求成功")
                return {
                    "success": True,
                    "elapsed_time": elapsed_time,
                    "response": response_data
                }
            else:
                error_text = response.text
                logger.error(f"同步请求失败: {error_text}")
                return {
                    "success": False,
                    "error": error_text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            logger.error(f"同步请求异常: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def test_stream_request(self) -> Dict[str, Any]:
        """测试流式请求"""
        logger.info("=== 测试流式请求 ===")
        
        request_data = {
            "model": "gemini-2.5-flash",
            "messages": [
                {
                    "role": "user",
                    "content": "请写一个关于春天的短诗，4行即可。"
                }
            ],
            "stream": True,
            "temperature": 0.7,
            "max_tokens": 200
        }
        
        try:
            start_time = time.time()
            logger.info("发送流式请求...")
            
            chunk_count = 0
            total_content = ""
            
            async with self.client.stream(
                "POST",
                f"{self.base_url}/api/v1/text/generate",
                json=request_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                logger.info(f"流式响应状态码: {response.status_code}")
                
                if response.status_code != 200:
                    error_text = await response.aread()
                    logger.error(f"流式请求失败: {error_text.decode()}")
                    return {
                        "success": False,
                        "error": error_text.decode(),
                        "status_code": response.status_code
                    }
                
                async for chunk in response.aiter_bytes():
                    chunk_count += 1
                    chunk_str = chunk.decode('utf-8')
                    
                    # 解析SSE数据
                    for line in chunk_str.split('\n'):
                        line = line.strip()
                        if line.startswith('data: '):
                            data_str = line[6:]
                            if data_str and data_str != '[DONE]':
                                try:
                                    data = json.loads(data_str)
                                    # 提取内容
                                    if 'choices' in data and data['choices']:
                                        choice = data['choices'][0]
                                        if 'delta' in choice and 'content' in choice['delta']:
                                            content = choice['delta']['content']
                                            total_content += content
                                            logger.debug(f"收到内容: {content}")
                                except json.JSONDecodeError:
                                    continue
            
            elapsed_time = time.time() - start_time
            logger.info(f"流式请求完成，耗时: {elapsed_time:.2f}秒")
            logger.info(f"共收到 {chunk_count} 个数据块")
            logger.info(f"生成内容: {total_content}")
            
            return {
                "success": True,
                "elapsed_time": elapsed_time,
                "chunk_count": chunk_count,
                "content": total_content
            }
            
        except Exception as e:
            logger.error(f"流式请求异常: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def test_error_handling(self) -> Dict[str, Any]:
        """测试错误处理"""
        logger.info("=== 测试错误处理 ===")
        
        # 使用无效的模型名称
        request_data = {
            "model": "invalid-model-name",
            "messages": [
                {
                    "role": "user",
                    "content": "测试错误处理"
                }
            ],
            "stream": False
        }
        
        try:
            logger.info("发送错误请求（无效模型）...")
            
            response = await self.client.post(
                f"{self.base_url}/api/v1/text/generate",
                json=request_data,
                headers={"Content-Type": "application/json"}
            )
            
            logger.info(f"错误请求响应状态码: {response.status_code}")
            
            if response.status_code != 200:
                error_text = response.text
                logger.info(f"正确捕获错误: {error_text}")
                return {
                    "success": True,
                    "error_handled": True,
                    "error_message": error_text
                }
            else:
                logger.warning("错误请求意外成功")
                return {
                    "success": False,
                    "error_handled": False,
                    "message": "错误请求意外成功"
                }
                
        except Exception as e:
            logger.error(f"错误处理测试异常: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def test_health_check(self) -> Dict[str, Any]:
        """测试健康检查"""
        logger.info("=== 测试健康检查 ===")
        
        try:
            response = await self.client.get(f"{self.base_url}/health")
            logger.info(f"健康检查状态码: {response.status_code}")
            
            if response.status_code == 200:
                health_data = response.json()
                logger.info(f"健康检查结果: {health_data}")
                return {
                    "success": True,
                    "health_data": health_data
                }
            else:
                logger.error(f"健康检查失败: {response.text}")
                return {
                    "success": False,
                    "error": response.text
                }
                
        except Exception as e:
            logger.error(f"健康检查异常: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("开始API修复效果测试...")
        
        results = {}
        
        # 1. 健康检查
        results["health_check"] = await self.test_health_check()
        
        # 2. 同步请求测试
        results["sync_request"] = await self.test_sync_request()
        
        # 3. 流式请求测试
        results["stream_request"] = await self.test_stream_request()
        
        # 4. 错误处理测试
        results["error_handling"] = await self.test_error_handling()
        
        return results
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


async def main():
    """主函数"""
    tester = APITester()
    
    try:
        results = await tester.run_all_tests()
        
        # 输出测试结果摘要
        logger.info("=== 测试结果摘要 ===")
        
        for test_name, result in results.items():
            status = "✅ 通过" if result.get("success", False) else "❌ 失败"
            logger.info(f"{test_name}: {status}")
            
            if not result.get("success", False) and "error" in result:
                logger.error(f"  错误: {result['error']}")
        
        # 计算总体成功率
        total_tests = len(results)
        successful_tests = sum(1 for r in results.values() if r.get("success", False))
        success_rate = (successful_tests / total_tests) * 100
        
        logger.info(f"总体成功率: {successful_tests}/{total_tests} ({success_rate:.1f}%)")
        
        if success_rate >= 75:
            logger.info("🎉 API修复效果良好！")
        elif success_rate >= 50:
            logger.warning("⚠️ API修复部分有效，仍需改进")
        else:
            logger.error("❌ API修复效果不佳，需要进一步调试")
        
        return results
        
    finally:
        await tester.close()


if __name__ == "__main__":
    asyncio.run(main())
