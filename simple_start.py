#!/usr/bin/env python3
"""
AI Gen Hub 简单启动脚本

最简化的启动方式，用于测试修复效果。

使用方法:
    python simple_start.py
"""

import os
import sys
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

# 设置环境变量
os.environ['DEBUG'] = 'true'
os.environ['LOG_LEVEL'] = 'DEBUG'
os.environ['ENVIRONMENT'] = 'development'
# 检查是否有真实的API密钥，如果没有则提示用户
google_ai_keys = os.environ.get('GOOGLE_AI_API_KEYS', '')
if not google_ai_keys or google_ai_keys == 'test-key-for-debugging':
    print("⚠️  需要设置真实的Google AI API密钥")
    print("请设置环境变量: export GOOGLE_AI_API_KEYS='your-real-api-key'")
    print("或者编辑.env文件")
    # 为了演示，我们仍然使用测试密钥，但会显示警告
    os.environ['GOOGLE_AI_API_KEYS'] = 'test-key-for-debugging'
os.environ['GOOGLE_AI_ENABLED'] = 'true'
os.environ['ENABLE_CACHING'] = 'false'  # 禁用缓存避免Redis依赖问题
os.environ['ENABLE_METRICS'] = 'false'  # 禁用指标避免依赖问题

def main():
    """主函数"""
    print("🚀 启动AI Gen Hub服务器（简化版）...")
    
    try:
        # 导入必要的模块
        from ai_gen_hub.api.app import create_app
        from ai_gen_hub.core.logging import setup_logging
        
        # 设置日志
        setup_logging(debug=True)
        print("✅ 日志系统初始化成功")
        
        # 创建FastAPI应用
        print("📦 创建FastAPI应用...")
        app = create_app()
        print("✅ FastAPI应用创建成功")
        
        # 启动服务器
        print("🌐 启动服务器...")
        print("📍 地址: http://localhost:8001")
        print("📖 API文档: http://localhost:8001/docs")
        print("🏥 健康检查: http://localhost:8001/health")
        print("按 Ctrl+C 停止服务器")
        
        import uvicorn
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8001,
            log_level="info",  # 使用info级别避免过多日志
            access_log=True
        )
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请安装必要的依赖:")
        print("   pip install --break-system-packages fastapi uvicorn httpx structlog pydantic")
        return 1
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
