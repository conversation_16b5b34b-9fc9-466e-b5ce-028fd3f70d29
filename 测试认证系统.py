#!/usr/bin/env python3
"""
测试认证与授权系统

验证用户管理、JWT认证、API密钥、权限控制等功能
"""

import asyncio
import sys
import json
import requests
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

try:
    from ai_gen_hub.services.auth_service import AuthService
    from ai_gen_hub.core.models.auth import (
        UserCreate, UserLogin, APIKeyCreate, PasswordChange,
        UserRole, Permission
    )
    from ai_gen_hub.core.auth import get_auth_service, set_auth_service
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)


def test_auth_service():
    """测试认证服务基础功能"""
    print("🔧 测试认证服务基础功能")
    print("=" * 60)
    
    try:
        # 创建认证服务
        auth_service = AuthService(
            jwt_secret_key="test-secret-key",
            jwt_algorithm="HS256",
            access_token_expire_minutes=30
        )
        
        print("✅ 认证服务创建成功")
        
        # 测试用户创建
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="testpassword123",
            full_name="测试用户",
            role=UserRole.USER
        )
        
        user = auth_service.create_user(user_data)
        print(f"✅ 用户创建成功: {user.username} ({user.email})")
        
        # 测试用户认证
        authenticated_user = auth_service.authenticate_user("testuser", "testpassword123")
        if authenticated_user:
            print("✅ 用户认证成功")
        else:
            print("❌ 用户认证失败")
            return False
        
        # 测试JWT Token生成
        access_token = auth_service.create_access_token(user)
        print(f"✅ JWT Token生成成功: {access_token[:20]}...")
        
        # 测试JWT Token验证
        payload = auth_service.verify_token(access_token)
        print(f"✅ JWT Token验证成功: 用户ID {payload.sub}")
        
        # 测试API密钥创建
        api_key_data = APIKeyCreate(
            name="测试密钥",
            permissions=[Permission.API_TEXT_GENERATE],
            rate_limit=100
        )
        
        api_key_obj, api_key = auth_service.create_api_key(user.id, api_key_data)
        print(f"✅ API密钥创建成功: {api_key[:10]}...")
        
        # 测试API密钥验证
        verified_key = auth_service.verify_api_key(api_key)
        if verified_key:
            print("✅ API密钥验证成功")
        else:
            print("❌ API密钥验证失败")
            return False
        
        # 测试权限检查
        has_permission = auth_service.check_permission(user, Permission.API_TEXT_GENERATE)
        print(f"✅ 权限检查: API_TEXT_GENERATE = {has_permission}")
        
        # 测试配额检查
        can_use_api = auth_service.check_quota(user.id)
        print(f"✅ 配额检查: 可以使用API = {can_use_api}")
        
        return True
        
    except Exception as e:
        print(f"❌ 认证服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_endpoints():
    """测试API端点"""
    print("\n🔧 测试认证API端点")
    print("=" * 60)
    
    # 启动服务器（假设已经在运行）
    base_url = "http://localhost:8001"
    
    try:
        # 测试健康检查
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print("❌ 服务器未运行，请先启动服务器")
            return False
        
        # 测试登录
        login_data = {
            "username": "admin",
            "password": "admin123456"
        }
        
        response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data["access_token"]
            print("✅ 用户登录成功")
            print(f"   Token: {access_token[:20]}...")
        else:
            print(f"❌ 用户登录失败: {response.status_code} - {response.text}")
            return False
        
        # 测试获取当前用户信息
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(f"{base_url}/api/v1/auth/me", headers=headers)
        if response.status_code == 200:
            user_info = response.json()
            print(f"✅ 获取用户信息成功: {user_info['username']}")
        else:
            print(f"❌ 获取用户信息失败: {response.status_code} - {response.text}")
            return False
        
        # 测试权限列表
        response = requests.get(f"{base_url}/api/v1/auth/permissions", headers=headers)
        if response.status_code == 200:
            permissions = response.json()
            print(f"✅ 获取权限列表成功: {len(permissions)} 个权限")
        else:
            print(f"❌ 获取权限列表失败: {response.status_code} - {response.text}")
        
        # 测试角色列表
        response = requests.get(f"{base_url}/api/v1/auth/roles", headers=headers)
        if response.status_code == 200:
            roles = response.json()
            print(f"✅ 获取角色列表成功: {len(roles)} 个角色")
        else:
            print(f"❌ 获取角色列表失败: {response.status_code} - {response.text}")
        
        # 测试创建API密钥
        api_key_data = {
            "name": "测试API密钥",
            "permissions": ["api:text:generate"],
            "rate_limit": 100
        }
        
        response = requests.post(f"{base_url}/api/v1/auth/me/api-keys", 
                               json=api_key_data, headers=headers)
        if response.status_code == 200:
            key_info = response.json()
            print("✅ API密钥创建成功")
            api_key = key_info["key"]
            print(f"   密钥: {api_key[:15]}...")
        else:
            print(f"❌ API密钥创建失败: {response.status_code} - {response.text}")
            api_key = None
        
        # 测试使用API密钥访问
        if api_key:
            api_headers = {"X-API-Key": api_key}
            response = requests.post(f"{base_url}/api/v1/text/generate",
                                   json={
                                       "messages": [{"role": "user", "content": "Hello"}],
                                       "model": "gemini-pro",
                                       "max_tokens": 10
                                   },
                                   headers=api_headers)
            print(f"✅ API密钥访问测试: {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
        return False
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_permission_system():
    """测试权限系统"""
    print("\n🔧 测试权限系统")
    print("=" * 60)
    
    try:
        auth_service = AuthService(
            jwt_secret_key="test-secret-key",
            jwt_algorithm="HS256"
        )
        
        # 创建不同角色的用户
        roles_to_test = [
            (UserRole.ADMIN, "管理员"),
            (UserRole.DEVELOPER, "开发者"),
            (UserRole.USER, "普通用户"),
            (UserRole.GUEST, "访客")
        ]
        
        for role, role_name in roles_to_test:
            user_data = UserCreate(
                username=f"test_{role.value}",
                email=f"test_{role.value}@example.com",
                password="password123",
                role=role
            )
            
            user = auth_service.create_user(user_data)
            print(f"\n👤 {role_name} ({user.username}):")
            
            # 测试各种权限
            permissions_to_test = [
                Permission.SYSTEM_ADMIN,
                Permission.USER_CREATE,
                Permission.API_TEXT_GENERATE,
                Permission.DEBUG_ACCESS
            ]
            
            for permission in permissions_to_test:
                has_perm = user.has_permission(permission)
                status = "✅" if has_perm else "❌"
                print(f"   {status} {permission.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 权限系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始认证与授权系统测试")
    print()
    
    tests = [
        ("认证服务基础功能", test_auth_service),
        ("权限系统", test_permission_system),
        ("API端点", test_api_endpoints),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed_tests += 1
            print(f"{'✅ 通过' if result else '❌ 失败'}: {test_name}")
        except Exception as e:
            print(f"❌ 异常: {test_name} - {e}")
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    print(f"📈 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！认证与授权系统工作正常！")
        print("\n主要功能:")
        print("  • 用户管理（创建、认证、更新）")
        print("  • JWT Token生成和验证")
        print("  • API密钥管理")
        print("  • 权限控制系统")
        print("  • 角色权限映射")
        print("  • RESTful API端点")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查实现")
        return False


if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
