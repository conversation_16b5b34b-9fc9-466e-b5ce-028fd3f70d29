#!/usr/bin/env python3
"""
Google AI Provider 修复验证脚本

用于测试修复后的 Google AI Provider 是否能正常工作，
特别是验证 API key 请求头传递方式的修复。

使用方法:
    python test_google_ai_fix.py
"""

import asyncio
import json
import os
import sys
import time
from typing import Dict, Any

import httpx

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ai_gen_hub.config.settings import Settings
from ai_gen_hub.providers.google_ai_provider import GoogleAIProvider
from ai_gen_hub.core.interfaces import TextGenerationRequest, Message, MessageRole
from ai_gen_hub.utils.key_manager import KeyManager


class GoogleAIFixTester:
    """Google AI Provider 修复测试器"""
    
    def __init__(self):
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.api_key = os.getenv('GOOGLE_AI_API_KEYS')
        self.api_key = self.api_key.split(",")[0]
        
        if not self.api_key:
            print("❌ 错误：未找到 GOOGLE_AI_API_KEY 环境变量")
            print("请设置环境变量：export GOOGLE_AI_API_KEY=your-api-key")
            sys.exit(1)
    
    async def test_direct_api_call(self) -> bool:
        """直接测试 Google AI API 调用（使用官方格式）"""
        print("\n🔍 测试1：直接调用 Google AI API（官方格式）")
        
        url = f"{self.base_url}/models/gemini-2.5-flash:generateContent"
        headers = {
            "x-goog-api-key": self.api_key,
            "Content-Type": "application/json"
        }
        
        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": "Hello, how are you?"
                        }
                    ]
                }
            ]
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                print(f"📤 发送请求到: {url}")
                print(f"📋 请求头: {dict(headers)}")
                print(f"📄 请求体: {json.dumps(payload, indent=2)}")
                
                start_time = time.time()
                response = await client.post(url, headers=headers, json=payload)
                elapsed_time = time.time() - start_time
                
                print(f"📥 响应状态: {response.status_code}")
                print(f"⏱️  响应时间: {elapsed_time:.2f}秒")
                
                if response.status_code == 200:
                    response_data = response.json()
                    if "candidates" in response_data:
                        content = response_data["candidates"][0]["content"]["parts"][0]["text"]
                        print(f"✅ 响应内容: {content[:100]}...")
                        return True
                    else:
                        print(f"❌ 响应格式异常: {response_data}")
                        return False
                else:
                    print(f"❌ API 调用失败: {response.text}")
                    return False
                    
        except Exception as e:
            print(f"❌ 直接 API 调用异常: {e}")
            return False
    
    async def test_provider_implementation(self) -> bool:
        """测试修复后的 Provider 实现"""
        print("\n🔍 测试2：修复后的 Google AI Provider")
        
        try:
            # 创建配置和 Provider
            settings = Settings()
            key_manager = KeyManager(settings)
            provider = GoogleAIProvider(settings.google_ai, key_manager)
            
            # 初始化 Provider
            await provider.initialize()
            print("✅ Provider 初始化成功")
            
            # 创建测试请求
            request = TextGenerationRequest(
                model="gemini-2.5-flash",
                messages=[
                    Message(role=MessageRole.USER, content="Hello, how are you?")
                ],
                max_tokens=100,
                temperature=0.7
            )
            
            print(f"📤 发送文本生成请求...")
            start_time = time.time()
            
            # 测试文本生成
            response = await provider.generate_text(request)
            elapsed_time = time.time() - start_time
            
            print(f"⏱️  响应时间: {elapsed_time:.2f}秒")
            
            if response and response.choices:
                content = response.choices[0].message.content
                print(f"✅ 生成内容: {content[:100]}...")
                print(f"📊 使用统计: {response.usage}")
                return True
            else:
                print("❌ 响应为空或格式异常")
                return False
                
        except Exception as e:
            print(f"❌ Provider 测试异常: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            if 'provider' in locals():
                await provider.cleanup()
    
    async def test_stream_generation(self) -> bool:
        """测试流式生成"""
        print("\n🔍 测试3：流式文本生成")
        
        try:
            # 创建配置和 Provider
            settings = Settings()
            key_manager = KeyManager(settings)
            provider = GoogleAIProvider(settings.google_ai, key_manager)
            
            # 初始化 Provider
            await provider.initialize()
            
            # 创建流式请求
            request = TextGenerationRequest(
                model="gemini-2.5-flash",
                messages=[
                    Message(role=MessageRole.USER, content="Write a short story about a cat.")
                ],
                max_tokens=200,
                temperature=0.7,
                stream=True
            )
            
            print(f"📤 发送流式生成请求...")
            start_time = time.time()
            
            # 测试流式生成
            chunk_count = 0
            content_parts = []
            
            async for chunk in await provider.generate_text(request):
                chunk_count += 1
                if chunk.choices and chunk.choices[0].delta.content:
                    content_parts.append(chunk.choices[0].delta.content)
                    if chunk_count <= 3:  # 只显示前几个块
                        print(f"📦 块 {chunk_count}: {chunk.choices[0].delta.content}")
            
            elapsed_time = time.time() - start_time
            full_content = "".join(content_parts)
            
            print(f"⏱️  流式响应时间: {elapsed_time:.2f}秒")
            print(f"📊 总块数: {chunk_count}")
            print(f"✅ 完整内容: {full_content[:150]}...")
            
            return chunk_count > 0
                
        except Exception as e:
            print(f"❌ 流式生成测试异常: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            if 'provider' in locals():
                await provider.cleanup()
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始 Google AI Provider 修复验证测试")
        print("=" * 60)
        
        results = []
        
        # 测试1：直接 API 调用
        result1 = await self.test_direct_api_call()
        results.append(("直接 API 调用", result1))
        
        # 测试2：Provider 实现
        result2 = await self.test_provider_implementation()
        results.append(("Provider 实现", result2))
        
        # 测试3：流式生成
        result3 = await self.test_stream_generation()
        results.append(("流式生成", result3))
        
        # 输出测试结果
        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        
        passed = 0
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n🎯 总体结果: {passed}/{len(results)} 测试通过")
        
        if passed == len(results):
            print("🎉 所有测试通过！Google AI Provider 修复成功！")
        else:
            print("⚠️  部分测试失败，需要进一步检查。")
        
        return passed == len(results)


async def main():
    """主函数"""
    tester = GoogleAIFixTester()
    success = await tester.run_all_tests()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
