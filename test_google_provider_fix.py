#!/usr/bin/env python3
"""
测试 GoogleAIProvider 抽象方法修复

验证 GoogleAIProvider 类是否可以正常实例化，以及所有抽象方法是否已正确实现。
"""

import sys
import os
import asyncio

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_google_provider_instantiation():
    """测试 GoogleAIProvider 实例化"""
    print("🧪 测试 GoogleAIProvider 实例化...")
    
    try:
        from ai_gen_hub.providers.google_ai_provider import GoogleAIProvider
        from ai_gen_hub.config.settings import ProviderConfig
        from ai_gen_hub.utils.key_manager import KeyManager
        
        print("✅ 模块导入成功")
        
        # 创建真实的配置对象
        config = ProviderConfig(
            name="google_ai",
            enabled=True,
            api_keys=["mock_key"],
            rate_limit=60,
            timeout=30
        )
        
        # 创建模拟的密钥管理器
        class MockKeyManager:
            async def get_key(self, provider: str):
                return "mock_key"
        
        key_manager = MockKeyManager()
        
        # 尝试实例化 GoogleAIProvider
        provider = GoogleAIProvider(config, key_manager)
        print("✅ GoogleAIProvider 实例化成功")
        
        # 测试抽象方法是否存在
        abstract_methods = [
            'generate_text',
            'generate_text_stream', 
            'generate_image',
            'get_models',
            'health_check'
        ]
        
        for method_name in abstract_methods:
            if hasattr(provider, method_name):
                method = getattr(provider, method_name)
                if callable(method):
                    print(f"✅ 方法 {method_name} 存在且可调用")
                else:
                    print(f"❌ 方法 {method_name} 存在但不可调用")
                    return False
            else:
                print(f"❌ 方法 {method_name} 不存在")
                return False
        
        # 测试 get_models 方法
        try:
            models = await provider.get_models()
            print(f"✅ get_models 方法调用成功，返回 {len(models)} 个模型")
            if models:
                print(f"   示例模型: {models[:3]}...")
        except Exception as e:
            print(f"❌ get_models 方法调用失败: {e}")
            return False
        
        print("🎉 所有抽象方法测试通过！")
        return True
        
    except TypeError as e:
        if "abstract methods" in str(e):
            print(f"❌ 仍然存在未实现的抽象方法: {e}")
            return False
        else:
            print(f"❌ 类型错误: {e}")
            return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_application_startup_with_google():
    """测试包含 Google AI 的应用启动"""
    print("\n🧪 测试包含 Google AI 的应用启动...")
    
    try:
        from ai_gen_hub.services.provider_manager import AIProviderManager
        from ai_gen_hub.config.settings import Settings, ProviderConfig
        from ai_gen_hub.utils.key_manager import KeyManager
        
        print("✅ 核心模块导入成功")
        
        # 创建模拟设置
        class MockSettings:
            def __init__(self):
                # 只启用 Google AI，禁用其他供应商
                self.openai = ProviderConfig(name="openai", enabled=False, api_keys=[])
                self.anthropic = ProviderConfig(name="anthropic", enabled=False, api_keys=[])
                self.dashscope = ProviderConfig(name="dashscope", enabled=False, api_keys=[])
                
                # 启用 Google AI
                self.google_ai = ProviderConfig(
                    name="google_ai",
                    enabled=True,
                    api_keys=["mock_key_1", "mock_key_2"]
                )
                
                # 监控配置
                class MockMonitoringConfig:
                    health_check_interval = 60
                
                self.monitoring = MockMonitoringConfig()
                
                # 安全配置
                class MockSecurityConfig:
                    encryption_key = "mock_encryption_key"
                
                self.security = MockSecurityConfig()
        
        settings = MockSettings()
        
        # 创建密钥管理器
        key_manager = KeyManager(settings)
        print("✅ KeyManager 创建成功")
        
        # 创建供应商管理器
        provider_manager = AIProviderManager(settings, key_manager)
        print("✅ AIProviderManager 创建成功")
        
        # 初始化供应商管理器（这会尝试初始化 Google AI）
        await provider_manager.initialize()
        print("✅ 供应商管理器初始化成功")
        
        # 检查 Google AI 是否成功初始化
        google_provider = await provider_manager.get_provider_by_name("google_ai")
        if google_provider:
            print("✅ Google AI 供应商成功注册")
        else:
            print("⚠️  Google AI 供应商未注册（可能是 API 密钥问题）")
        
        # 获取供应商状态
        status_map = await provider_manager.get_provider_status()
        print(f"✅ 供应商状态获取成功: {status_map}")
        
        # 清理
        await provider_manager.cleanup()
        print("✅ 供应商管理器清理成功")
        
        print("🎉 应用启动测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 应用启动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("🚀 开始 GoogleAIProvider 修复验证测试")
    print("=" * 50)
    
    # 运行测试
    test1_result = await test_google_provider_instantiation()
    test2_result = await test_application_startup_with_google()
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    tests = [
        ("GoogleAIProvider 实例化", test1_result),
        ("应用启动（含Google AI）", test2_result),
    ]
    
    passed = 0
    for test_name, success in tests:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:<25} {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(tests)} 测试通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！GoogleAIProvider 修复成功！")
        return 0
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
