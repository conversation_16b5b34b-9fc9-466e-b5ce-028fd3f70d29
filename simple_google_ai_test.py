#!/usr/bin/env python3
"""
简单的 Google AI API 测试脚本

用于验证 API key 请求头方式是否正常工作。
"""

import asyncio
import json
import os
import time

import httpx


async def test_url_param_method():
    """测试 URL 参数方式（旧方式）"""
    print("🔍 测试1：URL 参数方式传递 API key")
    
    # 使用一个假的 API key 进行测试
    fake_api_key = "fake-api-key-for-testing"
    
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key={fake_api_key}"
    headers = {
        "Content-Type": "application/json"
    }
    
    payload = {
        "contents": [
            {
                "parts": [
                    {
                        "text": "Hello"
                    }
                ]
            }
        ]
    }
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            print(f"📤 发送请求到: {url[:80]}...")
            
            start_time = time.time()
            response = await client.post(url, headers=headers, json=payload)
            elapsed_time = time.time() - start_time
            
            print(f"📥 响应状态: {response.status_code}")
            print(f"⏱️  响应时间: {elapsed_time:.2f}秒")
            print(f"📄 响应内容: {response.text[:200]}...")
            
            return response.status_code, elapsed_time
            
    except Exception as e:
        print(f"❌ URL 参数方式异常: {e}")
        return None, None


async def test_header_method():
    """测试请求头方式（新方式）"""
    print("\n🔍 测试2：请求头方式传递 API key")
    
    # 使用一个假的 API key 进行测试
    fake_api_key = "fake-api-key-for-testing"
    
    url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent"
    headers = {
        "x-goog-api-key": fake_api_key,
        "Content-Type": "application/json"
    }
    
    payload = {
        "contents": [
            {
                "parts": [
                    {
                        "text": "Hello"
                    }
                ]
            }
        ]
    }
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            print(f"📤 发送请求到: {url}")
            print(f"📋 请求头: x-goog-api-key: {fake_api_key[:10]}...")
            
            start_time = time.time()
            response = await client.post(url, headers=headers, json=payload)
            elapsed_time = time.time() - start_time
            
            print(f"📥 响应状态: {response.status_code}")
            print(f"⏱️  响应时间: {elapsed_time:.2f}秒")
            print(f"📄 响应内容: {response.text[:200]}...")
            
            return response.status_code, elapsed_time
            
    except Exception as e:
        print(f"❌ 请求头方式异常: {e}")
        return None, None


async def test_connectivity():
    """测试基本连接性"""
    print("\n🔍 测试3：基本连接性测试")
    
    url = "https://generativelanguage.googleapis.com/v1beta/models"
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            print(f"📤 发送 GET 请求到: {url}")
            
            start_time = time.time()
            response = await client.get(url)
            elapsed_time = time.time() - start_time
            
            print(f"📥 响应状态: {response.status_code}")
            print(f"⏱️  响应时间: {elapsed_time:.2f}秒")
            print(f"📄 响应内容: {response.text[:200]}...")
            
            return response.status_code, elapsed_time
            
    except Exception as e:
        print(f"❌ 连接性测试异常: {e}")
        return None, None


async def main():
    """主函数"""
    print("🚀 Google AI API 连接和认证方式测试")
    print("=" * 60)
    print("注意：使用假 API key 测试，预期会收到认证错误，但可以验证连接性和错误格式")
    print()
    
    # 测试1：URL 参数方式
    status1, time1 = await test_url_param_method()
    
    # 测试2：请求头方式
    status2, time2 = await test_header_method()
    
    # 测试3：基本连接性
    status3, time3 = await test_connectivity()
    
    # 分析结果
    print("\n" + "=" * 60)
    print("📊 测试结果分析:")
    
    print(f"\n1. URL 参数方式:")
    if status1:
        print(f"   状态码: {status1}, 响应时间: {time1:.2f}秒")
        if status1 == 400:
            print("   ✅ 正常：收到400错误（API key 无效）")
        elif status1 == 403:
            print("   ✅ 正常：收到403错误（API key 无效）")
        else:
            print(f"   ⚠️  意外状态码: {status1}")
    else:
        print("   ❌ 请求失败或超时")
    
    print(f"\n2. 请求头方式:")
    if status2:
        print(f"   状态码: {status2}, 响应时间: {time2:.2f}秒")
        if status2 == 400:
            print("   ✅ 正常：收到400错误（API key 无效）")
        elif status2 == 403:
            print("   ✅ 正常：收到403错误（API key 无效）")
        else:
            print(f"   ⚠️  意外状态码: {status2}")
    else:
        print("   ❌ 请求失败或超时")
    
    print(f"\n3. 基本连接性:")
    if status3:
        print(f"   状态码: {status3}, 响应时间: {time3:.2f}秒")
        if status3 == 403:
            print("   ✅ 正常：收到403错误（需要认证）")
        elif status3 == 401:
            print("   ✅ 正常：收到401错误（需要认证）")
        else:
            print(f"   ⚠️  意外状态码: {status3}")
    else:
        print("   ❌ 连接失败或超时")
    
    # 结论
    print(f"\n🎯 结论:")
    if status1 and status2:
        if time1 and time2:
            if time2 < time1:
                print("   ✅ 请求头方式响应更快，推荐使用")
            elif time1 < time2:
                print("   ⚠️  URL 参数方式响应更快，但请求头方式更安全")
            else:
                print("   ✅ 两种方式响应时间相近，推荐使用请求头方式（更安全）")
        else:
            print("   ✅ 两种方式都能正常连接到 API")
    else:
        print("   ❌ 存在连接问题，需要检查网络或 API 端点")


if __name__ == "__main__":
    asyncio.run(main())
