#!/usr/bin/env python3
"""
Redis兼容性测试脚本

测试Redis缓存功能是否正常工作，包括连接、读写、压缩等功能
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

try:
    from ai_gen_hub.config import get_settings
    from ai_gen_hub.cache.redis_cache_compat import CompatRedisCache
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)


class RedisCompatibilityTester:
    """Redis兼容性测试器"""
    
    def __init__(self):
        self.settings = None
        self.cache = None
    
    def print_header(self):
        """打印标题"""
        print("=" * 60)
        print("🔧 Redis兼容性测试")
        print("=" * 60)
        print()
    
    def print_section(self, title: str):
        """打印章节标题"""
        print(f"\n📋 {title}")
        print("-" * 40)
    
    def print_success(self, message: str):
        """打印成功信息"""
        print(f"✅ {message}")
    
    def print_warning(self, message: str):
        """打印警告信息"""
        print(f"⚠️  {message}")
    
    def print_error(self, message: str):
        """打印错误信息"""
        print(f"❌ {message}")
    
    def print_info(self, message: str):
        """打印信息"""
        print(f"ℹ️  {message}")
    
    async def test_redis_import(self):
        """测试Redis库导入"""
        self.print_section("Redis库导入测试")
        
        try:
            # 测试redis-py异步导入
            import redis.asyncio as redis_async
            self.print_success("redis-py异步库导入成功")
            
            # 测试基本功能
            try:
                # 创建一个测试连接
                test_redis = redis_async.Redis.from_url("redis://localhost:6379/0")
                await test_redis.ping()
                self.print_success("Redis连接测试成功")
                await test_redis.aclose()
            except Exception as e:
                self.print_warning(f"Redis连接测试失败: {e}")
                self.print_info("这可能是因为Redis服务器未运行")
            
        except ImportError:
            self.print_error("redis-py异步库导入失败")
            
            # 尝试aioredis
            try:
                import aioredis
                self.print_warning("回退到aioredis库")
                
                # 检查Python版本兼容性
                import sys
                if sys.version_info >= (3, 11):
                    self.print_warning("Python 3.11+可能存在TimeoutError冲突")
                
            except ImportError:
                self.print_error("aioredis库也不可用")
                return False
        
        return True
    
    async def test_cache_initialization(self):
        """测试缓存初始化"""
        self.print_section("缓存初始化测试")
        
        try:
            # 加载配置
            self.settings = get_settings()
            self.print_success("配置加载成功")
            
            # 创建Redis缓存实例
            self.cache = CompatRedisCache(
                redis_config=self.settings.redis,
                default_ttl=300,
                key_prefix="test_cache",
                compression_enabled=True,
                compression_threshold=100
            )
            self.print_success("Redis缓存实例创建成功")
            
            return True
            
        except Exception as e:
            self.print_error(f"缓存初始化失败: {e}")
            return False
    
    async def test_basic_operations(self):
        """测试基本操作"""
        self.print_section("基本操作测试")
        
        if not self.cache:
            self.print_error("缓存未初始化，跳过测试")
            return False
        
        try:
            # 测试设置值
            test_key = "test_key_basic"
            test_value = {"message": "Hello Redis!", "timestamp": time.time()}
            
            result = await self.cache.set(test_key, test_value, ttl=60)
            if result:
                self.print_success("设置缓存值成功")
            else:
                self.print_warning("设置缓存值失败（可能Redis不可用）")
                return False
            
            # 测试获取值
            retrieved_value = await self.cache.get(test_key)
            if retrieved_value == test_value:
                self.print_success("获取缓存值成功，数据一致")
            else:
                self.print_error(f"获取缓存值失败或数据不一致: {retrieved_value}")
                return False
            
            # 测试存在检查
            exists = await self.cache.exists(test_key)
            if exists:
                self.print_success("缓存键存在检查成功")
            else:
                self.print_error("缓存键存在检查失败")
                return False
            
            # 测试删除
            deleted = await self.cache.delete(test_key)
            if deleted:
                self.print_success("删除缓存值成功")
            else:
                self.print_error("删除缓存值失败")
                return False
            
            # 验证删除
            retrieved_after_delete = await self.cache.get(test_key)
            if retrieved_after_delete is None:
                self.print_success("删除验证成功，值已不存在")
            else:
                self.print_error("删除验证失败，值仍然存在")
                return False
            
            return True
            
        except Exception as e:
            self.print_error(f"基本操作测试失败: {e}")
            return False
    
    async def test_compression(self):
        """测试压缩功能"""
        self.print_section("压缩功能测试")
        
        if not self.cache:
            self.print_error("缓存未初始化，跳过测试")
            return False
        
        try:
            # 创建大数据测试压缩
            large_data = {
                "data": "x" * 2000,  # 2KB数据，超过压缩阈值
                "numbers": list(range(1000)),
                "metadata": {
                    "created": time.time(),
                    "size": "large",
                    "compressed": True
                }
            }
            
            test_key = "test_compression"
            
            # 设置大数据
            start_time = time.time()
            result = await self.cache.set(test_key, large_data, ttl=60)
            set_time = time.time() - start_time
            
            if result:
                self.print_success(f"大数据设置成功 (耗时: {set_time:.3f}s)")
            else:
                self.print_warning("大数据设置失败")
                return False
            
            # 获取大数据
            start_time = time.time()
            retrieved_data = await self.cache.get(test_key)
            get_time = time.time() - start_time
            
            if retrieved_data == large_data:
                self.print_success(f"大数据获取成功 (耗时: {get_time:.3f}s)")
                self.print_info("压缩/解压缩功能正常")
            else:
                self.print_error("大数据获取失败或数据不一致")
                return False
            
            # 清理
            await self.cache.delete(test_key)
            
            return True
            
        except Exception as e:
            self.print_error(f"压缩功能测试失败: {e}")
            return False
    
    async def test_batch_operations(self):
        """测试批量操作"""
        self.print_section("批量操作测试")
        
        if not self.cache:
            self.print_error("缓存未初始化，跳过测试")
            return False
        
        try:
            # 批量设置数据
            test_data = {}
            for i in range(10):
                key = f"batch_test_{i}"
                value = {"index": i, "data": f"test_data_{i}"}
                test_data[key] = value
                
                result = await self.cache.set(key, value, ttl=60)
                if not result:
                    self.print_warning(f"批量设置第{i}项失败")
            
            self.print_success(f"批量设置 {len(test_data)} 项完成")
            
            # 获取缓存大小
            cache_size = await self.cache.get_size()
            self.print_info(f"当前缓存大小: {cache_size} 项")
            
            # 获取键列表
            keys = await self.cache.get_keys("batch_test_*")
            self.print_success(f"获取到 {len(keys)} 个匹配的键")
            
            # 批量验证
            success_count = 0
            for key, expected_value in test_data.items():
                retrieved_value = await self.cache.get(key)
                if retrieved_value == expected_value:
                    success_count += 1
            
            self.print_success(f"批量验证: {success_count}/{len(test_data)} 项正确")
            
            # 批量删除
            deleted_count = await self.cache.delete_pattern("batch_test_*")
            self.print_success(f"批量删除: {deleted_count} 项")
            
            return True
            
        except Exception as e:
            self.print_error(f"批量操作测试失败: {e}")
            return False
    
    async def test_statistics(self):
        """测试统计功能"""
        self.print_section("统计功能测试")
        
        if not self.cache:
            self.print_error("缓存未初始化，跳过测试")
            return False
        
        try:
            # 获取统计信息
            stats = await self.cache.get_stats()
            
            self.print_info(f"缓存命中: {stats.hits}")
            self.print_info(f"缓存未命中: {stats.misses}")
            self.print_info(f"缓存设置: {stats.sets}")
            self.print_info(f"缓存删除: {stats.deletes}")
            
            if stats.hits + stats.misses > 0:
                hit_rate = stats.hits / (stats.hits + stats.misses) * 100
                self.print_info(f"缓存命中率: {hit_rate:.1f}%")
            
            self.print_success("统计功能正常")
            return True
            
        except Exception as e:
            self.print_error(f"统计功能测试失败: {e}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        if self.cache:
            try:
                await self.cache.cleanup()
                self.print_info("缓存资源已清理")
            except Exception as e:
                self.print_warning(f"缓存清理失败: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        self.print_header()
        
        test_results = []
        
        # 1. Redis库导入测试
        result = await self.test_redis_import()
        test_results.append(("Redis库导入", result))
        
        # 2. 缓存初始化测试
        result = await self.test_cache_initialization()
        test_results.append(("缓存初始化", result))
        
        if result:  # 只有初始化成功才继续其他测试
            # 3. 基本操作测试
            result = await self.test_basic_operations()
            test_results.append(("基本操作", result))
            
            # 4. 压缩功能测试
            result = await self.test_compression()
            test_results.append(("压缩功能", result))
            
            # 5. 批量操作测试
            result = await self.test_batch_operations()
            test_results.append(("批量操作", result))
            
            # 6. 统计功能测试
            result = await self.test_statistics()
            test_results.append(("统计功能", result))
        
        # 清理资源
        await self.cleanup()
        
        # 打印测试总结
        self.print_section("测试总结")
        
        passed_tests = sum(1 for _, result in test_results if result)
        total_tests = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        print(f"\n📊 测试结果: {passed_tests}/{total_tests} 通过")
        
        if passed_tests == total_tests:
            self.print_success("🎉 所有Redis兼容性测试通过！")
            return True
        else:
            self.print_warning("⚠️ 部分测试失败，请检查Redis配置和连接")
            return False


async def main():
    """主函数"""
    tester = RedisCompatibilityTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🚀 Redis缓存系统已准备就绪！")
    else:
        print("\n🔧 请根据测试结果修复Redis相关问题")
    
    return success


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
