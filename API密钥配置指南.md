# AI Gen Hub API密钥配置指南

## 🎯 配置目标

为AI Gen Hub配置真实的AI供应商API密钥，确保系统能够正常调用各个AI服务提供商的API。

## 📋 前置条件

在开始配置之前，您需要：

1. **获取AI供应商账户**: 注册并获取各个AI供应商的API密钥
2. **确认账户余额**: 确保账户有足够的余额或配额
3. **了解使用限制**: 了解各个供应商的API调用限制和定价

## 🔑 支持的AI供应商

### 1. OpenAI (推荐)
- **服务**: GPT-3.5, GPT-4, DALL-E
- **获取地址**: https://platform.openai.com/api-keys
- **配置格式**: `OPENAI_API_KEYS=sk-xxx,sk-yyy,sk-zzz`
- **注意事项**: 
  - 支持多个密钥，用逗号分隔
  - 密钥格式以 `sk-` 开头
  - 建议配置多个密钥以提高可用性

### 2. Google AI (Gemini)
- **服务**: Gemini Pro, Gemini Pro Vision
- **获取地址**: https://makersuite.google.com/app/apikey
- **配置格式**: `GOOGLE_AI_API_KEYS=AIzaSyXXX,AIzaSyYYY`
- **注意事项**:
  - 密钥格式以 `AIzaSy` 开头
  - 免费配额相对较高
  - 支持多模态功能

### 3. Anthropic (Claude)
- **服务**: Claude-3, Claude-2
- **获取地址**: https://console.anthropic.com/
- **配置格式**: `ANTHROPIC_API_KEYS=sk-ant-xxx,sk-ant-yyy`
- **注意事项**:
  - 密钥格式以 `sk-ant-` 开头
  - 需要申请API访问权限
  - 适合长文本处理

### 4. 阿里云DashScope
- **服务**: 通义千问, 通义万相
- **获取地址**: https://dashscope.console.aliyun.com/
- **配置格式**: `DASHSCOPE_API_KEYS=sk-xxx,sk-yyy`
- **注意事项**:
  - 国内用户推荐
  - 支持中文优化模型

## 🛠️ 配置步骤

### 步骤1: 复制配置文件

```bash
# 进入项目目录
cd /root/workspace/git.atjog.com/aier/ai-gen-hub

# 复制示例配置文件
cp .env.example .env
```

### 步骤2: 编辑配置文件

```bash
# 使用您喜欢的编辑器打开配置文件
nano .env
# 或者
vim .env
```

### 步骤3: 配置API密钥

在 `.env` 文件中找到对应的配置项，替换为您的真实API密钥：

```bash
# OpenAI配置 (推荐优先配置)
OPENAI_API_KEYS=sk-your-real-openai-key-here

# Google AI配置
GOOGLE_AI_API_KEYS=AIzaSy-your-real-google-ai-key-here

# Anthropic配置
ANTHROPIC_API_KEYS=sk-ant-your-real-anthropic-key-here

# 阿里云DashScope配置
DASHSCOPE_API_KEYS=sk-your-real-dashscope-key-here
```

### 步骤4: 配置其他必要设置

```bash
# 基础配置
ENVIRONMENT=development
DEBUG=true
API_PORT=8001

# 安全配置 (生产环境请更改)
JWT_SECRET_KEY=your-secure-jwt-secret-key
API_KEY=your-secure-api-key

# 启用功能
ENABLE_TEXT_GENERATION=true
ENABLE_IMAGE_GENERATION=true
ENABLE_CACHING=true
```

### 步骤5: 验证配置

```bash
# 激活虚拟环境
source venv/bin/activate

# 验证配置是否正确
python -c "
from ai_gen_hub.config import get_settings
settings = get_settings()
print('配置加载成功!')
print(f'已配置的供应商数量: {len([k for k in dir(settings) if k.endswith(\"_api_keys\") and getattr(settings, k)])}')
"
```

## 🧪 测试配置

### 方法1: 使用健康检查命令

```bash
# 检查所有供应商状态
python -m ai_gen_hub.main health-check

# 检查特定供应商
python -m ai_gen_hub.main health-check --provider openai
python -m ai_gen_hub.main health-check --provider google_ai
```

### 方法2: 启动服务器并测试

```bash
# 启动服务器
python simple_start.py

# 在另一个终端测试API
curl -X POST http://localhost:8001/api/v1/text/generate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "你好，请说一句话测试"}],
    "max_tokens": 50
  }'
```

### 方法3: 使用调试页面测试

1. 启动服务器: `python simple_start.py`
2. 访问调试页面: http://localhost:8001/debug
3. 在"API测试"页面测试各个供应商

## ⚠️ 常见问题和解决方案

### 问题1: "没有可用的供应商"

**原因**: 没有配置有效的API密钥或密钥无效

**解决方案**:
```bash
# 检查配置文件
cat .env | grep API_KEYS

# 验证密钥格式
# OpenAI: sk-开头，48字符
# Google AI: AIzaSy开头，39字符
# Anthropic: sk-ant-开头
```

### 问题2: API调用失败

**原因**: 密钥无效、余额不足或网络问题

**解决方案**:
```bash
# 检查密钥有效性
curl -H "Authorization: Bearer sk-your-openai-key" \
  https://api.openai.com/v1/models

# 检查网络连接
ping api.openai.com
```

### 问题3: 配置文件不生效

**原因**: 环境变量优先级或文件路径问题

**解决方案**:
```bash
# 确保.env文件在正确位置
ls -la .env

# 清除可能冲突的环境变量
unset OPENAI_API_KEYS
unset GOOGLE_AI_API_KEYS

# 重启服务器
```

## 🔒 安全最佳实践

### 1. 密钥安全
- **不要提交密钥到版本控制**: 确保 `.env` 在 `.gitignore` 中
- **定期轮换密钥**: 建议每3-6个月更换一次
- **使用最小权限**: 只授予必要的API权限

### 2. 环境隔离
```bash
# 开发环境
ENVIRONMENT=development
DEBUG=true

# 生产环境
ENVIRONMENT=production
DEBUG=false
JWT_SECRET_KEY=complex-production-secret
```

### 3. 监控和告警
```bash
# 启用监控
ENABLE_MONITORING=true
PROMETHEUS_ENABLED=true

# 配置使用限制
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
```

## 💰 成本控制建议

### 1. 设置使用限制
```bash
# 在.env中配置
MAX_TOKENS_PER_REQUEST=4000
MAX_REQUESTS_PER_USER_PER_DAY=1000
```

### 2. 启用缓存
```bash
# 缓存配置
ENABLE_CACHING=true
REDIS_CACHE_TTL=3600
MEMORY_CACHE_SIZE=1000
```

### 3. 选择合适的模型
- **文本生成**: 优先使用 GPT-3.5-turbo (成本较低)
- **复杂任务**: 使用 GPT-4 (质量更高)
- **中文任务**: 考虑使用 DashScope (国内优化)

## 📊 配置验证清单

- [ ] 复制并编辑 `.env` 文件
- [ ] 配置至少一个AI供应商的API密钥
- [ ] 设置基础安全配置 (JWT_SECRET_KEY, API_KEY)
- [ ] 启用必要的功能开关
- [ ] 运行健康检查验证配置
- [ ] 测试API调用功能
- [ ] 检查日志确认无错误
- [ ] 访问调试页面确认供应商状态

## 🚀 下一步

配置完成后，您可以：

1. **启动完整服务**: `python simple_start.py`
2. **访问API文档**: http://localhost:8001/docs
3. **使用调试页面**: http://localhost:8001/debug
4. **进行功能测试**: 测试文本生成、图像生成等功能
5. **监控系统状态**: 查看性能指标和日志

## 📞 获取帮助

如果在配置过程中遇到问题：

1. **查看日志**: 检查服务器启动日志中的错误信息
2. **运行诊断**: `python diagnose_server.py`
3. **查看文档**: 参考各供应商的官方API文档
4. **社区支持**: 在项目Issues中寻求帮助

---

**重要提醒**: 请妥善保管您的API密钥，不要在公共场所或代码仓库中暴露这些敏感信息。
