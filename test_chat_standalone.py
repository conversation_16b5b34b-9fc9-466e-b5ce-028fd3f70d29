#!/usr/bin/env python3
"""
独立的Chat接口测试脚本

直接测试Chat接口模块，不依赖整个ai_gen_hub包。
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_chat_interfaces():
    """测试Chat接口的基本功能"""
    print("🚀 开始测试Chat接口...")
    
    try:
        # 直接导入Chat接口模块，避免导入整个包
        from ai_gen_hub.core.chat_interfaces import (
            ChatMessage, 
            ChatMessageRole, 
            ChatConfig, 
            ChatResponse, 
            ChatUsage, 
            ChatFinishReason,
            ChatStreamChunk,
            ChatStreamEventType,
            ChatError,
            ChatValidationError
        )
        print("✅ Chat接口模块导入成功")
        
        # 测试创建用户消息
        user_message = ChatMessage(
            role=ChatMessageRole.USER,
            content="你好，AI助手！这是一条测试消息。"
        )
        print(f"✅ 用户消息创建成功: {user_message.role} - {user_message.content[:20]}...")
        
        # 测试创建系统消息
        system_message = ChatMessage(
            role=ChatMessageRole.SYSTEM,
            content="你是一个有用的AI助手。"
        )
        print(f"✅ 系统消息创建成功: {system_message.role} - {system_message.content}")
        
        # 测试创建助手消息
        assistant_message = ChatMessage(
            role=ChatMessageRole.ASSISTANT,
            content="你好！我是AI助手，很高兴为您服务。"
        )
        print(f"✅ 助手消息创建成功: {assistant_message.role} - {assistant_message.content[:20]}...")
        
        # 测试创建配置
        config = ChatConfig(
            temperature=0.7,
            max_tokens=1000,
            top_p=0.9,
            frequency_penalty=0.1,
            presence_penalty=0.1,
            stop_sequences=["停止", "结束"]
        )
        print(f"✅ 配置创建成功: temperature={config.temperature}, max_tokens={config.max_tokens}")
        
        # 测试创建使用量统计
        usage = ChatUsage(
            prompt_tokens=50,
            completion_tokens=25,
            total_tokens=75
        )
        print(f"✅ 使用量统计创建成功: {usage.prompt_tokens}/{usage.completion_tokens}/{usage.total_tokens}")
        
        # 测试创建流式响应块
        stream_chunk = ChatStreamChunk(
            id="chunk_123",
            event_type=ChatStreamEventType.CONTENT_DELTA,
            delta_content="这是一个"
        )
        print(f"✅ 流式响应块创建成功: {stream_chunk.event_type} - {stream_chunk.delta_content}")
        
        # 测试异常创建
        error = ChatValidationError("参数验证失败", provider="test_provider")
        print(f"✅ 异常创建成功: {type(error).__name__} - {error.message}")
        
        print("🎉 所有Chat接口组件测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ Chat接口测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_chat_base():
    """测试Chat基础实现"""
    print("\n🚀 开始测试Chat基础实现...")
    
    try:
        from ai_gen_hub.core.chat_base import BaseChatProvider, StreamAccumulator
        print("✅ Chat基础实现模块导入成功")
        
        # 测试流式累积器
        accumulator = StreamAccumulator()
        print("✅ 流式累积器创建成功")
        
        # 模拟添加流式块
        from ai_gen_hub.core.chat_interfaces import ChatStreamChunk, ChatStreamEventType, ChatFinishReason, ChatUsage
        
        chunk1 = ChatStreamChunk(
            id="chunk_1",
            event_type=ChatStreamEventType.CONTENT_DELTA,
            delta_content="你好"
        )
        
        chunk2 = ChatStreamChunk(
            id="chunk_2", 
            event_type=ChatStreamEventType.CONTENT_DELTA,
            delta_content="，世界！"
        )
        
        chunk3 = ChatStreamChunk(
            id="chunk_3",
            event_type=ChatStreamEventType.MESSAGE_STOP,
            finish_reason=ChatFinishReason.STOP,
            usage=ChatUsage(prompt_tokens=5, completion_tokens=3, total_tokens=8)
        )
        
        accumulator.add_chunk(chunk1)
        accumulator.add_chunk(chunk2)
        accumulator.add_chunk(chunk3)
        
        full_content = accumulator.get_accumulated_content()
        print(f"✅ 流式内容累积成功: '{full_content}'")
        
        # 测试构建完整响应
        response = accumulator.build_response("resp_123", "test-model", "test-provider")
        print(f"✅ 完整响应构建成功: {response.message.content}")
        
        print("🎉 Chat基础实现测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ Chat基础实现测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_chat_providers():
    """测试Chat供应商适配器"""
    print("\n🚀 开始测试Chat供应商适配器...")
    
    try:
        # 测试OpenAI适配器
        from ai_gen_hub.chat.providers.openai_chat import OpenAIChatProvider
        openai_provider = OpenAIChatProvider({"api_key": "test-key"})
        print(f"✅ OpenAI适配器创建成功: {openai_provider.provider_name}")
        
        # 测试Google AI适配器
        from ai_gen_hub.chat.providers.google_ai_chat import GoogleAIChatProvider
        google_provider = GoogleAIChatProvider({"api_key": "test-key"})
        print(f"✅ Google AI适配器创建成功: {google_provider.provider_name}")
        
        # 测试Anthropic适配器
        from ai_gen_hub.chat.providers.anthropic_chat import AnthropicChatProvider
        anthropic_provider = AnthropicChatProvider({"api_key": "test-key"})
        print(f"✅ Anthropic适配器创建成功: {anthropic_provider.provider_name}")
        
        print("🎉 Chat供应商适配器测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ Chat供应商适配器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_chat_manager():
    """测试Chat管理器"""
    print("\n🚀 开始测试Chat管理器...")
    
    try:
        from ai_gen_hub.chat.chat_manager import UnifiedChatManager, ProviderStats
        
        # 创建管理器
        manager = UnifiedChatManager({
            "default_provider": "openai",
            "max_retries": 3
        })
        print(f"✅ Chat管理器创建成功: 默认供应商={manager.default_provider}")
        
        # 测试供应商统计
        stats = ProviderStats()
        stats.record_success(1.5)
        stats.record_failure("测试错误")
        print(f"✅ 供应商统计测试成功: 成功率={stats.success_rate:.2%}")
        
        print("🎉 Chat管理器测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ Chat管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("AI Gen Hub 统一Chat接口 - 独立测试")
    print("=" * 60)
    
    all_passed = True
    
    # 运行各项测试
    tests = [
        test_chat_interfaces,
        test_chat_base,
        test_chat_providers,
        test_chat_manager
    ]
    
    for test_func in tests:
        if not test_func():
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！统一Chat接口实现正确。")
        print("✅ 核心功能验证完成：")
        print("   - 数据模型和枚举定义")
        print("   - 异常处理机制")
        print("   - 基础实现类")
        print("   - 供应商适配器")
        print("   - 统一管理器")
    else:
        print("❌ 部分测试失败，请检查实现。")
    print("=" * 60)
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
