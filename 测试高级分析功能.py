#!/usr/bin/env python3
"""
AI Gen Hu<PERSON> 高级分析功能测试脚本

测试新增的趋势分析、异常检测、报告生成和数据聚合功能
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ai_gen_hub.analytics import (
    TrendAnalyzer,
    AnomalyDetector,
    ReportGenerator,
    DataAggregator,
    DataSource,
    AggregationRule,
    AggregationType,
    TimeWindow,
    ReportConfig
)


async def test_trend_analyzer():
    """测试趋势分析器"""
    print("🔍 测试趋势分析器...")
    
    analyzer = TrendAnalyzer()
    
    # 创建测试数据 - 上升趋势
    base_time = datetime.now() - timedelta(days=30)
    data_points = []
    
    for i in range(720):  # 30天，每小时一个点
        timestamp = base_time + timedelta(hours=i)
        # 模拟上升趋势 + 随机噪声
        value = 100 + i * 0.1 + np.random.normal(0, 5)
        data_points.append({
            'timestamp': timestamp,
            'value': max(0, value)  # 确保值为正
        })
    
    # 分析趋势
    result = await analyzer.analyze_metric_trend(
        metric_name="test_requests_per_hour",
        data_points=data_points,
        prediction_days=7
    )
    
    print(f"  ✅ 指标名称: {result.metric_name}")
    print(f"  ✅ 趋势方向: {result.trend_direction}")
    print(f"  ✅ 趋势强度: {result.trend_strength:.3f}")
    print(f"  ✅ 预测值数量: {len(result.predicted_values)}")
    print(f"  ✅ 置信区间: [{result.confidence_interval[0]:.2f}, {result.confidence_interval[1]:.2f}]")
    
    if result.seasonal_pattern:
        print(f"  ✅ 检测到季节性模式")
    
    if result.anomalies:
        print(f"  ✅ 检测到 {len(result.anomalies)} 个异常点")
    
    print("  ✅ 趋势分析器测试完成\n")
    return True


async def test_anomaly_detector():
    """测试异常检测器"""
    print("🚨 测试异常检测器...")
    
    detector = AnomalyDetector()
    
    # 创建包含异常的测试数据
    base_time = datetime.now() - timedelta(hours=24)
    data_points = []
    
    for i in range(144):  # 24小时，每10分钟一个点
        timestamp = base_time + timedelta(minutes=i*10)
        
        # 在特定时间点插入异常值
        if i in [50, 80, 120]:
            value = np.random.normal(100, 10) * 3  # 异常值
        else:
            value = np.random.normal(100, 10)  # 正常值
        
        data_points.append({
            'timestamp': timestamp,
            'value': max(0, value)
        })
    
    # 检测异常
    anomalies = await detector.detect_anomalies(
        metric_name="test_response_time",
        data_points=data_points,
        detection_methods=['statistical', 'isolation_forest', 'trend_based']
    )
    
    print(f"  ✅ 检测到 {len(anomalies)} 个异常")
    
    # 显示异常详情
    for i, anomaly in enumerate(anomalies[:3]):  # 只显示前3个
        print(f"  ✅ 异常 {i+1}:")
        print(f"    - 时间: {anomaly.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"    - 值: {anomaly.value:.2f}")
        print(f"    - 类型: {anomaly.anomaly_type.value}")
        print(f"    - 严重程度: {anomaly.severity.value}")
        print(f"    - 置信度: {anomaly.confidence:.3f}")
        print(f"    - 描述: {anomaly.description}")
    
    # 获取异常摘要
    summary = await detector.get_anomaly_summary("test_response_time", anomalies)
    print(f"  ✅ 异常摘要:")
    print(f"    - 总异常数: {summary['total_anomalies']}")
    print(f"    - 严重程度分布: {summary['severity_distribution']}")
    print(f"    - 类型分布: {summary['type_distribution']}")
    print(f"    - 建议数量: {len(summary['recommendations'])}")
    
    print("  ✅ 异常检测器测试完成\n")
    return True


async def test_report_generator():
    """测试报告生成器"""
    print("📊 测试报告生成器...")
    
    generator = ReportGenerator()
    
    # 创建报告配置
    config = ReportConfig(
        name="test_daily_report",
        description="测试每日性能报告",
        metrics=["request_total", "response_time_avg", "error_rate"],
        time_range="daily",
        format="html",
        recipients=["<EMAIL>"]
    )
    
    # 注册报告配置
    generator.register_report(config)
    print(f"  ✅ 注册报告配置: {config.name}")
    
    # 生成报告
    report_data = await generator.generate_report("test_daily_report")
    print(f"  ✅ 生成报告: {report_data.title}")
    print(f"  ✅ 生成时间: {report_data.generated_at.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  ✅ 时间范围: {report_data.time_range['start'].strftime('%Y-%m-%d')} - {report_data.time_range['end'].strftime('%Y-%m-%d')}")
    print(f"  ✅ 指标数量: {len(report_data.metrics)}")
    print(f"  ✅ 图表数量: {len(report_data.charts)}")
    print(f"  ✅ 建议数量: {len(report_data.recommendations)}")
    
    # 导出报告
    try:
        html_path = await generator.export_report(report_data, format="html")
        print(f"  ✅ HTML报告导出: {html_path}")
        
        json_path = await generator.export_report(report_data, format="json")
        print(f"  ✅ JSON报告导出: {json_path}")
    except Exception as e:
        print(f"  ⚠️ 报告导出失败: {e}")
    
    print("  ✅ 报告生成器测试完成\n")
    return True


async def test_data_aggregator():
    """测试数据聚合器"""
    print("📈 测试数据聚合器...")
    
    aggregator = DataAggregator(max_workers=2)
    
    # 配置数据源
    source = DataSource(
        name="test_metrics",
        type="metrics_collector",
        connection_config={},
        query_config={},
        refresh_interval=30
    )
    aggregator.register_data_source(source)
    print(f"  ✅ 注册数据源: {source.name}")
    
    # 配置聚合规则
    rule = AggregationRule(
        name="hourly_request_rate",
        source_metrics=["ai_gen_hub_requests_total"],
        aggregation_type=AggregationType.RATE,
        time_window=TimeWindow.HOUR,
        output_metric="request_rate_per_hour"
    )
    aggregator.register_aggregation_rule(rule)
    print(f"  ✅ 注册聚合规则: {rule.name}")
    
    # 启动聚合器
    await aggregator.start()
    print("  ✅ 启动数据聚合器")
    
    # 等待一段时间让聚合器运行
    await asyncio.sleep(2)
    
    # 获取统计信息
    stats = await aggregator.get_stats()
    print(f"  ✅ 聚合器状态:")
    print(f"    - 运行状态: {stats['is_running']}")
    print(f"    - 数据源数量: {stats['data_sources_count']}")
    print(f"    - 聚合规则数量: {stats['aggregation_rules_count']}")
    print(f"    - 缓存指标数量: {stats['cached_metrics_count']}")
    
    # 停止聚合器
    await aggregator.stop()
    print("  ✅ 停止数据聚合器")
    
    print("  ✅ 数据聚合器测试完成\n")
    return True


async def test_integration():
    """集成测试"""
    print("🔗 运行集成测试...")
    
    # 创建所有组件
    trend_analyzer = TrendAnalyzer()
    anomaly_detector = AnomalyDetector()
    report_generator = ReportGenerator()
    
    # 创建综合测试数据
    base_time = datetime.now() - timedelta(days=7)
    data_points = []
    
    for i in range(168):  # 7天，每小时一个点
        timestamp = base_time + timedelta(hours=i)
        
        # 模拟真实场景：基础趋势 + 周期性变化 + 异常点
        base_value = 100 + i * 0.2  # 缓慢上升趋势
        daily_cycle = 20 * np.sin(2 * np.pi * i / 24)  # 日周期
        noise = np.random.normal(0, 5)  # 随机噪声
        
        # 在特定时间插入异常
        if i in [50, 100, 150]:
            anomaly_factor = 2.5
        else:
            anomaly_factor = 1.0
        
        value = max(0, (base_value + daily_cycle + noise) * anomaly_factor)
        
        data_points.append({
            'timestamp': timestamp,
            'value': value
        })
    
    # 1. 趋势分析
    trend_result = await trend_analyzer.analyze_metric_trend(
        metric_name="integration_test_metric",
        data_points=data_points,
        prediction_days=3
    )
    
    print(f"  ✅ 趋势分析结果:")
    print(f"    - 趋势方向: {trend_result.trend_direction}")
    print(f"    - 趋势强度: {trend_result.trend_strength:.3f}")
    print(f"    - 预测值: {[f'{v:.1f}' for v in trend_result.predicted_values[:3]]}")
    
    # 2. 异常检测
    anomalies = await anomaly_detector.detect_anomalies(
        metric_name="integration_test_metric",
        data_points=data_points
    )
    
    print(f"  ✅ 异常检测结果:")
    print(f"    - 检测到异常: {len(anomalies)} 个")
    
    # 3. 生成综合报告
    config = ReportConfig(
        name="integration_test_report",
        description="集成测试综合报告",
        metrics=["integration_test_metric"],
        time_range="weekly",
        format="html",
        recipients=[]
    )
    
    report_generator.register_report(config)
    report_data = await report_generator.generate_report("integration_test_report")
    
    print(f"  ✅ 报告生成结果:")
    print(f"    - 报告标题: {report_data.title}")
    print(f"    - 包含指标: {len(report_data.metrics)} 个")
    print(f"    - 生成建议: {len(report_data.recommendations)} 条")
    
    print("  ✅ 集成测试完成\n")
    return True


async def main():
    """主测试函数"""
    print("🚀 开始AI Gen Hub高级分析功能测试\n")
    
    test_results = []
    
    try:
        # 运行各个测试
        test_results.append(await test_trend_analyzer())
        test_results.append(await test_anomaly_detector())
        test_results.append(await test_report_generator())
        test_results.append(await test_data_aggregator())
        test_results.append(await test_integration())
        
        # 汇总结果
        passed_tests = sum(test_results)
        total_tests = len(test_results)
        
        print(f"📋 测试结果汇总:")
        print(f"  ✅ 通过测试: {passed_tests}/{total_tests}")
        
        if passed_tests == total_tests:
            print("  🎉 所有测试通过！高级分析功能运行正常。")
            return True
        else:
            print("  ❌ 部分测试失败，请检查错误信息。")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
