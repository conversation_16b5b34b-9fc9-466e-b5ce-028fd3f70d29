#!/usr/bin/env python3
"""
最小化测试服务器
用于验证基本功能是否正常
"""

import asyncio
import logging
from fastapi import FastAPI
from fastapi.responses import JSONResponse
import uvicorn

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(title="AI Gen Hub 测试服务器")

@app.get("/")
async def root():
    return {"message": "AI Gen Hub 测试服务器运行正常"}

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "message": "服务器运行正常"
    }

@app.post("/api/v1/text/generate")
async def test_generate():
    return {
        "id": "test-response",
        "object": "chat.completion",
        "choices": [{
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "这是一个测试响应，表示服务器基本功能正常。"
            },
            "finish_reason": "stop"
        }],
        "usage": {
            "prompt_tokens": 10,
            "completion_tokens": 20,
            "total_tokens": 30
        }
    }

if __name__ == "__main__":
    logger.info("启动最小化测试服务器...")
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="debug")
