#!/usr/bin/env python3
"""
测试中间件修复效果

验证 LoggingMiddleware 中的请求体读取问题是否已修复
"""

import asyncio
import os
import sys
import time
import httpx
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

# 设置环境变量
os.environ["PYTHONPATH"] = str(project_root / "src")
os.environ["DEBUG"] = "true"
os.environ["ENVIRONMENT"] = "development"

async def test_api_call():
    """测试API调用"""
    print("🧪 测试中间件修复效果...")
    
    # 测试数据
    test_request = {
        "messages": [{"role": "user", "content": "Hello, test!"}],
        "model": "gemini-2.5-flash",
        "generation": {"max_tokens": 10},
        "stream": {"enabled": False}
    }
    
    # 尝试不同的端口
    ports = [8001, 8006, 8000, 8002, 8003, 8004, 8005]
    
    for port in ports:
        print(f"\n🔍 尝试端口 {port}...")
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # 首先检查健康状态
                try:
                    health_response = await client.get(f"http://localhost:{port}/health")
                    if health_response.status_code == 200:
                        print(f"✅ 端口 {port} 健康检查通过")
                    else:
                        print(f"❌ 端口 {port} 健康检查失败: {health_response.status_code}")
                        continue
                except Exception as e:
                    print(f"⚪ 端口 {port} 无服务运行: {e}")
                    continue
                
                # 测试API调用
                print(f"📡 发送API请求到端口 {port}...")
                start_time = time.time()
                
                response = await client.post(
                    f"http://localhost:{port}/api/v1/text/v2/generate",
                    json=test_request,
                    headers={"Content-Type": "application/json"}
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                print(f"⏱️  请求耗时: {duration:.2f}秒")
                print(f"📊 响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ API调用成功！中间件修复生效")
                    try:
                        data = response.json()
                        print(f"📝 响应内容: {data.get('content', 'No content')[:100]}...")
                    except:
                        print("📝 响应不是JSON格式")
                    return True
                elif response.status_code == 500:
                    print("❌ 服务器内部错误，中间件可能仍有问题")
                    print(f"错误详情: {response.text[:200]}...")
                else:
                    print(f"⚠️  意外的响应状态码: {response.status_code}")
                    print(f"响应内容: {response.text[:200]}...")
                
        except httpx.TimeoutException:
            print(f"⏰ 端口 {port} 请求超时")
        except httpx.ConnectError:
            print(f"🔌 端口 {port} 连接失败")
        except Exception as e:
            print(f"❌ 端口 {port} 测试失败: {e}")
    
    print("\n❌ 所有端口测试失败")
    return False

async def main():
    """主函数"""
    print("🚀 开始测试中间件修复效果...")
    print("=" * 60)
    
    success = await test_api_call()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试成功！中间件修复生效")
    else:
        print("💥 测试失败！需要进一步调试")
        print("\n🔧 建议检查:")
        print("1. 服务器是否正在运行")
        print("2. 端口配置是否正确")
        print("3. 中间件修复是否完全生效")

if __name__ == "__main__":
    asyncio.run(main())
