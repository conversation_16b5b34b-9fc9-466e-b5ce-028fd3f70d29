#!/usr/bin/env python3
"""
AI Gen Hub API 调试工具

用于测试和调试文本生成接口，特别是Google AI供应商的问题。
提供详细的日志输出和错误诊断功能。

使用方法:
    python debug_api.py --provider google_ai --model gemini-2.5-flash --prompt "你好，请介绍一下自己"
    python debug_api.py --stream --provider google_ai --model gemini-2.5-flash --prompt "写一个简短的故事"
"""

import asyncio
import json
import logging
import sys
import time
from typing import Optional

import click
import httpx

# 设置详细的日志记录
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('debug_api.log', encoding='utf-8')
    ]
)

# 启用HTTP库的详细日志
logging.getLogger("httpx").setLevel(logging.DEBUG)
logging.getLogger("httpcore").setLevel(logging.DEBUG)

logger = logging.getLogger(__name__)


async def test_direct_google_ai_api(api_key: str, model: str, prompt: str, stream: bool = False):
    """直接测试Google AI API"""
    logger.info("=== 直接测试Google AI API ===")
    
    base_url = "https://generativelanguage.googleapis.com/v1beta"
    
    # 构建请求数据
    request_data = {
        "contents": [
            {
                "role": "user",
                "parts": [{"text": prompt}]
            }
        ],
        "generationConfig": {
            "temperature": 0.7,
            "maxOutputTokens": 1000
        }
    }
    
    logger.info(f"请求数据: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
    
    if stream:
        url = f"{base_url}/models/{model}:streamGenerateContent?key={api_key}"
        logger.info(f"流式请求URL: {url}")
        
        async with httpx.AsyncClient(timeout=120.0) as client:
            try:
                async with client.stream(
                    "POST",
                    url,
                    json=request_data,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    logger.info(f"响应状态码: {response.status_code}")
                    logger.info(f"响应头: {dict(response.headers)}")
                    
                    if response.status_code != 200:
                        error_text = await response.aread()
                        logger.error(f"API错误响应: {error_text.decode()}")
                        return
                    
                    chunk_count = 0
                    async for chunk in response.aiter_bytes():
                        chunk_count += 1
                        chunk_str = chunk.decode('utf-8')
                        logger.debug(f"收到数据块 {chunk_count}: {chunk_str[:200]}...")
                        
                        # 解析SSE数据
                        for line in chunk_str.split('\n'):
                            line = line.strip()
                            if line.startswith('data: '):
                                data_str = line[6:]
                                if data_str and data_str != '[DONE]':
                                    try:
                                        data = json.loads(data_str)
                                        logger.info(f"解析的数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                                    except json.JSONDecodeError as e:
                                        logger.warning(f"JSON解析失败: {e}, 数据: {data_str}")
                    
                    logger.info(f"流式响应完成，共收到 {chunk_count} 个数据块")
                    
            except Exception as e:
                logger.error(f"流式请求失败: {e}", exc_info=True)
    else:
        url = f"{base_url}/models/{model}:generateContent?key={api_key}"
        logger.info(f"同步请求URL: {url}")
        
        async with httpx.AsyncClient(timeout=120.0) as client:
            try:
                start_time = time.time()
                response = await client.post(
                    url,
                    json=request_data,
                    headers={"Content-Type": "application/json"}
                )
                elapsed_time = time.time() - start_time
                
                logger.info(f"响应状态码: {response.status_code}")
                logger.info(f"响应时间: {elapsed_time:.2f}秒")
                logger.info(f"响应头: {dict(response.headers)}")
                
                response_data = response.json()
                logger.info(f"响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
            except Exception as e:
                logger.error(f"同步请求失败: {e}", exc_info=True)


async def test_ai_gen_hub_api(base_url: str, provider: str, model: str, prompt: str, stream: bool = False):
    """测试AI Gen Hub API"""
    logger.info("=== 测试AI Gen Hub API ===")
    
    # 构建请求数据
    request_data = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": prompt
            }
        ],
        "stream": stream,
        "temperature": 0.7,
        "max_tokens": 1000
    }
    
    logger.info(f"请求数据: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
    
    if stream:
        url = f"{base_url}/api/v1/text/generate"
        logger.info(f"流式请求URL: {url}")
        
        async with httpx.AsyncClient(timeout=300.0) as client:
            try:
                async with client.stream(
                    "POST",
                    url,
                    json=request_data,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    logger.info(f"响应状态码: {response.status_code}")
                    logger.info(f"响应头: {dict(response.headers)}")
                    
                    if response.status_code != 200:
                        error_text = await response.aread()
                        logger.error(f"API错误响应: {error_text.decode()}")
                        return
                    
                    chunk_count = 0
                    async for chunk in response.aiter_bytes():
                        chunk_count += 1
                        chunk_str = chunk.decode('utf-8')
                        logger.debug(f"收到数据块 {chunk_count}: {chunk_str[:200]}...")
                        
                        # 解析SSE数据
                        for line in chunk_str.split('\n'):
                            line = line.strip()
                            if line.startswith('data: '):
                                data_str = line[6:]
                                if data_str and data_str != '[DONE]':
                                    try:
                                        data = json.loads(data_str)
                                        logger.info(f"解析的数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                                    except json.JSONDecodeError as e:
                                        logger.warning(f"JSON解析失败: {e}, 数据: {data_str}")
                    
                    logger.info(f"流式响应完成，共收到 {chunk_count} 个数据块")
                    
            except Exception as e:
                logger.error(f"流式请求失败: {e}", exc_info=True)
    else:
        url = f"{base_url}/api/v1/text/generate"
        logger.info(f"同步请求URL: {url}")
        
        async with httpx.AsyncClient(timeout=300.0) as client:
            try:
                start_time = time.time()
                response = await client.post(
                    url,
                    json=request_data,
                    headers={"Content-Type": "application/json"}
                )
                elapsed_time = time.time() - start_time
                
                logger.info(f"响应状态码: {response.status_code}")
                logger.info(f"响应时间: {elapsed_time:.2f}秒")
                logger.info(f"响应头: {dict(response.headers)}")
                
                if response.status_code == 200:
                    response_data = response.json()
                    logger.info(f"响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                else:
                    error_text = response.text
                    logger.error(f"API错误响应: {error_text}")
                
            except Exception as e:
                logger.error(f"同步请求失败: {e}", exc_info=True)


@click.command()
@click.option('--provider', default='google_ai', help='AI供应商名称')
@click.option('--model', default='gemini-2.5-flash', help='模型名称')
@click.option('--prompt', default='你好，请介绍一下自己', help='测试提示词')
@click.option('--stream', is_flag=True, help='是否使用流式响应')
@click.option('--direct', is_flag=True, help='是否直接测试供应商API（需要API密钥）')
@click.option('--api-key', help='API密钥（用于直接测试）')
@click.option('--hub-url', default='http://localhost:8001', help='AI Gen Hub服务地址')
def main(provider: str, model: str, prompt: str, stream: bool, direct: bool, api_key: Optional[str], hub_url: str):
    """AI Gen Hub API 调试工具"""
    
    async def run_tests():
        logger.info("开始API调试测试...")
        logger.info(f"供应商: {provider}")
        logger.info(f"模型: {model}")
        logger.info(f"提示词: {prompt}")
        logger.info(f"流式: {stream}")
        logger.info(f"直接测试: {direct}")
        
        if direct:
            if not api_key:
                logger.error("直接测试需要提供API密钥")
                return
            
            if provider == 'google_ai':
                await test_direct_google_ai_api(api_key, model, prompt, stream)
            else:
                logger.error(f"暂不支持直接测试供应商: {provider}")
        else:
            await test_ai_gen_hub_api(hub_url, provider, model, prompt, stream)
    
    asyncio.run(run_tests())


if __name__ == '__main__':
    main()
