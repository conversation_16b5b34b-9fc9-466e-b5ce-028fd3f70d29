#!/usr/bin/env python3
"""
测试AI内容个性化推荐系统

验证用户行为分析、内容推荐、偏好学习等功能
"""

import asyncio
import random
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

# 创建必要的模块
def create_mock_modules():
    """创建模拟模块"""
    # 创建基础接口
    class BaseRequest:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
            if not hasattr(self, 'id'):
                self.id = f"req_{int(time.time())}"
    
    class BaseResponse:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
    
    # 创建异常类
    class AIGenHubException(Exception):
        pass
    
    # 创建日志记录器
    class Logger:
        def info(self, msg, **kwargs):
            print(f"INFO: {msg}")
        
        def error(self, msg, **kwargs):
            print(f"ERROR: {msg}")
        
        def warning(self, msg, **kwargs):
            print(f"WARNING: {msg}")
    
    def get_logger(name):
        return Logger()
    
    return {
        'BaseRequest': BaseRequest,
        'BaseResponse': BaseResponse,
        'AIGenHubException': AIGenHubException,
        'get_logger': get_logger
    }

# 创建模拟模块
mock_modules = create_mock_modules()

# 简化的推荐系统
from enum import Enum
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta
import uuid
import math
from collections import defaultdict, Counter

class UserActionType(Enum):
    VIEW = "view"
    LIKE = "like"
    DISLIKE = "dislike"
    SHARE = "share"
    BOOKMARK = "bookmark"
    COMMENT = "comment"

class ContentCategory(Enum):
    TEXT = "text"
    IMAGE = "image"
    CODE = "code"
    DOCUMENT = "document"

class RecommendationType(Enum):
    CONTENT_BASED = "content_based"
    COLLABORATIVE = "collaborative"
    TRENDING = "trending"
    HYBRID = "hybrid"

@dataclass
class UserBehavior:
    behavior_id: str
    user_id: str
    content_id: str
    action_type: UserActionType
    duration: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class ContentFeature:
    content_id: str
    title: str
    category: ContentCategory
    tags: List[str] = field(default_factory=list)
    quality_score: float = 5.0
    complexity: float = 0.5
    author_id: str = ""
    view_count: int = 0
    like_count: int = 0
    share_count: int = 0
    created_at: datetime = field(default_factory=datetime.now)

@dataclass
class UserPreference:
    user_id: str
    category_preferences: Dict[ContentCategory, float] = field(default_factory=dict)
    tag_preferences: Dict[str, float] = field(default_factory=dict)
    complexity_preference: float = 0.5
    quality_threshold: float = 5.0
    confidence: float = 0.0

@dataclass
class UserProfile:
    user_id: str
    username: str = ""
    total_actions: int = 0
    preferences: Optional[UserPreference] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

@dataclass
class RecommendationItem:
    content_id: str
    content_feature: ContentFeature
    score: float = 0.0
    recommendation_type: RecommendationType = RecommendationType.HYBRID
    reason: str = ""
    rank: int = 0


class SimpleBehaviorAnalyzer:
    """简化的行为分析器"""
    
    def __init__(self):
        self.logger = mock_modules['get_logger'](__name__)
        self.action_weights = {
            UserActionType.VIEW: 1.0,
            UserActionType.LIKE: 5.0,
            UserActionType.DISLIKE: -3.0,
            UserActionType.SHARE: 4.0,
            UserActionType.BOOKMARK: 6.0,
            UserActionType.COMMENT: 3.0
        }
    
    def analyze_user_preferences(
        self, 
        user_id: str,
        behaviors: List[UserBehavior],
        content_features: Dict[str, ContentFeature]
    ) -> UserPreference:
        """分析用户偏好"""
        user_behaviors = [b for b in behaviors if b.user_id == user_id]
        
        if not user_behaviors:
            return UserPreference(user_id=user_id)
        
        # 分析类别偏好
        category_scores = defaultdict(float)
        for behavior in user_behaviors:
            content = content_features.get(behavior.content_id)
            if content:
                weight = self.action_weights.get(behavior.action_type, 1.0)
                category_scores[content.category] += weight
        
        # 标准化
        max_score = max(category_scores.values()) if category_scores else 1.0
        category_preferences = {
            category: score / max_score 
            for category, score in category_scores.items()
        }
        
        # 分析标签偏好
        tag_scores = defaultdict(float)
        for behavior in user_behaviors:
            content = content_features.get(behavior.content_id)
            if content and content.tags:
                weight = self.action_weights.get(behavior.action_type, 1.0)
                for tag in content.tags:
                    tag_scores[tag] += weight / len(content.tags)
        
        # 标准化标签偏好
        max_tag_score = max(tag_scores.values()) if tag_scores else 1.0
        tag_preferences = {
            tag: score / max_tag_score 
            for tag, score in tag_scores.items()
        }
        
        # 分析复杂度偏好
        complexity_scores = []
        for behavior in user_behaviors:
            content = content_features.get(behavior.content_id)
            if content and self.action_weights.get(behavior.action_type, 0) > 0:
                complexity_scores.append(content.complexity)
        
        complexity_preference = sum(complexity_scores) / len(complexity_scores) if complexity_scores else 0.5
        
        # 计算置信度
        confidence = min(len(user_behaviors) / 50.0, 1.0)
        
        return UserPreference(
            user_id=user_id,
            category_preferences=category_preferences,
            tag_preferences=tag_preferences,
            complexity_preference=complexity_preference,
            confidence=confidence
        )


class SimpleRecommendationEngine:
    """简化的推荐引擎"""
    
    def __init__(self):
        self.logger = mock_modules['get_logger'](__name__)
        self.behavior_analyzer = SimpleBehaviorAnalyzer()
    
    def content_based_recommend(
        self, 
        user_profile: UserProfile,
        content_features: List[ContentFeature],
        count: int = 10
    ) -> List[RecommendationItem]:
        """基于内容的推荐"""
        if not user_profile.preferences:
            return []
        
        recommendations = []
        preferences = user_profile.preferences
        
        for content in content_features:
            score = 0.0
            
            # 类别匹配
            category_score = preferences.category_preferences.get(content.category, 0.0)
            score += category_score * 0.4
            
            # 标签匹配
            if content.tags:
                tag_score = sum(preferences.tag_preferences.get(tag, 0.0) for tag in content.tags)
                score += (tag_score / len(content.tags)) * 0.3
            
            # 质量匹配
            if content.quality_score >= preferences.quality_threshold:
                score += (content.quality_score / 10.0) * 0.2
            
            # 复杂度匹配
            complexity_diff = abs(content.complexity - preferences.complexity_preference)
            score += (1.0 - complexity_diff) * 0.1
            
            if score > 0:
                item = RecommendationItem(
                    content_id=content.content_id,
                    content_feature=content,
                    score=score,
                    recommendation_type=RecommendationType.CONTENT_BASED,
                    reason="基于您的内容偏好推荐"
                )
                recommendations.append(item)
        
        recommendations.sort(key=lambda x: x.score, reverse=True)
        return recommendations[:count]
    
    def collaborative_recommend(
        self,
        target_user_id: str,
        behaviors: List[UserBehavior],
        content_features: List[ContentFeature],
        count: int = 10
    ) -> List[RecommendationItem]:
        """协同过滤推荐"""
        # 构建用户-物品矩阵
        user_item_matrix = defaultdict(lambda: defaultdict(float))
        
        for behavior in behaviors:
            weight = 1.0
            if behavior.action_type == UserActionType.LIKE:
                weight = 3.0
            elif behavior.action_type == UserActionType.SHARE:
                weight = 2.5
            elif behavior.action_type == UserActionType.DISLIKE:
                weight = -1.0
            
            user_item_matrix[behavior.user_id][behavior.content_id] += weight
        
        if target_user_id not in user_item_matrix:
            return []
        
        target_items = set(user_item_matrix[target_user_id].keys())
        content_dict = {c.content_id: c for c in content_features}
        
        # 找相似用户
        similar_users = []
        target_vector = user_item_matrix[target_user_id]
        
        for user_id, user_vector in user_item_matrix.items():
            if user_id == target_user_id:
                continue
            
            # 计算余弦相似度
            common_items = set(target_vector.keys()) & set(user_vector.keys())
            if not common_items:
                continue
            
            dot_product = sum(target_vector[item] * user_vector[item] for item in common_items)
            norm1 = math.sqrt(sum(score ** 2 for score in target_vector.values()))
            norm2 = math.sqrt(sum(score ** 2 for score in user_vector.values()))
            
            if norm1 > 0 and norm2 > 0:
                similarity = dot_product / (norm1 * norm2)
                if similarity > 0.1:
                    similar_users.append((user_id, similarity))
        
        # 生成推荐
        item_scores = defaultdict(float)
        for user_id, similarity in similar_users:
            user_items = user_item_matrix[user_id]
            for item_id, rating in user_items.items():
                if item_id not in target_items:
                    item_scores[item_id] += similarity * rating
        
        recommendations = []
        for item_id, score in item_scores.items():
            if item_id in content_dict and score > 0:
                content = content_dict[item_id]
                item = RecommendationItem(
                    content_id=item_id,
                    content_feature=content,
                    score=score,
                    recommendation_type=RecommendationType.COLLABORATIVE,
                    reason="基于相似用户的偏好推荐"
                )
                recommendations.append(item)
        
        recommendations.sort(key=lambda x: x.score, reverse=True)
        return recommendations[:count]
    
    def trending_recommend(self, content_features: List[ContentFeature], count: int = 10) -> List[RecommendationItem]:
        """热门推荐"""
        # 按热度排序（这里简化为按点赞数）
        trending_contents = sorted(content_features, key=lambda x: x.like_count + x.view_count, reverse=True)
        
        recommendations = []
        for i, content in enumerate(trending_contents[:count]):
            score = 1.0 - (i / count)  # 排名越高分数越高
            item = RecommendationItem(
                content_id=content.content_id,
                content_feature=content,
                score=score,
                recommendation_type=RecommendationType.TRENDING,
                reason=f"热门内容，排名第{i+1}位"
            )
            recommendations.append(item)
        
        return recommendations


class SimpleRecommendationService:
    """简化的推荐服务"""
    
    def __init__(self):
        self.logger = mock_modules['get_logger'](__name__)
        self.engine = SimpleRecommendationEngine()
        self.behaviors: List[UserBehavior] = []
        self.content_features: Dict[str, ContentFeature] = {}
        self.user_profiles: Dict[str, UserProfile] = {}
    
    async def record_behavior(self, user_id: str, content_id: str, action_type: UserActionType, duration: float = None) -> bool:
        """记录用户行为"""
        behavior = UserBehavior(
            behavior_id=str(uuid.uuid4()),
            user_id=user_id,
            content_id=content_id,
            action_type=action_type,
            duration=duration
        )
        
        self.behaviors.append(behavior)
        
        # 更新内容统计
        content = self.content_features.get(content_id)
        if content:
            if action_type == UserActionType.VIEW:
                content.view_count += 1
            elif action_type == UserActionType.LIKE:
                content.like_count += 1
            elif action_type == UserActionType.SHARE:
                content.share_count += 1
        
        self.logger.info(f"记录行为: {user_id} -> {action_type.value} -> {content_id}")
        return True
    
    async def register_content(self, content: ContentFeature) -> bool:
        """注册内容"""
        self.content_features[content.content_id] = content
        self.logger.info(f"注册内容: {content.title}")
        return True
    
    async def get_user_profile(self, user_id: str) -> UserProfile:
        """获取用户画像"""
        if user_id not in self.user_profiles:
            self.user_profiles[user_id] = UserProfile(user_id=user_id)
        
        profile = self.user_profiles[user_id]
        
        # 更新偏好
        user_behaviors = [b for b in self.behaviors if b.user_id == user_id]
        if user_behaviors:
            profile.preferences = self.engine.behavior_analyzer.analyze_user_preferences(
                user_id, self.behaviors, self.content_features
            )
            profile.total_actions = len(user_behaviors)
        
        return profile
    
    async def get_recommendations(self, user_id: str, count: int = 10, rec_type: str = "hybrid") -> List[RecommendationItem]:
        """获取推荐"""
        user_profile = await self.get_user_profile(user_id)
        content_list = list(self.content_features.values())
        
        if rec_type == "content_based":
            return self.engine.content_based_recommend(user_profile, content_list, count)
        elif rec_type == "collaborative":
            return self.engine.collaborative_recommend(user_id, self.behaviors, content_list, count)
        elif rec_type == "trending":
            return self.engine.trending_recommend(content_list, count)
        else:  # hybrid
            # 混合推荐
            content_recs = self.engine.content_based_recommend(user_profile, content_list, count // 2)
            collab_recs = self.engine.collaborative_recommend(user_id, self.behaviors, content_list, count // 2)
            trending_recs = self.engine.trending_recommend(content_list, count // 4)
            
            # 合并去重
            all_recs = content_recs + collab_recs + trending_recs
            seen_ids = set()
            unique_recs = []
            
            for rec in all_recs:
                if rec.content_id not in seen_ids:
                    seen_ids.add(rec.content_id)
                    unique_recs.append(rec)
            
            unique_recs.sort(key=lambda x: x.score, reverse=True)
            return unique_recs[:count]


async def test_content_registration():
    """测试内容注册"""
    print("🔧 测试内容注册")
    print("=" * 40)
    
    service = SimpleRecommendationService()
    
    # 创建测试内容
    contents = [
        ContentFeature("1", "Python编程入门", ContentCategory.CODE, ["python", "编程", "入门"], 8.5, 0.3, "author1"),
        ContentFeature("2", "机器学习基础", ContentCategory.DOCUMENT, ["机器学习", "AI", "基础"], 9.0, 0.7, "author2"),
        ContentFeature("3", "数据可视化", ContentCategory.IMAGE, ["数据", "可视化", "图表"], 7.5, 0.5, "author1"),
        ContentFeature("4", "深度学习实战", ContentCategory.DOCUMENT, ["深度学习", "实战", "神经网络"], 9.2, 0.8, "author3"),
        ContentFeature("5", "Web开发指南", ContentCategory.CODE, ["web", "开发", "前端"], 8.0, 0.4, "author2"),
    ]
    
    for content in contents:
        await service.register_content(content)
    
    print(f"✅ 注册了 {len(contents)} 个内容")
    
    return service


async def test_behavior_recording():
    """测试行为记录"""
    print("\n🔧 测试行为记录")
    print("=" * 40)
    
    service = await test_content_registration()
    
    # 模拟用户行为
    users = ["user1", "user2", "user3"]
    actions = [
        ("user1", "1", UserActionType.VIEW, 120),
        ("user1", "1", UserActionType.LIKE, None),
        ("user1", "2", UserActionType.VIEW, 300),
        ("user1", "3", UserActionType.BOOKMARK, None),
        ("user2", "2", UserActionType.VIEW, 180),
        ("user2", "2", UserActionType.SHARE, None),
        ("user2", "4", UserActionType.VIEW, 450),
        ("user2", "4", UserActionType.LIKE, None),
        ("user3", "1", UserActionType.VIEW, 90),
        ("user3", "3", UserActionType.VIEW, 200),
        ("user3", "3", UserActionType.LIKE, None),
        ("user3", "5", UserActionType.VIEW, 150),
    ]
    
    for user_id, content_id, action_type, duration in actions:
        await service.record_behavior(user_id, content_id, action_type, duration)
    
    print(f"✅ 记录了 {len(actions)} 个用户行为")
    
    # 显示用户画像
    for user_id in users:
        profile = await service.get_user_profile(user_id)
        print(f"\n用户 {user_id} 画像:")
        print(f"  总行为数: {profile.total_actions}")
        if profile.preferences:
            print(f"  置信度: {profile.preferences.confidence:.2f}")
            print(f"  类别偏好: {dict(profile.preferences.category_preferences)}")
            print(f"  复杂度偏好: {profile.preferences.complexity_preference:.2f}")
    
    return service


async def test_content_based_recommendation():
    """测试基于内容的推荐"""
    print("\n🔧 测试基于内容的推荐")
    print("=" * 40)
    
    service = await test_behavior_recording()
    
    # 为每个用户生成基于内容的推荐
    for user_id in ["user1", "user2", "user3"]:
        recommendations = await service.get_recommendations(user_id, count=3, rec_type="content_based")
        
        print(f"\n{user_id} 的基于内容推荐:")
        for i, rec in enumerate(recommendations):
            print(f"  {i+1}. {rec.content_feature.title} (分数: {rec.score:.3f})")
            print(f"     类别: {rec.content_feature.category.value}, 标签: {rec.content_feature.tags}")
    
    return True


async def test_collaborative_filtering():
    """测试协同过滤推荐"""
    print("\n🔧 测试协同过滤推荐")
    print("=" * 40)
    
    service = await test_behavior_recording()
    
    # 为每个用户生成协同过滤推荐
    for user_id in ["user1", "user2", "user3"]:
        recommendations = await service.get_recommendations(user_id, count=3, rec_type="collaborative")
        
        print(f"\n{user_id} 的协同过滤推荐:")
        for i, rec in enumerate(recommendations):
            print(f"  {i+1}. {rec.content_feature.title} (分数: {rec.score:.3f})")
            print(f"     原因: {rec.reason}")
    
    return True


async def test_trending_recommendation():
    """测试热门推荐"""
    print("\n🔧 测试热门推荐")
    print("=" * 40)
    
    service = await test_behavior_recording()
    
    # 生成热门推荐
    recommendations = await service.get_recommendations("user1", count=5, rec_type="trending")
    
    print("热门内容推荐:")
    for i, rec in enumerate(recommendations):
        content = rec.content_feature
        print(f"  {i+1}. {content.title}")
        print(f"     浏览: {content.view_count}, 点赞: {content.like_count}, 分享: {content.share_count}")
        print(f"     分数: {rec.score:.3f}")
    
    return True


async def test_hybrid_recommendation():
    """测试混合推荐"""
    print("\n🔧 测试混合推荐")
    print("=" * 40)
    
    service = await test_behavior_recording()
    
    # 为每个用户生成混合推荐
    for user_id in ["user1", "user2", "user3"]:
        recommendations = await service.get_recommendations(user_id, count=5, rec_type="hybrid")
        
        print(f"\n{user_id} 的混合推荐:")
        for i, rec in enumerate(recommendations):
            print(f"  {i+1}. {rec.content_feature.title}")
            print(f"     类型: {rec.recommendation_type.value}, 分数: {rec.score:.3f}")
            print(f"     原因: {rec.reason}")
    
    return True


async def test_preference_evolution():
    """测试偏好演化"""
    print("\n🔧 测试偏好演化")
    print("=" * 40)
    
    service = await test_behavior_recording()
    
    # 获取初始偏好
    initial_profile = await service.get_user_profile("user1")
    print("user1 初始偏好:")
    if initial_profile.preferences:
        print(f"  类别偏好: {dict(initial_profile.preferences.category_preferences)}")
        print(f"  置信度: {initial_profile.preferences.confidence:.2f}")
    
    # 模拟更多行为
    new_behaviors = [
        ("user1", "4", UserActionType.VIEW, 400),
        ("user1", "4", UserActionType.LIKE, None),
        ("user1", "4", UserActionType.BOOKMARK, None),
        ("user1", "5", UserActionType.VIEW, 250),
        ("user1", "5", UserActionType.SHARE, None),
    ]
    
    for user_id, content_id, action_type, duration in new_behaviors:
        await service.record_behavior(user_id, content_id, action_type, duration)
    
    # 获取更新后的偏好
    updated_profile = await service.get_user_profile("user1")
    print("\nuser1 更新后偏好:")
    if updated_profile.preferences:
        print(f"  类别偏好: {dict(updated_profile.preferences.category_preferences)}")
        print(f"  置信度: {updated_profile.preferences.confidence:.2f}")
    
    print(f"\n行为数量变化: {initial_profile.total_actions} -> {updated_profile.total_actions}")
    
    return True


async def main():
    """主测试函数"""
    print("🚀 开始AI内容个性化推荐系统测试")
    print()
    
    tests = [
        ("内容注册", test_content_registration),
        ("行为记录", test_behavior_recording),
        ("基于内容推荐", test_content_based_recommendation),
        ("协同过滤推荐", test_collaborative_filtering),
        ("热门推荐", test_trending_recommendation),
        ("混合推荐", test_hybrid_recommendation),
        ("偏好演化", test_preference_evolution),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed_tests += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 AI内容个性化推荐系统测试结果")
    print("=" * 50)
    
    print(f"📈 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 AI内容个性化推荐系统测试全部通过！")
        print("\n🚀 支持的功能:")
        print("  • 用户行为分析和偏好学习")
        print("  • 基于内容的推荐算法")
        print("  • 协同过滤推荐算法")
        print("  • 热门内容推荐")
        print("  • 混合推荐策略")
        print("  • 用户画像动态更新")
        print("\n📈 技术特性:")
        print("  • 多维度用户偏好建模")
        print("  • 实时行为分析")
        print("  • 多算法融合推荐")
        print("  • 个性化推荐解释")
        print("  • 偏好置信度评估")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查实现")
        return False


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)
