#!/usr/bin/env python3
"""
项目重构验证测试脚本

验证重构后的项目结构是否正常工作，包括：
1. 模型导入和创建
2. 请求格式转换
3. 供应商适配器
4. 兼容性检查
5. 向后兼容性
"""

import sys
import os

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_basic_imports():
    """测试基础导入功能"""
    print("🧪 测试基础导入功能...")
    
    try:
        from ai_gen_hub.core.interfaces import (
            OptimizedTextGenerationRequest,
            TextGenerationRequest,
            GenerationConfig,
            StreamConfig,
            SafetyConfig,
            MessageRole,
            Message,
            RequestAdapter
        )
        print("✅ 核心接口导入成功")
        return True
    except Exception as e:
        print(f"❌ 核心接口导入失败: {e}")
        return False


def test_model_creation():
    """测试模型创建功能"""
    print("🧪 测试模型创建功能...")
    
    try:
        from ai_gen_hub.core.interfaces import (
            OptimizedTextGenerationRequest,
            GenerationConfig,
            StreamConfig,
            SafetyConfig,
            MessageRole,
            Message
        )
        
        # 创建优化版本请求
        request = OptimizedTextGenerationRequest(
            messages=[
                Message(role=MessageRole.SYSTEM, content="你是一个有用的助手"),
                Message(role=MessageRole.USER, content="你好，请介绍一下自己")
            ],
            model="gpt-4",
            generation=GenerationConfig(
                temperature=0.8,
                max_tokens=1000,
                top_p=0.9
            ),
            stream=StreamConfig(enabled=True),
            safety=SafetyConfig(
                content_filter=True,
                safety_level="medium"
            )
        )
        
        print("✅ OptimizedTextGenerationRequest 创建成功")
        print(f"   - 消息数量: {len(request.messages)}")
        print(f"   - 模型: {request.model}")
        print(f"   - 温度: {request.generation.temperature}")
        print(f"   - 流式输出: {request.stream.enabled}")
        
        return True
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_format_conversion():
    """测试格式转换功能"""
    print("🧪 测试格式转换功能...")
    
    try:
        from ai_gen_hub.core.interfaces import (
            OptimizedTextGenerationRequest,
            GenerationConfig,
            MessageRole,
            Message
        )
        
        # 创建传统格式请求
        legacy_request = {
            "messages": [
                {"role": "user", "content": "Hello"}
            ],
            "model": "gpt-4",
            "temperature": 0.8,
            "max_tokens": 1000,
            "stream": True,
            "functions": [
                {
                    "name": "get_weather",
                    "description": "获取天气信息"
                }
            ]
        }
        
        # 转换为优化版本
        optimized = OptimizedTextGenerationRequest.from_legacy_request(legacy_request)
        print("✅ 传统格式 → 优化版本转换成功")
        
        # 转换回传统格式
        legacy_format = optimized.to_legacy_format()
        print("✅ 优化版本 → 传统格式转换成功")
        
        # 验证数据一致性
        assert legacy_format["model"] == legacy_request["model"]
        assert legacy_format["temperature"] == legacy_request["temperature"]
        assert legacy_format["stream"] == legacy_request["stream"]
        print("✅ 数据一致性验证通过")
        
        return True
    except Exception as e:
        print(f"❌ 格式转换失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_provider_adapters():
    """测试供应商适配器功能"""
    print("🧪 测试供应商适配器功能...")
    
    try:
        from ai_gen_hub.core.adapters.provider_adapter import ProviderAdapterFactory
        from ai_gen_hub.core.interfaces import (
            OptimizedTextGenerationRequest,
            GenerationConfig,
            MessageRole,
            Message
        )
        
        # 创建测试请求
        request = OptimizedTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="Hello")],
            model="gpt-4",
            generation=GenerationConfig(temperature=0.8, max_tokens=1000)
        )
        
        # 测试所有支持的供应商
        providers = ProviderAdapterFactory.get_supported_providers()
        print(f"✅ 支持的供应商: {providers}")
        
        success_count = 0
        for provider in providers:
            try:
                adapter = ProviderAdapterFactory.create_adapter(provider)
                adapted_params = adapter.adapt_request(request)
                capabilities = adapter.get_capabilities()
                
                print(f"✅ {provider.upper()} 适配成功")
                success_count += 1
                
            except Exception as e:
                print(f"❌ {provider.upper()} 适配失败: {e}")
        
        print(f"✅ 供应商适配器测试完成: {success_count}/{len(providers)} 成功")
        return success_count == len(providers)
        
    except Exception as e:
        print(f"❌ 供应商适配器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_compatibility():
    """测试兼容性功能"""
    print("🧪 测试兼容性功能...")
    
    try:
        from ai_gen_hub.core.adapters.compatibility import CompatibilityChecker, MigrationHelper
        
        # 测试兼容性检查
        legacy_requests = [
            {
                "messages": [{"role": "user", "content": "Hello"}],
                "model": "gpt-4",
                "temperature": 0.8
            },
            {
                "messages": [{"role": "user", "content": "Hi"}],
                "model": "claude-3-sonnet",
                "max_tokens": 2000,
                "functions": [{"name": "test"}]
            }
        ]
        
        # 兼容性检查
        for i, request in enumerate(legacy_requests):
            compatibility = CompatibilityChecker.check_legacy_compatibility(request)
            print(f"✅ 请求 {i+1} 兼容性检查: {compatibility['compatible']}")
        
        # 迁移影响分析
        analysis = MigrationHelper.analyze_migration_impact(legacy_requests)
        print(f"✅ 迁移影响分析完成")
        print(f"   - 兼容请求: {analysis['compatible_requests']}/{analysis['total_requests']}")
        
        # 迁移计划
        plan = MigrationHelper.create_migration_plan(legacy_requests)
        print(f"✅ 迁移计划创建完成: {len(plan['phases'])} 个阶段")
        
        return True
        
    except Exception as e:
        print(f"❌ 兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_backward_compatibility():
    """测试向后兼容性"""
    print("🧪 测试向后兼容性...")
    
    try:
        # 测试旧的导入方式是否仍然有效
        from ai_gen_hub.core.interfaces import (
            TextGenerationRequest,
            TextGenerationResponse,
            Message,
            MessageRole
        )
        
        # 创建传统请求
        traditional_request = TextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="Hello")],
            model="gpt-4",
            temperature=0.7,
            stream=False
        )
        
        print("✅ 传统请求模型创建成功")
        print(f"   - 模型: {traditional_request.model}")
        print(f"   - 温度: {traditional_request.temperature}")
        
        return True
        
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始项目重构验证测试")
    print("=" * 50)
    
    tests = [
        ("基础导入", test_basic_imports),
        ("模型创建", test_model_creation),
        ("格式转换", test_format_conversion),
        ("供应商适配器", test_provider_adapters),
        ("兼容性功能", test_compatibility),
        ("向后兼容性", test_backward_compatibility),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        success = test_func()
        results.append((test_name, success))
        print()
    
    # 汇总结果
    print("=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！项目重构成功！")
        return 0
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
