#!/usr/bin/env python3
"""
测试V2 API StreamConfig修复
"""

import asyncio
import json
import logging
import httpx

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_v2_api_chunk_size_fix():
    """测试V2 API chunk_size修复"""
    logger.info("🧪 测试V2 API StreamConfig chunk_size修复...")
    
    base_url = "http://localhost:8001"
    
    # 测试用例：包含chunk_size=0的请求（之前会失败）
    test_request = {
        "messages": [
            {
                "role": "user",
                "content": "你是谁? 可以帮忙做什么?"
            }
        ],
        "model": "gemini-2.5-flash",
        "stream": {
            "enabled": False,
            "chunk_size": 0,  # 这是之前导致验证错误的值
            "include_usage": True
        }
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            logger.info("📤 发送V2 API请求...")
            response = await client.post(
                f"{base_url}/api/v1/text/v2/generate",
                json=test_request,
                headers={"Content-Type": "application/json"}
            )
            
            logger.info(f"📥 收到响应: status={response.status_code}")
            
            if response.status_code == 200:
                logger.info("✅ V2 API请求成功！StreamConfig chunk_size=0 修复生效")
                return True
                
            elif response.status_code == 422:
                # Pydantic验证错误
                try:
                    error_data = response.json()
                    error_detail = str(error_data.get("detail", ""))
                    
                    if "validation error" in error_detail.lower() and "streamconfig" in error_detail.lower():
                        logger.error("❌ StreamConfig验证错误仍然存在！")
                        logger.error(f"错误详情: {error_detail}")
                        return False
                    else:
                        logger.warning(f"⚠️  其他验证错误: {error_detail}")
                        return False
                        
                except Exception as e:
                    logger.error(f"❌ 解析错误响应失败: {e}")
                    logger.error(f"原始响应: {response.text}")
                    return False
                    
            elif response.status_code == 400:
                # 业务错误（如API密钥问题）
                try:
                    error_data = response.json()
                    logger.warning(f"⚠️  业务错误: {error_data}")
                    logger.info("✅ 验证通过：没有StreamConfig验证错误，只是业务逻辑错误")
                    return True
                except Exception as e:
                    logger.error(f"❌ 解析业务错误响应失败: {e}")
                    return False
                    
            else:
                logger.error(f"❌ 意外的响应状态码: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 请求异常: {e}")
            return False

async def main():
    """主函数"""
    logger.info("🚀 开始V2 API StreamConfig修复测试...")
    
    # 等待服务器启动
    await asyncio.sleep(10)
    
    # 测试主要修复
    success = await test_v2_api_chunk_size_fix()
    
    # 总结
    logger.info("\n" + "="*60)
    logger.info("📋 测试总结:")
    logger.info(f"  修复测试: {'✅ 通过' if success else '❌ 失败'}")
    
    if success:
        logger.info("🎉 V2 API StreamConfig修复成功！")
    else:
        logger.error("💥 仍有问题需要解决")

if __name__ == "__main__":
    asyncio.run(main())
