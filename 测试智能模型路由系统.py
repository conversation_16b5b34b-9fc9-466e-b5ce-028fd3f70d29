#!/usr/bin/env python3
"""
测试智能模型路由系统

验证模型选择、A/B测试、性能监控等功能
"""

import asyncio
import sys
import time
import random
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

# 创建必要的模块
def create_mock_modules():
    """创建模拟模块"""
    # 创建基础接口
    class BaseRequest:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
            if not hasattr(self, 'id'):
                self.id = f"req_{int(time.time())}"
    
    class BaseResponse:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
    
    # 创建异常类
    class AIGenHubException(Exception):
        pass
    
    # 创建日志记录器
    class Logger:
        def info(self, msg, **kwargs):
            print(f"INFO: {msg}")
        
        def error(self, msg, **kwargs):
            print(f"ERROR: {msg}")
        
        def warning(self, msg, **kwargs):
            print(f"WARNING: {msg}")
    
    def get_logger(name):
        return Logger()
    
    return {
        'BaseRequest': BaseRequest,
        'BaseResponse': BaseResponse,
        'AIGenHubException': AIGenHubException,
        'get_logger': get_logger
    }

# 创建模拟模块
mock_modules = create_mock_modules()

# 简化的智能路由系统
from enum import Enum
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import uuid
import statistics

class ModelProvider(Enum):
    OPENAI = "openai"
    GOOGLE = "google"
    ANTHROPIC = "anthropic"
    BAIDU = "baidu"

class RoutingStrategy(Enum):
    ROUND_ROBIN = "round_robin"
    WEIGHTED = "weighted"
    PERFORMANCE_BASED = "performance_based"
    COST_OPTIMIZED = "cost_optimized"
    A_B_TEST = "a_b_test"

@dataclass
class ModelConfig:
    provider: ModelProvider
    model_name: str
    cost_per_token: float = 0.001
    max_tokens: int = 4096
    capabilities: List[str] = field(default_factory=list)
    weight: float = 1.0
    enabled: bool = True

@dataclass
class PerformanceMetrics:
    provider: ModelProvider
    model_name: str
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_response_time: float = 0.0
    avg_cost: float = 0.0
    quality_score: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)

@dataclass
class ABTestConfig:
    test_id: str
    name: str
    model_a: str
    model_b: str
    traffic_split: float = 0.5  # 0.0-1.0, A模型的流量比例
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    active: bool = True

@dataclass
class RoutingRequest:
    user_id: str
    task_type: str
    prompt: str
    requirements: Dict[str, Any] = field(default_factory=dict)
    context: Dict[str, Any] = field(default_factory=dict)

@dataclass
class RoutingResult:
    selected_model: ModelConfig
    reason: str
    confidence: float
    alternatives: List[ModelConfig] = field(default_factory=list)
    routing_time: float = 0.0


class SimpleModelRoutingService:
    """简化的智能模型路由服务"""
    
    def __init__(self):
        self.logger = mock_modules['get_logger'](__name__)
        self.models: Dict[str, ModelConfig] = {}
        self.metrics: Dict[str, PerformanceMetrics] = {}
        self.ab_tests: Dict[str, ABTestConfig] = {}
        self.routing_history: List[Dict[str, Any]] = []
        
        # 初始化默认模型
        self._initialize_default_models()
    
    def _initialize_default_models(self):
        """初始化默认模型配置"""
        default_models = [
            ModelConfig(
                provider=ModelProvider.OPENAI,
                model_name="gpt-3.5-turbo",
                cost_per_token=0.0015,
                max_tokens=4096,
                capabilities=["text_generation", "conversation", "code"],
                weight=1.0
            ),
            ModelConfig(
                provider=ModelProvider.OPENAI,
                model_name="gpt-4",
                cost_per_token=0.03,
                max_tokens=8192,
                capabilities=["text_generation", "conversation", "code", "reasoning"],
                weight=0.8
            ),
            ModelConfig(
                provider=ModelProvider.GOOGLE,
                model_name="gemini-pro",
                cost_per_token=0.001,
                max_tokens=32768,
                capabilities=["text_generation", "conversation", "multimodal"],
                weight=0.9
            ),
            ModelConfig(
                provider=ModelProvider.ANTHROPIC,
                model_name="claude-3-sonnet",
                cost_per_token=0.015,
                max_tokens=200000,
                capabilities=["text_generation", "conversation", "analysis"],
                weight=0.7
            )
        ]
        
        for model in default_models:
            model_key = f"{model.provider.value}:{model.model_name}"
            self.models[model_key] = model
            
            # 初始化性能指标
            self.metrics[model_key] = PerformanceMetrics(
                provider=model.provider,
                model_name=model.model_name,
                avg_response_time=random.uniform(0.5, 2.0),
                quality_score=random.uniform(0.7, 0.95)
            )
    
    async def register_model(self, model_config: ModelConfig) -> str:
        """注册模型"""
        model_key = f"{model_config.provider.value}:{model_config.model_name}"
        self.models[model_key] = model_config
        
        # 初始化性能指标
        if model_key not in self.metrics:
            self.metrics[model_key] = PerformanceMetrics(
                provider=model_config.provider,
                model_name=model_config.model_name
            )
        
        self.logger.info(f"注册模型: {model_key}")
        return model_key
    
    async def route_request(self, request: RoutingRequest, strategy: RoutingStrategy = RoutingStrategy.PERFORMANCE_BASED) -> RoutingResult:
        """路由请求到最佳模型"""
        start_time = time.time()
        
        # 过滤可用模型
        available_models = self._filter_available_models(request)
        
        if not available_models:
            raise mock_modules['AIGenHubException']("没有可用的模型")
        
        # 根据策略选择模型
        if strategy == RoutingStrategy.ROUND_ROBIN:
            selected_model = self._round_robin_selection(available_models)
            reason = "轮询选择"
        elif strategy == RoutingStrategy.WEIGHTED:
            selected_model = self._weighted_selection(available_models)
            reason = "权重选择"
        elif strategy == RoutingStrategy.PERFORMANCE_BASED:
            selected_model = self._performance_based_selection(available_models)
            reason = "性能优先"
        elif strategy == RoutingStrategy.COST_OPTIMIZED:
            selected_model = self._cost_optimized_selection(available_models)
            reason = "成本优化"
        elif strategy == RoutingStrategy.A_B_TEST:
            selected_model = self._ab_test_selection(available_models, request)
            reason = "A/B测试"
        else:
            selected_model = available_models[0]
            reason = "默认选择"
        
        routing_time = time.time() - start_time
        
        # 计算置信度
        confidence = self._calculate_confidence(selected_model, available_models)
        
        # 获取备选模型
        alternatives = [m for m in available_models if m != selected_model][:3]
        
        result = RoutingResult(
            selected_model=selected_model,
            reason=reason,
            confidence=confidence,
            alternatives=alternatives,
            routing_time=routing_time
        )
        
        # 记录路由历史
        self._record_routing(request, result)
        
        self.logger.info(f"路由选择: {selected_model.provider.value}:{selected_model.model_name} ({reason})")
        return result
    
    async def create_ab_test(self, name: str, model_a: str, model_b: str, traffic_split: float = 0.5, duration_hours: int = 24) -> str:
        """创建A/B测试"""
        test_id = str(uuid.uuid4())
        
        ab_test = ABTestConfig(
            test_id=test_id,
            name=name,
            model_a=model_a,
            model_b=model_b,
            traffic_split=traffic_split,
            end_time=datetime.now() + timedelta(hours=duration_hours)
        )
        
        self.ab_tests[test_id] = ab_test
        
        self.logger.info(f"创建A/B测试: {name} ({model_a} vs {model_b})")
        return test_id
    
    async def get_ab_test_results(self, test_id: str) -> Dict[str, Any]:
        """获取A/B测试结果"""
        ab_test = self.ab_tests.get(test_id)
        if not ab_test:
            raise mock_modules['AIGenHubException']("A/B测试不存在")
        
        # 统计测试结果
        model_a_metrics = self.metrics.get(ab_test.model_a, PerformanceMetrics(ModelProvider.OPENAI, "unknown"))
        model_b_metrics = self.metrics.get(ab_test.model_b, PerformanceMetrics(ModelProvider.OPENAI, "unknown"))
        
        results = {
            "test_id": test_id,
            "name": ab_test.name,
            "status": "active" if ab_test.active else "completed",
            "duration": (datetime.now() - ab_test.start_time).total_seconds() / 3600,
            "model_a": {
                "name": ab_test.model_a,
                "requests": model_a_metrics.total_requests,
                "success_rate": model_a_metrics.successful_requests / max(model_a_metrics.total_requests, 1),
                "avg_response_time": model_a_metrics.avg_response_time,
                "quality_score": model_a_metrics.quality_score
            },
            "model_b": {
                "name": ab_test.model_b,
                "requests": model_b_metrics.total_requests,
                "success_rate": model_b_metrics.successful_requests / max(model_b_metrics.total_requests, 1),
                "avg_response_time": model_b_metrics.avg_response_time,
                "quality_score": model_b_metrics.quality_score
            }
        }
        
        # 计算统计显著性（简化）
        if model_a_metrics.total_requests > 10 and model_b_metrics.total_requests > 10:
            quality_diff = abs(model_a_metrics.quality_score - model_b_metrics.quality_score)
            results["statistical_significance"] = quality_diff > 0.05
            results["winner"] = ab_test.model_a if model_a_metrics.quality_score > model_b_metrics.quality_score else ab_test.model_b
        else:
            results["statistical_significance"] = False
            results["winner"] = None
        
        return results
    
    async def update_model_metrics(self, model_key: str, response_time: float, success: bool, cost: float, quality_score: float = None):
        """更新模型性能指标"""
        if model_key not in self.metrics:
            return
        
        metrics = self.metrics[model_key]
        
        # 更新请求统计
        metrics.total_requests += 1
        if success:
            metrics.successful_requests += 1
        else:
            metrics.failed_requests += 1
        
        # 更新平均响应时间
        if metrics.total_requests == 1:
            metrics.avg_response_time = response_time
        else:
            metrics.avg_response_time = (metrics.avg_response_time * (metrics.total_requests - 1) + response_time) / metrics.total_requests
        
        # 更新平均成本
        if metrics.total_requests == 1:
            metrics.avg_cost = cost
        else:
            metrics.avg_cost = (metrics.avg_cost * (metrics.total_requests - 1) + cost) / metrics.total_requests
        
        # 更新质量评分
        if quality_score is not None:
            if metrics.total_requests == 1:
                metrics.quality_score = quality_score
            else:
                metrics.quality_score = (metrics.quality_score * 0.9 + quality_score * 0.1)  # 指数移动平均
        
        metrics.last_updated = datetime.now()
        
        self.logger.info(f"更新模型指标: {model_key}")
    
    async def get_model_performance(self, model_key: str = None) -> Dict[str, Any]:
        """获取模型性能统计"""
        if model_key:
            if model_key not in self.metrics:
                raise mock_modules['AIGenHubException']("模型不存在")
            
            metrics = self.metrics[model_key]
            return {
                "model": model_key,
                "total_requests": metrics.total_requests,
                "success_rate": metrics.successful_requests / max(metrics.total_requests, 1),
                "avg_response_time": metrics.avg_response_time,
                "avg_cost": metrics.avg_cost,
                "quality_score": metrics.quality_score,
                "last_updated": metrics.last_updated
            }
        else:
            # 返回所有模型的性能统计
            all_metrics = {}
            for key, metrics in self.metrics.items():
                all_metrics[key] = {
                    "total_requests": metrics.total_requests,
                    "success_rate": metrics.successful_requests / max(metrics.total_requests, 1),
                    "avg_response_time": metrics.avg_response_time,
                    "avg_cost": metrics.avg_cost,
                    "quality_score": metrics.quality_score
                }
            return all_metrics
    
    def _filter_available_models(self, request: RoutingRequest) -> List[ModelConfig]:
        """过滤可用模型"""
        available_models = []
        
        for model in self.models.values():
            if not model.enabled:
                continue
            
            # 检查能力匹配
            if request.requirements.get('capabilities'):
                required_caps = request.requirements['capabilities']
                if not all(cap in model.capabilities for cap in required_caps):
                    continue
            
            # 检查最大token限制
            if request.requirements.get('max_tokens'):
                if request.requirements['max_tokens'] > model.max_tokens:
                    continue
            
            available_models.append(model)
        
        return available_models
    
    def _round_robin_selection(self, models: List[ModelConfig]) -> ModelConfig:
        """轮询选择"""
        # 简化实现：基于时间戳选择
        index = int(time.time()) % len(models)
        return models[index]
    
    def _weighted_selection(self, models: List[ModelConfig]) -> ModelConfig:
        """权重选择"""
        total_weight = sum(model.weight for model in models)
        if total_weight == 0:
            return models[0]
        
        rand_val = random.uniform(0, total_weight)
        current_weight = 0
        
        for model in models:
            current_weight += model.weight
            if rand_val <= current_weight:
                return model
        
        return models[-1]
    
    def _performance_based_selection(self, models: List[ModelConfig]) -> ModelConfig:
        """基于性能选择"""
        best_model = None
        best_score = -1
        
        for model in models:
            model_key = f"{model.provider.value}:{model.model_name}"
            metrics = self.metrics.get(model_key)
            
            if not metrics or metrics.total_requests == 0:
                # 新模型给予中等评分
                score = 0.5
            else:
                # 综合评分：质量 * 0.4 + 成功率 * 0.3 + 响应时间倒数 * 0.3
                success_rate = metrics.successful_requests / metrics.total_requests
                response_time_score = 1.0 / (1.0 + metrics.avg_response_time)  # 响应时间越短分数越高
                
                score = (metrics.quality_score * 0.4 + 
                        success_rate * 0.3 + 
                        response_time_score * 0.3)
            
            if score > best_score:
                best_score = score
                best_model = model
        
        return best_model or models[0]
    
    def _cost_optimized_selection(self, models: List[ModelConfig]) -> ModelConfig:
        """成本优化选择"""
        # 选择成本最低且质量可接受的模型
        acceptable_models = []
        
        for model in models:
            model_key = f"{model.provider.value}:{model.model_name}"
            metrics = self.metrics.get(model_key)
            
            # 质量阈值
            if metrics and metrics.quality_score < 0.7:
                continue
            
            acceptable_models.append(model)
        
        if not acceptable_models:
            acceptable_models = models
        
        # 选择成本最低的
        return min(acceptable_models, key=lambda m: m.cost_per_token)
    
    def _ab_test_selection(self, models: List[ModelConfig], request: RoutingRequest) -> ModelConfig:
        """A/B测试选择"""
        # 查找活跃的A/B测试
        active_tests = [test for test in self.ab_tests.values() 
                       if test.active and (test.end_time is None or datetime.now() < test.end_time)]
        
        if not active_tests:
            return self._performance_based_selection(models)
        
        # 使用第一个活跃测试
        ab_test = active_tests[0]
        
        # 根据用户ID和流量分割决定使用哪个模型
        user_hash = hash(request.user_id) % 100 / 100.0
        
        if user_hash < ab_test.traffic_split:
            target_model = ab_test.model_a
        else:
            target_model = ab_test.model_b
        
        # 查找目标模型
        for model in models:
            model_key = f"{model.provider.value}:{model.model_name}"
            if model_key == target_model:
                return model
        
        # 如果找不到目标模型，使用默认选择
        return self._performance_based_selection(models)
    
    def _calculate_confidence(self, selected_model: ModelConfig, all_models: List[ModelConfig]) -> float:
        """计算选择置信度"""
        if len(all_models) <= 1:
            return 1.0
        
        model_key = f"{selected_model.provider.value}:{selected_model.model_name}"
        metrics = self.metrics.get(model_key)
        
        if not metrics or metrics.total_requests == 0:
            return 0.5  # 新模型的默认置信度
        
        # 基于历史性能计算置信度
        success_rate = metrics.successful_requests / metrics.total_requests
        quality_score = metrics.quality_score
        
        # 综合置信度
        confidence = (success_rate * 0.5 + quality_score * 0.5)
        
        # 根据样本数量调整置信度
        sample_factor = min(metrics.total_requests / 100.0, 1.0)
        confidence = confidence * sample_factor + 0.5 * (1 - sample_factor)
        
        return min(confidence, 1.0)
    
    def _record_routing(self, request: RoutingRequest, result: RoutingResult):
        """记录路由历史"""
        record = {
            "timestamp": datetime.now(),
            "user_id": request.user_id,
            "task_type": request.task_type,
            "selected_model": f"{result.selected_model.provider.value}:{result.selected_model.model_name}",
            "reason": result.reason,
            "confidence": result.confidence,
            "routing_time": result.routing_time
        }
        
        self.routing_history.append(record)
        
        # 保持历史记录在合理范围内
        if len(self.routing_history) > 10000:
            self.routing_history = self.routing_history[-5000:]


async def test_model_registration():
    """测试模型注册"""
    print("🔧 测试模型注册")
    print("=" * 40)
    
    service = SimpleModelRoutingService()
    
    # 注册新模型
    new_model = ModelConfig(
        provider=ModelProvider.BAIDU,
        model_name="ernie-bot-turbo",
        cost_per_token=0.0008,
        max_tokens=8192,
        capabilities=["text_generation", "conversation"],
        weight=0.6
    )
    
    model_key = await service.register_model(new_model)
    
    print(f"✅ 注册模型: {model_key}")
    print(f"   供应商: {new_model.provider.value}")
    print(f"   成本: {new_model.cost_per_token}/token")
    print(f"   最大tokens: {new_model.max_tokens}")
    print(f"   能力: {new_model.capabilities}")
    
    return service


async def test_basic_routing():
    """测试基本路由"""
    print("\n🔧 测试基本路由")
    print("=" * 40)
    
    service = await test_model_registration()
    
    # 创建路由请求
    request = RoutingRequest(
        user_id="user_123",
        task_type="text_generation",
        prompt="写一篇关于AI的文章",
        requirements={
            "capabilities": ["text_generation"],
            "max_tokens": 2000
        }
    )
    
    # 测试不同路由策略
    strategies = [
        RoutingStrategy.PERFORMANCE_BASED,
        RoutingStrategy.COST_OPTIMIZED,
        RoutingStrategy.WEIGHTED,
        RoutingStrategy.ROUND_ROBIN
    ]
    
    for strategy in strategies:
        result = await service.route_request(request, strategy)
        
        print(f"✅ {strategy.value} 策略:")
        print(f"   选择模型: {result.selected_model.provider.value}:{result.selected_model.model_name}")
        print(f"   选择原因: {result.reason}")
        print(f"   置信度: {result.confidence:.2f}")
        print(f"   路由时间: {result.routing_time:.4f}s")
    
    return service


async def test_ab_testing():
    """测试A/B测试"""
    print("\n🔧 测试A/B测试")
    print("=" * 40)
    
    service = await test_model_registration()
    
    # 创建A/B测试
    test_id = await service.create_ab_test(
        name="GPT-3.5 vs Gemini Pro",
        model_a="openai:gpt-3.5-turbo",
        model_b="google:gemini-pro",
        traffic_split=0.6,
        duration_hours=24
    )
    
    print(f"✅ 创建A/B测试: {test_id}")
    
    # 模拟多个用户请求
    users = [f"user_{i}" for i in range(10)]
    model_selections = {"openai:gpt-3.5-turbo": 0, "google:gemini-pro": 0}
    
    for user_id in users:
        request = RoutingRequest(
            user_id=user_id,
            task_type="text_generation",
            prompt="测试提示"
        )
        
        result = await service.route_request(request, RoutingStrategy.A_B_TEST)
        model_key = f"{result.selected_model.provider.value}:{result.selected_model.model_name}"
        model_selections[model_key] += 1
    
    print(f"✅ A/B测试流量分配:")
    for model, count in model_selections.items():
        print(f"   {model}: {count} 次 ({count/len(users)*100:.1f}%)")
    
    # 获取测试结果
    results = await service.get_ab_test_results(test_id)
    print(f"✅ A/B测试结果:")
    print(f"   测试状态: {results['status']}")
    print(f"   模型A请求数: {results['model_a']['requests']}")
    print(f"   模型B请求数: {results['model_b']['requests']}")
    
    return service


async def test_performance_monitoring():
    """测试性能监控"""
    print("\n🔧 测试性能监控")
    print("=" * 40)
    
    service = await test_model_registration()
    
    # 模拟一些请求和性能数据
    models = ["openai:gpt-3.5-turbo", "openai:gpt-4", "google:gemini-pro"]
    
    for model_key in models:
        # 模拟多次请求
        for _ in range(random.randint(5, 15)):
            response_time = random.uniform(0.5, 3.0)
            success = random.random() > 0.1  # 90%成功率
            cost = random.uniform(0.001, 0.05)
            quality_score = random.uniform(0.7, 0.95)
            
            await service.update_model_metrics(
                model_key=model_key,
                response_time=response_time,
                success=success,
                cost=cost,
                quality_score=quality_score
            )
    
    # 获取性能统计
    all_performance = await service.get_model_performance()
    
    print("✅ 模型性能统计:")
    for model_key, metrics in all_performance.items():
        print(f"   {model_key}:")
        print(f"     总请求数: {metrics['total_requests']}")
        print(f"     成功率: {metrics['success_rate']:.2%}")
        print(f"     平均响应时间: {metrics['avg_response_time']:.2f}s")
        print(f"     平均成本: ${metrics['avg_cost']:.4f}")
        print(f"     质量评分: {metrics['quality_score']:.2f}")
    
    return service


async def test_intelligent_routing():
    """测试智能路由"""
    print("\n🔧 测试智能路由")
    print("=" * 40)
    
    service = await test_performance_monitoring()
    
    # 测试不同类型的请求
    test_requests = [
        {
            "name": "简单对话",
            "request": RoutingRequest(
                user_id="user_simple",
                task_type="conversation",
                prompt="你好",
                requirements={"capabilities": ["conversation"]}
            )
        },
        {
            "name": "代码生成",
            "request": RoutingRequest(
                user_id="user_coder",
                task_type="code_generation",
                prompt="写一个Python排序函数",
                requirements={"capabilities": ["code"], "max_tokens": 1000}
            )
        },
        {
            "name": "长文本分析",
            "request": RoutingRequest(
                user_id="user_analyst",
                task_type="analysis",
                prompt="分析这份长文档",
                requirements={"capabilities": ["analysis"], "max_tokens": 10000}
            )
        },
        {
            "name": "成本敏感任务",
            "request": RoutingRequest(
                user_id="user_budget",
                task_type="simple_task",
                prompt="简单的文本处理",
                requirements={"cost_sensitive": True}
            )
        }
    ]
    
    for test_case in test_requests:
        print(f"\n📝 {test_case['name']}:")
        
        # 性能优先路由
        result = await service.route_request(test_case['request'], RoutingStrategy.PERFORMANCE_BASED)
        print(f"   性能优先: {result.selected_model.provider.value}:{result.selected_model.model_name} (置信度: {result.confidence:.2f})")
        
        # 成本优化路由
        result = await service.route_request(test_case['request'], RoutingStrategy.COST_OPTIMIZED)
        print(f"   成本优化: {result.selected_model.provider.value}:{result.selected_model.model_name} (成本: ${result.selected_model.cost_per_token:.4f}/token)")
    
    return True


async def test_routing_analytics():
    """测试路由分析"""
    print("\n🔧 测试路由分析")
    print("=" * 40)
    
    service = await test_performance_monitoring()
    
    # 模拟大量路由请求
    for i in range(50):
        request = RoutingRequest(
            user_id=f"user_{i % 10}",
            task_type=random.choice(["text_generation", "conversation", "analysis"]),
            prompt=f"测试请求 {i}"
        )
        
        strategy = random.choice(list(RoutingStrategy))
        await service.route_request(request, strategy)
    
    # 分析路由历史
    history = service.routing_history
    
    # 统计路由策略使用情况
    strategy_counts = {}
    model_counts = {}
    
    for record in history[-50:]:  # 最近50条记录
        reason = record['reason']
        model = record['selected_model']
        
        strategy_counts[reason] = strategy_counts.get(reason, 0) + 1
        model_counts[model] = model_counts.get(model, 0) + 1
    
    print("✅ 路由策略使用统计:")
    for strategy, count in strategy_counts.items():
        print(f"   {strategy}: {count} 次")
    
    print("\n✅ 模型选择统计:")
    for model, count in model_counts.items():
        print(f"   {model}: {count} 次")
    
    # 计算平均路由时间
    avg_routing_time = statistics.mean([r['routing_time'] for r in history[-50:]])
    print(f"\n✅ 平均路由时间: {avg_routing_time:.4f}s")
    
    return True


async def main():
    """主测试函数"""
    print("🚀 开始智能模型路由系统测试")
    print()
    
    tests = [
        ("模型注册", test_model_registration),
        ("基本路由", test_basic_routing),
        ("A/B测试", test_ab_testing),
        ("性能监控", test_performance_monitoring),
        ("智能路由", test_intelligent_routing),
        ("路由分析", test_routing_analytics),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed_tests += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 智能模型路由系统测试结果")
    print("=" * 50)
    
    print(f"📈 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 智能模型路由系统测试全部通过！")
        print("\n🚀 支持的功能:")
        print("  • 多供应商模型管理")
        print("  • 智能路由策略")
        print("  • A/B测试框架")
        print("  • 实时性能监控")
        print("  • 成本优化选择")
        print("  • 路由分析统计")
        print("\n📈 技术特性:")
        print("  • 多种路由算法")
        print("  • 动态性能评估")
        print("  • 自适应模型选择")
        print("  • 统计显著性检验")
        print("  • 实时指标更新")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查实现")
        return False


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)
