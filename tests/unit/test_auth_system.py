"""
认证系统单元测试

测试认证服务、权限管理、JWT Token等功能
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4

from ai_gen_hub.services.auth_service import AuthService
from ai_gen_hub.core.models.auth import (
    UserCreate, UserUpdate, UserLogin, PasswordChange,
    APIKeyCreate, UserRole, Permission, UserStatus
)
from ai_gen_hub.core.exceptions import (
    AuthenticationError, AuthorizationError, ValidationError
)


class TestAuthService:
    """认证服务测试"""
    
    @pytest.fixture
    def auth_service(self):
        """创建认证服务实例"""
        return AuthService(
            jwt_secret_key="test-secret-key",
            jwt_algorithm="HS256",
            access_token_expire_minutes=30
        )
    
    async def test_create_user(self, auth_service):
        """测试用户创建"""
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="password123",
            role=UserRole.USER
        )
        
        user = await auth_service.create_user(user_data)
        
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.role == UserRole.USER
        assert user.is_active()
        assert user.has_permission(Permission.API_TEXT_GENERATE)
    
    def test_create_duplicate_user(self, auth_service):
        """测试创建重复用户"""
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="password123"
        )
        
        # 创建第一个用户
        auth_service.create_user(user_data)
        
        # 尝试创建重复用户名的用户
        with pytest.raises(ValidationError, match="用户名.*已存在"):
            auth_service.create_user(user_data)
        
        # 尝试创建重复邮箱的用户
        user_data2 = UserCreate(
            username="testuser2",
            email="<EMAIL>",
            password="password123"
        )
        with pytest.raises(ValidationError, match="邮箱.*已存在"):
            auth_service.create_user(user_data2)
    
    def test_authenticate_user(self, auth_service):
        """测试用户认证"""
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="password123"
        )
        
        user = auth_service.create_user(user_data)
        
        # 正确的用户名和密码
        authenticated = auth_service.authenticate_user("testuser", "password123")
        assert authenticated is not None
        assert authenticated.id == user.id
        
        # 错误的密码
        authenticated = auth_service.authenticate_user("testuser", "wrongpassword")
        assert authenticated is None
        
        # 不存在的用户
        authenticated = auth_service.authenticate_user("nonexistent", "password123")
        assert authenticated is None
    
    def test_jwt_token_operations(self, auth_service):
        """测试JWT Token操作"""
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="password123"
        )
        
        user = auth_service.create_user(user_data)
        
        # 创建访问令牌
        access_token = auth_service.create_access_token(user)
        assert access_token is not None
        assert isinstance(access_token, str)
        
        # 验证令牌
        payload = auth_service.verify_token(access_token)
        assert payload.sub == str(user.id)
        assert payload.type.value == "access"
        
        # 创建刷新令牌
        refresh_token = auth_service.create_refresh_token(user)
        assert refresh_token is not None
        
        # 使用刷新令牌获取新的访问令牌
        new_access_token = auth_service.refresh_access_token(refresh_token)
        assert new_access_token is not None
        assert new_access_token != access_token
    
    def test_api_key_operations(self, auth_service):
        """测试API密钥操作"""
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="password123"
        )
        
        user = auth_service.create_user(user_data)
        
        # 创建API密钥
        key_data = APIKeyCreate(
            name="测试密钥",
            permissions=[Permission.API_TEXT_GENERATE],
            rate_limit=100
        )
        
        api_key_obj, api_key = auth_service.create_api_key(user.id, key_data)
        
        assert api_key_obj.name == "测试密钥"
        assert api_key_obj.user_id == user.id
        assert Permission.API_TEXT_GENERATE in api_key_obj.permissions
        assert api_key.startswith("ak_")
        
        # 验证API密钥
        verified_key = auth_service.verify_api_key(api_key)
        assert verified_key is not None
        assert verified_key.id == api_key_obj.id
        
        # 获取用户的API密钥列表
        keys = auth_service.list_api_keys(user.id)
        assert len(keys) == 1
        assert keys[0].id == api_key_obj.id
        
        # 删除API密钥
        success = auth_service.delete_api_key(user.id, api_key_obj.id)
        assert success is True
        
        # 验证已删除的密钥
        verified_key = auth_service.verify_api_key(api_key)
        assert verified_key is None
    
    def test_permission_checks(self, auth_service):
        """测试权限检查"""
        # 创建不同角色的用户
        admin_data = UserCreate(
            username="admin",
            email="<EMAIL>",
            password="password123",
            role=UserRole.ADMIN
        )
        admin = auth_service.create_user(admin_data)
        
        user_data = UserCreate(
            username="user",
            email="<EMAIL>",
            password="password123",
            role=UserRole.USER
        )
        user = auth_service.create_user(user_data)
        
        # 管理员权限检查
        assert auth_service.check_permission(admin, Permission.SYSTEM_ADMIN)
        assert auth_service.check_permission(admin, Permission.USER_CREATE)
        assert auth_service.check_permission(admin, Permission.API_TEXT_GENERATE)
        
        # 普通用户权限检查
        assert not auth_service.check_permission(user, Permission.SYSTEM_ADMIN)
        assert not auth_service.check_permission(user, Permission.USER_CREATE)
        assert auth_service.check_permission(user, Permission.API_TEXT_GENERATE)
        
        # 权限要求测试
        with pytest.raises(AuthorizationError):
            auth_service.require_permission(user, Permission.SYSTEM_ADMIN)
        
        # 不应该抛出异常
        auth_service.require_permission(admin, Permission.SYSTEM_ADMIN)
    
    def test_user_update(self, auth_service):
        """测试用户更新"""
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="password123"
        )
        
        user = auth_service.create_user(user_data)
        
        # 更新用户信息
        update_data = UserUpdate(
            full_name="Test User",
            role=UserRole.DEVELOPER
        )
        
        updated_user = auth_service.update_user(user.id, update_data)
        
        assert updated_user.full_name == "Test User"
        assert updated_user.role == UserRole.DEVELOPER
        assert updated_user.has_permission(Permission.DEBUG_ACCESS)
    
    def test_password_change(self, auth_service):
        """测试密码修改"""
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="oldpassword"
        )
        
        user = auth_service.create_user(user_data)
        
        # 正确的密码修改
        password_data = PasswordChange(
            current_password="oldpassword",
            new_password="newpassword123"
        )
        
        success = auth_service.change_password(user.id, password_data)
        assert success is True
        
        # 验证新密码
        authenticated = auth_service.authenticate_user("testuser", "newpassword123")
        assert authenticated is not None
        
        # 验证旧密码不再有效
        authenticated = auth_service.authenticate_user("testuser", "oldpassword")
        assert authenticated is None
        
        # 错误的当前密码
        wrong_password_data = PasswordChange(
            current_password="wrongpassword",
            new_password="anothernewpassword"
        )
        
        with pytest.raises(AuthenticationError, match="当前密码错误"):
            auth_service.change_password(user.id, wrong_password_data)
    
    def test_quota_management(self, auth_service):
        """测试配额管理"""
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="password123",
            api_quota=10
        )
        
        user = auth_service.create_user(user_data)
        
        # 初始状态应该可以使用API
        assert auth_service.check_quota(user.id) is True
        
        # 记录使用量
        for _ in range(10):
            auth_service.record_usage(user.id, "text_generation", "gpt-3.5-turbo", 100)
        
        # 配额用完后不应该能使用API
        updated_user = auth_service.get_user_by_id(user.id)
        assert updated_user.api_quota_used >= 10
        assert not updated_user.can_use_api()
    
    def test_login_flow(self, auth_service):
        """测试登录流程"""
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="password123"
        )
        
        auth_service.create_user(user_data)
        
        # 成功登录
        login_data = UserLogin(
            username="testuser",
            password="password123",
            remember_me=True
        )
        
        token = auth_service.login(login_data)
        
        assert token.access_token is not None
        assert token.refresh_token is not None  # remember_me=True
        assert token.token_type == "bearer"
        assert token.expires_in == 30 * 60  # 30分钟
        
        # 登录失败
        wrong_login_data = UserLogin(
            username="testuser",
            password="wrongpassword"
        )
        
        with pytest.raises(AuthenticationError):
            auth_service.login(wrong_login_data)


class TestUserModel:
    """用户模型测试"""
    
    def test_user_permissions(self):
        """测试用户权限方法"""
        from ai_gen_hub.core.models.auth import User, RolePermissions
        
        # 创建管理员用户
        admin_permissions = RolePermissions.get_permissions_for_role(UserRole.ADMIN)
        admin = User(
            username="admin",
            email="<EMAIL>",
            password_hash="hashed",
            role=UserRole.ADMIN,
            permissions=admin_permissions
        )
        
        # 测试权限检查方法
        assert admin.has_permission(Permission.SYSTEM_ADMIN)
        assert admin.has_any_permission([Permission.SYSTEM_ADMIN, Permission.USER_CREATE])
        assert admin.has_all_permissions([Permission.API_TEXT_GENERATE, Permission.USER_READ])
        assert admin.is_admin()
        assert admin.is_active()
        assert admin.can_use_api()
        
        # 创建普通用户
        user_permissions = RolePermissions.get_permissions_for_role(UserRole.USER)
        user = User(
            username="user",
            email="<EMAIL>",
            password_hash="hashed",
            role=UserRole.USER,
            permissions=user_permissions
        )
        
        assert not user.has_permission(Permission.SYSTEM_ADMIN)
        assert user.has_permission(Permission.API_TEXT_GENERATE)
        assert not user.is_admin()
        assert user.is_active()
    
    def test_role_permissions_mapping(self):
        """测试角色权限映射"""
        from ai_gen_hub.core.models.auth import RolePermissions
        
        # 测试管理员权限
        admin_perms = RolePermissions.get_permissions_for_role(UserRole.ADMIN)
        assert Permission.SYSTEM_ADMIN in admin_perms
        assert Permission.USER_CREATE in admin_perms
        assert Permission.API_TEXT_GENERATE in admin_perms
        
        # 测试开发者权限
        dev_perms = RolePermissions.get_permissions_for_role(UserRole.DEVELOPER)
        assert Permission.SYSTEM_ADMIN not in dev_perms
        assert Permission.DEBUG_ACCESS in dev_perms
        assert Permission.API_TEXT_GENERATE in dev_perms
        
        # 测试普通用户权限
        user_perms = RolePermissions.get_permissions_for_role(UserRole.USER)
        assert Permission.SYSTEM_ADMIN not in user_perms
        assert Permission.DEBUG_ACCESS not in user_perms
        assert Permission.API_TEXT_GENERATE in user_perms
        
        # 测试访客权限
        guest_perms = RolePermissions.get_permissions_for_role(UserRole.GUEST)
        assert Permission.API_TEXT_GENERATE not in guest_perms
        assert Permission.USER_READ in guest_perms


class TestPasswordManager:
    """密码管理器测试"""
    
    def test_password_hashing(self):
        """测试密码哈希"""
        from ai_gen_hub.core.models.auth import PasswordManager
        
        pm = PasswordManager()
        password = "testpassword123"
        
        # 哈希密码
        hashed = pm.hash_password(password)
        assert hashed != password
        assert hashed.startswith("$2b$")
        
        # 验证密码
        assert pm.verify_password(password, hashed) is True
        assert pm.verify_password("wrongpassword", hashed) is False
        
        # 检查是否需要更新
        needs_update = pm.needs_update(hashed)
        assert isinstance(needs_update, bool)
