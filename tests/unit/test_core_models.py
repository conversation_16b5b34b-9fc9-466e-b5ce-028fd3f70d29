"""
核心模型单元测试

测试请求、响应、基础模型等核心数据结构
"""

import pytest
from datetime import datetime
from typing import List

from ai_gen_hub.core.models.base import (
    Message, MessageRole, Usage, FinishReason, ModelType
)
from ai_gen_hub.core.models.requests import (
    TextGenerationRequest, OptimizedTextGenerationRequest,
    GenerationConfig, StreamConfig, SafetyConfig
)
from ai_gen_hub.core.models.responses import (
    TextGenerationResponse, TextGenerationChoice,
    OptimizedTextGenerationResponse, Choice, UsageStats,
    PerformanceMetrics, ProviderInfo
)


class TestBaseModels:
    """基础模型测试"""
    
    def test_message_creation(self):
        """测试消息创建"""
        message = Message(
            role=MessageRole.USER,
            content="Hello, world!"
        )
        
        assert message.role == MessageRole.USER
        assert message.content == "Hello, world!"
        assert message.name is None
        assert message.function_call is None
        assert message.tool_calls is None
    
    def test_message_with_function_call(self):
        """测试带函数调用的消息"""
        message = Message(
            role=MessageRole.ASSISTANT,
            content="I'll help you with that.",
            function_call={
                "name": "get_weather",
                "arguments": '{"location": "Beijing"}'
            }
        )
        
        assert message.role == MessageRole.ASSISTANT
        assert message.function_call["name"] == "get_weather"
    
    def test_usage_calculation(self):
        """测试使用量计算"""
        usage = Usage(
            prompt_tokens=10,
            completion_tokens=5
        )
        
        # 应该自动计算总token数
        usage.__post_init__()
        assert usage.total_tokens == 15
        
        # 手动设置总token数
        usage2 = Usage(
            prompt_tokens=10,
            completion_tokens=5,
            total_tokens=20
        )
        assert usage2.total_tokens == 20
    
    def test_message_role_enum(self):
        """测试消息角色枚举"""
        assert MessageRole.SYSTEM.value == "system"
        assert MessageRole.USER.value == "user"
        assert MessageRole.ASSISTANT.value == "assistant"
        assert MessageRole.FUNCTION.value == "function"
        assert MessageRole.TOOL.value == "tool"
    
    def test_finish_reason_enum(self):
        """测试完成原因枚举"""
        assert FinishReason.STOP.value == "stop"
        assert FinishReason.LENGTH.value == "length"
        assert FinishReason.FUNCTION_CALL.value == "function_call"
        assert FinishReason.TOOL_CALLS.value == "tool_calls"
        assert FinishReason.CONTENT_FILTER.value == "content_filter"


class TestRequestModels:
    """请求模型测试"""
    
    def test_text_generation_request(self):
        """测试文本生成请求"""
        messages = [
            Message(role=MessageRole.USER, content="Hello")
        ]
        
        request = TextGenerationRequest(
            model="gpt-3.5-turbo",
            messages=messages,
            max_tokens=100,
            temperature=0.7
        )
        
        assert request.model == "gpt-3.5-turbo"
        assert len(request.messages) == 1
        assert request.max_tokens == 100
        assert request.temperature == 0.7
        assert request.stream is False  # 默认值
    
    def test_optimized_text_generation_request(self):
        """测试优化版文本生成请求"""
        messages = [
            Message(role=MessageRole.USER, content="Hello")
        ]
        
        generation_config = GenerationConfig(
            max_tokens=100,
            temperature=0.7,
            top_p=0.9
        )
        
        stream_config = StreamConfig(
            enabled=True,
            chunk_size=512
        )
        
        request = OptimizedTextGenerationRequest(
            messages=messages,
            model="gpt-4",
            generation=generation_config,
            stream=stream_config
        )
        
        assert request.model == "gpt-4"
        assert request.generation.max_tokens == 100
        assert request.stream.enabled is True
        assert request.stream.chunk_size == 512
    
    def test_generation_config(self):
        """测试生成配置"""
        config = GenerationConfig(
            max_tokens=200,
            temperature=0.8,
            top_p=0.95,
            frequency_penalty=0.1,
            presence_penalty=0.2,
            stop=[".", "!", "?"]
        )
        
        assert config.max_tokens == 200
        assert config.temperature == 0.8
        assert config.top_p == 0.95
        assert config.frequency_penalty == 0.1
        assert config.presence_penalty == 0.2
        assert config.stop == [".", "!", "?"]
    
    def test_stream_config(self):
        """测试流配置"""
        config = StreamConfig(
            enabled=True,
            chunk_size=512,
            include_usage=True
        )

        assert config.enabled is True
        assert config.chunk_size == 512
        assert config.include_usage is True
    
    def test_safety_config(self):
        """测试安全配置"""
        config = SafetyConfig(
            content_filter=True,
            safety_level="high",
            custom_filters=["violence", "hate_speech"]
        )
        
        assert config.content_filter is True
        assert config.safety_level == "high"
        assert "violence" in config.custom_filters
        assert "hate_speech" in config.custom_filters
    
    def test_request_validation(self):
        """测试请求验证"""
        messages = [
            Message(role=MessageRole.USER, content="Hello")
        ]
        
        # 有效请求
        request = TextGenerationRequest(
            model="gpt-3.5-turbo",
            messages=messages
        )
        
        # 验证供应商兼容性
        result = request.validate_for_provider("openai")
        assert "errors" in result
        assert "warnings" in result
        assert isinstance(result["errors"], list)
        assert isinstance(result["warnings"], list)
    
    def test_provider_capabilities(self):
        """测试供应商能力"""
        request = OptimizedTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="Hello")],
            model="gpt-4"
        )
        
        # 获取供应商能力
        capabilities = request._get_provider_capabilities("openai")
        
        assert "max_tokens_limit" in capabilities
        assert "supports_functions" in capabilities
        assert "supports_tools" in capabilities
        assert "message_roles" in capabilities
        
        # OpenAI应该支持函数调用
        assert capabilities["supports_functions"] is True
        assert "user" in capabilities["message_roles"]
        assert "assistant" in capabilities["message_roles"]


class TestResponseModels:
    """响应模型测试"""
    
    def test_text_generation_response(self):
        """测试文本生成响应"""
        choice = TextGenerationChoice(
            index=0,
            message=Message(
                role=MessageRole.ASSISTANT,
                content="Hello! How can I help you?"
            ),
            finish_reason=FinishReason.STOP
        )
        
        usage = Usage(
            prompt_tokens=5,
            completion_tokens=8,
            total_tokens=13
        )
        
        response = TextGenerationResponse(
            id="test-response-id",
            object="chat.completion",
            created=**********,
            model="gpt-3.5-turbo",
            choices=[choice],
            usage=usage,
            provider="openai",
            request_id="test-request-id",
            processing_time=1.5
        )
        
        assert response.id == "test-response-id"
        assert response.model == "gpt-3.5-turbo"
        assert len(response.choices) == 1
        assert response.choices[0].message.content == "Hello! How can I help you?"
        assert response.usage.total_tokens == 13
        assert response.provider == "openai"
        assert response.processing_time == 1.5
    
    def test_optimized_response(self):
        """测试优化版响应"""
        choice = Choice(
            index=0,
            message=Message(
                role=MessageRole.ASSISTANT,
                content="Optimized response"
            ),
            finish_reason=FinishReason.STOP
        )
        
        usage_stats = UsageStats(
            prompt_tokens=10,
            completion_tokens=15,
            total_tokens=25,
            estimated_cost=0.001
        )
        
        performance = PerformanceMetrics(
            processing_time=2.0,
            first_token_time=0.5,
            tokens_per_second=30.0
        )
        
        provider_info = ProviderInfo(
            name="openai",
            model="gpt-4",
            version="2024-01"
        )
        
        response = OptimizedTextGenerationResponse(
            id="opt-response-id",
            created=**********,
            request_id="opt-request-id",
            processing_time=2.0,
            choices=[choice],
            usage=usage_stats,
            performance=performance,
            provider=provider_info
        )
        
        assert response.id == "opt-response-id"
        assert len(response.choices) == 1
        assert response.usage.estimated_cost == 0.001
        assert response.performance.tokens_per_second == 30.0
        assert response.provider.name == "openai"
    
    def test_usage_stats(self):
        """测试使用量统计"""
        stats = UsageStats(
            prompt_tokens=20,
            completion_tokens=30,
            thinking_tokens=5,
            cached_tokens=10,
            estimated_cost=0.002,
            cost_breakdown={
                "input": 0.001,
                "output": 0.001
            }
        )
        
        # 应该自动计算总token数
        stats.__post_init__()
        assert stats.total_tokens == 50
        assert stats.thinking_tokens == 5
        assert stats.cached_tokens == 10
        assert stats.estimated_cost == 0.002
        assert stats.cost_breakdown["input"] == 0.001
    
    def test_performance_metrics(self):
        """测试性能指标"""
        metrics = PerformanceMetrics(
            processing_time=3.5,
            queue_time=0.2,
            inference_time=3.0,
            first_token_time=0.8,
            tokens_per_second=25.5,
            network_latency=0.1,
            retry_count=1
        )
        
        assert metrics.processing_time == 3.5
        assert metrics.queue_time == 0.2
        assert metrics.inference_time == 3.0
        assert metrics.first_token_time == 0.8
        assert metrics.tokens_per_second == 25.5
        assert metrics.network_latency == 0.1
        assert metrics.retry_count == 1
    
    def test_provider_info(self):
        """测试供应商信息"""
        provider = ProviderInfo(
            name="google_ai",
            model="gemini-pro",
            version="1.0",
            region="us-central1",
            endpoint="https://generativelanguage.googleapis.com"
        )
        
        assert provider.name == "google_ai"
        assert provider.model == "gemini-pro"
        assert provider.version == "1.0"
        assert provider.region == "us-central1"
        assert provider.endpoint == "https://generativelanguage.googleapis.com"


class TestModelValidation:
    """模型验证测试"""
    
    def test_message_content_validation(self):
        """测试消息内容验证"""
        # 空内容应该失败
        with pytest.raises(ValueError):
            Message(role=MessageRole.USER, content="")
    
    def test_generation_config_validation(self):
        """测试生成配置验证"""
        # 温度范围验证
        with pytest.raises(ValueError):
            GenerationConfig(temperature=2.5)  # 超出范围
        
        with pytest.raises(ValueError):
            GenerationConfig(temperature=-0.5)  # 负数
        
        # top_p范围验证
        with pytest.raises(ValueError):
            GenerationConfig(top_p=1.5)  # 超出范围
        
        # max_tokens验证
        with pytest.raises(ValueError):
            GenerationConfig(max_tokens=0)  # 应该大于0
    
    def test_request_model_validation(self):
        """测试请求模型验证"""
        # 空消息列表
        with pytest.raises(ValueError):
            TextGenerationRequest(
                model="gpt-3.5-turbo",
                messages=[]
            )
        
        # 无效模型名称
        with pytest.raises(ValueError):
            TextGenerationRequest(
                model="",
                messages=[Message(role=MessageRole.USER, content="Hello")]
            )
