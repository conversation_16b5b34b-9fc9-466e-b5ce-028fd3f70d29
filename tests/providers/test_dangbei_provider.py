"""
当贝 AI 供应商适配器测试

测试当贝 AI 供应商适配器的各项功能，包括：
- 基础配置和初始化
- 健康检查功能
- 模型列表获取
- 文本生成（非流式）
- 文本生成（流式）
- 错误处理
"""

import json
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.core.interfaces import (
    Message,
    MessageRole,
    ModelType,
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
)
from ai_gen_hub.providers.dangbei_provider import DangbeiProvider
from ai_gen_hub.utils.key_manager import KeyManager


class TestDangbeiProvider:
    """当贝 AI 供应商适配器测试类"""
    
    @pytest.fixture
    def provider_config(self):
        """创建测试用的供应商配置"""
        return ProviderConfig(
            name="dangbei",
            base_url="https://api.dangbei.com",
            timeout=30,
            max_retries=3,
            retry_delay=1.0,
            rate_limit=100
        )
    
    @pytest.fixture
    def key_manager(self):
        """创建测试用的密钥管理器"""
        mock_key_manager = AsyncMock(spec=KeyManager)
        mock_key = MagicMock()
        mock_key.key = "test-api-key"
        mock_key_manager.get_key.return_value = mock_key
        return mock_key_manager
    
    @pytest.fixture
    def provider(self, provider_config, key_manager):
        """创建测试用的当贝 AI 供应商实例"""
        return DangbeiProvider(provider_config, key_manager)
    
    def test_provider_initialization(self, provider):
        """测试供应商初始化"""
        assert provider.name == "dangbei"
        assert provider.base_url == "https://api.dangbei.com"
        assert ModelType.TEXT_GENERATION in provider._supported_model_types
        assert "deepseek" in provider._supported_models
        assert provider._model_mapping["deepseek-latest"] == "deepseek"
    
    @pytest.mark.asyncio
    async def test_health_check_success(self, provider):
        """测试健康检查成功"""
        # 模拟成功的API响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "data": {
                "models": [{"id": "deepseek", "name": "DeepSeek-R1"}]
            }
        }
        
        with patch.object(provider, '_make_request', return_value=mock_response):
            result = await provider._perform_health_check("test-api-key")
            assert result is True
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, provider):
        """测试健康检查失败"""
        # 模拟失败的API响应
        mock_response = MagicMock()
        mock_response.status_code = 401
        
        with patch.object(provider, '_make_request', return_value=mock_response):
            result = await provider._perform_health_check("invalid-api-key")
            assert result is False
    
    @pytest.mark.asyncio
    async def test_get_models_success(self, provider):
        """测试获取模型列表成功"""
        # 模拟成功的模型列表响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "data": {
                "defaultModel": "deepseek",
                "models": [
                    {
                        "id": "deepseek",
                        "name": "DeepSeek-R1最新版",
                        "description": "专注逻辑推理与深度分析",
                        "recommended": True
                    }
                ]
            }
        }
        
        with patch.object(provider, '_make_request', return_value=mock_response):
            models = await provider.get_models("test-api-key")
            
            assert len(models) == 1
            assert models[0]["id"] == "deepseek"
            assert models[0]["name"] == "DeepSeek-R1最新版"
            assert "deepseek" in provider._supported_models
    
    def test_build_chat_request(self, provider):
        """测试构建聊天请求"""
        request = TextGenerationRequest(
            model="deepseek",
            messages=[
                Message(role=MessageRole.USER, content="你好")
            ],
            max_tokens=1000,
            temperature=0.7,
            stream=False,
            provider_params={
                "deep_thinking": True,
                "online_search": False
            }
        )
        
        request_data = provider._build_chat_request(request)
        
        assert request_data["model"] == "deepseek"
        assert len(request_data["messages"]) == 1
        assert request_data["messages"][0]["role"] == "user"
        assert request_data["messages"][0]["content"] == "你好"
        assert request_data["max_tokens"] == 1000
        assert request_data["temperature"] == 0.7
        assert request_data["stream"] is False
        assert request_data["options"]["deep_thinking"] is True
        assert request_data["options"]["online_search"] is False
    
    def test_parse_text_response(self, provider):
        """测试解析文本响应"""
        request = TextGenerationRequest(
            model="deepseek",
            messages=[Message(role=MessageRole.USER, content="你好")]
        )
        
        response_data = {
            "success": True,
            "data": {
                "message": {
                    "role": "assistant",
                    "content": "你好！我是当贝AI助手。"
                },
                "message_id": "msg_123456",
                "model": "deepseek",
                "finish_reason": "stop",
                "usage": {
                    "prompt_tokens": 10,
                    "completion_tokens": 20,
                    "total_tokens": 30
                },
                "timestamp": **********000
            }
        }
        
        response = provider._parse_text_response(response_data, request)
        
        assert isinstance(response, TextGenerationResponse)
        assert response.id == "msg_123456"
        assert response.model == "deepseek"
        assert len(response.choices) == 1
        assert response.choices[0].message.content == "你好！我是当贝AI助手。"
        assert response.usage.prompt_tokens == 10
        assert response.usage.completion_tokens == 20
        assert response.usage.total_tokens == 30
    
    def test_parse_stream_chunk(self, provider):
        """测试解析流式数据块"""
        request = TextGenerationRequest(
            model="deepseek",
            messages=[Message(role=MessageRole.USER, content="你好")]
        )
        
        chunk_data = {
            "id": "msg_123",
            "object": "chat.completion.chunk",
            "created": **********,
            "model": "deepseek",
            "choices": [
                {
                    "index": 0,
                    "delta": {
                        "content": "你好"
                    },
                    "finish_reason": None
                }
            ]
        }
        
        chunk = provider._parse_stream_chunk(chunk_data, request)
        
        assert isinstance(chunk, TextGenerationStreamChunk)
        assert chunk.id == "msg_123"
        assert chunk.model == "deepseek"
        assert len(chunk.choices) == 1
        assert chunk.choices[0].delta.content == "你好"
        assert chunk.choices[0].finish_reason is None
    
    def test_map_finish_reason(self, provider):
        """测试完成原因映射"""
        from ai_gen_hub.core.interfaces import FinishReason
        
        assert provider._map_finish_reason("stop") == FinishReason.STOP
        assert provider._map_finish_reason("length") == FinishReason.LENGTH
        assert provider._map_finish_reason("content_filter") == FinishReason.CONTENT_FILTER
        assert provider._map_finish_reason("unknown") == FinishReason.STOP
        assert provider._map_finish_reason(None) is None
    
    def test_handle_dangbei_errors(self, provider):
        """测试当贝 AI 错误处理"""
        from ai_gen_hub.core.exceptions import (
            AuthenticationError,
            QuotaExceededError,
            ModelNotSupportedError,
            RateLimitError,
            APIError
        )
        
        # 测试认证错误
        with pytest.raises(AuthenticationError):
            provider._handle_dangbei_errors(
                {"error": "Invalid API key"}, 401
            )
        
        # 测试配额错误
        with pytest.raises(QuotaExceededError):
            provider._handle_dangbei_errors(
                {"error": "Quota exceeded"}, 429
            )
        
        # 测试模型不支持错误
        with pytest.raises(ModelNotSupportedError):
            provider._handle_dangbei_errors(
                {"error": "Model not found"}, 404
            )
        
        # 测试频率限制错误
        with pytest.raises(RateLimitError):
            provider._handle_dangbei_errors(
                {"error": "Rate limit exceeded"}, 429
            )
        
        # 测试通用API错误
        with pytest.raises(APIError):
            provider._handle_dangbei_errors(
                {"error": "Internal server error"}, 500
            )
    
    @pytest.mark.asyncio
    async def test_generate_image_not_supported(self, provider):
        """测试图像生成不支持"""
        from ai_gen_hub.core.interfaces import ImageGenerationRequest
        from ai_gen_hub.core.exceptions import ModelNotSupportedError
        
        request = ImageGenerationRequest(
            prompt="生成一张图片",
            model="deepseek"
        )
        
        with pytest.raises(ModelNotSupportedError):
            await provider._generate_image_impl(request, "test-api-key")
