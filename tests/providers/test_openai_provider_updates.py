"""
OpenAI Provider 更新功能测试

测试 OpenAI Provider 的最新更新，包括：
- 新模型支持
- 模型映射
- 流式响应改进
- 函数调用参数优化
- 错误处理增强
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import json

from ai_gen_hub.providers.openai_provider import OpenAIProvider
from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.core.interfaces import (
    TextGenerationRequest, 
    Message, 
    MessageRole,
    Usage
)
from ai_gen_hub.core.exceptions import (
    AuthenticationError,
    QuotaExceededError,
    ModelNotSupportedError,
    RateLimitError,
    APIError
)


class TestOpenAIProviderUpdates:
    """测试 OpenAI Provider 的更新功能"""
    
    @pytest.fixture
    def provider_config(self):
        """创建测试用的 Provider 配置"""
        return ProviderConfig(
            api_keys=["test-api-key"],
            base_url="https://api.openai.com/v1",
            timeout=60,
            max_retries=3,
            enabled=True
        )
    
    @pytest.fixture
    def key_manager(self):
        """创建模拟的密钥管理器"""
        mock_key_manager = AsyncMock()
        mock_key = MagicMock()
        mock_key.key = "test-api-key"
        mock_key_manager.get_key.return_value = mock_key
        return mock_key_manager
    
    @pytest.fixture
    def openai_provider(self, provider_config, key_manager):
        """创建 OpenAI Provider 实例"""
        return OpenAIProvider(provider_config, key_manager)
    
    def test_new_models_support(self, openai_provider):
        """测试新模型的支持"""
        # 测试 GPT-4o 系列模型
        gpt4o_models = [
            "gpt-4o",
            "gpt-4o-2024-08-06",
            "gpt-4o-2024-05-13",
            "gpt-4o-mini",
            "gpt-4o-mini-2024-07-18"
        ]
        
        for model in gpt4o_models:
            assert openai_provider.supports_model(model), f"模型 {model} 应该被支持"
        
        # 测试 GPT-4 Turbo 系列模型
        gpt4_turbo_models = [
            "gpt-4-turbo",
            "gpt-4-turbo-2024-04-09"
        ]
        
        for model in gpt4_turbo_models:
            assert openai_provider.supports_model(model), f"模型 {model} 应该被支持"
    
    def test_model_mapping_updates(self, openai_provider):
        """测试模型映射的更新"""
        # 测试最新模型映射
        assert openai_provider.map_model_name("gpt-4-latest") == "gpt-4o"
        assert openai_provider.map_model_name("gpt-4o-latest") == "gpt-4o"
        assert openai_provider.map_model_name("gpt-4-turbo-latest") == "gpt-4-turbo"
        assert openai_provider.map_model_name("gpt-4-mini") == "gpt-4o-mini"
        assert openai_provider.map_model_name("dalle-latest") == "dall-e-3"
    
    def test_stream_options_in_request(self, openai_provider):
        """测试流式请求中的选项设置"""
        request = TextGenerationRequest(
            model="gpt-4o",
            messages=[Message(role=MessageRole.USER, content="测试消息")],
            stream=True
        )
        
        request_data = openai_provider._build_text_request(request)
        
        # 验证流式选项被正确添加
        assert "stream_options" in request_data
        assert request_data["stream_options"]["include_usage"] is True
        assert request_data["stream"] is True
    
    def test_functions_to_tools_conversion(self, openai_provider):
        """测试 functions 参数到 tools 参数的转换"""
        # 创建使用旧 functions 格式的请求
        functions = [
            {
                "name": "get_weather",
                "description": "获取天气信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {"type": "string", "description": "城市名称"}
                    },
                    "required": ["location"]
                }
            }
        ]
        
        request = TextGenerationRequest(
            model="gpt-4o",
            messages=[Message(role=MessageRole.USER, content="今天天气怎么样？")],
            functions=functions,
            function_call="auto"
        )
        
        request_data = openai_provider._build_text_request(request)
        
        # 验证 functions 被转换为 tools 格式
        assert "tools" in request_data
        assert len(request_data["tools"]) == 1
        assert request_data["tools"][0]["type"] == "function"
        assert request_data["tools"][0]["function"]["name"] == "get_weather"
        
        # 验证 function_call 被转换为 tool_choice
        assert request_data["tool_choice"] == "auto"
    
    def test_function_call_specific_conversion(self, openai_provider):
        """测试特定函数调用的转换"""
        functions = [
            {
                "name": "calculate",
                "description": "执行计算",
                "parameters": {"type": "object", "properties": {}}
            }
        ]
        
        request = TextGenerationRequest(
            model="gpt-4o",
            messages=[Message(role=MessageRole.USER, content="计算 2+2")],
            functions=functions,
            function_call={"name": "calculate"}
        )
        
        request_data = openai_provider._build_text_request(request)
        
        # 验证特定函数调用的转换
        assert request_data["tool_choice"]["type"] == "function"
        assert request_data["tool_choice"]["function"]["name"] == "calculate"
    
    def test_openai_specific_error_handling(self, openai_provider):
        """测试 OpenAI 特定的错误处理"""
        # 测试 API 密钥无效错误
        error_data = {
            "error": {
                "type": "invalid_api_key",
                "message": "Invalid API key provided"
            }
        }
        
        with pytest.raises(AuthenticationError) as exc_info:
            openai_provider._handle_openai_specific_errors(error_data, 401)
        assert "OpenAI API 密钥无效" in str(exc_info.value)
        
        # 测试配额不足错误
        error_data = {
            "error": {
                "type": "insufficient_quota",
                "message": "You have exceeded your quota"
            }
        }
        
        with pytest.raises(QuotaExceededError) as exc_info:
            openai_provider._handle_openai_specific_errors(error_data, 429)
        assert "OpenAI 配额不足" in str(exc_info.value)
        
        # 测试模型不存在错误
        error_data = {
            "error": {
                "type": "model_not_found",
                "message": "Model not found"
            }
        }
        
        with pytest.raises(ModelNotSupportedError) as exc_info:
            openai_provider._handle_openai_specific_errors(error_data, 404)
        assert "OpenAI 模型不支持" in str(exc_info.value)
        
        # 测试请求频率超限错误
        error_data = {
            "error": {
                "type": "rate_limit_exceeded",
                "message": "Rate limit exceeded"
            }
        }
        
        with pytest.raises(RateLimitError) as exc_info:
            openai_provider._handle_openai_specific_errors(error_data, 429)
        assert "OpenAI 请求频率超限" in str(exc_info.value)
        
        # 测试上下文长度超限错误
        error_data = {
            "error": {
                "code": "context_length_exceeded",
                "message": "Context length exceeded"
            }
        }
        
        with pytest.raises(APIError) as exc_info:
            openai_provider._handle_openai_specific_errors(error_data, 400)
        assert "OpenAI 上下文长度超限" in str(exc_info.value)
        assert exc_info.value.retryable is False
        
        # 测试内容过滤错误
        error_data = {
            "error": {
                "code": "content_filter",
                "message": "Content filtered"
            }
        }
        
        with pytest.raises(APIError) as exc_info:
            openai_provider._handle_openai_specific_errors(error_data, 400)
        assert "OpenAI 内容被过滤" in str(exc_info.value)
        assert exc_info.value.retryable is False
    
    @pytest.mark.asyncio
    async def test_stream_response_with_usage(self, openai_provider):
        """测试流式响应中的使用量信息处理"""
        # 模拟流式响应数据
        stream_data = [
            'data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gpt-4o","choices":[{"index":0,"delta":{"role":"assistant","content":"Hello"},"finish_reason":null}]}\n\n',
            'data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gpt-4o","choices":[{"index":0,"delta":{"content":" world"},"finish_reason":null}]}\n\n',
            'data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gpt-4o","choices":[{"index":0,"delta":{},"finish_reason":"stop"}],"usage":{"prompt_tokens":10,"completion_tokens":5,"total_tokens":15}}\n\n',
            'data: [DONE]\n\n'
        ]
        
        request = TextGenerationRequest(
            model="gpt-4o",
            messages=[Message(role=MessageRole.USER, content="Hello")],
            stream=True
        )
        
        # 模拟 _make_request 方法返回流式数据
        async def mock_stream_generator():
            for data in stream_data:
                yield data.encode('utf-8')
        
        with patch.object(openai_provider, '_make_request', return_value=mock_stream_generator()):
            chunks = []
            async for chunk in openai_provider._handle_stream_response(
                request, 
                {"Authorization": "Bearer test-key"}, 
                "https://api.openai.com/v1/chat/completions",
                {"model": "gpt-4o", "messages": [], "stream": True}
            ):
                chunks.append(chunk)
        
        # 验证流式响应处理
        assert len(chunks) == 3  # 应该有3个有效的数据块
        
        # 验证最后一个块包含使用量信息
        last_chunk = chunks[-1]
        assert hasattr(last_chunk, 'usage')
        assert last_chunk.usage.prompt_tokens == 10
        assert last_chunk.usage.completion_tokens == 5
        assert last_chunk.usage.total_tokens == 15


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
