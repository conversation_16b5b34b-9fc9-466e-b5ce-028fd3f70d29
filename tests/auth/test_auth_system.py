"""
认证系统测试

测试完整的身份认证和授权系统功能，包括：
- IdP集成测试
- API Token管理测试
- RBAC权限控制测试
- 安全特性测试
"""

import asyncio
import pytest
from datetime import datetime, timedelta
from typing import Dict, Any
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

from ai_gen_hub.auth import (
    AuthUser,
    APIToken,
    TokenScope,
    Permission,
    UserRole,
    UserStatus,
    IdPConfig,
    IdPType,
    AuthConfig,
)
from ai_gen_hub.auth.tokens import APITokenManager, JWTTokenManager
from ai_gen_hub.auth.rbac import RBACManager
from ai_gen_hub.auth.security import SecurityManager, RateLimiter, AuditLogger
from ai_gen_hub.auth.idp import create_idp_provider
from ai_gen_hub.auth.exceptions import (
    AuthenticationError,
    AuthorizationError,
    TokenError,
    RateLimitError,
)


class TestAPITokenManager:
    """API Token管理器测试"""
    
    @pytest.fixture
    def token_manager(self):
        """创建Token管理器实例"""
        return APITokenManager("test-secret-key")
    
    @pytest.fixture
    def test_user_id(self):
        """测试用户ID"""
        return uuid4()
    
    def test_generate_api_token(self, token_manager, test_user_id):
        """测试生成API令牌"""
        # 生成令牌
        api_token, token_value = token_manager.generate_api_token(
            user_id=test_user_id,
            name="测试令牌",
            scopes={TokenScope.API_ACCESS},
            permissions={Permission.API_TEXT_GENERATE},
            expires_in_days=30,
            rate_limit=100
        )
        
        # 验证令牌属性
        assert api_token.user_id == test_user_id
        assert api_token.name == "测试令牌"
        assert TokenScope.API_ACCESS in api_token.scopes
        assert Permission.API_TEXT_GENERATE in api_token.permissions
        assert api_token.rate_limit == 100
        assert api_token.is_active
        assert api_token.expires_at is not None
        
        # 验证令牌格式
        assert token_value.startswith("ak_")
        assert len(token_value) == 35  # "ak_" + 32字符
        
        # 验证令牌前缀
        assert api_token.token_prefix == token_value[:8] + "..."
    
    def test_verify_api_token(self, token_manager, test_user_id):
        """测试验证API令牌"""
        # 生成令牌
        api_token, token_value = token_manager.generate_api_token(
            user_id=test_user_id,
            name="测试令牌",
            scopes={TokenScope.API_ACCESS}
        )
        
        # 验证令牌
        verified_token = token_manager.verify_api_token(token_value)
        assert verified_token is not None
        assert verified_token.id == api_token.id
        assert verified_token.user_id == test_user_id
        
        # 验证无效令牌
        invalid_token = token_manager.verify_api_token("invalid_token")
        assert invalid_token is None
    
    def test_revoke_api_token(self, token_manager, test_user_id):
        """测试撤销API令牌"""
        # 生成令牌
        api_token, token_value = token_manager.generate_api_token(
            user_id=test_user_id,
            name="测试令牌"
        )
        
        # 撤销令牌
        success = token_manager.revoke_api_token(api_token.id)
        assert success
        
        # 验证撤销后的令牌
        verified_token = token_manager.verify_api_token(token_value)
        assert verified_token is None
    
    def test_get_user_tokens(self, token_manager, test_user_id):
        """测试获取用户令牌"""
        # 生成多个令牌
        token1, _ = token_manager.generate_api_token(
            user_id=test_user_id,
            name="令牌1"
        )
        token2, _ = token_manager.generate_api_token(
            user_id=test_user_id,
            name="令牌2"
        )
        
        # 获取用户令牌
        user_tokens = token_manager.get_user_tokens(test_user_id)
        assert len(user_tokens) == 2
        
        token_ids = {token.id for token in user_tokens}
        assert token1.id in token_ids
        assert token2.id in token_ids
    
    def test_cleanup_expired_tokens(self, token_manager, test_user_id):
        """测试清理过期令牌"""
        # 生成过期令牌
        api_token, token_value = token_manager.generate_api_token(
            user_id=test_user_id,
            name="过期令牌",
            expires_in_days=1
        )
        
        # 手动设置为过期
        api_token.expires_at = datetime.utcnow() - timedelta(days=1)
        
        # 清理过期令牌
        cleaned_count = token_manager.cleanup_expired_tokens()
        assert cleaned_count == 1
        
        # 验证过期令牌已被清理
        verified_token = token_manager.verify_api_token(token_value)
        assert verified_token is None


class TestJWTTokenManager:
    """JWT Token管理器测试"""
    
    @pytest.fixture
    def jwt_manager(self):
        """创建JWT管理器实例"""
        return JWTTokenManager(
            secret_key="test-secret-key",
            access_token_expire_minutes=30,
            refresh_token_expire_days=7
        )
    
    @pytest.fixture
    def test_user(self):
        """测试用户"""
        return AuthUser(
            username="testuser",
            email="<EMAIL>",
            role=UserRole.USER,
            permissions={Permission.API_TEXT_GENERATE}
        )
    
    def test_create_access_token(self, jwt_manager, test_user):
        """测试创建访问令牌"""
        token = jwt_manager.create_access_token(test_user)
        assert isinstance(token, str)
        assert len(token) > 0
        
        # 验证令牌
        payload = jwt_manager.verify_token(token)
        assert payload["sub"] == str(test_user.id)
        assert payload["username"] == test_user.username
        assert payload["type"] == "access"
    
    def test_create_refresh_token(self, jwt_manager, test_user):
        """测试创建刷新令牌"""
        token = jwt_manager.create_refresh_token(test_user)
        assert isinstance(token, str)
        assert len(token) > 0
        
        # 验证令牌
        payload = jwt_manager.verify_token(token)
        assert payload["sub"] == str(test_user.id)
        assert payload["type"] == "refresh"
    
    def test_refresh_access_token(self, jwt_manager, test_user):
        """测试刷新访问令牌"""
        # 创建刷新令牌
        refresh_token = jwt_manager.create_refresh_token(test_user)
        
        # 使用刷新令牌获取新的访问令牌
        new_access_token = jwt_manager.refresh_access_token(refresh_token, test_user)
        assert isinstance(new_access_token, str)
        
        # 验证新的访问令牌
        payload = jwt_manager.verify_token(new_access_token)
        assert payload["sub"] == str(test_user.id)
        assert payload["type"] == "access"
    
    def test_revoke_refresh_token(self, jwt_manager, test_user):
        """测试撤销刷新令牌"""
        # 创建刷新令牌
        refresh_token = jwt_manager.create_refresh_token(test_user)
        
        # 撤销令牌
        success = jwt_manager.revoke_refresh_token(refresh_token)
        assert success
        
        # 尝试使用撤销的令牌
        with pytest.raises(TokenError):
            jwt_manager.refresh_access_token(refresh_token, test_user)


class TestRBACManager:
    """RBAC管理器测试"""
    
    @pytest.fixture
    def rbac_manager(self):
        """创建RBAC管理器实例"""
        return RBACManager()
    
    @pytest.fixture
    def test_user_id(self):
        """测试用户ID"""
        return uuid4()
    
    def test_system_roles_initialization(self, rbac_manager):
        """测试系统角色初始化"""
        # 检查系统角色是否正确初始化
        super_admin_role = rbac_manager.get_role_by_name("super_admin")
        assert super_admin_role is not None
        assert super_admin_role.is_system
        assert Permission.SYSTEM_ADMIN in super_admin_role.permissions
        
        user_role = rbac_manager.get_role_by_name("user")
        assert user_role is not None
        assert user_role.is_system
        assert Permission.API_TEXT_GENERATE in user_role.permissions
    
    def test_create_custom_role(self, rbac_manager):
        """测试创建自定义角色"""
        role = rbac_manager.create_role(
            name="custom_role",
            display_name="自定义角色",
            description="测试自定义角色",
            permissions={Permission.API_TEXT_GENERATE, Permission.USER_READ}
        )
        
        assert role.name == "custom_role"
        assert role.display_name == "自定义角色"
        assert not role.is_system
        assert Permission.API_TEXT_GENERATE in role.permissions
        assert Permission.USER_READ in role.permissions
    
    def test_assign_role_to_user(self, rbac_manager, test_user_id):
        """测试为用户分配角色"""
        # 获取用户角色
        user_role = rbac_manager.get_role_by_name("user")
        
        # 分配角色
        success = rbac_manager.assign_role_to_user(test_user_id, user_role.id)
        assert success
        
        # 验证角色分配
        user_roles = rbac_manager.get_user_roles(test_user_id)
        assert len(user_roles) == 1
        assert user_roles[0].id == user_role.id
    
    def test_check_permission(self, rbac_manager, test_user_id):
        """测试权限检查"""
        # 分配用户角色
        user_role = rbac_manager.get_role_by_name("user")
        rbac_manager.assign_role_to_user(test_user_id, user_role.id)
        
        # 检查权限
        has_permission = rbac_manager.check_permission(
            test_user_id, Permission.API_TEXT_GENERATE
        )
        assert has_permission
        
        # 检查没有的权限
        has_admin_permission = rbac_manager.check_permission(
            test_user_id, Permission.SYSTEM_ADMIN
        )
        assert not has_admin_permission
    
    def test_get_user_permissions(self, rbac_manager, test_user_id):
        """测试获取用户权限"""
        # 分配开发者角色
        developer_role = rbac_manager.get_role_by_name("developer")
        rbac_manager.assign_role_to_user(test_user_id, developer_role.id)
        
        # 获取用户权限
        user_permissions = rbac_manager.get_user_permissions(test_user_id)
        
        # 验证权限包含开发者角色的权限
        assert Permission.API_TEXT_GENERATE in user_permissions
        assert Permission.API_IMAGE_GENERATE in user_permissions
        assert Permission.CONSOLE_ACCESS in user_permissions


class TestSecurityManager:
    """安全管理器测试"""
    
    @pytest.fixture
    def security_manager(self):
        """创建安全管理器实例"""
        rate_limiter = RateLimiter(default_requests_per_minute=60)
        audit_logger = AuditLogger()
        return SecurityManager(rate_limiter, audit_logger)
    
    @pytest.fixture
    def mock_request(self):
        """模拟HTTP请求"""
        request = MagicMock()
        request.client.host = "127.0.0.1"
        request.headers = {"User-Agent": "test-agent"}
        return request
    
    def test_rate_limiter(self, security_manager):
        """测试频率限制"""
        # 测试正常请求
        allowed, _ = security_manager.rate_limiter.check_rate_limit("test_user", "user")
        assert allowed
        
        # 测试超出限制
        for _ in range(100):  # 超出默认限制
            security_manager.rate_limiter.check_rate_limit("test_user", "user")
        
        allowed, _ = security_manager.rate_limiter.check_rate_limit("test_user", "user")
        assert not allowed
    
    def test_audit_logger(self, security_manager):
        """测试审计日志"""
        user_id = uuid4()
        
        # 记录认证事件
        security_manager.audit_logger.log_authentication_event(
            user_id=user_id,
            event_type="login",
            success=True,
            ip_address="127.0.0.1"
        )
        
        # 获取审计日志
        logs = security_manager.audit_logger.get_audit_logs(
            category="authentication",
            user_id=user_id
        )
        
        assert len(logs) == 1
        assert logs[0]["event_type"] == "login"
        assert logs[0]["success"] is True
    
    def test_failed_authentication_tracking(self, security_manager):
        """测试认证失败跟踪"""
        identifier = "<EMAIL>"
        ip_address = "127.0.0.1"
        
        # 记录多次失败尝试
        for _ in range(5):
            security_manager.record_failed_authentication(identifier, ip_address)
        
        # 检查账户是否被锁定
        is_locked = security_manager.is_account_locked(identifier)
        assert is_locked
        
        # 清除失败尝试
        security_manager.clear_failed_attempts(identifier)
        is_locked = security_manager.is_account_locked(identifier)
        assert not is_locked


class TestIdPIntegration:
    """IdP集成测试"""
    
    @pytest.fixture
    def google_oauth_config(self):
        """Google OAuth配置"""
        return IdPConfig(
            name="google_oauth",
            type=IdPType.GOOGLE_OAUTH,
            client_id="test_client_id",
            client_secret="test_client_secret",
            authorization_url="https://accounts.google.com/o/oauth2/v2/auth",
            token_url="https://oauth2.googleapis.com/token",
            userinfo_url="https://openidconnect.googleapis.com/v1/userinfo",
            jwks_url="https://www.googleapis.com/oauth2/v3/certs",
            issuer="https://accounts.google.com",
            redirect_uri="http://localhost:8000/auth/callback/google",
            scopes=["openid", "profile", "email"]
        )
    
    @pytest.mark.asyncio
    async def test_create_idp_provider(self, google_oauth_config):
        """测试创建IdP提供商"""
        provider = create_idp_provider(google_oauth_config)
        assert provider is not None
        assert provider.config.name == "google_oauth"
        assert provider.config.type == IdPType.GOOGLE_OAUTH
    
    @pytest.mark.asyncio
    async def test_generate_authorization_url(self, google_oauth_config):
        """测试生成授权URL"""
        provider = create_idp_provider(google_oauth_config)
        await provider.initialize()
        
        auth_url, state, nonce = provider.generate_authorization_url()
        
        assert auth_url.startswith("https://accounts.google.com/o/oauth2/v2/auth")
        assert "client_id=test_client_id" in auth_url
        assert "redirect_uri=" in auth_url
        assert "scope=openid+profile+email" in auth_url
        assert len(state) > 0
        assert len(nonce) > 0
        
        await provider.cleanup()


@pytest.mark.asyncio
async def test_integration_auth_flow():
    """集成测试：完整认证流程"""
    # 创建管理器实例
    jwt_manager = JWTTokenManager("test-secret")
    api_token_manager = APITokenManager("test-secret")
    rbac_manager = RBACManager()
    security_manager = SecurityManager()
    
    # 创建测试用户
    user = AuthUser(
        username="testuser",
        email="<EMAIL>",
        role=UserRole.DEVELOPER,
        permissions={Permission.API_TEXT_GENERATE, Permission.API_TOKEN_MANAGE}
    )
    
    # 分配角色
    developer_role = rbac_manager.get_role_by_name("developer")
    rbac_manager.assign_role_to_user(user.id, developer_role.id)
    
    # 创建JWT令牌
    access_token = jwt_manager.create_access_token(user)
    refresh_token = jwt_manager.create_refresh_token(user)
    
    # 验证JWT令牌
    payload = jwt_manager.verify_token(access_token)
    assert payload["sub"] == str(user.id)
    
    # 创建API令牌
    api_token, token_value = api_token_manager.generate_api_token(
        user_id=user.id,
        name="测试API令牌",
        scopes={TokenScope.API_ACCESS}
    )
    
    # 验证API令牌
    verified_token = api_token_manager.verify_api_token(token_value)
    assert verified_token is not None
    assert verified_token.user_id == user.id
    
    # 检查权限
    has_permission = rbac_manager.check_permission(
        user.id, Permission.API_TEXT_GENERATE
    )
    assert has_permission
    
    # 测试频率限制
    allowed, _ = security_manager.rate_limiter.check_rate_limit(
        str(user.id), "user", user.rate_limit
    )
    assert allowed
    
    print("✅ 集成测试通过：完整认证流程正常工作")


if __name__ == "__main__":
    # 运行集成测试
    asyncio.run(test_integration_auth_flow())
