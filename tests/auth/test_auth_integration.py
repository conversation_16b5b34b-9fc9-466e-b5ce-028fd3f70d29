"""
认证系统集成测试

测试认证系统各组件之间的集成和协作，包括：
- 完整的认证流程测试
- 中间件集成测试
- 安全特性集成测试
- API路由集成测试
"""

import asyncio
import pytest
from datetime import datetime, timedelta
from typing import Dict, Any
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

from fastapi import FastAPI, Depends
from fastapi.testclient import TestClient
from httpx import AsyncClient

from ai_gen_hub.auth import (
    AuthUser,
    APIToken,
    TokenScope,
    Permission,
    UserRole,
    UserStatus,
    create_idp_provider,
)
from ai_gen_hub.auth.tokens import APITokenManager, JWTTokenManager
from ai_gen_hub.auth.rbac import RBACManager
from ai_gen_hub.auth.security import (
    SecurityManager,
    RateLimiter,
    AuditLogger,
    JWTSecurityValidator,
    ThreatDetector,
    SecurityHeadersMiddleware,
)
from ai_gen_hub.auth.middleware import (
    AuthenticationMiddleware,
    AuthorizationMiddleware,
    require_auth,
    require_permissions,
)
from ai_gen_hub.api.auth import create_auth_router


class MockUserService:
    """模拟用户服务"""
    
    def __init__(self):
        self.users: Dict[str, AuthUser] = {}
        self.users_by_email: Dict[str, AuthUser] = {}
    
    async def get_user_by_id(self, user_id: str) -> AuthUser:
        """根据ID获取用户"""
        return self.users.get(str(user_id))
    
    async def get_user_by_email(self, email: str) -> AuthUser:
        """根据邮箱获取用户"""
        return self.users_by_email.get(email)
    
    async def create_user(self, user: AuthUser) -> AuthUser:
        """创建用户"""
        self.users[str(user.id)] = user
        self.users_by_email[user.email] = user
        return user
    
    async def update_user(self, user: AuthUser) -> AuthUser:
        """更新用户"""
        self.users[str(user.id)] = user
        self.users_by_email[user.email] = user
        return user
    
    async def find_or_create_user(self, auth_user: AuthUser) -> AuthUser:
        """查找或创建用户"""
        existing_user = await self.get_user_by_email(auth_user.email)
        if existing_user:
            return existing_user
        return await self.create_user(auth_user)


@pytest.fixture
def mock_user_service():
    """用户服务fixture"""
    return MockUserService()


@pytest.fixture
def jwt_manager():
    """JWT管理器fixture"""
    return JWTTokenManager(
        secret_key="test-secret-key",
        access_token_expire_minutes=30,
        refresh_token_expire_days=7
    )


@pytest.fixture
def api_token_manager():
    """API Token管理器fixture"""
    return APITokenManager("test-secret-key")


@pytest.fixture
def rbac_manager():
    """RBAC管理器fixture"""
    return RBACManager()


@pytest.fixture
def security_manager():
    """安全管理器fixture"""
    rate_limiter = RateLimiter(default_requests_per_minute=1000)
    audit_logger = AuditLogger()
    return SecurityManager(rate_limiter, audit_logger)


@pytest.fixture
def jwt_security_validator():
    """JWT安全验证器fixture"""
    return JWTSecurityValidator("test-secret-key")


@pytest.fixture
def threat_detector():
    """威胁检测器fixture"""
    return ThreatDetector()


@pytest.fixture
def test_user():
    """测试用户fixture"""
    return AuthUser(
        username="testuser",
        email="<EMAIL>",
        role=UserRole.DEVELOPER,
        permissions={Permission.API_TEXT_GENERATE, Permission.API_TOKEN_MANAGE}
    )


@pytest.fixture
def test_app(
    jwt_manager,
    api_token_manager,
    rbac_manager,
    security_manager,
    jwt_security_validator,
    threat_detector,
    mock_user_service
):
    """测试应用fixture"""
    app = FastAPI()
    
    # 添加安全头部中间件
    app.add_middleware(SecurityHeadersMiddleware)
    
    # 添加认证中间件
    app.add_middleware(
        AuthenticationMiddleware,
        jwt_manager=jwt_manager,
        api_token_manager=api_token_manager,
        user_service=mock_user_service,
        security_manager=security_manager,
        jwt_security_validator=jwt_security_validator,
        threat_detector=threat_detector,
        excluded_paths=["/health", "/auth/login"]
    )
    
    # 添加授权中间件
    app.add_middleware(
        AuthorizationMiddleware,
        rbac_manager=rbac_manager,
        security_manager=security_manager
    )
    
    # 添加认证路由
    auth_router = create_auth_router(
        jwt_manager=jwt_manager,
        api_token_manager=api_token_manager,
        rbac_manager=rbac_manager,
        security_manager=security_manager,
        user_service=mock_user_service
    )
    app.include_router(auth_router)
    
    # 添加测试路由
    @app.get("/health")
    async def health():
        return {"status": "ok"}
    
    @app.get("/protected")
    async def protected_endpoint(user: AuthUser = Depends(require_auth())):
        return {"message": "访问成功", "user": user.username}
    
    @app.get("/admin-only")
    async def admin_only_endpoint(
        user: AuthUser = Depends(require_permissions(Permission.SYSTEM_ADMIN))
    ):
        return {"message": "管理员访问成功", "user": user.username}
    
    return app


class TestAuthenticationIntegration:
    """认证集成测试"""
    
    @pytest.mark.asyncio
    async def test_jwt_authentication_flow(
        self,
        test_app,
        test_user,
        jwt_manager,
        mock_user_service
    ):
        """测试JWT认证流程"""
        # 创建用户
        await mock_user_service.create_user(test_user)
        
        # 生成JWT令牌
        access_token = jwt_manager.create_access_token(test_user)
        
        # 测试受保护的端点
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            response = await client.get(
                "/protected",
                headers={"Authorization": f"Bearer {access_token}"}
            )
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "访问成功"
        assert data["user"] == test_user.username
    
    @pytest.mark.asyncio
    async def test_api_token_authentication_flow(
        self,
        test_app,
        test_user,
        api_token_manager,
        mock_user_service
    ):
        """测试API Token认证流程"""
        # 创建用户
        await mock_user_service.create_user(test_user)
        
        # 生成API令牌
        api_token, token_value = api_token_manager.generate_api_token(
            user_id=test_user.id,
            name="测试令牌",
            scopes={TokenScope.API_ACCESS}
        )
        
        # 测试受保护的端点
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            response = await client.get(
                "/protected",
                headers={"Authorization": f"Bearer {token_value}"}
            )
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "访问成功"
        assert data["user"] == test_user.username
    
    @pytest.mark.asyncio
    async def test_permission_based_access_control(
        self,
        test_app,
        test_user,
        jwt_manager,
        mock_user_service,
        rbac_manager
    ):
        """测试基于权限的访问控制"""
        # 创建用户
        await mock_user_service.create_user(test_user)
        
        # 分配开发者角色
        developer_role = rbac_manager.get_role_by_name("developer")
        rbac_manager.assign_role_to_user(test_user.id, developer_role.id)
        
        # 生成JWT令牌
        access_token = jwt_manager.create_access_token(test_user)
        
        # 测试普通受保护端点（应该成功）
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            response = await client.get(
                "/protected",
                headers={"Authorization": f"Bearer {access_token}"}
            )
        
        assert response.status_code == 200
        
        # 测试管理员端点（应该失败）
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            response = await client.get(
                "/admin-only",
                headers={"Authorization": f"Bearer {access_token}"}
            )
        
        assert response.status_code == 403
    
    @pytest.mark.asyncio
    async def test_security_headers_middleware(self, test_app):
        """测试安全头部中间件"""
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            response = await client.get("/health")
        
        # 检查安全头部
        assert "Content-Security-Policy" in response.headers
        assert "Strict-Transport-Security" in response.headers
        assert "X-Frame-Options" in response.headers
        assert "X-Content-Type-Options" in response.headers
        assert "X-XSS-Protection" in response.headers
        assert response.headers["X-Frame-Options"] == "DENY"
        assert response.headers["X-Content-Type-Options"] == "nosniff"
    
    @pytest.mark.asyncio
    async def test_threat_detection_integration(self, test_app):
        """测试威胁检测集成"""
        # 测试恶意请求检测
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            response = await client.post(
                "/auth/login",
                json={"username": "test'; DROP TABLE users; --"}
            )
        
        # 应该被威胁检测器阻止
        assert response.status_code == 400
        data = response.json()
        assert data["error"] == "malicious_request"
    
    @pytest.mark.asyncio
    async def test_rate_limiting_integration(
        self,
        test_app,
        test_user,
        jwt_manager,
        mock_user_service
    ):
        """测试频率限制集成"""
        # 创建用户
        await mock_user_service.create_user(test_user)
        
        # 生成JWT令牌
        access_token = jwt_manager.create_access_token(test_user)
        
        # 快速发送多个请求
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            responses = []
            for _ in range(5):
                response = await client.get(
                    "/protected",
                    headers={"Authorization": f"Bearer {access_token}"}
                )
                responses.append(response)
        
        # 前几个请求应该成功
        assert all(r.status_code == 200 for r in responses[:3])
    
    @pytest.mark.asyncio
    async def test_jwt_security_validation(
        self,
        test_app,
        test_user,
        jwt_manager,
        jwt_security_validator,
        mock_user_service
    ):
        """测试JWT安全验证"""
        # 创建用户
        await mock_user_service.create_user(test_user)
        
        # 生成JWT令牌
        access_token = jwt_manager.create_access_token(test_user)
        
        # 将令牌加入黑名单
        jwt_security_validator.blacklist_token(access_token, "test")
        
        # 尝试使用被黑名单的令牌
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            response = await client.get(
                "/protected",
                headers={"Authorization": f"Bearer {access_token}"}
            )
        
        # 应该被拒绝
        assert response.status_code == 401


class TestAuthAPIIntegration:
    """认证API集成测试"""
    
    @pytest.mark.asyncio
    async def test_create_api_token_endpoint(
        self,
        test_app,
        test_user,
        jwt_manager,
        mock_user_service,
        rbac_manager
    ):
        """测试创建API令牌端点"""
        # 创建用户并分配权限
        await mock_user_service.create_user(test_user)
        developer_role = rbac_manager.get_role_by_name("developer")
        rbac_manager.assign_role_to_user(test_user.id, developer_role.id)
        
        # 生成JWT令牌
        access_token = jwt_manager.create_access_token(test_user)
        
        # 创建API令牌
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            response = await client.post(
                "/auth/tokens",
                headers={"Authorization": f"Bearer {access_token}"},
                json={
                    "name": "测试令牌",
                    "scopes": ["api_access"],
                    "permissions": ["api:text:generate"],
                    "expires_in_days": 30
                }
            )
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "测试令牌"
        assert "token" in data
        assert data["token"].startswith("ak_")
    
    @pytest.mark.asyncio
    async def test_get_user_info_endpoint(
        self,
        test_app,
        test_user,
        jwt_manager,
        mock_user_service
    ):
        """测试获取用户信息端点"""
        # 创建用户
        await mock_user_service.create_user(test_user)
        
        # 生成JWT令牌
        access_token = jwt_manager.create_access_token(test_user)
        
        # 获取用户信息
        async with AsyncClient(app=test_app, base_url="http://test") as client:
            response = await client.get(
                "/auth/me",
                headers={"Authorization": f"Bearer {access_token}"}
            )
        
        assert response.status_code == 200
        data = response.json()
        assert data["username"] == test_user.username
        assert data["email"] == test_user.email
        assert data["role"] == test_user.role.value


@pytest.mark.asyncio
async def test_complete_authentication_system():
    """完整认证系统集成测试"""
    print("🚀 开始完整认证系统集成测试")
    
    # 初始化所有组件
    jwt_manager = JWTTokenManager("test-secret")
    api_token_manager = APITokenManager("test-secret")
    rbac_manager = RBACManager()
    security_manager = SecurityManager()
    user_service = MockUserService()
    
    # 创建测试用户
    user = AuthUser(
        username="integration_test_user",
        email="<EMAIL>",
        role=UserRole.DEVELOPER
    )
    await user_service.create_user(user)
    
    # 分配角色
    developer_role = rbac_manager.get_role_by_name("developer")
    rbac_manager.assign_role_to_user(user.id, developer_role.id)
    
    # 测试JWT流程
    access_token = jwt_manager.create_access_token(user)
    payload = jwt_manager.verify_token(access_token)
    assert payload["sub"] == str(user.id)
    
    # 测试API Token流程
    api_token, token_value = api_token_manager.generate_api_token(
        user_id=user.id,
        name="集成测试令牌",
        scopes={TokenScope.API_ACCESS}
    )
    verified_token = api_token_manager.verify_api_token(token_value)
    assert verified_token.user_id == user.id
    
    # 测试权限检查
    has_permission = rbac_manager.check_permission(
        user.id, Permission.API_TEXT_GENERATE
    )
    assert has_permission
    
    # 测试频率限制
    allowed, _ = security_manager.rate_limiter.check_rate_limit(
        str(user.id), "user"
    )
    assert allowed
    
    print("✅ 完整认证系统集成测试通过")


if __name__ == "__main__":
    # 运行集成测试
    asyncio.run(test_complete_authentication_system())
