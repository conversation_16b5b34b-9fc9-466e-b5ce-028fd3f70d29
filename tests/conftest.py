"""
AI Gen Hub 测试配置

提供测试用的fixtures和配置
"""

import asyncio
import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock

from ai_gen_hub.config import Settings, get_settings
from ai_gen_hub.cache import MemoryCache
from ai_gen_hub.services import AIProviderManager, RequestRouter
from ai_gen_hub.utils import KeyManager


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_settings():
    """测试配置"""
    return Settings(
        environment="test",
        debug=True,
        
        # 禁用外部依赖
        features={
            "enable_text_generation": True,
            "enable_image_generation": True,
            "enable_streaming": True,
            "enable_caching": True,
            "enable_monitoring": False,
            "enable_rate_limiting": False,
            "enable_load_balancing": True,
            "enable_circuit_breaker": False,
        },
        
        # 测试用的供应商配置
        openai={
            "api_keys": ["test-openai-key-1", "test-openai-key-2"],
            "enabled": True,
            "timeout": 10,
            "max_retries": 1,
        },
        
        google_ai={
            "api_keys": ["test-google-key-1"],
            "enabled": True,
            "timeout": 10,
            "max_retries": 1,
        },
        
        anthropic={
            "api_keys": ["test-anthropic-key-1"],
            "enabled": True,
            "timeout": 10,
            "max_retries": 1,
        },
        
        # 缓存配置
        cache={
            "enable_memory_cache": True,
            "enable_redis_cache": False,
            "memory_cache_size": 100,
            "memory_cache_ttl": 300,
        },
        
        # Redis配置（测试时不使用）
        redis={
            "url": "redis://localhost:6379/15",  # 使用测试数据库
        }
    )


@pytest_asyncio.fixture
async def memory_cache():
    """内存缓存fixture"""
    cache = MemoryCache(
        max_size=100,
        default_ttl=300,
        key_prefix="test"
    )
    yield cache
    await cache.cleanup()


@pytest_asyncio.fixture
async def key_manager(test_settings):
    """密钥管理器fixture"""
    manager = KeyManager(test_settings)
    await manager.initialize()
    yield manager
    await manager.cleanup()


@pytest_asyncio.fixture
async def mock_provider():
    """模拟供应商fixture"""
    provider = AsyncMock()
    provider.name = "test_provider"
    provider.supports_model_type.return_value = True
    provider.supports_model.return_value = True
    provider.status = "healthy"
    
    # 模拟文本生成响应
    from ai_gen_hub.core.interfaces import (
        TextGenerationResponse,
        TextGenerationChoice,
        Message,
        MessageRole,
        Usage
    )
    
    mock_response = TextGenerationResponse(
        id="test-response-id",
        object="chat.completion",
        created=**********,
        model="test-model",
        choices=[
            TextGenerationChoice(
                index=0,
                message=Message(
                    role=MessageRole.ASSISTANT,
                    content="这是一个测试响应"
                ),
                finish_reason="stop"
            )
        ],
        usage=Usage(
            prompt_tokens=10,
            completion_tokens=5,
            total_tokens=15
        ),
        provider="test_provider",
        request_id="test-request-id",
        processing_time=0.5
    )
    
    provider.generate_text.return_value = mock_response
    
    # 模拟图像生成响应
    from ai_gen_hub.core.interfaces import (
        ImageGenerationResponse,
        ImageData
    )
    
    mock_image_response = ImageGenerationResponse(
        id="test-image-response-id",
        created=**********,
        data=[
            ImageData(
                url="https://example.com/test-image.png",
                b64_json=None,
                revised_prompt=None
            )
        ],
        provider="test_provider",
        request_id="test-request-id",
        processing_time=2.0
    )
    
    provider.generate_image.return_value = mock_image_response
    
    return provider


@pytest_asyncio.fixture
async def provider_manager(test_settings, key_manager, mock_provider):
    """供应商管理器fixture"""
    manager = AIProviderManager(test_settings, key_manager)
    
    # 添加模拟供应商
    await manager.add_provider(mock_provider)
    
    yield manager
    await manager.cleanup()


@pytest_asyncio.fixture
async def router(test_settings, provider_manager):
    """请求路由器fixture"""
    router = RequestRouter(test_settings, provider_manager)
    yield router


@pytest.fixture
def mock_http_client():
    """模拟HTTP客户端"""
    client = MagicMock()
    
    # 模拟成功响应
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "id": "test-response",
        "object": "chat.completion",
        "created": **********,
        "model": "test-model",
        "choices": [
            {
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": "测试响应内容"
                },
                "finish_reason": "stop"
            }
        ],
        "usage": {
            "prompt_tokens": 10,
            "completion_tokens": 5,
            "total_tokens": 15
        }
    }
    
    client.request.return_value = mock_response
    client.stream.return_value.__aenter__.return_value.aiter_bytes.return_value = [
        'data: {"id":"test","choices":[{"delta":{"content":"测试"}}]}\n\n'.encode('utf-8'),
        b'data: [DONE]\n\n'
    ]
    
    return client


@pytest.fixture
def sample_text_request():
    """示例文本生成请求"""
    from ai_gen_hub.core.interfaces import TextGenerationRequest, Message, MessageRole
    
    return TextGenerationRequest(
        model="test-model",
        messages=[
            Message(
                role=MessageRole.USER,
                content="你好，请介绍一下自己"
            )
        ],
        max_tokens=100,
        temperature=0.7,
        stream=False
    )


@pytest.fixture
def sample_image_request():
    """示例图像生成请求"""
    from ai_gen_hub.core.interfaces import ImageGenerationRequest
    
    return ImageGenerationRequest(
        prompt="一只可爱的小猫",
        model="dall-e-3",
        n=1,
        size="1024x1024",
        quality="standard",
        response_format="url"
    )


@pytest.fixture
def mock_redis():
    """模拟Redis客户端"""
    redis_mock = AsyncMock()
    
    # 模拟Redis操作
    redis_mock.ping.return_value = True
    redis_mock.get.return_value = None
    redis_mock.set.return_value = True
    redis_mock.setex.return_value = True
    redis_mock.delete.return_value = 1
    redis_mock.exists.return_value = 0
    redis_mock.keys.return_value = []
    redis_mock.info.return_value = {
        "redis_version": "6.0.0",
        "used_memory": 1024000,
        "used_memory_human": "1.00M",
        "connected_clients": 1,
        "uptime_in_seconds": 3600,
    }
    
    return redis_mock


@pytest.fixture
def mock_database():
    """模拟数据库连接"""
    db_mock = AsyncMock()
    
    # 模拟数据库操作
    db_mock.execute.return_value = None
    db_mock.fetch.return_value = []
    db_mock.fetchrow.return_value = None
    
    return db_mock


# 测试标记
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.e2e = pytest.mark.e2e
