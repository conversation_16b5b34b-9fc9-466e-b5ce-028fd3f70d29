"""
AI Gen Hub 高级分析功能测试

测试趋势分析、异常检测、报告生成和数据聚合功能
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
import pandas as pd
import numpy as np

from ai_gen_hub.analytics.trend_analyzer import Trend<PERSON>nal<PERSON><PERSON>, TrendResult
from ai_gen_hub.analytics.anomaly_detector import (
    AnomalyDetector, 
    AnomalyResult, 
    AnomalyType, 
    AnomalySeverity
)
from ai_gen_hub.analytics.report_generator import (
    ReportGenerator, 
    ReportConfig, 
    ReportData
)
from ai_gen_hub.analytics.data_aggregator import (
    DataAggregator,
    DataSource,
    AggregationRule,
    AggregationType,
    TimeWindow
)


class TestTrendAnalyzer:
    """趋势分析器测试"""
    
    @pytest.fixture
    def trend_analyzer(self):
        """创建趋势分析器实例"""
        return TrendAnalyzer()
    
    @pytest.fixture
    def sample_data_points(self):
        """创建示例数据点"""
        base_time = datetime.now() - timedelta(days=30)
        data_points = []
        
        for i in range(720):  # 30天，每小时一个点
            timestamp = base_time + timedelta(hours=i)
            # 模拟上升趋势 + 噪声
            value = 100 + i * 0.1 + np.random.normal(0, 5)
            data_points.append({
                'timestamp': timestamp,
                'value': value
            })
        
        return data_points
    
    @pytest.mark.asyncio
    async def test_analyze_metric_trend_basic(self, trend_analyzer, sample_data_points):
        """测试基本趋势分析"""
        result = await trend_analyzer.analyze_metric_trend(
            metric_name="test_metric",
            data_points=sample_data_points,
            prediction_days=7
        )
        
        assert isinstance(result, TrendResult)
        assert result.metric_name == "test_metric"
        assert result.trend_direction in ['increasing', 'decreasing', 'stable']
        assert 0 <= result.trend_strength <= 1
        assert len(result.predicted_values) == 7
        assert len(result.confidence_interval) == 2
    
    @pytest.mark.asyncio
    async def test_analyze_metric_trend_insufficient_data(self, trend_analyzer):
        """测试数据不足的情况"""
        data_points = [
            {'timestamp': datetime.now(), 'value': 100}
        ]
        
        result = await trend_analyzer.analyze_metric_trend(
            metric_name="test_metric",
            data_points=data_points
        )
        
        assert result.trend_direction == 'unknown'
        assert result.trend_strength == 0.0
        assert len(result.predicted_values) == 0
    
    @pytest.mark.asyncio
    async def test_get_trend_summary(self, trend_analyzer):
        """测试趋势摘要"""
        metrics = ['metric1', 'metric2', 'metric3']
        summary = await trend_analyzer.get_trend_summary(metrics, days=30)
        
        assert 'analysis_period' in summary
        assert 'timestamp' in summary
        assert 'metrics_summary' in summary
        assert 'overall_health' in summary
        assert summary['analysis_period'] == 30


class TestAnomalyDetector:
    """异常检测器测试"""
    
    @pytest.fixture
    def anomaly_detector(self):
        """创建异常检测器实例"""
        return AnomalyDetector()
    
    @pytest.fixture
    def normal_data_points(self):
        """创建正常数据点"""
        base_time = datetime.now() - timedelta(hours=24)
        data_points = []
        
        for i in range(144):  # 24小时，每10分钟一个点
            timestamp = base_time + timedelta(minutes=i*10)
            # 正常数据，均值100，标准差10
            value = np.random.normal(100, 10)
            data_points.append({
                'timestamp': timestamp,
                'value': value
            })
        
        return data_points
    
    @pytest.fixture
    def anomaly_data_points(self):
        """创建包含异常的数据点"""
        base_time = datetime.now() - timedelta(hours=24)
        data_points = []
        
        for i in range(144):
            timestamp = base_time + timedelta(minutes=i*10)
            
            # 在第50个点插入异常值
            if i == 50:
                value = 200  # 明显异常值
            else:
                value = np.random.normal(100, 10)
            
            data_points.append({
                'timestamp': timestamp,
                'value': value
            })
        
        return data_points
    
    @pytest.mark.asyncio
    async def test_detect_anomalies_normal_data(self, anomaly_detector, normal_data_points):
        """测试正常数据的异常检测"""
        anomalies = await anomaly_detector.detect_anomalies(
            metric_name="test_metric",
            data_points=normal_data_points
        )
        
        # 正常数据应该检测到很少或没有异常
        assert len(anomalies) <= 5  # 允许少量误报
    
    @pytest.mark.asyncio
    async def test_detect_anomalies_with_outliers(self, anomaly_detector, anomaly_data_points):
        """测试包含异常值的数据"""
        anomalies = await anomaly_detector.detect_anomalies(
            metric_name="test_metric",
            data_points=anomaly_data_points
        )
        
        # 应该检测到异常
        assert len(anomalies) > 0
        
        # 检查异常结果结构
        for anomaly in anomalies:
            assert isinstance(anomaly, AnomalyResult)
            assert isinstance(anomaly.anomaly_type, AnomalyType)
            assert isinstance(anomaly.severity, AnomalySeverity)
            assert 0 <= anomaly.confidence <= 1
    
    @pytest.mark.asyncio
    async def test_detect_anomalies_insufficient_data(self, anomaly_detector):
        """测试数据不足的情况"""
        data_points = [
            {'timestamp': datetime.now(), 'value': 100}
        ]
        
        anomalies = await anomaly_detector.detect_anomalies(
            metric_name="test_metric",
            data_points=data_points
        )
        
        assert len(anomalies) == 0
    
    @pytest.mark.asyncio
    async def test_get_anomaly_summary(self, anomaly_detector, anomaly_data_points):
        """测试异常摘要"""
        anomalies = await anomaly_detector.detect_anomalies(
            metric_name="test_metric",
            data_points=anomaly_data_points
        )
        
        summary = await anomaly_detector.get_anomaly_summary(
            metric_name="test_metric",
            anomalies=anomalies
        )
        
        assert 'metric_name' in summary
        assert 'total_anomalies' in summary
        assert 'severity_distribution' in summary
        assert 'type_distribution' in summary
        assert 'recommendations' in summary


class TestReportGenerator:
    """报告生成器测试"""
    
    @pytest.fixture
    def report_generator(self):
        """创建报告生成器实例"""
        return ReportGenerator()
    
    @pytest.fixture
    def sample_report_config(self):
        """创建示例报告配置"""
        return ReportConfig(
            name="test_report",
            description="测试报告",
            metrics=["metric1", "metric2"],
            time_range="daily",
            format="html",
            recipients=["<EMAIL>"]
        )
    
    def test_register_report(self, report_generator, sample_report_config):
        """测试报告注册"""
        report_generator.register_report(sample_report_config)
        
        assert "test_report" in report_generator.report_configs
        assert report_generator.report_configs["test_report"] == sample_report_config
    
    @pytest.mark.asyncio
    async def test_generate_report(self, report_generator, sample_report_config):
        """测试报告生成"""
        report_generator.register_report(sample_report_config)
        
        report_data = await report_generator.generate_report("test_report")
        
        assert isinstance(report_data, ReportData)
        assert report_data.title == sample_report_config.description
        assert isinstance(report_data.generated_at, datetime)
        assert 'start' in report_data.time_range
        assert 'end' in report_data.time_range
    
    @pytest.mark.asyncio
    async def test_export_report_html(self, report_generator, sample_report_config):
        """测试HTML报告导出"""
        report_generator.register_report(sample_report_config)
        report_data = await report_generator.generate_report("test_report")
        
        file_path = await report_generator.export_report(
            report_data=report_data,
            format="html"
        )
        
        assert file_path.endswith('.html')
        # 检查文件是否存在
        import os
        assert os.path.exists(file_path)
    
    @pytest.mark.asyncio
    async def test_export_report_json(self, report_generator, sample_report_config):
        """测试JSON报告导出"""
        report_generator.register_report(sample_report_config)
        report_data = await report_generator.generate_report("test_report")
        
        file_path = await report_generator.export_report(
            report_data=report_data,
            format="json"
        )
        
        assert file_path.endswith('.json')
        # 检查文件是否存在
        import os
        assert os.path.exists(file_path)


class TestDataAggregator:
    """数据聚合器测试"""
    
    @pytest.fixture
    def data_aggregator(self):
        """创建数据聚合器实例"""
        return DataAggregator(max_workers=2)
    
    @pytest.fixture
    def sample_data_source(self):
        """创建示例数据源"""
        return DataSource(
            name="test_source",
            type="metrics_collector",
            connection_config={},
            query_config={},
            refresh_interval=60
        )
    
    @pytest.fixture
    def sample_aggregation_rule(self):
        """创建示例聚合规则"""
        return AggregationRule(
            name="test_rule",
            source_metrics=["test_metric"],
            aggregation_type=AggregationType.AVERAGE,
            time_window=TimeWindow.HOUR,
            output_metric="test_aggregated"
        )
    
    def test_register_data_source(self, data_aggregator, sample_data_source):
        """测试数据源注册"""
        data_aggregator.register_data_source(sample_data_source)
        
        assert "test_source" in data_aggregator.data_sources
        assert data_aggregator.data_sources["test_source"] == sample_data_source
    
    def test_register_aggregation_rule(self, data_aggregator, sample_aggregation_rule):
        """测试聚合规则注册"""
        data_aggregator.register_aggregation_rule(sample_aggregation_rule)
        
        assert "test_rule" in data_aggregator.aggregation_rules
        assert data_aggregator.aggregation_rules["test_rule"] == sample_aggregation_rule
    
    @pytest.mark.asyncio
    async def test_start_stop_aggregator(self, data_aggregator, sample_data_source):
        """测试聚合器启动和停止"""
        data_aggregator.register_data_source(sample_data_source)
        
        # 启动聚合器
        await data_aggregator.start()
        assert data_aggregator.is_running
        
        # 等待一小段时间
        await asyncio.sleep(0.1)
        
        # 停止聚合器
        await data_aggregator.stop()
        assert not data_aggregator.is_running
    
    @pytest.mark.asyncio
    async def test_get_aggregated_data(self, data_aggregator):
        """测试获取聚合数据"""
        # 添加一些测试数据
        from collections import deque
        from ai_gen_hub.analytics.data_aggregator import AggregatedData
        
        test_data = AggregatedData(
            metric_name="test_metric",
            timestamp=datetime.now(),
            value=100.0,
            aggregation_type=AggregationType.AVERAGE,
            time_window=TimeWindow.HOUR,
            source_count=10
        )
        
        data_aggregator.aggregated_data["test_metric"].append(test_data)
        
        # 获取数据
        result = await data_aggregator.get_aggregated_data("test_metric")
        
        assert len(result) == 1
        assert result[0].metric_name == "test_metric"
        assert result[0].value == 100.0
    
    @pytest.mark.asyncio
    async def test_get_stats(self, data_aggregator):
        """测试获取统计信息"""
        stats = await data_aggregator.get_stats()
        
        assert 'is_running' in stats
        assert 'data_sources_count' in stats
        assert 'aggregation_rules_count' in stats
        assert 'performance_stats' in stats


@pytest.mark.asyncio
async def test_integration_trend_and_anomaly():
    """集成测试：趋势分析和异常检测"""
    trend_analyzer = TrendAnalyzer()
    anomaly_detector = AnomalyDetector()
    
    # 创建测试数据
    base_time = datetime.now() - timedelta(days=7)
    data_points = []
    
    for i in range(168):  # 7天，每小时一个点
        timestamp = base_time + timedelta(hours=i)
        # 正常趋势 + 一个异常点
        if i == 100:
            value = 500  # 异常值
        else:
            value = 100 + i * 0.5 + np.random.normal(0, 5)
        
        data_points.append({
            'timestamp': timestamp,
            'value': value
        })
    
    # 趋势分析
    trend_result = await trend_analyzer.analyze_metric_trend(
        metric_name="integration_test",
        data_points=data_points
    )
    
    # 异常检测
    anomalies = await anomaly_detector.detect_anomalies(
        metric_name="integration_test",
        data_points=data_points
    )
    
    # 验证结果
    assert trend_result.trend_direction == 'increasing'
    assert len(anomalies) > 0
    
    # 验证异常检测到了我们插入的异常值
    anomaly_timestamps = [a.timestamp for a in anomalies]
    expected_anomaly_time = base_time + timedelta(hours=100)
    
    # 检查是否有异常时间点接近我们插入的异常点
    found_anomaly = any(
        abs((ts - expected_anomaly_time).total_seconds()) < 3600  # 1小时内
        for ts in anomaly_timestamps
    )
    assert found_anomaly


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
