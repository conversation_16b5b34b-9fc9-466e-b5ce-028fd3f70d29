"""
统一Chat接口单元测试

测试统一Chat接口的数据模型、枚举、异常等核心组件。

测试内容：
- 数据模型的创建和验证
- 枚举值的正确性
- 异常的创建和属性
- 配置参数的验证

作者：AI Gen Hub Team
创建时间：2025-08-24
"""

import pytest
from datetime import datetime
from typing import List

from ai_gen_hub.core.chat_interfaces import (
    ChatMessage,
    ChatConfig,
    ChatResponse,
    ChatStreamChunk,
    ChatUsage,
    ChatMessageRole,
    ChatFinishReason,
    ChatStreamEventType,
    ChatError,
    ChatValidationError,
    ChatProviderError,
    ChatRateLimitError,
    ChatContentFilterError,
)


class TestChatMessage:
    """测试ChatMessage数据模型"""
    
    def test_create_user_message(self):
        """测试创建用户消息"""
        message = ChatMessage(
            role=ChatMessageRole.USER,
            content="你好，AI助手！"
        )
        
        assert message.role == ChatMessageRole.USER
        assert message.content == "你好，AI助手！"
        assert message.name is None
        assert message.tool_calls is None
        assert message.tool_call_id is None
    
    def test_create_assistant_message(self):
        """测试创建助手消息"""
        message = ChatMessage(
            role=ChatMessageRole.ASSISTANT,
            content="你好！我是AI助手，很高兴为您服务。"
        )
        
        assert message.role == ChatMessageRole.ASSISTANT
        assert message.content == "你好！我是AI助手，很高兴为您服务。"
    
    def test_create_system_message(self):
        """测试创建系统消息"""
        message = ChatMessage(
            role=ChatMessageRole.SYSTEM,
            content="你是一个有用的AI助手。"
        )
        
        assert message.role == ChatMessageRole.SYSTEM
        assert message.content == "你是一个有用的AI助手。"
    
    def test_create_message_with_tool_calls(self):
        """测试创建带工具调用的消息"""
        tool_calls = [
            {
                "id": "call_123",
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "arguments": '{"location": "北京"}'
                }
            }
        ]
        
        message = ChatMessage(
            role=ChatMessageRole.ASSISTANT,
            content="我来帮您查询北京的天气。",
            tool_calls=tool_calls
        )
        
        assert message.tool_calls == tool_calls
    
    def test_create_tool_response_message(self):
        """测试创建工具响应消息"""
        message = ChatMessage(
            role=ChatMessageRole.TOOL,
            content="北京今天晴天，温度25°C",
            tool_call_id="call_123"
        )
        
        assert message.role == ChatMessageRole.TOOL
        assert message.tool_call_id == "call_123"
    
    def test_message_with_metadata(self):
        """测试带元数据的消息"""
        metadata = {"source": "user_input", "timestamp": "2025-08-24T10:00:00Z"}
        
        message = ChatMessage(
            role=ChatMessageRole.USER,
            content="测试消息",
            metadata=metadata
        )
        
        assert message.metadata == metadata


class TestChatConfig:
    """测试ChatConfig配置模型"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = ChatConfig()
        
        assert config.temperature == 0.7
        assert config.frequency_penalty == 0.0
        assert config.presence_penalty == 0.0
        assert config.stream is False
        assert config.max_tokens is None
        assert config.top_p is None
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = ChatConfig(
            max_tokens=1000,
            temperature=0.5,
            top_p=0.9,
            frequency_penalty=0.1,
            presence_penalty=0.1,
            stream=True,
            stop_sequences=["停止", "结束"]
        )
        
        assert config.max_tokens == 1000
        assert config.temperature == 0.5
        assert config.top_p == 0.9
        assert config.frequency_penalty == 0.1
        assert config.presence_penalty == 0.1
        assert config.stream is True
        assert config.stop_sequences == ["停止", "结束"]
    
    def test_config_validation(self):
        """测试配置参数验证"""
        # 测试温度参数范围
        with pytest.raises(ValueError):
            ChatConfig(temperature=-0.1)  # 温度不能小于0
        
        with pytest.raises(ValueError):
            ChatConfig(temperature=2.1)   # 温度不能大于2
        
        # 测试top_p参数范围
        with pytest.raises(ValueError):
            ChatConfig(top_p=-0.1)  # top_p不能小于0
        
        with pytest.raises(ValueError):
            ChatConfig(top_p=1.1)   # top_p不能大于1
        
        # 测试max_tokens参数
        with pytest.raises(ValueError):
            ChatConfig(max_tokens=0)  # max_tokens必须大于0
    
    def test_config_with_tools(self):
        """测试带工具配置"""
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "description": "获取天气信息",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {"type": "string", "description": "城市名称"}
                        },
                        "required": ["location"]
                    }
                }
            }
        ]
        
        config = ChatConfig(
            tools=tools,
            tool_choice="auto"
        )
        
        assert config.tools == tools
        assert config.tool_choice == "auto"


class TestChatUsage:
    """测试ChatUsage使用量模型"""
    
    def test_create_usage(self):
        """测试创建使用量统计"""
        usage = ChatUsage(
            prompt_tokens=100,
            completion_tokens=50,
            total_tokens=150
        )
        
        assert usage.prompt_tokens == 100
        assert usage.completion_tokens == 50
        assert usage.total_tokens == 150
    
    def test_usage_auto_total(self):
        """测试自动计算总token数"""
        usage = ChatUsage(
            prompt_tokens=100,
            completion_tokens=50
        )
        
        # 应该自动计算total_tokens
        usage.__post_init__()
        assert usage.total_tokens == 150


class TestChatResponse:
    """测试ChatResponse响应模型"""
    
    def test_create_response(self):
        """测试创建Chat响应"""
        message = ChatMessage(
            role=ChatMessageRole.ASSISTANT,
            content="这是AI助手的回复。"
        )
        
        usage = ChatUsage(
            prompt_tokens=20,
            completion_tokens=10,
            total_tokens=30
        )
        
        response = ChatResponse(
            model="gpt-3.5-turbo",
            provider="openai",
            message=message,
            finish_reason=ChatFinishReason.STOP,
            usage=usage
        )
        
        assert response.model == "gpt-3.5-turbo"
        assert response.provider == "openai"
        assert response.message == message
        assert response.finish_reason == ChatFinishReason.STOP
        assert response.usage == usage
        assert isinstance(response.created, datetime)
        assert response.id is not None


class TestChatStreamChunk:
    """测试ChatStreamChunk流式响应块模型"""
    
    def test_create_content_chunk(self):
        """测试创建内容增量块"""
        chunk = ChatStreamChunk(
            id="chunk_123",
            event_type=ChatStreamEventType.CONTENT_DELTA,
            delta_content="你好"
        )
        
        assert chunk.id == "chunk_123"
        assert chunk.event_type == ChatStreamEventType.CONTENT_DELTA
        assert chunk.delta_content == "你好"
        assert chunk.finish_reason is None
    
    def test_create_stop_chunk(self):
        """测试创建结束块"""
        usage = ChatUsage(prompt_tokens=10, completion_tokens=5, total_tokens=15)
        
        chunk = ChatStreamChunk(
            id="chunk_456",
            event_type=ChatStreamEventType.MESSAGE_STOP,
            finish_reason=ChatFinishReason.STOP,
            usage=usage
        )
        
        assert chunk.event_type == ChatStreamEventType.MESSAGE_STOP
        assert chunk.finish_reason == ChatFinishReason.STOP
        assert chunk.usage == usage


class TestChatExceptions:
    """测试Chat异常类"""
    
    def test_chat_error(self):
        """测试基础Chat错误"""
        error = ChatError("测试错误", error_code="TEST_ERROR", provider="test_provider")
        
        assert str(error) == "测试错误"
        assert error.message == "测试错误"
        assert error.error_code == "TEST_ERROR"
        assert error.provider == "test_provider"
    
    def test_chat_validation_error(self):
        """测试Chat验证错误"""
        error = ChatValidationError("参数验证失败", provider="openai")
        
        assert isinstance(error, ChatError)
        assert error.message == "参数验证失败"
        assert error.provider == "openai"
    
    def test_chat_provider_error(self):
        """测试Chat供应商错误"""
        error = ChatProviderError("供应商服务不可用", error_code="SERVICE_UNAVAILABLE")
        
        assert isinstance(error, ChatError)
        assert error.message == "供应商服务不可用"
        assert error.error_code == "SERVICE_UNAVAILABLE"
    
    def test_chat_rate_limit_error(self):
        """测试Chat速率限制错误"""
        error = ChatRateLimitError("请求频率超限", provider="anthropic")
        
        assert isinstance(error, ChatError)
        assert error.message == "请求频率超限"
        assert error.provider == "anthropic"
    
    def test_chat_content_filter_error(self):
        """测试Chat内容过滤错误"""
        error = ChatContentFilterError("内容被安全过滤器阻止", provider="google_ai")
        
        assert isinstance(error, ChatError)
        assert error.message == "内容被安全过滤器阻止"
        assert error.provider == "google_ai"


class TestEnums:
    """测试枚举类"""
    
    def test_message_roles(self):
        """测试消息角色枚举"""
        assert ChatMessageRole.SYSTEM == "system"
        assert ChatMessageRole.USER == "user"
        assert ChatMessageRole.ASSISTANT == "assistant"
        assert ChatMessageRole.FUNCTION == "function"
        assert ChatMessageRole.TOOL == "tool"
    
    def test_finish_reasons(self):
        """测试完成原因枚举"""
        assert ChatFinishReason.STOP == "stop"
        assert ChatFinishReason.LENGTH == "length"
        assert ChatFinishReason.CONTENT_FILTER == "content_filter"
        assert ChatFinishReason.TOOL_CALLS == "tool_calls"
        assert ChatFinishReason.FUNCTION_CALL == "function_call"
        assert ChatFinishReason.ERROR == "error"
    
    def test_stream_event_types(self):
        """测试流式事件类型枚举"""
        assert ChatStreamEventType.MESSAGE_START == "message_start"
        assert ChatStreamEventType.CONTENT_DELTA == "content_delta"
        assert ChatStreamEventType.TOOL_CALL_DELTA == "tool_call_delta"
        assert ChatStreamEventType.MESSAGE_STOP == "message_stop"
        assert ChatStreamEventType.ERROR == "error"
