"""
统一Chat接口集成测试

测试统一Chat接口的端到端功能，包括多供应商协作、故障转移等。

注意：这些测试需要真实的API密钥才能运行，默认跳过。
可以通过设置环境变量来启用真实API测试。

环境变量：
- OPENAI_API_KEY: OpenAI API密钥
- GOOGLE_AI_API_KEY: Google AI API密钥  
- ANTHROPIC_API_KEY: Anthropic API密钥
- ENABLE_REAL_API_TESTS: 设置为"true"启用真实API测试

作者：AI Gen Hub Team
创建时间：2025-08-24
"""

import os
import pytest
import asyncio
from typing import List

from ai_gen_hub.chat import (
    UnifiedChatManager,
    OpenAIChatProvider,
    GoogleAIChatProvider,
    AnthropicChatProvider,
    ChatMessage,
    ChatConfig,
    ChatMessageRole,
    ChatError,
    create_chat_manager,
    create_openai_provider,
    create_google_ai_provider,
    create_anthropic_provider,
)

# 检查是否启用真实API测试
ENABLE_REAL_API_TESTS = os.getenv("ENABLE_REAL_API_TESTS", "false").lower() == "true"

# API密钥
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
GOOGLE_AI_API_KEY = os.getenv("GOOGLE_AI_API_KEY")
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")


class TestConvenienceFunctions:
    """测试便捷函数"""
    
    def test_create_chat_manager(self):
        """测试创建Chat管理器的便捷函数"""
        manager = create_chat_manager()
        
        assert isinstance(manager, UnifiedChatManager)
        assert manager.max_retries == 3  # 默认值
    
    def test_create_chat_manager_with_config(self):
        """测试带配置创建Chat管理器"""
        config = {"max_retries": 5, "default_provider": "openai"}
        manager = create_chat_manager(config)
        
        assert manager.max_retries == 5
        assert manager.default_provider == "openai"
    
    def test_create_openai_provider(self):
        """测试创建OpenAI供应商的便捷函数"""
        provider = create_openai_provider("test-key", timeout=60)
        
        assert isinstance(provider, OpenAIChatProvider)
        assert provider.api_key == "test-key"
        assert provider.timeout == 60
    
    def test_create_google_ai_provider(self):
        """测试创建Google AI供应商的便捷函数"""
        provider = create_google_ai_provider("test-key", timeout=45)
        
        assert isinstance(provider, GoogleAIChatProvider)
        assert provider.api_key == "test-key"
        assert provider.timeout == 45
    
    def test_create_anthropic_provider(self):
        """测试创建Anthropic供应商的便捷函数"""
        provider = create_anthropic_provider("test-key", timeout=30)
        
        assert isinstance(provider, AnthropicChatProvider)
        assert provider.api_key == "test-key"
        assert provider.timeout == 30


class TestMultiProviderSetup:
    """测试多供应商设置"""
    
    def test_setup_multiple_providers(self):
        """测试设置多个供应商"""
        manager = create_chat_manager()
        
        # 创建多个供应商（使用测试密钥）
        openai_provider = create_openai_provider("test-openai-key")
        google_provider = create_google_ai_provider("test-google-key")
        anthropic_provider = create_anthropic_provider("test-anthropic-key")
        
        # 注册供应商
        manager.register_provider(openai_provider, weight=1.0)
        manager.register_provider(google_provider, weight=1.5)
        manager.register_provider(anthropic_provider, weight=0.8)
        
        # 验证注册
        assert len(manager._providers) == 3
        assert "openai" in manager._providers
        assert "google_ai" in manager._providers
        assert "anthropic" in manager._providers
        
        # 验证权重
        assert manager._provider_weights["openai"] == 1.0
        assert manager._provider_weights["google_ai"] == 1.5
        assert manager._provider_weights["anthropic"] == 0.8
    
    def test_model_provider_mapping(self):
        """测试模型供应商映射"""
        manager = create_chat_manager()
        
        # 设置模型映射
        model_mappings = {
            "gpt-4": "openai",
            "gpt-3.5-turbo": "openai",
            "gemini-2.5-pro": "google_ai",
            "gemini-2.5-flash": "google_ai",
            "claude-3-5-sonnet": "anthropic",
            "claude-3-opus": "anthropic",
        }
        
        manager.set_model_provider_mapping(model_mappings)
        
        # 验证映射
        assert manager._model_provider_mapping == model_mappings


@pytest.mark.skipif(not ENABLE_REAL_API_TESTS, reason="真实API测试被禁用")
class TestRealAPIIntegration:
    """真实API集成测试（需要真实API密钥）"""
    
    @pytest.mark.asyncio
    @pytest.mark.skipif(not OPENAI_API_KEY, reason="需要OPENAI_API_KEY环境变量")
    async def test_openai_real_chat(self):
        """测试真实OpenAI Chat请求"""
        provider = create_openai_provider(OPENAI_API_KEY)
        
        messages = [
            ChatMessage(role=ChatMessageRole.USER, content="请用一句话介绍人工智能。")
        ]
        config = ChatConfig(
            temperature=0.7,
            max_tokens=100,
            provider_params={"model": "gpt-3.5-turbo"}
        )
        
        try:
            response = await provider.chat(messages, config)
            
            assert response.provider == "openai"
            assert response.message.role == ChatMessageRole.ASSISTANT
            assert len(response.message.content) > 0
            assert response.usage.total_tokens > 0
            
            print(f"OpenAI响应: {response.message.content}")
            
        except ChatError as e:
            pytest.fail(f"OpenAI Chat请求失败: {e}")
    
    @pytest.mark.asyncio
    @pytest.mark.skipif(not GOOGLE_AI_API_KEY, reason="需要GOOGLE_AI_API_KEY环境变量")
    async def test_google_ai_real_chat(self):
        """测试真实Google AI Chat请求"""
        provider = create_google_ai_provider(GOOGLE_AI_API_KEY)
        
        messages = [
            ChatMessage(role=ChatMessageRole.SYSTEM, content="你是一个有用的AI助手。"),
            ChatMessage(role=ChatMessageRole.USER, content="请用一句话介绍机器学习。")
        ]
        config = ChatConfig(
            temperature=0.7,
            max_tokens=100,
            provider_params={"model": "gemini-2.5-flash"}
        )
        
        try:
            response = await provider.chat(messages, config)
            
            assert response.provider == "google_ai"
            assert response.message.role == ChatMessageRole.ASSISTANT
            assert len(response.message.content) > 0
            assert response.usage.total_tokens > 0
            
            print(f"Google AI响应: {response.message.content}")
            
        except ChatError as e:
            pytest.fail(f"Google AI Chat请求失败: {e}")
    
    @pytest.mark.asyncio
    @pytest.mark.skipif(not ANTHROPIC_API_KEY, reason="需要ANTHROPIC_API_KEY环境变量")
    async def test_anthropic_real_chat(self):
        """测试真实Anthropic Chat请求"""
        provider = create_anthropic_provider(ANTHROPIC_API_KEY)
        
        messages = [
            ChatMessage(role=ChatMessageRole.SYSTEM, content="你是一个有用的AI助手。"),
            ChatMessage(role=ChatMessageRole.USER, content="请用一句话介绍深度学习。")
        ]
        config = ChatConfig(
            temperature=0.7,
            max_tokens=100,
            provider_params={"model": "claude-3-5-sonnet-20241022"}
        )
        
        try:
            response = await provider.chat(messages, config)
            
            assert response.provider == "anthropic"
            assert response.message.role == ChatMessageRole.ASSISTANT
            assert len(response.message.content) > 0
            assert response.usage.total_tokens > 0
            
            print(f"Anthropic响应: {response.message.content}")
            
        except ChatError as e:
            pytest.fail(f"Anthropic Chat请求失败: {e}")
    
    @pytest.mark.asyncio
    @pytest.mark.skipif(not (OPENAI_API_KEY and GOOGLE_AI_API_KEY), 
                       reason="需要OPENAI_API_KEY和GOOGLE_AI_API_KEY环境变量")
    async def test_multi_provider_manager(self):
        """测试多供应商管理器"""
        manager = create_chat_manager({
            "default_provider": "openai",
            "max_retries": 2
        })
        
        # 注册多个供应商
        openai_provider = create_openai_provider(OPENAI_API_KEY)
        google_provider = create_google_ai_provider(GOOGLE_AI_API_KEY)
        
        manager.register_provider(openai_provider)
        manager.register_provider(google_provider)
        
        # 设置模型映射
        manager.set_model_provider_mapping({
            "gpt-3.5-turbo": "openai",
            "gemini-2.5-flash": "google_ai"
        })
        
        messages = [
            ChatMessage(role=ChatMessageRole.USER, content="请简单介绍一下自然语言处理。")
        ]
        
        # 测试使用OpenAI
        openai_config = ChatConfig(
            temperature=0.7,
            max_tokens=80,
            provider_params={"model": "gpt-3.5-turbo"}
        )
        
        openai_response = await manager.chat(messages, openai_config)
        assert openai_response.provider == "openai"
        print(f"OpenAI管理器响应: {openai_response.message.content}")
        
        # 测试使用Google AI
        google_config = ChatConfig(
            temperature=0.7,
            max_tokens=80,
            provider_params={"model": "gemini-2.5-flash"}
        )
        
        google_response = await manager.chat(messages, google_config)
        assert google_response.provider == "google_ai"
        print(f"Google AI管理器响应: {google_response.message.content}")
        
        # 检查统计信息
        stats = manager.get_provider_stats()
        assert "openai" in stats
        assert "google_ai" in stats
        assert stats["openai"]["successful_requests"] >= 1
        assert stats["google_ai"]["successful_requests"] >= 1
    
    @pytest.mark.asyncio
    @pytest.mark.skipif(not OPENAI_API_KEY, reason="需要OPENAI_API_KEY环境变量")
    async def test_stream_chat(self):
        """测试流式Chat请求"""
        provider = create_openai_provider(OPENAI_API_KEY)
        
        messages = [
            ChatMessage(role=ChatMessageRole.USER, content="请写一首关于春天的短诗。")
        ]
        config = ChatConfig(
            temperature=0.8,
            max_tokens=150,
            stream=True,
            provider_params={"model": "gpt-3.5-turbo"}
        )
        
        try:
            content_parts = []
            async for chunk in provider.chat_stream(messages, config):
                if chunk.delta_content:
                    content_parts.append(chunk.delta_content)
                    print(chunk.delta_content, end="", flush=True)
                
                if chunk.finish_reason:
                    print(f"\n完成原因: {chunk.finish_reason}")
                    if chunk.usage:
                        print(f"使用token: {chunk.usage.total_tokens}")
            
            full_content = "".join(content_parts)
            assert len(full_content) > 0
            print(f"\n完整内容: {full_content}")
            
        except ChatError as e:
            pytest.fail(f"流式Chat请求失败: {e}")


class TestErrorHandling:
    """测试错误处理"""
    
    @pytest.mark.asyncio
    async def test_invalid_api_key(self):
        """测试无效API密钥的错误处理"""
        provider = create_openai_provider("invalid-api-key")
        
        messages = [
            ChatMessage(role=ChatMessageRole.USER, content="测试消息")
        ]
        config = ChatConfig(provider_params={"model": "gpt-3.5-turbo"})
        
        with pytest.raises(ChatError):
            await provider.chat(messages, config)
    
    @pytest.mark.asyncio
    async def test_no_providers_available(self):
        """测试没有可用供应商的错误处理"""
        manager = create_chat_manager()
        
        messages = [
            ChatMessage(role=ChatMessageRole.USER, content="测试消息")
        ]
        config = ChatConfig()
        
        with pytest.raises(ChatError):
            await manager.chat(messages, config)
    
    def test_invalid_message_validation(self):
        """测试无效消息的验证"""
        from ai_gen_hub.core.chat_base import BaseChatProvider
        
        provider = BaseChatProvider("test", {})
        
        # 测试空消息列表
        with pytest.raises(Exception):
            provider._validate_messages([])
        
        # 测试空内容消息
        empty_message = ChatMessage(role=ChatMessageRole.USER, content="")
        with pytest.raises(Exception):
            provider._validate_messages([empty_message])


if __name__ == "__main__":
    # 运行测试的示例
    print("运行统一Chat接口集成测试...")
    print("注意：真实API测试需要设置相应的环境变量")
    print("ENABLE_REAL_API_TESTS=true pytest tests/chat/test_integration.py -v")
