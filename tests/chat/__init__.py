"""
AI Gen Hub Chat模块测试

本目录包含统一Chat接口的所有测试用例。

测试文件：
- test_chat_interfaces.py: 核心接口和数据模型测试
- test_chat_manager.py: 统一Chat管理器测试
- test_integration.py: 集成测试和真实API测试

运行测试：
```bash
# 运行所有Chat测试
pytest tests/chat/ -v

# 运行特定测试文件
pytest tests/chat/test_chat_interfaces.py -v

# 运行真实API测试（需要API密钥）
ENABLE_REAL_API_TESTS=true OPENAI_API_KEY=your-key pytest tests/chat/test_integration.py -v
```

作者：AI Gen Hub Team
创建时间：2025-08-24
"""
