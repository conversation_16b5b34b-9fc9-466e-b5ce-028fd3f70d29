"""
统一Chat管理器单元测试

测试统一Chat管理器的核心功能，包括供应商管理、负载均衡、故障转移等。

测试内容：
- 供应商注册和注销
- 供应商选择和负载均衡
- 健康检查和故障转移
- 统计信息收集
- 配置验证

作者：AI Gen Hub Team
创建时间：2025-08-24
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta

from ai_gen_hub.chat.chat_manager import UnifiedChatManager, ProviderStats
from ai_gen_hub.core.chat_interfaces import (
    ChatProvider,
    ChatMessage,
    ChatConfig,
    ChatResponse,
    ChatStreamChunk,
    ChatUsage,
    ChatMessageRole,
    ChatFinishReason,
    ChatStreamEventType,
    ChatProviderError,
    ChatValidationError,
)


class MockChatProvider(ChatProvider):
    """模拟Chat供应商，用于测试"""
    
    def __init__(self, provider_name: str, should_fail: bool = False):
        super().__init__(provider_name, {})
        self.should_fail = should_fail
        self.call_count = 0
        self.health_check_result = True
        
    async def chat(self, messages, config):
        """模拟chat方法"""
        self.call_count += 1
        
        if self.should_fail:
            raise ChatProviderError(f"模拟{self.provider_name}供应商错误", provider=self.provider_name)
        
        # 模拟响应
        response_message = ChatMessage(
            role=ChatMessageRole.ASSISTANT,
            content=f"来自{self.provider_name}的回复"
        )
        
        usage = ChatUsage(prompt_tokens=10, completion_tokens=5, total_tokens=15)
        
        return ChatResponse(
            model="test-model",
            provider=self.provider_name,
            message=response_message,
            finish_reason=ChatFinishReason.STOP,
            usage=usage
        )
    
    async def chat_stream(self, messages, config):
        """模拟chat_stream方法"""
        self.call_count += 1
        
        if self.should_fail:
            raise ChatProviderError(f"模拟{self.provider_name}流式错误", provider=self.provider_name)
        
        # 模拟流式响应
        chunks = [
            ChatStreamChunk(
                id="chunk_1",
                event_type=ChatStreamEventType.CONTENT_DELTA,
                delta_content="来自"
            ),
            ChatStreamChunk(
                id="chunk_2", 
                event_type=ChatStreamEventType.CONTENT_DELTA,
                delta_content=f"{self.provider_name}"
            ),
            ChatStreamChunk(
                id="chunk_3",
                event_type=ChatStreamEventType.CONTENT_DELTA,
                delta_content="的流式回复"
            ),
            ChatStreamChunk(
                id="chunk_4",
                event_type=ChatStreamEventType.MESSAGE_STOP,
                finish_reason=ChatFinishReason.STOP,
                usage=ChatUsage(prompt_tokens=10, completion_tokens=8, total_tokens=18)
            )
        ]
        
        for chunk in chunks:
            yield chunk
    
    async def validate_config(self, config):
        """模拟配置验证"""
        return True
    
    async def get_supported_models(self):
        """模拟获取支持的模型"""
        return [f"{self.provider_name}-model-1", f"{self.provider_name}-model-2"]
    
    async def health_check(self):
        """模拟健康检查"""
        return self.health_check_result


class TestProviderStats:
    """测试供应商统计信息"""
    
    def test_initial_stats(self):
        """测试初始统计信息"""
        stats = ProviderStats()
        
        assert stats.total_requests == 0
        assert stats.successful_requests == 0
        assert stats.failed_requests == 0
        assert stats.success_rate == 1.0  # 初始成功率为1.0
        assert stats.average_response_time == 0.0
        assert stats.consecutive_failures == 0
    
    def test_record_success(self):
        """测试记录成功请求"""
        stats = ProviderStats()
        
        stats.record_success(1.5)
        
        assert stats.total_requests == 1
        assert stats.successful_requests == 1
        assert stats.failed_requests == 0
        assert stats.success_rate == 1.0
        assert stats.average_response_time == 1.5
        assert stats.consecutive_failures == 0
        assert stats.last_request_time is not None
    
    def test_record_failure(self):
        """测试记录失败请求"""
        stats = ProviderStats()
        
        stats.record_failure("测试错误")
        
        assert stats.total_requests == 1
        assert stats.successful_requests == 0
        assert stats.failed_requests == 1
        assert stats.success_rate == 0.0
        assert stats.consecutive_failures == 1
        assert stats.last_error_message == "测试错误"
        assert stats.last_error_time is not None
    
    def test_mixed_requests(self):
        """测试混合请求统计"""
        stats = ProviderStats()
        
        # 记录一些成功和失败请求
        stats.record_success(1.0)
        stats.record_success(2.0)
        stats.record_failure("错误1")
        stats.record_success(1.5)
        
        assert stats.total_requests == 4
        assert stats.successful_requests == 3
        assert stats.failed_requests == 1
        assert stats.success_rate == 0.75
        assert stats.average_response_time == 1.5  # (1.0 + 2.0 + 1.5) / 3
        assert stats.consecutive_failures == 0  # 最后一次是成功的


class TestUnifiedChatManager:
    """测试统一Chat管理器"""
    
    def test_init_manager(self):
        """测试初始化管理器"""
        config = {
            "default_provider": "openai",
            "max_retries": 5,
            "retry_delay": 2.0,
            "failure_threshold": 5
        }
        
        manager = UnifiedChatManager(config)
        
        assert manager.default_provider == "openai"
        assert manager.max_retries == 5
        assert manager.retry_delay == 2.0
        assert manager.failure_threshold == 5
    
    def test_register_provider(self):
        """测试注册供应商"""
        manager = UnifiedChatManager({})
        provider = MockChatProvider("test_provider")
        
        manager.register_provider(provider, weight=2.0)
        
        assert "test_provider" in manager._providers
        assert manager._providers["test_provider"] == provider
        assert manager._provider_weights["test_provider"] == 2.0
        assert "test_provider" in manager._provider_stats
    
    def test_unregister_provider(self):
        """测试注销供应商"""
        manager = UnifiedChatManager({})
        provider = MockChatProvider("test_provider")
        
        manager.register_provider(provider)
        manager.unregister_provider("test_provider")
        
        assert "test_provider" not in manager._providers
        assert "test_provider" not in manager._provider_stats
        assert "test_provider" not in manager._provider_weights
    
    def test_set_model_provider_mapping(self):
        """测试设置模型供应商映射"""
        manager = UnifiedChatManager({})
        
        mappings = {
            "gpt-4": "openai",
            "gemini-pro": "google_ai",
            "claude-3": "anthropic"
        }
        
        manager.set_model_provider_mapping(mappings)
        
        assert manager._model_provider_mapping == mappings
    
    @pytest.mark.asyncio
    async def test_get_available_providers(self):
        """测试获取可用供应商"""
        manager = UnifiedChatManager({})
        
        # 注册几个供应商
        provider1 = MockChatProvider("provider1")
        provider2 = MockChatProvider("provider2")
        provider3 = MockChatProvider("provider3")
        
        manager.register_provider(provider1)
        manager.register_provider(provider2)
        manager.register_provider(provider3)
        
        # 禁用一个供应商
        manager._disabled_providers.add("provider2")
        
        available = await manager.get_available_providers()
        
        assert "provider1" in available
        assert "provider2" not in available
        assert "provider3" in available
    
    @pytest.mark.asyncio
    async def test_chat_success(self):
        """测试成功的Chat请求"""
        manager = UnifiedChatManager({})
        provider = MockChatProvider("test_provider")
        manager.register_provider(provider)
        
        messages = [ChatMessage(role=ChatMessageRole.USER, content="测试消息")]
        config = ChatConfig()
        
        response = await manager.chat(messages, config)
        
        assert response.provider == "test_provider"
        assert response.message.content == "来自test_provider的回复"
        assert provider.call_count == 1
        
        # 检查统计信息
        stats = manager._provider_stats["test_provider"]
        assert stats.successful_requests == 1
        assert stats.failed_requests == 0
    
    @pytest.mark.asyncio
    async def test_chat_with_provider_selection(self):
        """测试指定供应商的Chat请求"""
        manager = UnifiedChatManager({})
        
        provider1 = MockChatProvider("provider1")
        provider2 = MockChatProvider("provider2")
        
        manager.register_provider(provider1)
        manager.register_provider(provider2)
        
        messages = [ChatMessage(role=ChatMessageRole.USER, content="测试消息")]
        config = ChatConfig()
        
        # 指定使用provider2
        response = await manager.chat(messages, config, provider="provider2")
        
        assert response.provider == "provider2"
        assert provider1.call_count == 0
        assert provider2.call_count == 1
    
    @pytest.mark.asyncio
    async def test_chat_with_model_mapping(self):
        """测试基于模型映射的供应商选择"""
        manager = UnifiedChatManager({})
        
        provider1 = MockChatProvider("openai")
        provider2 = MockChatProvider("google_ai")
        
        manager.register_provider(provider1)
        manager.register_provider(provider2)
        
        # 设置模型映射
        manager.set_model_provider_mapping({
            "gpt-4": "openai",
            "gemini-pro": "google_ai"
        })
        
        messages = [ChatMessage(role=ChatMessageRole.USER, content="测试消息")]
        config = ChatConfig(provider_params={"model": "gemini-pro"})
        
        response = await manager.chat(messages, config)
        
        assert response.provider == "google_ai"
        assert provider1.call_count == 0
        assert provider2.call_count == 1
    
    @pytest.mark.asyncio
    async def test_chat_failure_and_retry(self):
        """测试Chat请求失败和重试"""
        manager = UnifiedChatManager({"max_retries": 2, "retry_delay": 0.1})
        
        # 创建一个会失败的供应商
        provider = MockChatProvider("failing_provider", should_fail=True)
        manager.register_provider(provider)
        
        messages = [ChatMessage(role=ChatMessageRole.USER, content="测试消息")]
        config = ChatConfig()
        
        with pytest.raises(ChatProviderError):
            await manager.chat(messages, config)
        
        # 应该重试了3次（初始请求 + 2次重试）
        assert provider.call_count == 3
        
        # 检查统计信息
        stats = manager._provider_stats["failing_provider"]
        assert stats.failed_requests == 3
        assert stats.consecutive_failures == 3
    
    @pytest.mark.asyncio
    async def test_chat_stream_success(self):
        """测试成功的流式Chat请求"""
        manager = UnifiedChatManager({})
        provider = MockChatProvider("test_provider")
        manager.register_provider(provider)
        
        messages = [ChatMessage(role=ChatMessageRole.USER, content="测试消息")]
        config = ChatConfig()
        
        chunks = []
        async for chunk in manager.chat_stream(messages, config):
            chunks.append(chunk)
        
        assert len(chunks) == 4  # 3个内容块 + 1个结束块
        assert chunks[0].delta_content == "来自"
        assert chunks[1].delta_content == "test_provider"
        assert chunks[2].delta_content == "的流式回复"
        assert chunks[3].event_type == ChatStreamEventType.MESSAGE_STOP
        
        # 检查统计信息
        stats = manager._provider_stats["test_provider"]
        assert stats.successful_requests == 1
    
    @pytest.mark.asyncio
    async def test_health_check(self):
        """测试健康检查"""
        manager = UnifiedChatManager({})
        
        provider1 = MockChatProvider("healthy_provider")
        provider2 = MockChatProvider("unhealthy_provider")
        provider2.health_check_result = False
        
        manager.register_provider(provider1)
        manager.register_provider(provider2)
        
        # 执行健康检查
        await manager._perform_health_checks()
        
        # 检查结果
        healthy_result = await manager._check_provider_health("healthy_provider")
        unhealthy_result = await manager._check_provider_health("unhealthy_provider")
        
        assert healthy_result is True
        assert unhealthy_result is False
    
    def test_get_provider_stats(self):
        """测试获取供应商统计信息"""
        manager = UnifiedChatManager({})
        provider = MockChatProvider("test_provider")
        manager.register_provider(provider, weight=1.5)
        
        # 记录一些统计信息
        stats = manager._provider_stats["test_provider"]
        stats.record_success(2.0)
        stats.record_failure("测试错误")
        
        # 获取统计信息
        all_stats = manager.get_provider_stats()
        provider_stats = manager.get_provider_stats("test_provider")
        
        assert "test_provider" in all_stats
        assert "test_provider" in provider_stats
        
        test_stats = provider_stats["test_provider"]
        assert test_stats["total_requests"] == 2
        assert test_stats["successful_requests"] == 1
        assert test_stats["failed_requests"] == 1
        assert test_stats["success_rate"] == 0.5
        assert test_stats["weight"] == 1.5
    
    def test_reset_provider_stats(self):
        """测试重置供应商统计信息"""
        manager = UnifiedChatManager({})
        provider = MockChatProvider("test_provider")
        manager.register_provider(provider)
        
        # 记录一些统计信息
        stats = manager._provider_stats["test_provider"]
        stats.record_failure("测试错误")
        manager._disabled_providers.add("test_provider")
        
        # 重置统计信息
        manager.reset_provider_stats("test_provider")
        
        # 检查重置结果
        reset_stats = manager._provider_stats["test_provider"]
        assert reset_stats.total_requests == 0
        assert reset_stats.failed_requests == 0
        assert "test_provider" not in manager._disabled_providers
    
    @pytest.mark.asyncio
    async def test_validate_config(self):
        """测试配置验证"""
        manager = UnifiedChatManager({})
        provider = MockChatProvider("test_provider")
        manager.register_provider(provider)
        
        config = ChatConfig(temperature=0.5)
        
        # 测试通用配置验证
        is_valid = await manager.validate_config(config)
        assert is_valid is True
        
        # 测试指定供应商的配置验证
        is_valid_specific = await manager.validate_config(config, "test_provider")
        assert is_valid_specific is True
        
        # 测试不存在的供应商
        is_valid_nonexistent = await manager.validate_config(config, "nonexistent_provider")
        assert is_valid_nonexistent is False
    
    @pytest.mark.asyncio
    async def test_get_supported_models(self):
        """测试获取支持的模型"""
        manager = UnifiedChatManager({})
        
        provider1 = MockChatProvider("provider1")
        provider2 = MockChatProvider("provider2")
        
        manager.register_provider(provider1)
        manager.register_provider(provider2)
        
        # 获取所有供应商的模型
        all_models = await manager.get_supported_models()
        
        assert "provider1" in all_models
        assert "provider2" in all_models
        assert "provider1-model-1" in all_models["provider1"]
        assert "provider2-model-2" in all_models["provider2"]
        
        # 获取特定供应商的模型
        provider1_models = await manager.get_supported_models("provider1")
        
        assert "provider1" in provider1_models
        assert "provider2" not in provider1_models
