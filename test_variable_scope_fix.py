#!/usr/bin/env python3
"""
测试变量作用域修复效果

验证 OptimizedTextGenerationRequest 变量作用域问题是否已修复
"""

import os
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

# 设置环境变量
os.environ["PYTHONPATH"] = str(project_root / "src")

def test_import():
    """测试导入"""
    print("🧪 测试导入...")
    
    try:
        from ai_gen_hub.api.routers.text import OptimizedRequest
        print(f"✅ OptimizedRequest 导入成功: {OptimizedRequest}")
        
        from ai_gen_hub.core.interfaces import OptimizedTextGenerationRequest
        print(f"✅ OptimizedTextGenerationRequest 导入成功: {OptimizedTextGenerationRequest}")
        
        # 验证它们是同一个类
        if OptimizedRequest is OptimizedTextGenerationRequest:
            print("✅ OptimizedRequest 和 OptimizedTextGenerationRequest 是同一个类")
        else:
            print("❌ OptimizedRequest 和 OptimizedTextGenerationRequest 不是同一个类")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_from_legacy_request():
    """测试 from_legacy_request 方法"""
    print("\n🧪 测试 from_legacy_request 方法...")
    
    try:
        from ai_gen_hub.api.routers.text import OptimizedRequest
        
        # 测试数据
        test_data = {
            "messages": [{"role": "user", "content": "Hello"}],
            "model": "gemini-2.5-flash",
            "max_tokens": 10,
            "temperature": 0.7
        }
        
        # 调用方法
        result = OptimizedRequest.from_legacy_request(test_data)
        print(f"✅ from_legacy_request 调用成功: {type(result)}")
        print(f"   模型: {result.model}")
        print(f"   消息数量: {len(result.messages)}")
        print(f"   最大token: {result.generation.max_tokens}")
        
        return True
    except Exception as e:
        print(f"❌ from_legacy_request 调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_variable_scope_simulation():
    """模拟路由函数中的变量作用域情况"""
    print("\n🧪 模拟路由函数中的变量作用域...")
    
    try:
        from ai_gen_hub.api.routers.text import OptimizedRequest
        
        # 模拟路由函数的逻辑
        request_data = {
            "messages": [{"role": "user", "content": "Hello"}],
            "model": "gemini-2.5-flash"
        }
        
        # 适配请求格式（模拟路由函数中的逻辑）
        if isinstance(request_data, dict):
            print("转换字典格式为优化版本")
            optimized_request = OptimizedRequest.from_legacy_request(request_data)
            print(f"✅ 请求转换成功: {type(optimized_request)}")
            
            # 模拟异常处理中的重新创建
            try:
                # 模拟可能的异常情况
                optimized_request.stream.enabled = False
                print("✅ 流式配置修改成功")
            except Exception:
                # 兼容性处理：若出现赋值校验问题，构造一个新的请求对象
                optimized_request = OptimizedRequest(**optimized_request.dict())
                optimized_request.stream.enabled = False
                print("✅ 异常处理中的重新创建成功")
        
        return True
    except Exception as e:
        print(f"❌ 变量作用域测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始测试变量作用域修复效果...")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # 测试导入
    if test_import():
        success_count += 1
    
    # 测试 from_legacy_request 方法
    if test_from_legacy_request():
        success_count += 1
    
    # 测试变量作用域模拟
    if test_variable_scope_simulation():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！变量作用域问题已修复")
        return True
    else:
        print("💥 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
