#!/usr/bin/env python3
"""
AI Gen Hub 服务器重启脚本（应用修复）

应用所有修复后重启服务器，并进行验证测试。

使用方法:
    python restart_server_with_fixes.py
"""

import asyncio
import logging
import os
import signal
import subprocess
import sys
import time
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ServerManager:
    """服务器管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.server_process = None
    
    def check_environment(self) -> bool:
        """检查环境配置"""
        logger.info("=== 检查环境配置 ===")
        
        # 检查.env文件
        env_file = self.project_root / ".env"
        if not env_file.exists():
            logger.error("❌ .env文件不存在，请先运行 python quick_fix_server.py")
            return False
        
        # 检查Google AI API密钥
        google_ai_keys = os.environ.get('GOOGLE_AI_API_KEYS', '')
        if not google_ai_keys or 'your-google-ai-api-key' in google_ai_keys:
            logger.error("❌ Google AI API密钥未正确配置")
            logger.error("   请编辑.env文件，设置真实的GOOGLE_AI_API_KEYS")
            return False
        
        logger.info("✅ 环境配置检查通过")
        return True
    
    def kill_existing_server(self) -> bool:
        """终止现有的服务器进程"""
        logger.info("=== 终止现有服务器进程 ===")
        
        try:
            # 查找占用8001端口的进程
            result = subprocess.run(
                ["lsof", "-ti:8001"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    try:
                        pid = int(pid.strip())
                        logger.info(f"终止进程 PID: {pid}")
                        os.kill(pid, signal.SIGTERM)
                        time.sleep(2)  # 等待进程优雅退出
                        
                        # 如果进程仍然存在，强制终止
                        try:
                            os.kill(pid, 0)  # 检查进程是否还存在
                            logger.warning(f"强制终止进程 PID: {pid}")
                            os.kill(pid, signal.SIGKILL)
                        except ProcessLookupError:
                            pass  # 进程已经退出
                            
                    except (ValueError, ProcessLookupError):
                        continue
                
                logger.info("✅ 现有服务器进程已终止")
            else:
                logger.info("ℹ️  没有发现运行中的服务器进程")
            
            return True
            
        except FileNotFoundError:
            logger.warning("⚠️  lsof命令不可用，跳过进程检查")
            return True
        except Exception as e:
            logger.error(f"❌ 终止服务器进程失败: {e}")
            return False
    
    def start_server(self) -> bool:
        """启动服务器"""
        logger.info("=== 启动AI Gen Hub服务器 ===")
        
        try:
            # 设置环境变量
            env = os.environ.copy()
            env['DEBUG'] = 'true'
            env['LOG_LEVEL'] = 'DEBUG'
            
            # 启动服务器 - 使用修复版启动脚本
            cmd = [
                sys.executable, "start_server_fixed.py"
            ]
            
            logger.info(f"执行命令: {' '.join(cmd)}")
            
            self.server_process = subprocess.Popen(
                cmd,
                cwd=self.project_root,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            logger.info(f"✅ 服务器进程已启动，PID: {self.server_process.pid}")
            
            # 等待服务器启动
            logger.info("等待服务器启动...")
            
            # 监控服务器输出
            startup_timeout = 60  # 60秒启动超时
            start_time = time.time()
            
            while time.time() - start_time < startup_timeout:
                if self.server_process.poll() is not None:
                    logger.error("❌ 服务器进程意外退出")
                    return False
                
                # 读取输出
                try:
                    line = self.server_process.stdout.readline()
                    if line:
                        print(f"[SERVER] {line.strip()}")
                        
                        # 检查启动成功的标志
                        if "Application startup complete" in line or "Uvicorn running on" in line:
                            logger.info("✅ 服务器启动成功")
                            return True
                except:
                    pass
                
                time.sleep(0.1)
            
            logger.error("❌ 服务器启动超时")
            return False
            
        except Exception as e:
            logger.error(f"❌ 启动服务器失败: {e}")
            return False
    
    async def verify_server(self) -> bool:
        """验证服务器功能"""
        logger.info("=== 验证服务器功能 ===")
        
        # 等待服务器完全启动
        await asyncio.sleep(5)
        
        try:
            # 运行诊断脚本
            logger.info("运行诊断脚本...")
            
            result = subprocess.run(
                [sys.executable, "diagnose_server.py"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode == 0:
                logger.info("✅ 服务器功能验证通过")
                logger.info("诊断脚本输出:")
                for line in result.stdout.split('\n'):
                    if line.strip():
                        logger.info(f"  {line}")
                return True
            else:
                logger.error("❌ 服务器功能验证失败")
                logger.error("诊断脚本错误输出:")
                for line in result.stderr.split('\n'):
                    if line.strip():
                        logger.error(f"  {line}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ 服务器功能验证超时")
            return False
        except Exception as e:
            logger.error(f"❌ 服务器功能验证异常: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        if self.server_process and self.server_process.poll() is None:
            logger.info("清理服务器进程...")
            self.server_process.terminate()
            try:
                self.server_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.server_process.kill()
    
    async def run_full_restart(self) -> bool:
        """执行完整的重启流程"""
        logger.info("开始AI Gen Hub服务器重启流程...")
        
        try:
            # 1. 检查环境
            if not self.check_environment():
                return False
            
            # 2. 终止现有服务器
            if not self.kill_existing_server():
                return False
            
            # 3. 启动新服务器
            if not self.start_server():
                return False
            
            # 4. 验证服务器功能
            if not await self.verify_server():
                return False
            
            logger.info("🎉 服务器重启和验证完成！")
            logger.info("📍 服务器地址: http://localhost:8001")
            logger.info("📖 API文档: http://localhost:8001/docs")
            logger.info("🏥 健康检查: http://localhost:8001/health")
            
            return True
            
        except KeyboardInterrupt:
            logger.info("用户中断操作")
            return False
        except Exception as e:
            logger.error(f"重启流程异常: {e}")
            return False
        finally:
            # 注意：不在这里清理，让服务器继续运行
            pass


async def main():
    """主函数"""
    manager = ServerManager()
    
    try:
        success = await manager.run_full_restart()
        
        if success:
            logger.info("=== 重启成功 ===")
            logger.info("服务器正在运行，按 Ctrl+C 停止服务器")
            
            # 保持脚本运行，让用户可以手动停止服务器
            try:
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                logger.info("正在停止服务器...")
                manager.cleanup()
                logger.info("服务器已停止")
        else:
            logger.error("=== 重启失败 ===")
            logger.error("请检查错误信息并手动修复问题")
            manager.cleanup()
            return 1
            
    except Exception as e:
        logger.error(f"主程序异常: {e}")
        manager.cleanup()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
