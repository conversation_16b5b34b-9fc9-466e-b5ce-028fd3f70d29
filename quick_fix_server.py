#!/usr/bin/env python3
"""
AI Gen Hub 服务器快速修复工具

用于快速修复常见的服务器问题，包括：
1. 配置问题修复
2. 服务初始化问题修复
3. 依赖服务检查和修复

使用方法:
    python quick_fix_server.py
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import Dict, Any

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ServerQuickFix:
    """服务器快速修复器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.env_file = self.project_root / ".env"
    
    def check_and_create_env_file(self) -> bool:
        """检查并创建.env文件"""
        logger.info("=== 检查和创建.env文件 ===")
        
        if self.env_file.exists():
            logger.info(f".env文件已存在: {self.env_file}")
            return True
        
        logger.info("创建.env文件...")
        
        # 创建基本的.env文件内容
        env_content = """# AI Gen Hub 配置文件
# 请根据需要修改以下配置

# 基础配置
DEBUG=true
LOG_LEVEL=DEBUG
ENVIRONMENT=development

# Google AI 配置
# 请将 your-google-ai-api-key 替换为实际的API密钥
GOOGLE_AI_API_KEYS=your-google-ai-api-key
GOOGLE_AI_ENABLED=true
GOOGLE_AI_TIMEOUT=120

# OpenAI 配置（可选）
# OPENAI_API_KEYS=your-openai-api-key
# OPENAI_ENABLED=false

# Anthropic 配置（可选）
# ANTHROPIC_API_KEYS=your-anthropic-api-key
# ANTHROPIC_ENABLED=false

# 服务配置
API_HOST=0.0.0.0
API_PORT=8001

# 缓存配置（可选）
REDIS_URL=redis://localhost:6379/0
ENABLE_CACHING=false

# 监控配置
ENABLE_METRICS=true
HEALTH_CHECK_INTERVAL=60
"""
        
        try:
            with open(self.env_file, 'w', encoding='utf-8') as f:
                f.write(env_content)
            
            logger.info(f"✅ .env文件创建成功: {self.env_file}")
            logger.warning("⚠️  请编辑.env文件，设置正确的API密钥")
            return True
            
        except Exception as e:
            logger.error(f"❌ .env文件创建失败: {e}")
            return False
    
    def check_google_ai_config(self) -> Dict[str, Any]:
        """检查Google AI配置"""
        logger.info("=== 检查Google AI配置 ===")
        
        api_keys = os.environ.get('GOOGLE_AI_API_KEYS', '')
        enabled = os.environ.get('GOOGLE_AI_ENABLED', 'true').lower() == 'true'
        
        config_status = {
            'api_keys_set': bool(api_keys.strip()),
            'enabled': enabled,
            'keys_count': len([k for k in api_keys.split(',') if k.strip()]) if api_keys else 0
        }
        
        if config_status['api_keys_set']:
            if 'your-google-ai-api-key' in api_keys:
                logger.warning("⚠️  检测到默认API密钥，请设置真实的Google AI API密钥")
                config_status['needs_real_key'] = True
            else:
                logger.info(f"✅ Google AI API密钥已配置 ({config_status['keys_count']} 个)")
                config_status['needs_real_key'] = False
        else:
            logger.error("❌ Google AI API密钥未配置")
            config_status['needs_real_key'] = True
        
        logger.info(f"Google AI启用状态: {enabled}")
        
        return config_status
    
    def fix_import_paths(self) -> bool:
        """修复可能的导入路径问题"""
        logger.info("=== 修复导入路径问题 ===")
        
        try:
            # 确保项目根目录在Python路径中
            project_root_str = str(self.project_root)
            if project_root_str not in sys.path:
                sys.path.insert(0, project_root_str)
                logger.info(f"✅ 添加项目根目录到Python路径: {project_root_str}")
            
            # 确保src目录在Python路径中
            src_dir = self.project_root / "src"
            if src_dir.exists():
                src_dir_str = str(src_dir)
                if src_dir_str not in sys.path:
                    sys.path.insert(0, src_dir_str)
                    logger.info(f"✅ 添加src目录到Python路径: {src_dir_str}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 修复导入路径失败: {e}")
            return False
    
    async def test_basic_imports(self) -> Dict[str, Any]:
        """测试基本模块导入"""
        logger.info("=== 测试基本模块导入 ===")
        
        import_results = {}
        
        # 测试核心模块导入
        modules_to_test = [
            'ai_gen_hub.config.settings',
            'ai_gen_hub.core.logging',
            'ai_gen_hub.providers.google_ai_provider',
            'ai_gen_hub.services.text_generation',
            'ai_gen_hub.api.app'
        ]
        
        for module_name in modules_to_test:
            try:
                __import__(module_name)
                logger.info(f"✅ 模块导入成功: {module_name}")
                import_results[module_name] = True
            except Exception as e:
                logger.error(f"❌ 模块导入失败: {module_name} - {e}")
                import_results[module_name] = False
        
        return import_results
    
    async def test_service_initialization(self) -> Dict[str, Any]:
        """测试服务初始化"""
        logger.info("=== 测试服务初始化 ===")
        
        try:
            from ai_gen_hub.config.settings import get_settings
            from ai_gen_hub.core.logging import setup_logging
            
            # 设置日志
            setup_logging(debug=True)
            logger.info("✅ 日志系统初始化成功")
            
            # 加载配置
            settings = get_settings(debug_logging=True)
            logger.info("✅ 配置加载成功")
            
            # 检查Google AI配置
            if settings.google_ai.enabled and settings.google_ai.api_keys:
                logger.info("✅ Google AI配置有效")
                return {
                    "initialization_success": True,
                    "google_ai_configured": True
                }
            else:
                logger.warning("⚠️  Google AI配置无效或未启用")
                return {
                    "initialization_success": True,
                    "google_ai_configured": False
                }
            
        except Exception as e:
            logger.error(f"❌ 服务初始化失败: {e}")
            return {
                "initialization_success": False,
                "error": str(e)
            }
    
    async def create_minimal_test_server(self) -> bool:
        """创建最小化测试服务器"""
        logger.info("=== 创建最小化测试服务器 ===")
        
        test_server_code = '''#!/usr/bin/env python3
"""
最小化测试服务器
用于验证基本功能是否正常
"""

import asyncio
import logging
from fastapi import FastAPI
from fastapi.responses import JSONResponse
import uvicorn

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(title="AI Gen Hub 测试服务器")

@app.get("/")
async def root():
    return {"message": "AI Gen Hub 测试服务器运行正常"}

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "message": "服务器运行正常"
    }

@app.post("/api/v1/text/generate")
async def test_generate():
    return {
        "id": "test-response",
        "object": "chat.completion",
        "choices": [{
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "这是一个测试响应，表示服务器基本功能正常。"
            },
            "finish_reason": "stop"
        }],
        "usage": {
            "prompt_tokens": 10,
            "completion_tokens": 20,
            "total_tokens": 30
        }
    }

if __name__ == "__main__":
    logger.info("启动最小化测试服务器...")
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="debug")
'''
        
        test_server_file = self.project_root / "test_server.py"
        
        try:
            with open(test_server_file, 'w', encoding='utf-8') as f:
                f.write(test_server_code)
            
            logger.info(f"✅ 最小化测试服务器创建成功: {test_server_file}")
            logger.info("💡 可以运行以下命令启动测试服务器:")
            logger.info(f"   python {test_server_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建测试服务器失败: {e}")
            return False
    
    async def run_quick_fix(self) -> Dict[str, Any]:
        """运行快速修复"""
        logger.info("开始服务器快速修复...")
        
        results = {}
        
        # 1. 修复导入路径
        results["import_paths"] = self.fix_import_paths()
        
        # 2. 检查和创建.env文件
        results["env_file"] = self.check_and_create_env_file()
        
        # 3. 检查Google AI配置
        results["google_ai_config"] = self.check_google_ai_config()
        
        # 4. 测试基本模块导入
        results["module_imports"] = await self.test_basic_imports()
        
        # 5. 测试服务初始化
        results["service_init"] = await self.test_service_initialization()
        
        # 6. 创建测试服务器
        results["test_server"] = await self.create_minimal_test_server()
        
        return results


async def main():
    """主函数"""
    fixer = ServerQuickFix()
    
    try:
        results = await fixer.run_quick_fix()
        
        # 输出修复结果摘要
        logger.info("=== 快速修复结果摘要 ===")
        
        success_count = 0
        total_count = 0
        
        for category, result in results.items():
            total_count += 1
            if isinstance(result, bool):
                status = "✅ 成功" if result else "❌ 失败"
                if result:
                    success_count += 1
            elif isinstance(result, dict):
                # 对于复杂结果，检查是否有成功指标
                if result.get('initialization_success', False) or \
                   result.get('api_keys_set', False) or \
                   any(result.values()) if isinstance(result, dict) else False:
                    status = "✅ 部分成功"
                    success_count += 1
                else:
                    status = "❌ 失败"
            else:
                status = "⚠️  未知"
            
            logger.info(f"{category}: {status}")
        
        logger.info(f"总体成功率: {success_count}/{total_count}")
        
        # 提供下一步建议
        logger.info("=== 下一步建议 ===")
        
        google_ai_config = results.get("google_ai_config", {})
        if google_ai_config.get("needs_real_key", True):
            logger.info("1. 🔧 设置真实的Google AI API密钥:")
            logger.info("   - 编辑.env文件")
            logger.info("   - 将GOOGLE_AI_API_KEYS设置为真实的API密钥")
        
        service_init = results.get("service_init", {})
        if not service_init.get("initialization_success", False):
            logger.info("2. 🔧 修复服务初始化问题:")
            logger.info("   - 检查依赖是否正确安装")
            logger.info("   - 运行: pip install -e .")
        
        logger.info("3. 🧪 测试修复效果:")
        logger.info("   - 运行诊断工具: python diagnose_server.py")
        logger.info("   - 或启动测试服务器: python test_server.py")
        logger.info("   - 启动完整服务器: python -m ai_gen_hub.main serve --debug")

        logger.info("4. 📖 查看详细指南:")
        logger.info("   - 阅读: API接口卡住问题解决指南.md")
        
        return results
        
    except Exception as e:
        logger.error(f"快速修复过程中发生错误: {e}")
        return {"error": str(e)}


if __name__ == "__main__":
    asyncio.run(main())
