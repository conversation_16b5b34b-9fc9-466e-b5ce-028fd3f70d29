"""
AI Gen Hub Python SDK 主客户端
"""

import asyncio
import json
import time
from typing import Optional, Dict, Any, List, Union, AsyncIterator
from urllib.parse import urljoin

import httpx

from .exceptions import (
    AIGenHubError,
    AuthenticationError,
    RateLimitError,
    QuotaExceededError,
    ProviderError,
    ValidationError
)
from .models import (
    TextGenerationRequest,
    TextGenerationResponse,
    ImageGenerationRequest,
    ImageGenerationResponse,
    Message
)
from .services import TextService, ImageService, AuthService


class AIGenHubClient:
    """AI Gen Hub Python SDK 主客户端"""
    
    def __init__(
        self,
        base_url: str = "http://localhost:8001",
        api_key: Optional[str] = None,
        username: Optional[str] = None,
        password: Optional[str] = None,
        timeout: float = 30.0,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ):
        """
        初始化AI Gen Hub客户端
        
        Args:
            base_url: API基础URL
            api_key: API密钥（推荐）
            username: 用户名（与password一起使用）
            password: 密码（与username一起使用）
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # 创建HTTP客户端
        self._client = httpx.Client(
            timeout=timeout,
            headers={
                "User-Agent": f"ai-gen-hub-sdk-python/1.0.0",
                "Content-Type": "application/json"
            }
        )
        
        # 异步HTTP客户端
        self._async_client = httpx.AsyncClient(
            timeout=timeout,
            headers={
                "User-Agent": f"ai-gen-hub-sdk-python/1.0.0",
                "Content-Type": "application/json"
            }
        )
        
        # 认证信息
        self._api_key = api_key
        self._username = username
        self._password = password
        self._access_token: Optional[str] = None
        self._token_expires_at: Optional[float] = None
        
        # 初始化服务
        self.auth = AuthService(self)
        self.text = TextService(self)
        self.image = ImageService(self)
        
        # 如果提供了用户名密码，立即登录
        if username and password and not api_key:
            self._login()
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {
            "Content-Type": "application/json"
        }
        
        if self._api_key:
            headers["X-API-Key"] = self._api_key
        elif self._access_token:
            headers["Authorization"] = f"Bearer {self._access_token}"
        
        return headers
    
    def _login(self) -> None:
        """用户登录获取访问令牌"""
        if not self._username or not self._password:
            raise AuthenticationError("需要提供用户名和密码")
        
        url = urljoin(self.base_url, "/api/v1/auth/login")
        data = {
            "username": self._username,
            "password": self._password
        }
        
        try:
            response = self._client.post(url, json=data)
            self._handle_response(response)
            
            result = response.json()
            self._access_token = result["access_token"]
            # 假设token有效期为1小时
            self._token_expires_at = time.time() + 3600
            
        except Exception as e:
            raise AuthenticationError(f"登录失败: {e}")
    
    def _ensure_authenticated(self) -> None:
        """确保已认证"""
        if self._api_key:
            return
        
        if not self._access_token or (
            self._token_expires_at and time.time() >= self._token_expires_at
        ):
            self._login()
    
    def _handle_response(self, response: httpx.Response) -> None:
        """处理HTTP响应"""
        if response.status_code == 200:
            return
        
        try:
            error_data = response.json()
            error_message = error_data.get("error", {}).get("message", "未知错误")
            error_code = error_data.get("error", {}).get("code", "UNKNOWN_ERROR")
        except:
            error_message = response.text or f"HTTP {response.status_code}"
            error_code = "HTTP_ERROR"
        
        if response.status_code == 401:
            raise AuthenticationError(error_message)
        elif response.status_code == 429:
            if "rate limit" in error_message.lower():
                raise RateLimitError(error_message)
            else:
                raise QuotaExceededError(error_message)
        elif response.status_code == 400:
            raise ValidationError(error_message)
        elif response.status_code >= 500:
            raise ProviderError(error_message)
        else:
            raise AIGenHubError(f"{error_code}: {error_message}")
    
    def _request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        stream: bool = False
    ) -> httpx.Response:
        """发送HTTP请求"""
        self._ensure_authenticated()
        
        url = urljoin(self.base_url, endpoint)
        headers = self._get_headers()
        
        for attempt in range(self.max_retries + 1):
            try:
                if stream:
                    headers["Accept"] = "text/event-stream"
                
                response = self._client.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data,
                    params=params,
                    stream=stream
                )
                
                if not stream:
                    self._handle_response(response)
                
                return response
                
            except (httpx.TimeoutException, httpx.ConnectError) as e:
                if attempt == self.max_retries:
                    raise AIGenHubError(f"请求失败: {e}")
                
                time.sleep(self.retry_delay * (2 ** attempt))
                continue
    
    async def _async_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        stream: bool = False
    ) -> httpx.Response:
        """发送异步HTTP请求"""
        # 异步版本的认证检查
        if self._api_key:
            pass
        elif not self._access_token or (
            self._token_expires_at and time.time() >= self._token_expires_at
        ):
            await self._async_login()
        
        url = urljoin(self.base_url, endpoint)
        headers = self._get_headers()
        
        for attempt in range(self.max_retries + 1):
            try:
                if stream:
                    headers["Accept"] = "text/event-stream"
                
                response = await self._async_client.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data,
                    params=params
                )
                
                if not stream:
                    self._handle_response(response)
                
                return response
                
            except (httpx.TimeoutException, httpx.ConnectError) as e:
                if attempt == self.max_retries:
                    raise AIGenHubError(f"请求失败: {e}")
                
                await asyncio.sleep(self.retry_delay * (2 ** attempt))
                continue
    
    async def _async_login(self) -> None:
        """异步用户登录"""
        if not self._username or not self._password:
            raise AuthenticationError("需要提供用户名和密码")
        
        url = urljoin(self.base_url, "/api/v1/auth/login")
        data = {
            "username": self._username,
            "password": self._password
        }
        
        try:
            response = await self._async_client.post(url, json=data)
            self._handle_response(response)
            
            result = response.json()
            self._access_token = result["access_token"]
            self._token_expires_at = time.time() + 3600
            
        except Exception as e:
            raise AuthenticationError(f"登录失败: {e}")
    
    def close(self) -> None:
        """关闭客户端连接"""
        self._client.close()
    
    async def aclose(self) -> None:
        """异步关闭客户端连接"""
        await self._async_client.aclose()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.aclose()
