"""
AI Gen Hub SDK 异常类定义
"""


class AIGenHubError(Exception):
    """AI Gen Hub SDK 基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class AuthenticationError(AIGenHubError):
    """认证错误
    
    当API密钥无效、JWT token过期或用户名密码错误时抛出
    """
    pass


class AuthorizationError(AIGenHubError):
    """授权错误
    
    当用户没有权限访问特定资源时抛出
    """
    pass


class ValidationError(AIGenHubError):
    """请求验证错误
    
    当请求参数无效或格式错误时抛出
    """
    
    def __init__(self, message: str, field: str = None, **kwargs):
        super().__init__(message, **kwargs)
        self.field = field


class RateLimitError(AIGenHubError):
    """请求频率限制错误
    
    当请求频率超过限制时抛出
    """
    
    def __init__(self, message: str, retry_after: int = None, **kwargs):
        super().__init__(message, **kwargs)
        self.retry_after = retry_after


class QuotaExceededError(AIGenHubError):
    """配额超限错误
    
    当API使用量超过配额时抛出
    """
    
    def __init__(self, message: str, quota_type: str = None, **kwargs):
        super().__init__(message, **kwargs)
        self.quota_type = quota_type


class ProviderError(AIGenHubError):
    """AI供应商错误
    
    当上游AI服务出现错误时抛出
    """
    
    def __init__(self, message: str, provider: str = None, **kwargs):
        super().__init__(message, **kwargs)
        self.provider = provider


class NetworkError(AIGenHubError):
    """网络错误
    
    当网络连接失败或超时时抛出
    """
    pass


class TimeoutError(NetworkError):
    """请求超时错误"""
    pass


class ServerError(AIGenHubError):
    """服务器内部错误
    
    当服务器返回5xx状态码时抛出
    """
    
    def __init__(self, message: str, status_code: int = None, **kwargs):
        super().__init__(message, **kwargs)
        self.status_code = status_code


class ModelNotFoundError(AIGenHubError):
    """模型不存在错误
    
    当请求的AI模型不存在或不可用时抛出
    """
    
    def __init__(self, message: str, model: str = None, **kwargs):
        super().__init__(message, **kwargs)
        self.model = model


class ContentFilterError(AIGenHubError):
    """内容过滤错误
    
    当生成的内容被安全过滤器拦截时抛出
    """
    pass


class TokenLimitError(ValidationError):
    """Token限制错误
    
    当请求的token数量超过模型限制时抛出
    """
    
    def __init__(self, message: str, max_tokens: int = None, requested_tokens: int = None, **kwargs):
        super().__init__(message, **kwargs)
        self.max_tokens = max_tokens
        self.requested_tokens = requested_tokens


# 异常映射表，用于根据错误代码创建对应的异常
ERROR_CODE_MAPPING = {
    "AUTHENTICATION_FAILED": AuthenticationError,
    "INVALID_API_KEY": AuthenticationError,
    "TOKEN_EXPIRED": AuthenticationError,
    "PERMISSION_DENIED": AuthorizationError,
    "INSUFFICIENT_PERMISSIONS": AuthorizationError,
    "INVALID_REQUEST": ValidationError,
    "MISSING_PARAMETER": ValidationError,
    "INVALID_PARAMETER": ValidationError,
    "RATE_LIMIT_EXCEEDED": RateLimitError,
    "QUOTA_EXCEEDED": QuotaExceededError,
    "DAILY_QUOTA_EXCEEDED": QuotaExceededError,
    "MONTHLY_QUOTA_EXCEEDED": QuotaExceededError,
    "PROVIDER_ERROR": ProviderError,
    "PROVIDER_UNAVAILABLE": ProviderError,
    "MODEL_NOT_FOUND": ModelNotFoundError,
    "MODEL_UNAVAILABLE": ModelNotFoundError,
    "CONTENT_FILTERED": ContentFilterError,
    "TOKEN_LIMIT_EXCEEDED": TokenLimitError,
    "NETWORK_ERROR": NetworkError,
    "TIMEOUT": TimeoutError,
    "INTERNAL_ERROR": ServerError,
    "SERVICE_UNAVAILABLE": ServerError,
}


def create_exception_from_error_code(error_code: str, message: str, **kwargs) -> AIGenHubError:
    """根据错误代码创建对应的异常实例"""
    exception_class = ERROR_CODE_MAPPING.get(error_code, AIGenHubError)
    return exception_class(message, error_code=error_code, **kwargs)
