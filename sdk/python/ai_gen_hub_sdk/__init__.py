"""
AI Gen Hub Python SDK

一个简单易用的Python SDK，用于访问AI Gen Hub的各种AI服务。

示例用法:
    from ai_gen_hub_sdk import AIGenHubClient
    
    # 使用API密钥初始化
    client = AIGenHubClient(
        base_url="http://localhost:8001",
        api_key="your_api_key"
    )
    
    # 或使用用户名密码
    client = AIGenHubClient(
        base_url="http://localhost:8001",
        username="your_username",
        password="your_password"
    )
    
    # 生成文本
    response = client.text.generate(
        messages=[
            {"role": "user", "content": "Hello, world!"}
        ],
        model="gemini-pro"
    )
    
    print(response.content)
"""

from .client import AIGenHubClient
from .exceptions import (
    AIGenHubError,
    AuthenticationError,
    RateLimitError,
    QuotaExceededError,
    ProviderError,
    ValidationError
)
from .models import (
    TextGenerationRequest,
    TextGenerationResponse,
    ImageGenerationRequest,
    ImageGenerationResponse,
    Message,
    Usage
)

__version__ = "1.0.0"
__author__ = "AI Gen Hub Team"
__email__ = "<EMAIL>"

__all__ = [
    # 主要客户端
    "AIGenHubClient",
    
    # 异常类
    "AIGenHubError",
    "AuthenticationError", 
    "RateLimitError",
    "QuotaExceededError",
    "ProviderError",
    "ValidationError",
    
    # 数据模型
    "TextGenerationRequest",
    "TextGenerationResponse",
    "ImageGenerationRequest", 
    "ImageGenerationResponse",
    "Message",
    "Usage",
    
    # 版本信息
    "__version__",
    "__author__",
    "__email__"
]
