"""
AI Gen Hub SDK 数据模型定义
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime


@dataclass
class Message:
    """对话消息"""
    role: str  # "system", "user", "assistant"
    content: str
    name: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "role": self.role,
            "content": self.content
        }
        if self.name:
            result["name"] = self.name
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Message":
        """从字典创建实例"""
        return cls(
            role=data["role"],
            content=data["content"],
            name=data.get("name")
        )


@dataclass
class Usage:
    """Token使用统计"""
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Usage":
        """从字典创建实例"""
        return cls(
            prompt_tokens=data.get("prompt_tokens", 0),
            completion_tokens=data.get("completion_tokens", 0),
            total_tokens=data.get("total_tokens", 0)
        )


@dataclass
class TextGenerationRequest:
    """文本生成请求"""
    messages: List[Message]
    model: str
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    top_p: Optional[float] = None
    frequency_penalty: Optional[float] = None
    presence_penalty: Optional[float] = None
    stop: Optional[Union[str, List[str]]] = None
    stream: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "messages": [msg.to_dict() for msg in self.messages],
            "model": self.model,
            "stream": self.stream
        }
        
        # 只包含非None的可选参数
        optional_params = {
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "frequency_penalty": self.frequency_penalty,
            "presence_penalty": self.presence_penalty,
            "stop": self.stop
        }
        
        for key, value in optional_params.items():
            if value is not None:
                result[key] = value
        
        return result


@dataclass
class TextGenerationResponse:
    """文本生成响应"""
    id: str
    content: str
    model: str
    usage: Usage
    finish_reason: str
    created_at: datetime
    provider: str
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "TextGenerationResponse":
        """从字典创建实例"""
        return cls(
            id=data["id"],
            content=data["content"],
            model=data["model"],
            usage=Usage.from_dict(data["usage"]),
            finish_reason=data["finish_reason"],
            created_at=datetime.fromisoformat(data["created_at"].replace("Z", "+00:00")),
            provider=data["provider"]
        )


@dataclass
class StreamChunk:
    """流式响应数据块"""
    type: str  # "start", "content", "end"
    content: Optional[str] = None
    usage: Optional[Usage] = None
    finish_reason: Optional[str] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "StreamChunk":
        """从字典创建实例"""
        usage = None
        if "usage" in data and data["usage"]:
            usage = Usage.from_dict(data["usage"])
        
        return cls(
            type=data["type"],
            content=data.get("content"),
            usage=usage,
            finish_reason=data.get("finish_reason")
        )


@dataclass
class ImageGenerationRequest:
    """图像生成请求"""
    prompt: str
    model: str
    size: Optional[str] = None  # "256x256", "512x512", "1024x1024"
    quality: Optional[str] = None  # "standard", "hd"
    style: Optional[str] = None  # "vivid", "natural"
    n: int = 1  # 生成图片数量
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "prompt": self.prompt,
            "model": self.model,
            "n": self.n
        }
        
        optional_params = {
            "size": self.size,
            "quality": self.quality,
            "style": self.style
        }
        
        for key, value in optional_params.items():
            if value is not None:
                result[key] = value
        
        return result


@dataclass
class ImageData:
    """图像数据"""
    url: Optional[str] = None
    b64_json: Optional[str] = None
    revised_prompt: Optional[str] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ImageData":
        """从字典创建实例"""
        return cls(
            url=data.get("url"),
            b64_json=data.get("b64_json"),
            revised_prompt=data.get("revised_prompt")
        )


@dataclass
class ImageGenerationResponse:
    """图像生成响应"""
    id: str
    images: List[ImageData]
    model: str
    created_at: datetime
    provider: str
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ImageGenerationResponse":
        """从字典创建实例"""
        return cls(
            id=data["id"],
            images=[ImageData.from_dict(img) for img in data["images"]],
            model=data["model"],
            created_at=datetime.fromisoformat(data["created_at"].replace("Z", "+00:00")),
            provider=data["provider"]
        )


@dataclass
class Model:
    """AI模型信息"""
    id: str
    name: str
    provider: str
    max_tokens: int
    supports_streaming: bool = False
    supports_functions: bool = False
    pricing: Optional[Dict[str, float]] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Model":
        """从字典创建实例"""
        return cls(
            id=data["id"],
            name=data["name"],
            provider=data["provider"],
            max_tokens=data["max_tokens"],
            supports_streaming=data.get("supports_streaming", False),
            supports_functions=data.get("supports_functions", False),
            pricing=data.get("pricing")
        )


@dataclass
class User:
    """用户信息"""
    id: str
    username: str
    email: str
    role: str
    status: str
    api_quota: int
    api_quota_used: int
    created_at: datetime
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "User":
        """从字典创建实例"""
        return cls(
            id=data["id"],
            username=data["username"],
            email=data["email"],
            role=data["role"],
            status=data["status"],
            api_quota=data["api_quota"],
            api_quota_used=data["api_quota_used"],
            created_at=datetime.fromisoformat(data["created_at"].replace("Z", "+00:00"))
        )


@dataclass
class APIResponse:
    """通用API响应"""
    success: bool
    data: Any
    message: Optional[str] = None
    request_id: Optional[str] = None
    timestamp: Optional[datetime] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "APIResponse":
        """从字典创建实例"""
        timestamp = None
        if "timestamp" in data and data["timestamp"]:
            timestamp = datetime.fromisoformat(data["timestamp"].replace("Z", "+00:00"))
        
        return cls(
            success=data["success"],
            data=data.get("data"),
            message=data.get("message"),
            request_id=data.get("request_id"),
            timestamp=timestamp
        )
