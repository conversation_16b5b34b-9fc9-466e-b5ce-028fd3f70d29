"""
AI Gen Hub SDK 服务类
"""

import json
from typing import List, Optional, Dict, Any, Iterator, AsyncIterator, Union

from .models import (
    Message,
    TextGenerationRequest,
    TextGenerationResponse,
    ImageGenerationRequest,
    ImageGenerationResponse,
    StreamChunk,
    Model,
    User
)
from .exceptions import AIGenHubError


class BaseService:
    """基础服务类"""
    
    def __init__(self, client):
        self.client = client


class AuthService(BaseService):
    """认证服务"""
    
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """用户登录"""
        response = self.client._request(
            "POST",
            "/api/v1/auth/login",
            data={"username": username, "password": password}
        )
        return response.json()
    
    def register(self, username: str, email: str, password: str, **kwargs) -> Dict[str, Any]:
        """用户注册"""
        data = {
            "username": username,
            "email": email,
            "password": password,
            **kwargs
        }
        response = self.client._request("POST", "/api/v1/auth/register", data=data)
        return response.json()
    
    def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """刷新访问令牌"""
        response = self.client._request(
            "POST",
            "/api/v1/auth/refresh",
            data={"refresh_token": refresh_token}
        )
        return response.json()
    
    def get_profile(self) -> User:
        """获取用户资料"""
        response = self.client._request("GET", "/api/v1/auth/profile")
        data = response.json()["data"]
        return User.from_dict(data)


class TextService(BaseService):
    """文本生成服务"""
    
    def generate(
        self,
        messages: List[Union[Message, Dict[str, str]]],
        model: str,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        frequency_penalty: Optional[float] = None,
        presence_penalty: Optional[float] = None,
        stop: Optional[Union[str, List[str]]] = None
    ) -> TextGenerationResponse:
        """生成文本"""
        # 转换消息格式
        formatted_messages = []
        for msg in messages:
            if isinstance(msg, dict):
                formatted_messages.append(Message.from_dict(msg))
            else:
                formatted_messages.append(msg)
        
        request = TextGenerationRequest(
            messages=formatted_messages,
            model=model,
            max_tokens=max_tokens,
            temperature=temperature,
            top_p=top_p,
            frequency_penalty=frequency_penalty,
            presence_penalty=presence_penalty,
            stop=stop
        )
        
        response = self.client._request(
            "POST",
            "/api/v1/text/generate",
            data=request.to_dict()
        )
        
        result = response.json()
        return TextGenerationResponse.from_dict(result["data"])
    
    def stream(
        self,
        messages: List[Union[Message, Dict[str, str]]],
        model: str,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        **kwargs
    ) -> Iterator[StreamChunk]:
        """流式生成文本"""
        # 转换消息格式
        formatted_messages = []
        for msg in messages:
            if isinstance(msg, dict):
                formatted_messages.append(Message.from_dict(msg))
            else:
                formatted_messages.append(msg)
        
        request = TextGenerationRequest(
            messages=formatted_messages,
            model=model,
            max_tokens=max_tokens,
            temperature=temperature,
            stream=True,
            **kwargs
        )
        
        response = self.client._request(
            "POST",
            "/api/v1/text/stream",
            data=request.to_dict(),
            stream=True
        )
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    try:
                        data = json.loads(line[6:])
                        yield StreamChunk.from_dict(data)
                    except json.JSONDecodeError:
                        continue
    
    async def agenerate(
        self,
        messages: List[Union[Message, Dict[str, str]]],
        model: str,
        **kwargs
    ) -> TextGenerationResponse:
        """异步生成文本"""
        # 转换消息格式
        formatted_messages = []
        for msg in messages:
            if isinstance(msg, dict):
                formatted_messages.append(Message.from_dict(msg))
            else:
                formatted_messages.append(msg)
        
        request = TextGenerationRequest(
            messages=formatted_messages,
            model=model,
            **kwargs
        )
        
        response = await self.client._async_request(
            "POST",
            "/api/v1/text/generate",
            data=request.to_dict()
        )
        
        result = response.json()
        return TextGenerationResponse.from_dict(result["data"])
    
    async def astream(
        self,
        messages: List[Union[Message, Dict[str, str]]],
        model: str,
        **kwargs
    ) -> AsyncIterator[StreamChunk]:
        """异步流式生成文本"""
        # 转换消息格式
        formatted_messages = []
        for msg in messages:
            if isinstance(msg, dict):
                formatted_messages.append(Message.from_dict(msg))
            else:
                formatted_messages.append(msg)
        
        request = TextGenerationRequest(
            messages=formatted_messages,
            model=model,
            stream=True,
            **kwargs
        )
        
        response = await self.client._async_request(
            "POST",
            "/api/v1/text/stream",
            data=request.to_dict(),
            stream=True
        )
        
        async for line in response.aiter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    try:
                        data = json.loads(line[6:])
                        yield StreamChunk.from_dict(data)
                    except json.JSONDecodeError:
                        continue
    
    def get_models(self) -> List[Model]:
        """获取可用模型列表"""
        response = self.client._request("GET", "/api/v1/text/models")
        result = response.json()
        return [Model.from_dict(model) for model in result["data"]["models"]]
    
    def chat(
        self,
        message: str,
        model: str,
        conversation_id: Optional[str] = None,
        context_length: int = 10
    ) -> Dict[str, Any]:
        """对话生成"""
        data = {
            "message": message,
            "model": model,
            "context_length": context_length
        }
        if conversation_id:
            data["conversation_id"] = conversation_id
        
        response = self.client._request("POST", "/api/v1/text/chat", data=data)
        return response.json()["data"]
    
    def complete(
        self,
        prompt: str,
        model: str,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None
    ) -> Dict[str, Any]:
        """文本补全"""
        data = {
            "prompt": prompt,
            "model": model
        }
        if max_tokens is not None:
            data["max_tokens"] = max_tokens
        if temperature is not None:
            data["temperature"] = temperature
        
        response = self.client._request("POST", "/api/v1/text/complete", data=data)
        return response.json()["data"]


class ImageService(BaseService):
    """图像生成服务"""
    
    def generate(
        self,
        prompt: str,
        model: str,
        size: Optional[str] = None,
        quality: Optional[str] = None,
        style: Optional[str] = None,
        n: int = 1
    ) -> ImageGenerationResponse:
        """生成图像"""
        request = ImageGenerationRequest(
            prompt=prompt,
            model=model,
            size=size,
            quality=quality,
            style=style,
            n=n
        )
        
        response = self.client._request(
            "POST",
            "/api/v1/image/generate",
            data=request.to_dict()
        )
        
        result = response.json()
        return ImageGenerationResponse.from_dict(result["data"])
    
    async def agenerate(
        self,
        prompt: str,
        model: str,
        **kwargs
    ) -> ImageGenerationResponse:
        """异步生成图像"""
        request = ImageGenerationRequest(
            prompt=prompt,
            model=model,
            **kwargs
        )
        
        response = await self.client._async_request(
            "POST",
            "/api/v1/image/generate",
            data=request.to_dict()
        )
        
        result = response.json()
        return ImageGenerationResponse.from_dict(result["data"])
    
    def get_models(self) -> List[Model]:
        """获取可用图像模型列表"""
        response = self.client._request("GET", "/api/v1/image/models")
        result = response.json()
        return [Model.from_dict(model) for model in result["data"]["models"]]
