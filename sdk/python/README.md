# AI Gen Hub Python SDK

[![PyPI version](https://badge.fury.io/py/ai-gen-hub-sdk.svg)](https://badge.fury.io/py/ai-gen-hub-sdk)
[![Python](https://img.shields.io/pypi/pyversions/ai-gen-hub-sdk.svg)](https://pypi.org/project/ai-gen-hub-sdk/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

AI Gen Hub Python SDK 是一个简单易用的Python客户端库，用于访问AI Gen Hub的各种AI服务，包括文本生成、图像生成等功能。

## ✨ 特性

- 🚀 **简单易用** - 直观的API设计，几行代码即可开始使用
- 🔄 **同步/异步支持** - 同时支持同步和异步操作
- 🌊 **流式输出** - 支持实时流式文本生成
- 🔐 **多种认证方式** - 支持API密钥和用户名密码认证
- 🛡️ **错误处理** - 完善的异常处理和重试机制
- 📝 **类型提示** - 完整的类型注解，IDE友好
- 🧪 **测试覆盖** - 高测试覆盖率，保证代码质量

## 📦 安装

```bash
pip install ai-gen-hub-sdk
```

或者从源码安装：

```bash
git clone https://github.com/your-org/ai-gen-hub.git
cd ai-gen-hub/sdk/python
pip install -e .
```

## 🚀 快速开始

### 基础用法

```python
from ai_gen_hub_sdk import AIGenHubClient

# 使用API密钥初始化客户端
client = AIGenHubClient(
    base_url="http://localhost:8001",
    api_key="your_api_key"
)

# 生成文本
response = client.text.generate(
    messages=[
        {"role": "user", "content": "写一首关于春天的诗"}
    ],
    model="gemini-pro",
    max_tokens=500
)

print(response.content)
```

### 使用用户名密码认证

```python
client = AIGenHubClient(
    base_url="http://localhost:8001",
    username="your_username",
    password="your_password"
)

response = client.text.generate(
    messages=[
        {"role": "system", "content": "你是一个有用的AI助手"},
        {"role": "user", "content": "解释什么是人工智能"}
    ],
    model="gemini-pro"
)

print(response.content)
```

### 流式文本生成

```python
# 流式生成，实时获取结果
for chunk in client.text.stream(
    messages=[
        {"role": "user", "content": "详细解释机器学习的工作原理"}
    ],
    model="gemini-pro"
):
    if chunk.type == "content":
        print(chunk.content, end="", flush=True)
    elif chunk.type == "end":
        print(f"\n\n使用token: {chunk.usage.total_tokens}")
```

### 异步操作

```python
import asyncio
from ai_gen_hub_sdk import AIGenHubClient

async def main():
    async with AIGenHubClient(
        base_url="http://localhost:8001",
        api_key="your_api_key"
    ) as client:
        # 异步文本生成
        response = await client.text.agenerate(
            messages=[
                {"role": "user", "content": "Hello, world!"}
            ],
            model="gemini-pro"
        )
        print(response.content)
        
        # 异步流式生成
        async for chunk in client.text.astream(
            messages=[
                {"role": "user", "content": "写一个Python函数"}
            ],
            model="gemini-pro"
        ):
            if chunk.type == "content":
                print(chunk.content, end="", flush=True)

asyncio.run(main())
```

### 图像生成

```python
# 生成图像
response = client.image.generate(
    prompt="一只可爱的小猫在花园里玩耍",
    model="dall-e-3",
    size="1024x1024",
    quality="hd"
)

# 获取图像URL
image_url = response.images[0].url
print(f"生成的图像: {image_url}")
```

## 📚 详细用法

### 文本生成参数

```python
response = client.text.generate(
    messages=[
        {"role": "system", "content": "你是一个创意写作助手"},
        {"role": "user", "content": "写一个科幻故事的开头"}
    ],
    model="gemini-pro",
    max_tokens=1000,        # 最大生成token数
    temperature=0.8,        # 控制随机性 (0.0-2.0)
    top_p=0.9,             # 核采样参数 (0.0-1.0)
    frequency_penalty=0.1,  # 频率惩罚 (-2.0-2.0)
    presence_penalty=0.1,   # 存在惩罚 (-2.0-2.0)
    stop=["END", "\n\n"]   # 停止序列
)
```

### 对话管理

```python
# 持续对话
conversation_id = None

while True:
    user_input = input("用户: ")
    if user_input.lower() in ['quit', 'exit']:
        break
    
    response = client.text.chat(
        message=user_input,
        model="gemini-pro",
        conversation_id=conversation_id,
        context_length=10  # 保留最近10轮对话
    )
    
    conversation_id = response["conversation_id"]
    print(f"助手: {response['content']}")
```

### 获取可用模型

```python
# 获取文本生成模型
text_models = client.text.get_models()
for model in text_models:
    print(f"模型: {model.name} ({model.id})")
    print(f"供应商: {model.provider}")
    print(f"最大token: {model.max_tokens}")
    print(f"支持流式: {model.supports_streaming}")
    print("---")

# 获取图像生成模型
image_models = client.image.get_models()
for model in image_models:
    print(f"图像模型: {model.name} ({model.id})")
```

### 错误处理

```python
from ai_gen_hub_sdk import (
    AIGenHubError,
    AuthenticationError,
    RateLimitError,
    QuotaExceededError,
    ValidationError
)

try:
    response = client.text.generate(
        messages=[{"role": "user", "content": "Hello"}],
        model="invalid-model"
    )
except AuthenticationError:
    print("认证失败，请检查API密钥")
except ValidationError as e:
    print(f"请求参数错误: {e.message}")
except RateLimitError as e:
    print(f"请求频率超限，请等待 {e.retry_after} 秒")
except QuotaExceededError:
    print("API配额已用完")
except AIGenHubError as e:
    print(f"API错误: {e.message}")
```

### 自定义配置

```python
client = AIGenHubClient(
    base_url="http://localhost:8001",
    api_key="your_api_key",
    timeout=60.0,      # 请求超时时间（秒）
    max_retries=3,     # 最大重试次数
    retry_delay=1.0    # 重试延迟时间（秒）
)
```

## 🔧 高级用法

### 批量处理

```python
import asyncio

async def batch_generate(prompts):
    async with AIGenHubClient(api_key="your_api_key") as client:
        tasks = []
        for prompt in prompts:
            task = client.text.agenerate(
                messages=[{"role": "user", "content": prompt}],
                model="gemini-pro"
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        return results

prompts = [
    "解释量子计算",
    "什么是区块链",
    "人工智能的未来"
]

results = asyncio.run(batch_generate(prompts))
for i, result in enumerate(results):
    print(f"问题 {i+1}: {result.content[:100]}...")
```

### 自定义HTTP客户端

```python
import httpx

# 使用自定义HTTP客户端（例如配置代理）
custom_client = httpx.Client(
    proxies="http://proxy.example.com:8080",
    verify=False  # 跳过SSL验证（不推荐在生产环境使用）
)

client = AIGenHubClient(
    base_url="http://localhost:8001",
    api_key="your_api_key"
)
client._client = custom_client
```

## 📖 API参考

### AIGenHubClient

主客户端类，提供对所有服务的访问。

#### 初始化参数

- `base_url` (str): API基础URL
- `api_key` (str, 可选): API密钥
- `username` (str, 可选): 用户名
- `password` (str, 可选): 密码
- `timeout` (float): 请求超时时间，默认30秒
- `max_retries` (int): 最大重试次数，默认3次
- `retry_delay` (float): 重试延迟时间，默认1秒

#### 服务属性

- `client.text`: 文本生成服务
- `client.image`: 图像生成服务
- `client.auth`: 认证服务

### TextService

文本生成服务类。

#### 方法

- `generate()`: 生成文本
- `stream()`: 流式生成文本
- `agenerate()`: 异步生成文本
- `astream()`: 异步流式生成文本
- `get_models()`: 获取可用模型
- `chat()`: 对话生成
- `complete()`: 文本补全

### ImageService

图像生成服务类。

#### 方法

- `generate()`: 生成图像
- `agenerate()`: 异步生成图像
- `get_models()`: 获取可用模型

## 🧪 测试

```bash
# 安装开发依赖
pip install -e ".[dev]"

# 运行测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=ai_gen_hub_sdk --cov-report=html
```

## 🤝 贡献

欢迎贡献代码！请查看 [贡献指南](../../CONTRIBUTING.md) 了解详情。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../../LICENSE) 文件了解详情。

## 🆘 支持

- 📖 [完整文档](https://docs.ai-gen-hub.com)
- 🐛 [问题反馈](https://github.com/your-org/ai-gen-hub/issues)
- 💬 [讨论区](https://github.com/your-org/ai-gen-hub/discussions)
- 📧 [邮件支持](mailto:<EMAIL>)
