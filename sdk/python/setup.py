"""
AI Gen Hub Python SDK 安装配置
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), "README.md")
    if os.path.exists(readme_path):
        with open(readme_path, "r", encoding="utf-8") as f:
            return f.read()
    return ""

# 读取requirements文件
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), "requirements.txt")
    if os.path.exists(requirements_path):
        with open(requirements_path, "r", encoding="utf-8") as f:
            return [line.strip() for line in f if line.strip() and not line.startswith("#")]
    return []

setup(
    name="ai-gen-hub-sdk",
    version="1.0.0",
    author="AI Gen Hub Team",
    author_email="<EMAIL>",
    description="Python SDK for AI Gen Hub - 统一的AI生成服务平台",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/ai-gen-hub",
    project_urls={
        "Documentation": "https://docs.ai-gen-hub.com",
        "Source": "https://github.com/your-org/ai-gen-hub",
        "Tracker": "https://github.com/your-org/ai-gen-hub/issues",
    },
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    ],
    python_requires=">=3.8",
    install_requires=[
        "httpx>=0.24.0",
        "typing-extensions>=4.0.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "isort>=5.12.0",
            "mypy>=1.0.0",
            "flake8>=6.0.0",
        ],
        "docs": [
            "sphinx>=5.0.0",
            "sphinx-rtd-theme>=1.2.0",
            "myst-parser>=1.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "ai-gen-hub=ai_gen_hub_sdk.cli:main",
        ],
    },
    keywords=[
        "ai",
        "artificial intelligence",
        "text generation",
        "image generation",
        "openai",
        "google ai",
        "anthropic",
        "sdk",
        "api client",
    ],
    include_package_data=True,
    zip_safe=False,
)
