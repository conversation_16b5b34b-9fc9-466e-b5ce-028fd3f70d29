{"name": "ai-gen-hub-sdk", "version": "1.0.0", "description": "JavaScript/TypeScript SDK for AI Gen Hub - 统一的AI生成服务平台", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "src", "README.md", "LICENSE"], "scripts": {"build": "rollup -c", "build:watch": "rollup -c -w", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts,.js", "lint:fix": "eslint src --ext .ts,.js --fix", "type-check": "tsc --noEmit", "docs": "typedoc src/index.ts", "prepublishOnly": "npm run build", "dev": "node examples/basic-usage.js"}, "keywords": ["ai", "artificial-intelligence", "text-generation", "image-generation", "openai", "google-ai", "anthropic", "sdk", "api-client", "typescript", "javascript"], "author": "AI Gen Hub Team <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/ai-gen-hub.git", "directory": "sdk/javascript"}, "bugs": {"url": "https://github.com/your-org/ai-gen-hub/issues"}, "homepage": "https://github.com/your-org/ai-gen-hub#readme", "dependencies": {"axios": "^1.6.0", "eventsource": "^2.0.2"}, "devDependencies": {"@types/eventsource": "^1.1.12", "@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.50.0", "jest": "^29.7.0", "rollup": "^4.0.0", "@rollup/plugin-typescript": "^11.1.0", "@rollup/plugin-node-resolve": "^15.2.0", "@rollup/plugin-commonjs": "^25.0.0", "rollup-plugin-terser": "^7.0.2", "ts-jest": "^29.1.0", "typedoc": "^0.25.0", "typescript": "^5.2.0"}, "engines": {"node": ">=14.0.0"}, "publishConfig": {"access": "public"}}