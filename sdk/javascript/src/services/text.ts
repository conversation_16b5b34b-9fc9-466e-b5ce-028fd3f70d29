/**
 * 文本生成服务
 */

import { AIGenHubClient } from '../client';
import { 
  TextGenerationRequest, 
  TextGenerationResponse, 
  StreamChunk, 
  Model,
  Message
} from '../types';
import { API_ENDPOINTS } from '../constants';

/**
 * 文本生成服务类
 */
export class TextService {
  constructor(private client: AIGenHubClient) {}

  /**
   * 生成文本
   */
  async generate(request: TextGenerationRequest): Promise<TextGenerationResponse> {
    const response = await this.client.request<TextGenerationResponse>({
      method: 'POST',
      url: API_ENDPOINTS.TEXT.GENERATE,
      data: this.formatRequest(request)
    });

    return response.data.data!;
  }

  /**
   * 流式生成文本
   */
  async *stream(request: TextGenerationRequest): AsyncGenerator<StreamChunk, void, unknown> {
    const streamRequest = { ...request, stream: true };
    
    // 注意：这里需要使用EventSource或类似的流式处理
    // 由于axios不直接支持SSE，这里提供一个简化的实现
    const response = await fetch(`${this.client.getConfig().baseUrl}${API_ENDPOINTS.TEXT.STREAM}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        ...this.getAuthHeaders()
      },
      body: JSON.stringify(this.formatRequest(streamRequest))
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              yield data as StreamChunk;
            } catch (error) {
              // 忽略解析错误
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * 获取可用模型列表
   */
  async getModels(): Promise<Model[]> {
    const response = await this.client.request<{ models: Model[] }>({
      method: 'GET',
      url: API_ENDPOINTS.TEXT.MODELS
    });

    return response.data.data!.models;
  }

  /**
   * 对话生成
   */
  async chat(
    message: string,
    model: string,
    conversationId?: string,
    contextLength: number = 10
  ): Promise<any> {
    const data: any = {
      message,
      model,
      contextLength
    };

    if (conversationId) {
      data.conversationId = conversationId;
    }

    const response = await this.client.request({
      method: 'POST',
      url: API_ENDPOINTS.TEXT.CHAT,
      data
    });

    return response.data.data;
  }

  /**
   * 文本补全
   */
  async complete(
    prompt: string,
    model: string,
    maxTokens?: number,
    temperature?: number
  ): Promise<any> {
    const data: any = {
      prompt,
      model
    };

    if (maxTokens !== undefined) {
      data.maxTokens = maxTokens;
    }

    if (temperature !== undefined) {
      data.temperature = temperature;
    }

    const response = await this.client.request({
      method: 'POST',
      url: API_ENDPOINTS.TEXT.COMPLETE,
      data
    });

    return response.data.data;
  }

  /**
   * 格式化请求数据
   */
  private formatRequest(request: TextGenerationRequest): any {
    const formatted: any = {
      messages: request.messages,
      model: request.model
    };

    // 添加可选参数
    if (request.maxTokens !== undefined) {
      formatted.max_tokens = request.maxTokens;
    }

    if (request.temperature !== undefined) {
      formatted.temperature = request.temperature;
    }

    if (request.topP !== undefined) {
      formatted.top_p = request.topP;
    }

    if (request.frequencyPenalty !== undefined) {
      formatted.frequency_penalty = request.frequencyPenalty;
    }

    if (request.presencePenalty !== undefined) {
      formatted.presence_penalty = request.presencePenalty;
    }

    if (request.stop !== undefined) {
      formatted.stop = request.stop;
    }

    if (request.stream !== undefined) {
      formatted.stream = request.stream;
    }

    return formatted;
  }

  /**
   * 获取认证头
   */
  private getAuthHeaders(): Record<string, string> {
    const config = this.client.getConfig();
    const headers: Record<string, string> = {};

    if (config.apiKey) {
      headers['X-API-Key'] = config.apiKey;
    }

    return headers;
  }
}
