/**
 * 图像生成服务
 */

import { AIGenHubClient } from '../client';
import { 
  ImageGenerationRequest, 
  ImageGenerationResponse, 
  Model
} from '../types';
import { API_ENDPOINTS } from '../constants';

/**
 * 图像生成服务类
 */
export class ImageService {
  constructor(private client: AIGenHubClient) {}

  /**
   * 生成图像
   */
  async generate(request: ImageGenerationRequest): Promise<ImageGenerationResponse> {
    const response = await this.client.request<ImageGenerationResponse>({
      method: 'POST',
      url: API_ENDPOINTS.IMAGE.GENERATE,
      data: this.formatRequest(request)
    });

    return response.data.data!;
  }

  /**
   * 获取可用图像模型列表
   */
  async getModels(): Promise<Model[]> {
    const response = await this.client.request<{ models: Model[] }>({
      method: 'GET',
      url: API_ENDPOINTS.IMAGE.MODELS
    });

    return response.data.data!.models;
  }

  /**
   * 格式化请求数据
   */
  private formatRequest(request: ImageGenerationRequest): any {
    const formatted: any = {
      prompt: request.prompt,
      model: request.model,
      n: request.n || 1
    };

    // 添加可选参数
    if (request.size) {
      formatted.size = request.size;
    }

    if (request.quality) {
      formatted.quality = request.quality;
    }

    if (request.style) {
      formatted.style = request.style;
    }

    return formatted;
  }
}
