/**
 * 认证服务
 */

import { AIGenHubClient } from '../client';
import { 
  LoginRequest, 
  LoginResponse, 
  RegisterRequest, 
  User
} from '../types';
import { API_ENDPOINTS } from '../constants';

/**
 * 认证服务类
 */
export class AuthService {
  constructor(private client: AIGenHubClient) {}

  /**
   * 用户登录
   */
  async login(request: LoginRequest): Promise<LoginResponse> {
    const response = await this.client.request<LoginResponse>({
      method: 'POST',
      url: API_ENDPOINTS.AUTH.LOGIN,
      data: request
    });

    const loginData = response.data.data!;
    
    // 自动设置访问令牌
    this.client.setAccessToken(loginData.accessToken, loginData.expiresIn);

    return loginData;
  }

  /**
   * 用户注册
   */
  async register(request: RegisterRequest): Promise<User> {
    const response = await this.client.request<User>({
      method: 'POST',
      url: API_ENDPOINTS.AUTH.REGISTER,
      data: request
    });

    return response.data.data!;
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken(refreshToken: string): Promise<LoginResponse> {
    const response = await this.client.request<LoginResponse>({
      method: 'POST',
      url: API_ENDPOINTS.AUTH.REFRESH,
      data: { refresh_token: refreshToken }
    });

    const loginData = response.data.data!;
    
    // 自动设置新的访问令牌
    this.client.setAccessToken(loginData.accessToken, loginData.expiresIn);

    return loginData;
  }

  /**
   * 获取用户资料
   */
  async getProfile(): Promise<User> {
    const response = await this.client.request<User>({
      method: 'GET',
      url: API_ENDPOINTS.AUTH.PROFILE
    });

    return response.data.data!;
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      await this.client.request({
        method: 'POST',
        url: API_ENDPOINTS.AUTH.LOGOUT
      });
    } finally {
      // 无论请求是否成功，都清除本地认证信息
      this.client.clearAuth();
    }
  }
}
