/**
 * AI Gen Hub JavaScript/TypeScript SDK
 * 
 * 一个简单易用的JavaScript/TypeScript SDK，用于访问AI Gen Hub的各种AI服务。
 * 
 * @example
 * ```typescript
 * import { AIGenHubClient } from 'ai-gen-hub-sdk';
 * 
 * const client = new AIGenHubClient({
 *   baseUrl: 'http://localhost:8001',
 *   apiKey: 'your_api_key'
 * });
 * 
 * const response = await client.text.generate({
 *   messages: [
 *     { role: 'user', content: 'Hello, world!' }
 *   ],
 *   model: 'gemini-pro'
 * });
 * 
 * console.log(response.content);
 * ```
 */

export { AIGenHubClient } from './client';
export { TextService } from './services/text';
export { ImageService } from './services/image';
export { AuthService } from './services/auth';

// 导出类型定义
export type {
  ClientConfig,
  Message,
  TextGenerationRequest,
  TextGenerationResponse,
  ImageGenerationRequest,
  ImageGenerationResponse,
  StreamChunk,
  Model,
  User,
  Usage,
  APIResponse
} from './types';

// 导出异常类
export {
  AIGenHubError,
  AuthenticationError,
  AuthorizationError,
  ValidationError,
  RateLimitError,
  QuotaExceededError,
  ProviderError,
  NetworkError,
  TimeoutError,
  ServerError,
  ModelNotFoundError,
  ContentFilterError,
  TokenLimitError
} from './exceptions';

// 导出常量
export { API_ENDPOINTS, DEFAULT_CONFIG } from './constants';

// 版本信息
export const VERSION = '1.0.0';
export const AUTHOR = 'AI Gen Hub Team';
export const EMAIL = '<EMAIL>';
