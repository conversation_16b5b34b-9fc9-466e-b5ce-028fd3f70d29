/**
 * AI Gen Hub SDK 类型定义
 */

/**
 * 客户端配置
 */
export interface ClientConfig {
  /** API基础URL */
  baseUrl?: string;
  /** API密钥 */
  apiKey?: string;
  /** 用户名 */
  username?: string;
  /** 密码 */
  password?: string;
  /** 请求超时时间（毫秒） */
  timeout?: number;
  /** 最大重试次数 */
  maxRetries?: number;
  /** 重试延迟时间（毫秒） */
  retryDelay?: number;
}

/**
 * 对话消息
 */
export interface Message {
  /** 角色：system, user, assistant */
  role: 'system' | 'user' | 'assistant';
  /** 消息内容 */
  content: string;
  /** 可选的发送者名称 */
  name?: string;
}

/**
 * Token使用统计
 */
export interface Usage {
  /** 提示词token数 */
  promptTokens: number;
  /** 生成token数 */
  completionTokens: number;
  /** 总token数 */
  totalTokens: number;
}

/**
 * 文本生成请求
 */
export interface TextGenerationRequest {
  /** 对话消息列表 */
  messages: Message[];
  /** 使用的模型名称 */
  model: string;
  /** 最大生成token数 */
  maxTokens?: number;
  /** 控制随机性 (0.0-2.0) */
  temperature?: number;
  /** 核采样参数 (0.0-1.0) */
  topP?: number;
  /** 频率惩罚 (-2.0-2.0) */
  frequencyPenalty?: number;
  /** 存在惩罚 (-2.0-2.0) */
  presencePenalty?: number;
  /** 停止序列 */
  stop?: string | string[];
  /** 是否流式输出 */
  stream?: boolean;
}

/**
 * 文本生成响应
 */
export interface TextGenerationResponse {
  /** 生成结果的唯一ID */
  id: string;
  /** 生成的文本内容 */
  content: string;
  /** 使用的模型名称 */
  model: string;
  /** Token使用统计 */
  usage: Usage;
  /** 结束原因 */
  finishReason: 'stop' | 'length' | 'content_filter' | 'function_call';
  /** 创建时间 */
  createdAt: string;
  /** 使用的AI供应商 */
  provider: string;
}

/**
 * 流式响应数据块
 */
export interface StreamChunk {
  /** 数据块类型 */
  type: 'start' | 'content' | 'end';
  /** 内容（仅content类型） */
  content?: string;
  /** 使用统计（仅end类型） */
  usage?: Usage;
  /** 结束原因（仅end类型） */
  finishReason?: string;
}

/**
 * 图像生成请求
 */
export interface ImageGenerationRequest {
  /** 图像描述提示词 */
  prompt: string;
  /** 使用的模型名称 */
  model: string;
  /** 图像尺寸 */
  size?: '256x256' | '512x512' | '1024x1024' | '1792x1024' | '1024x1792';
  /** 图像质量 */
  quality?: 'standard' | 'hd';
  /** 图像风格 */
  style?: 'vivid' | 'natural';
  /** 生成图片数量 */
  n?: number;
}

/**
 * 图像数据
 */
export interface ImageData {
  /** 图像URL */
  url?: string;
  /** Base64编码的图像数据 */
  b64Json?: string;
  /** 修订后的提示词 */
  revisedPrompt?: string;
}

/**
 * 图像生成响应
 */
export interface ImageGenerationResponse {
  /** 生成结果的唯一ID */
  id: string;
  /** 生成的图像列表 */
  images: ImageData[];
  /** 使用的模型名称 */
  model: string;
  /** 创建时间 */
  createdAt: string;
  /** 使用的AI供应商 */
  provider: string;
}

/**
 * AI模型信息
 */
export interface Model {
  /** 模型ID */
  id: string;
  /** 模型名称 */
  name: string;
  /** 供应商 */
  provider: string;
  /** 最大token数 */
  maxTokens: number;
  /** 是否支持流式输出 */
  supportsStreaming: boolean;
  /** 是否支持函数调用 */
  supportsFunctions: boolean;
  /** 定价信息 */
  pricing?: {
    input: number;
    output: number;
  };
}

/**
 * 用户信息
 */
export interface User {
  /** 用户ID */
  id: string;
  /** 用户名 */
  username: string;
  /** 邮箱 */
  email: string;
  /** 角色 */
  role: string;
  /** 状态 */
  status: string;
  /** API配额 */
  apiQuota: number;
  /** 已使用配额 */
  apiQuotaUsed: number;
  /** 创建时间 */
  createdAt: string;
}

/**
 * 通用API响应
 */
export interface APIResponse<T = any> {
  /** 是否成功 */
  success: boolean;
  /** 响应数据 */
  data?: T;
  /** 响应消息 */
  message?: string;
  /** 请求ID */
  requestId?: string;
  /** 时间戳 */
  timestamp?: string;
  /** 错误信息（失败时） */
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

/**
 * 分页信息
 */
export interface Pagination {
  /** 当前页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 总数量 */
  total: number;
  /** 总页数 */
  pages: number;
  /** 是否有下一页 */
  hasNext: boolean;
  /** 是否有上一页 */
  hasPrev: boolean;
}

/**
 * 分页响应
 */
export interface PaginatedResponse<T> {
  /** 数据项列表 */
  items: T[];
  /** 分页信息 */
  pagination: Pagination;
}

/**
 * 登录请求
 */
export interface LoginRequest {
  /** 用户名 */
  username: string;
  /** 密码 */
  password: string;
}

/**
 * 登录响应
 */
export interface LoginResponse {
  /** 访问令牌 */
  accessToken: string;
  /** 刷新令牌 */
  refreshToken?: string;
  /** 令牌类型 */
  tokenType: string;
  /** 过期时间（秒） */
  expiresIn: number;
  /** 用户信息 */
  user: User;
}

/**
 * 注册请求
 */
export interface RegisterRequest {
  /** 用户名 */
  username: string;
  /** 邮箱 */
  email: string;
  /** 密码 */
  password: string;
  /** 角色（可选） */
  role?: string;
}

/**
 * HTTP请求方法
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

/**
 * HTTP请求配置
 */
export interface RequestConfig {
  /** 请求方法 */
  method: HttpMethod;
  /** 请求URL */
  url: string;
  /** 请求头 */
  headers?: Record<string, string>;
  /** 请求参数 */
  params?: Record<string, any>;
  /** 请求体 */
  data?: any;
  /** 是否流式请求 */
  stream?: boolean;
  /** 超时时间 */
  timeout?: number;
}

/**
 * 错误响应
 */
export interface ErrorResponse {
  /** 错误代码 */
  code: string;
  /** 错误消息 */
  message: string;
  /** 错误详情 */
  details?: any;
  /** 请求ID */
  requestId?: string;
  /** 时间戳 */
  timestamp?: string;
}
