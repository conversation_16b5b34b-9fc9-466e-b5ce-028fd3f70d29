/**
 * AI Gen Hub SDK 异常类定义
 */

/**
 * AI Gen Hub SDK 基础异常类
 */
export class AIGenHubError extends Error {
  public readonly errorCode?: string;
  public readonly details?: any;

  constructor(message: string, errorCode?: string, details?: any) {
    super(message);
    this.name = 'AIGenHubError';
    this.errorCode = errorCode;
    this.details = details;

    // 确保错误堆栈正确显示
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AIGenHubError);
    }
  }

  toString(): string {
    if (this.errorCode) {
      return `[${this.errorCode}] ${this.message}`;
    }
    return this.message;
  }
}

/**
 * 认证错误
 * 
 * 当API密钥无效、JWT token过期或用户名密码错误时抛出
 */
export class AuthenticationError extends AIGenHubError {
  constructor(message: string, errorCode?: string, details?: any) {
    super(message, errorCode, details);
    this.name = 'AuthenticationError';
  }
}

/**
 * 授权错误
 * 
 * 当用户没有权限访问特定资源时抛出
 */
export class AuthorizationError extends AIGenHubError {
  constructor(message: string, errorCode?: string, details?: any) {
    super(message, errorCode, details);
    this.name = 'AuthorizationError';
  }
}

/**
 * 请求验证错误
 * 
 * 当请求参数无效或格式错误时抛出
 */
export class ValidationError extends AIGenHubError {
  public readonly field?: string;

  constructor(message: string, field?: string, errorCode?: string, details?: any) {
    super(message, errorCode, details);
    this.name = 'ValidationError';
    this.field = field;
  }
}

/**
 * 请求频率限制错误
 * 
 * 当请求频率超过限制时抛出
 */
export class RateLimitError extends AIGenHubError {
  public readonly retryAfter?: number;

  constructor(message: string, retryAfter?: number, errorCode?: string, details?: any) {
    super(message, errorCode, details);
    this.name = 'RateLimitError';
    this.retryAfter = retryAfter;
  }
}

/**
 * 配额超限错误
 * 
 * 当API使用量超过配额时抛出
 */
export class QuotaExceededError extends AIGenHubError {
  public readonly quotaType?: string;

  constructor(message: string, quotaType?: string, errorCode?: string, details?: any) {
    super(message, errorCode, details);
    this.name = 'QuotaExceededError';
    this.quotaType = quotaType;
  }
}

/**
 * AI供应商错误
 * 
 * 当上游AI服务出现错误时抛出
 */
export class ProviderError extends AIGenHubError {
  public readonly provider?: string;

  constructor(message: string, provider?: string, errorCode?: string, details?: any) {
    super(message, errorCode, details);
    this.name = 'ProviderError';
    this.provider = provider;
  }
}

/**
 * 网络错误
 * 
 * 当网络连接失败或超时时抛出
 */
export class NetworkError extends AIGenHubError {
  constructor(message: string, errorCode?: string, details?: any) {
    super(message, errorCode, details);
    this.name = 'NetworkError';
  }
}

/**
 * 请求超时错误
 */
export class TimeoutError extends NetworkError {
  constructor(message: string = '请求超时', errorCode?: string, details?: any) {
    super(message, errorCode, details);
    this.name = 'TimeoutError';
  }
}

/**
 * 服务器内部错误
 * 
 * 当服务器返回5xx状态码时抛出
 */
export class ServerError extends AIGenHubError {
  public readonly statusCode?: number;

  constructor(message: string, statusCode?: number, errorCode?: string, details?: any) {
    super(message, errorCode, details);
    this.name = 'ServerError';
    this.statusCode = statusCode;
  }
}

/**
 * 模型不存在错误
 * 
 * 当请求的AI模型不存在或不可用时抛出
 */
export class ModelNotFoundError extends AIGenHubError {
  public readonly model?: string;

  constructor(message: string, model?: string, errorCode?: string, details?: any) {
    super(message, errorCode, details);
    this.name = 'ModelNotFoundError';
    this.model = model;
  }
}

/**
 * 内容过滤错误
 * 
 * 当生成的内容被安全过滤器拦截时抛出
 */
export class ContentFilterError extends AIGenHubError {
  constructor(message: string, errorCode?: string, details?: any) {
    super(message, errorCode, details);
    this.name = 'ContentFilterError';
  }
}

/**
 * Token限制错误
 * 
 * 当请求的token数量超过模型限制时抛出
 */
export class TokenLimitError extends ValidationError {
  public readonly maxTokens?: number;
  public readonly requestedTokens?: number;

  constructor(
    message: string, 
    maxTokens?: number, 
    requestedTokens?: number, 
    errorCode?: string, 
    details?: any
  ) {
    super(message, undefined, errorCode, details);
    this.name = 'TokenLimitError';
    this.maxTokens = maxTokens;
    this.requestedTokens = requestedTokens;
  }
}

/**
 * 异常映射表，用于根据错误代码创建对应的异常
 */
export const ERROR_CODE_MAPPING: Record<string, typeof AIGenHubError> = {
  'AUTHENTICATION_FAILED': AuthenticationError,
  'INVALID_API_KEY': AuthenticationError,
  'TOKEN_EXPIRED': AuthenticationError,
  'PERMISSION_DENIED': AuthorizationError,
  'INSUFFICIENT_PERMISSIONS': AuthorizationError,
  'INVALID_REQUEST': ValidationError,
  'MISSING_PARAMETER': ValidationError,
  'INVALID_PARAMETER': ValidationError,
  'RATE_LIMIT_EXCEEDED': RateLimitError,
  'QUOTA_EXCEEDED': QuotaExceededError,
  'DAILY_QUOTA_EXCEEDED': QuotaExceededError,
  'MONTHLY_QUOTA_EXCEEDED': QuotaExceededError,
  'PROVIDER_ERROR': ProviderError,
  'PROVIDER_UNAVAILABLE': ProviderError,
  'MODEL_NOT_FOUND': ModelNotFoundError,
  'MODEL_UNAVAILABLE': ModelNotFoundError,
  'CONTENT_FILTERED': ContentFilterError,
  'TOKEN_LIMIT_EXCEEDED': TokenLimitError,
  'NETWORK_ERROR': NetworkError,
  'TIMEOUT': TimeoutError,
  'INTERNAL_ERROR': ServerError,
  'SERVICE_UNAVAILABLE': ServerError,
};

/**
 * 根据错误代码创建对应的异常实例
 */
export function createExceptionFromErrorCode(
  errorCode: string, 
  message: string, 
  details?: any
): AIGenHubError {
  const ExceptionClass = ERROR_CODE_MAPPING[errorCode] || AIGenHubError;
  return new ExceptionClass(message, errorCode, details);
}
