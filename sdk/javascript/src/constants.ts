/**
 * AI Gen Hub SDK 常量定义
 */

import { ClientConfig } from './types';

/**
 * 默认客户端配置
 */
export const DEFAULT_CONFIG: Required<ClientConfig> = {
  baseUrl: 'http://localhost:8001',
  apiKey: undefined,
  username: undefined,
  password: undefined,
  timeout: 30000, // 30秒
  maxRetries: 3,
  retryDelay: 1000 // 1秒
};

/**
 * API端点常量
 */
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/api/v1/auth/login',
    REGISTER: '/api/v1/auth/register',
    REFRESH: '/api/v1/auth/refresh',
    PROFILE: '/api/v1/auth/profile',
    LOGOUT: '/api/v1/auth/logout'
  },

  // 文本生成
  TEXT: {
    GENERATE: '/api/v1/text/generate',
    STREAM: '/api/v1/text/stream',
    MODELS: '/api/v1/text/models',
    CHAT: '/api/v1/text/chat',
    COMPLETE: '/api/v1/text/complete'
  },

  // 图像生成
  IMAGE: {
    GENERATE: '/api/v1/image/generate',
    MODELS: '/api/v1/image/models'
  },

  // 系统相关
  SYSTEM: {
    HEALTH: '/health',
    METRICS: '/metrics'
  },

  // 管理相关
  ADMIN: {
    DASHBOARD: '/admin/dashboard',
    USERS: '/admin/users',
    PROVIDERS: '/admin/providers',
    SETTINGS: '/admin/settings'
  }
} as const;

/**
 * HTTP状态码常量
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503
} as const;

/**
 * 错误代码常量
 */
export const ERROR_CODES = {
  // 认证错误
  AUTHENTICATION_FAILED: 'AUTHENTICATION_FAILED',
  INVALID_API_KEY: 'INVALID_API_KEY',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',

  // 授权错误
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',

  // 验证错误
  INVALID_REQUEST: 'INVALID_REQUEST',
  MISSING_PARAMETER: 'MISSING_PARAMETER',
  INVALID_PARAMETER: 'INVALID_PARAMETER',

  // 限制错误
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  TOKEN_LIMIT_EXCEEDED: 'TOKEN_LIMIT_EXCEEDED',

  // 供应商错误
  PROVIDER_ERROR: 'PROVIDER_ERROR',
  PROVIDER_UNAVAILABLE: 'PROVIDER_UNAVAILABLE',
  MODEL_NOT_FOUND: 'MODEL_NOT_FOUND',

  // 内容错误
  CONTENT_FILTERED: 'CONTENT_FILTERED',

  // 系统错误
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT: 'TIMEOUT'
} as const;

/**
 * 支持的模型常量
 */
export const MODELS = {
  TEXT: {
    GEMINI_PRO: 'gemini-pro',
    GEMINI_PRO_VISION: 'gemini-pro-vision',
    GPT_3_5_TURBO: 'gpt-3.5-turbo',
    GPT_4: 'gpt-4',
    GPT_4_TURBO: 'gpt-4-turbo',
    CLAUDE_3_SONNET: 'claude-3-sonnet',
    CLAUDE_3_OPUS: 'claude-3-opus'
  },
  IMAGE: {
    DALL_E_2: 'dall-e-2',
    DALL_E_3: 'dall-e-3',
    STABLE_DIFFUSION: 'stable-diffusion-xl'
  }
} as const;

/**
 * 图像尺寸常量
 */
export const IMAGE_SIZES = {
  SMALL: '256x256',
  MEDIUM: '512x512',
  LARGE: '1024x1024',
  WIDE: '1792x1024',
  TALL: '1024x1792'
} as const;

/**
 * 图像质量常量
 */
export const IMAGE_QUALITY = {
  STANDARD: 'standard',
  HD: 'hd'
} as const;

/**
 * 图像风格常量
 */
export const IMAGE_STYLE = {
  VIVID: 'vivid',
  NATURAL: 'natural'
} as const;

/**
 * 消息角色常量
 */
export const MESSAGE_ROLES = {
  SYSTEM: 'system',
  USER: 'user',
  ASSISTANT: 'assistant'
} as const;

/**
 * 流式响应类型常量
 */
export const STREAM_TYPES = {
  START: 'start',
  CONTENT: 'content',
  END: 'end'
} as const;

/**
 * 结束原因常量
 */
export const FINISH_REASONS = {
  STOP: 'stop',
  LENGTH: 'length',
  CONTENT_FILTER: 'content_filter',
  FUNCTION_CALL: 'function_call'
} as const;

/**
 * 用户角色常量
 */
export const USER_ROLES = {
  ADMIN: 'admin',
  USER: 'user',
  GUEST: 'guest'
} as const;

/**
 * 用户状态常量
 */
export const USER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  SUSPENDED: 'suspended'
} as const;

/**
 * 供应商常量
 */
export const PROVIDERS = {
  GOOGLE_AI: 'google_ai',
  OPENAI: 'openai',
  ANTHROPIC: 'anthropic',
  AZURE_OPENAI: 'azure_openai'
} as const;

/**
 * 请求头常量
 */
export const HEADERS = {
  CONTENT_TYPE: 'Content-Type',
  AUTHORIZATION: 'Authorization',
  API_KEY: 'X-API-Key',
  USER_AGENT: 'User-Agent',
  ACCEPT: 'Accept'
} as const;

/**
 * 内容类型常量
 */
export const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM_DATA: 'multipart/form-data',
  EVENT_STREAM: 'text/event-stream'
} as const;

/**
 * SDK版本信息
 */
export const SDK_INFO = {
  NAME: 'ai-gen-hub-sdk-js',
  VERSION: '1.0.0',
  USER_AGENT: 'ai-gen-hub-sdk-js/1.0.0'
} as const;
