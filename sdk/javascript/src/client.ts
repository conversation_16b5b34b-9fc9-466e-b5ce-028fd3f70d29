/**
 * AI Gen Hub JavaScript/TypeScript SDK 主客户端
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { TextService } from './services/text';
import { ImageService } from './services/image';
import { AuthService } from './services/auth';
import { 
  ClientConfig, 
  APIResponse, 
  RequestConfig,
  LoginResponse 
} from './types';
import { 
  AIGenHubError,
  AuthenticationError,
  RateLimitError,
  QuotaExceededError,
  ValidationError,
  ProviderError,
  NetworkError,
  TimeoutError
} from './exceptions';
import { DEFAULT_CONFIG } from './constants';

/**
 * AI Gen Hub JavaScript/TypeScript SDK 主客户端
 */
export class AIGenHubClient {
  private config: Required<ClientConfig>;
  private httpClient: AxiosInstance;
  private accessToken?: string;
  private tokenExpiresAt?: number;

  // 服务实例
  public readonly text: TextService;
  public readonly image: ImageService;
  public readonly auth: AuthService;

  /**
   * 创建AI Gen Hub客户端实例
   * 
   * @param config 客户端配置
   */
  constructor(config: ClientConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    
    // 创建HTTP客户端
    this.httpClient = axios.create({
      baseURL: this.config.baseUrl,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'ai-gen-hub-sdk-js/1.0.0'
      }
    });

    // 设置请求拦截器
    this.httpClient.interceptors.request.use(
      (config) => {
        // 添加认证头
        const headers = this.getAuthHeaders();
        Object.assign(config.headers, headers);
        return config;
      },
      (error) => Promise.reject(error)
    );

    // 设置响应拦截器
    this.httpClient.interceptors.response.use(
      (response) => response,
      (error) => {
        throw this.handleError(error);
      }
    );

    // 初始化服务
    this.text = new TextService(this);
    this.image = new ImageService(this);
    this.auth = new AuthService(this);

    // 如果提供了用户名密码，立即登录
    if (this.config.username && this.config.password && !this.config.apiKey) {
      this.login().catch(() => {
        // 静默处理登录错误，让用户在实际使用时处理
      });
    }
  }

  /**
   * 获取认证头
   */
  private getAuthHeaders(): Record<string, string> {
    const headers: Record<string, string> = {};

    if (this.config.apiKey) {
      headers['X-API-Key'] = this.config.apiKey;
    } else if (this.accessToken) {
      headers['Authorization'] = `Bearer ${this.accessToken}`;
    }

    return headers;
  }

  /**
   * 用户登录
   */
  private async login(): Promise<void> {
    if (!this.config.username || !this.config.password) {
      throw new AuthenticationError('需要提供用户名和密码');
    }

    try {
      const response = await this.httpClient.post<APIResponse<LoginResponse>>('/api/v1/auth/login', {
        username: this.config.username,
        password: this.config.password
      });

      const loginData = response.data.data!;
      this.accessToken = loginData.accessToken;
      // 假设token有效期为1小时
      this.tokenExpiresAt = Date.now() + (loginData.expiresIn * 1000);
    } catch (error) {
      throw new AuthenticationError(`登录失败: ${error}`);
    }
  }

  /**
   * 确保已认证
   */
  private async ensureAuthenticated(): Promise<void> {
    if (this.config.apiKey) {
      return;
    }

    if (!this.accessToken || (this.tokenExpiresAt && Date.now() >= this.tokenExpiresAt)) {
      await this.login();
    }
  }

  /**
   * 处理HTTP错误
   */
  private handleError(error: any): Error {
    if (error.code === 'ECONNABORTED') {
      return new TimeoutError('请求超时');
    }

    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return new NetworkError('网络连接失败');
    }

    if (!error.response) {
      return new NetworkError(`网络错误: ${error.message}`);
    }

    const { status, data } = error.response;
    const errorMessage = data?.error?.message || data?.message || '未知错误';
    const errorCode = data?.error?.code || 'UNKNOWN_ERROR';

    switch (status) {
      case 401:
        return new AuthenticationError(errorMessage);
      case 429:
        if (errorMessage.toLowerCase().includes('rate limit')) {
          return new RateLimitError(errorMessage);
        } else {
          return new QuotaExceededError(errorMessage);
        }
      case 400:
        return new ValidationError(errorMessage);
      case 502:
      case 503:
        return new ProviderError(errorMessage);
      default:
        return new AIGenHubError(`${errorCode}: ${errorMessage}`);
    }
  }

  /**
   * 发送HTTP请求
   */
  public async request<T = any>(config: RequestConfig): Promise<AxiosResponse<APIResponse<T>>> {
    await this.ensureAuthenticated();

    const requestConfig = {
      method: config.method,
      url: config.url,
      headers: config.headers,
      params: config.params,
      data: config.data,
      timeout: config.timeout || this.config.timeout
    };

    // 重试逻辑
    let lastError: Error;
    for (let attempt = 0; attempt <= this.config.maxRetries; attempt++) {
      try {
        return await this.httpClient.request(requestConfig);
      } catch (error: any) {
        lastError = error;
        
        // 如果是最后一次尝试，或者是不可重试的错误，直接抛出
        if (attempt === this.config.maxRetries || 
            error instanceof AuthenticationError ||
            error instanceof ValidationError) {
          throw error;
        }

        // 等待后重试
        await new Promise(resolve => 
          setTimeout(resolve, this.config.retryDelay * Math.pow(2, attempt))
        );
      }
    }

    throw lastError!;
  }

  /**
   * 获取客户端配置
   */
  public getConfig(): Readonly<Required<ClientConfig>> {
    return { ...this.config };
  }

  /**
   * 更新客户端配置
   */
  public updateConfig(newConfig: Partial<ClientConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 更新HTTP客户端配置
    this.httpClient.defaults.baseURL = this.config.baseUrl;
    this.httpClient.defaults.timeout = this.config.timeout;
  }

  /**
   * 设置API密钥
   */
  public setApiKey(apiKey: string): void {
    this.config.apiKey = apiKey;
    this.accessToken = undefined;
    this.tokenExpiresAt = undefined;
  }

  /**
   * 设置访问令牌
   */
  public setAccessToken(token: string, expiresIn?: number): void {
    this.accessToken = token;
    if (expiresIn) {
      this.tokenExpiresAt = Date.now() + (expiresIn * 1000);
    }
  }

  /**
   * 清除认证信息
   */
  public clearAuth(): void {
    this.accessToken = undefined;
    this.tokenExpiresAt = undefined;
    this.config.apiKey = undefined;
  }
}
