# AI Gen Hub JavaScript/TypeScript SDK

[![npm version](https://badge.fury.io/js/ai-gen-hub-sdk.svg)](https://badge.fury.io/js/ai-gen-hub-sdk)
[![TypeScript](https://img.shields.io/badge/TypeScript-Ready-blue.svg)](https://www.typescriptlang.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

AI Gen Hub JavaScript/TypeScript SDK 是一个简单易用的客户端库，用于访问AI Gen Hub的各种AI服务，包括文本生成、图像生成等功能。

## ✨ 特性

- 🚀 **简单易用** - 直观的API设计，几行代码即可开始使用
- 📝 **TypeScript支持** - 完整的类型定义，IDE友好
- 🌊 **流式输出** - 支持实时流式文本生成
- 🔐 **多种认证方式** - 支持API密钥和用户名密码认证
- 🛡️ **错误处理** - 完善的异常处理和重试机制
- 🌐 **浏览器和Node.js** - 同时支持浏览器和Node.js环境
- ⚡ **现代化** - 使用ES6+语法，支持Promise和async/await

## 📦 安装

```bash
npm install ai-gen-hub-sdk
```

或使用yarn：

```bash
yarn add ai-gen-hub-sdk
```

## 🚀 快速开始

### 基础用法

```typescript
import { AIGenHubClient } from 'ai-gen-hub-sdk';

// 使用API密钥初始化客户端
const client = new AIGenHubClient({
  baseUrl: 'http://localhost:8001',
  apiKey: 'your_api_key'
});

// 生成文本
const response = await client.text.generate({
  messages: [
    { role: 'user', content: '写一首关于春天的诗' }
  ],
  model: 'gemini-pro',
  maxTokens: 500
});

console.log(response.content);
```

### 使用用户名密码认证

```typescript
const client = new AIGenHubClient({
  baseUrl: 'http://localhost:8001',
  username: 'your_username',
  password: 'your_password'
});

const response = await client.text.generate({
  messages: [
    { role: 'system', content: '你是一个有用的AI助手' },
    { role: 'user', content: '解释什么是人工智能' }
  ],
  model: 'gemini-pro'
});

console.log(response.content);
```

### 流式文本生成

```typescript
// 流式生成，实时获取结果
for await (const chunk of client.text.stream({
  messages: [
    { role: 'user', content: '详细解释机器学习的工作原理' }
  ],
  model: 'gemini-pro'
})) {
  if (chunk.type === 'content') {
    process.stdout.write(chunk.content);
  } else if (chunk.type === 'end') {
    console.log(`\n\n使用token: ${chunk.usage?.totalTokens}`);
  }
}
```

### 图像生成

```typescript
// 生成图像
const response = await client.image.generate({
  prompt: '一只可爱的小猫在花园里玩耍',
  model: 'dall-e-3',
  size: '1024x1024',
  quality: 'hd'
});

// 获取图像URL
const imageUrl = response.images[0].url;
console.log(`生成的图像: ${imageUrl}`);
```

## 📚 详细用法

### 文本生成参数

```typescript
const response = await client.text.generate({
  messages: [
    { role: 'system', content: '你是一个创意写作助手' },
    { role: 'user', content: '写一个科幻故事的开头' }
  ],
  model: 'gemini-pro',
  maxTokens: 1000,        // 最大生成token数
  temperature: 0.8,       // 控制随机性 (0.0-2.0)
  topP: 0.9,             // 核采样参数 (0.0-1.0)
  frequencyPenalty: 0.1,  // 频率惩罚 (-2.0-2.0)
  presencePenalty: 0.1,   // 存在惩罚 (-2.0-2.0)
  stop: ['END', '\n\n']   // 停止序列
});
```

### 对话管理

```typescript
// 持续对话
let conversationId: string | undefined;

const userInput = '你好，我想了解人工智能';

const response = await client.text.chat(
  userInput,
  'gemini-pro',
  conversationId,
  10  // 保留最近10轮对话
);

conversationId = response.conversationId;
console.log(`助手: ${response.content}`);
```

### 获取可用模型

```typescript
// 获取文本生成模型
const textModels = await client.text.getModels();
textModels.forEach(model => {
  console.log(`模型: ${model.name} (${model.id})`);
  console.log(`供应商: ${model.provider}`);
  console.log(`最大token: ${model.maxTokens}`);
  console.log(`支持流式: ${model.supportsStreaming}`);
  console.log('---');
});

// 获取图像生成模型
const imageModels = await client.image.getModels();
imageModels.forEach(model => {
  console.log(`图像模型: ${model.name} (${model.id})`);
});
```

### 错误处理

```typescript
import {
  AIGenHubError,
  AuthenticationError,
  RateLimitError,
  QuotaExceededError,
  ValidationError
} from 'ai-gen-hub-sdk';

try {
  const response = await client.text.generate({
    messages: [{ role: 'user', content: 'Hello' }],
    model: 'invalid-model'
  });
} catch (error) {
  if (error instanceof AuthenticationError) {
    console.error('认证失败，请检查API密钥');
  } else if (error instanceof ValidationError) {
    console.error(`请求参数错误: ${error.message}`);
  } else if (error instanceof RateLimitError) {
    console.error(`请求频率超限，请等待 ${error.retryAfter} 秒`);
  } else if (error instanceof QuotaExceededError) {
    console.error('API配额已用完');
  } else if (error instanceof AIGenHubError) {
    console.error(`API错误: ${error.message}`);
  } else {
    console.error('未知错误:', error);
  }
}
```

### 自定义配置

```typescript
const client = new AIGenHubClient({
  baseUrl: 'http://localhost:8001',
  apiKey: 'your_api_key',
  timeout: 60000,     // 请求超时时间（毫秒）
  maxRetries: 3,      // 最大重试次数
  retryDelay: 1000    // 重试延迟时间（毫秒）
});

// 动态更新配置
client.updateConfig({
  timeout: 30000,
  maxRetries: 5
});
```

## 🔧 高级用法

### 批量处理

```typescript
const prompts = [
  '解释量子计算',
  '什么是区块链',
  '人工智能的未来'
];

const promises = prompts.map(prompt =>
  client.text.generate({
    messages: [{ role: 'user', content: prompt }],
    model: 'gemini-pro'
  })
);

const results = await Promise.all(promises);
results.forEach((result, index) => {
  console.log(`问题 ${index + 1}: ${result.content.substring(0, 100)}...`);
});
```

### 认证管理

```typescript
// 手动登录
const loginResponse = await client.auth.login({
  username: 'your_username',
  password: 'your_password'
});

console.log('登录成功:', loginResponse.user.username);

// 获取用户资料
const profile = await client.auth.getProfile();
console.log('用户信息:', profile);

// 登出
await client.auth.logout();
```

## 🌐 浏览器使用

```html
<!DOCTYPE html>
<html>
<head>
  <title>AI Gen Hub SDK Demo</title>
</head>
<body>
  <script type="module">
    import { AIGenHubClient } from 'https://unpkg.com/ai-gen-hub-sdk@latest/dist/index.esm.js';

    const client = new AIGenHubClient({
      baseUrl: 'http://localhost:8001',
      apiKey: 'your_api_key'
    });

    async function generateText() {
      try {
        const response = await client.text.generate({
          messages: [
            { role: 'user', content: 'Hello from browser!' }
          ],
          model: 'gemini-pro'
        });
        
        document.body.innerHTML += `<p>${response.content}</p>`;
      } catch (error) {
        console.error('Error:', error);
      }
    }

    generateText();
  </script>
</body>
</html>
```

## 📖 API参考

### AIGenHubClient

主客户端类，提供对所有服务的访问。

#### 构造函数

```typescript
new AIGenHubClient(config?: ClientConfig)
```

#### 配置选项

- `baseUrl` (string): API基础URL，默认 `http://localhost:8001`
- `apiKey` (string, 可选): API密钥
- `username` (string, 可选): 用户名
- `password` (string, 可选): 密码
- `timeout` (number): 请求超时时间（毫秒），默认30000
- `maxRetries` (number): 最大重试次数，默认3
- `retryDelay` (number): 重试延迟时间（毫秒），默认1000

#### 服务属性

- `client.text`: 文本生成服务
- `client.image`: 图像生成服务
- `client.auth`: 认证服务

### TextService

文本生成服务类。

#### 方法

- `generate(request)`: 生成文本
- `stream(request)`: 流式生成文本
- `getModels()`: 获取可用模型
- `chat(message, model, conversationId?, contextLength?)`: 对话生成
- `complete(prompt, model, maxTokens?, temperature?)`: 文本补全

### ImageService

图像生成服务类。

#### 方法

- `generate(request)`: 生成图像
- `getModels()`: 获取可用模型

### AuthService

认证服务类。

#### 方法

- `login(request)`: 用户登录
- `register(request)`: 用户注册
- `refreshToken(refreshToken)`: 刷新令牌
- `getProfile()`: 获取用户资料
- `logout()`: 用户登出

## 🧪 测试

```bash
# 安装开发依赖
npm install

# 运行测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage
```

## 🔨 构建

```bash
# 构建项目
npm run build

# 监听模式构建
npm run build:watch
```

## 🤝 贡献

欢迎贡献代码！请查看 [贡献指南](../../CONTRIBUTING.md) 了解详情。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../../LICENSE) 文件了解详情。

## 🆘 支持

- 📖 [完整文档](https://docs.ai-gen-hub.com)
- 🐛 [问题反馈](https://github.com/your-org/ai-gen-hub/issues)
- 💬 [讨论区](https://github.com/your-org/ai-gen-hub/discussions)
- 📧 [邮件支持](mailto:<EMAIL>)
