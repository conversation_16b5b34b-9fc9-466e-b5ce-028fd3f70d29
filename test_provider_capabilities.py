#!/usr/bin/env python3
"""
测试供应商能力检查修复

这个脚本测试get_provider_capabilities方法是否正确返回Google AI的能力信息。
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ai_gen_hub.core.optimized_request_utils import OptimizedRequestMixin

def test_provider_capabilities():
    """测试供应商能力检查"""
    print("🧪 测试供应商能力检查...")
    
    # 测试不同的供应商名称
    test_providers = [
        "google",
        "google_ai", 
        "openai",
        "anthropic",
        "unknown_provider"
    ]
    
    for provider in test_providers:
        print(f"\n📋 测试供应商: {provider}")
        capabilities = OptimizedRequestMixin.get_provider_capabilities(provider)
        
        if capabilities:
            print(f"   ✅ 找到能力信息")
            print(f"   支持流式: {capabilities.get('supports_streaming', 'N/A')}")
            print(f"   支持函数: {capabilities.get('supports_functions', 'N/A')}")
            print(f"   最大tokens: {capabilities.get('max_tokens_limit', 'N/A')}")
            print(f"   必需参数: {capabilities.get('required_parameters', 'N/A')}")
        else:
            print(f"   ❌ 未找到能力信息")

def test_validation():
    """测试验证方法"""
    print("\n🧪 测试验证方法...")
    
    # 创建一个简单的测试请求
    from ai_gen_hub.core.interfaces import OptimizedTextGenerationRequest, GenerationConfig, StreamConfig, Message, MessageRole
    
    test_request = OptimizedTextGenerationRequest(
        messages=[
            Message(role=MessageRole.USER, content="Hello, test message")
        ],
        model="gemini-2.5-flash",
        generation=GenerationConfig(
            temperature=0.7,
            max_tokens=50
        ),
        stream=StreamConfig(enabled=False)
    )
    
    # 测试不同供应商的验证
    test_providers = ["google", "google_ai", "openai", "unknown_provider"]
    
    for provider in test_providers:
        print(f"\n📋 验证供应商: {provider}")
        try:
            validation_result = test_request.validate_for_provider(provider)
            print(f"   验证结果: {validation_result}")
            
            if validation_result["errors"]:
                print(f"   ❌ 验证失败: {validation_result['errors']}")
            else:
                print(f"   ✅ 验证通过")
                if validation_result["warnings"]:
                    print(f"   ⚠️  警告: {validation_result['warnings']}")
        except Exception as e:
            print(f"   ❌ 验证异常: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 AI Gen Hub 供应商能力测试")
    print("=" * 60)
    
    try:
        test_provider_capabilities()
        test_validation()
        
        print("\n" + "=" * 60)
        print("📊 测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
