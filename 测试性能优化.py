#!/usr/bin/env python3
"""
测试性能优化功能

验证负载均衡、缓存管理、性能监控等优化功能
"""

import asyncio
import time
import sys
import json
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from ai_gen_hub.core.performance.load_balancer import SmartLoadBalancer, LoadBalancingStrategy
    from ai_gen_hub.core.performance.cache_manager import SmartCacheManager, CacheLevel
    from ai_gen_hub.core.performance.performance_monitor import PerformanceMonitor
except ImportError as e:
    print(f"❌ 导入性能优化模块失败: {e}")
    sys.exit(1)


class MockProvider:
    """模拟AI供应商"""
    
    def __init__(self, provider_id: str, response_time: float = 1.0, success_rate: float = 0.95):
        self.provider_id = provider_id
        self.response_time = response_time
        self.success_rate = success_rate
        self.status = 'active'
    
    async def generate_text(self, prompt: str) -> dict:
        """模拟文本生成"""
        await asyncio.sleep(self.response_time)
        
        import random
        if random.random() < self.success_rate:
            return {
                "content": f"Generated response for: {prompt[:50]}...",
                "provider": self.provider_id,
                "success": True
            }
        else:
            raise Exception(f"Provider {self.provider_id} failed")


async def test_load_balancer():
    """测试负载均衡器"""
    print("🔧 测试负载均衡器")
    print("=" * 60)
    
    # 创建负载均衡器
    lb = SmartLoadBalancer(strategy=LoadBalancingStrategy.ADAPTIVE)
    
    # 添加模拟供应商
    providers = [
        MockProvider("provider_1", response_time=0.5, success_rate=0.98),
        MockProvider("provider_2", response_time=1.0, success_rate=0.95),
        MockProvider("provider_3", response_time=1.5, success_rate=0.90)
    ]
    
    for provider in providers:
        lb.add_provider(provider, weight=1.0)
    
    print(f"✅ 添加了 {len(providers)} 个供应商")
    
    # 启动健康检查
    await lb.start_health_check()
    print("✅ 健康检查已启动")
    
    # 模拟请求
    total_requests = 50
    successful_requests = 0
    
    for i in range(total_requests):
        try:
            # 选择供应商
            provider = await lb.select_provider()
            if provider:
                # 记录请求开始
                lb.record_request_start(provider.provider_id)
                
                start_time = time.time()
                try:
                    # 模拟请求
                    result = await provider.generate_text(f"Test prompt {i}")
                    response_time = time.time() - start_time
                    
                    # 记录请求结束
                    lb.record_request_end(provider.provider_id, response_time, True)
                    successful_requests += 1
                    
                except Exception as e:
                    response_time = time.time() - start_time
                    lb.record_request_end(provider.provider_id, response_time, False)
                    print(f"⚠️ 请求失败: {e}")
            
        except Exception as e:
            print(f"❌ 负载均衡错误: {e}")
    
    # 获取指标摘要
    metrics = lb.get_metrics_summary()
    print(f"\n📊 负载均衡指标:")
    print(f"  策略: {metrics['strategy']}")
    print(f"  总供应商: {metrics['total_providers']}")
    print(f"  健康供应商: {metrics['healthy_providers']}")
    print(f"  成功请求: {successful_requests}/{total_requests}")
    
    for provider_id, provider_metrics in metrics["providers"].items():
        print(f"  {provider_id}:")
        print(f"    请求数: {provider_metrics['total_requests']}")
        print(f"    成功率: {provider_metrics['success_rate']}")
        print(f"    平均响应时间: {provider_metrics['avg_response_time']}")
        print(f"    健康分数: {provider_metrics['health_score']}")
    
    # 停止健康检查
    await lb.stop_health_check()
    
    return successful_requests >= total_requests * 0.8  # 80%成功率


async def test_cache_manager():
    """测试缓存管理器"""
    print("\n🔧 测试缓存管理器")
    print("=" * 60)
    
    # 创建缓存管理器
    cache = SmartCacheManager(memory_cache_size=100)
    
    # 测试基本缓存操作
    test_data = {
        "key1": "Simple string value",
        "key2": {"complex": "object", "with": ["nested", "data"]},
        "key3": list(range(1000)),  # 大数据测试压缩
    }
    
    # 设置缓存
    for key, value in test_data.items():
        success = await cache.set(key, value, ttl=3600)
        if success:
            print(f"✅ 设置缓存: {key}")
        else:
            print(f"❌ 设置缓存失败: {key}")
    
    # 获取缓存
    cache_hits = 0
    for key in test_data.keys():
        value = await cache.get(key)
        if value is not None:
            cache_hits += 1
            print(f"✅ 缓存命中: {key}")
        else:
            print(f"❌ 缓存未命中: {key}")
    
    # 测试缓存键生成
    cache_key = cache.generate_cache_key("test_prefix", "arg1", "arg2", param1="value1")
    print(f"✅ 生成缓存键: {cache_key}")
    
    # 测试get_or_set
    async def expensive_operation():
        await asyncio.sleep(0.1)  # 模拟耗时操作
        return {"result": "expensive_data", "timestamp": time.time()}
    
    # 第一次调用（缓存未命中）
    start_time = time.time()
    result1 = await cache.get_or_set("expensive_key", expensive_operation, ttl=3600)
    first_call_time = time.time() - start_time
    
    # 第二次调用（缓存命中）
    start_time = time.time()
    result2 = await cache.get_or_set("expensive_key", expensive_operation, ttl=3600)
    second_call_time = time.time() - start_time
    
    print(f"✅ 第一次调用时间: {first_call_time:.3f}秒")
    print(f"✅ 第二次调用时间: {second_call_time:.3f}秒")
    print(f"✅ 缓存加速比: {first_call_time / second_call_time:.1f}x")
    
    # 获取缓存统计
    stats = cache.get_stats()
    print(f"\n📊 缓存统计:")
    print(f"  总请求: {stats['total_requests']}")
    print(f"  内存命中: {stats['memory_hits']}")
    print(f"  Redis命中: {stats['redis_hits']}")
    print(f"  未命中: {stats['misses']}")
    print(f"  命中率: {stats['hit_rate']:.2%}")
    print(f"  内存缓存项: {stats['memory_cache']['items']}")
    
    await cache.close()
    
    return cache_hits >= len(test_data) * 0.8  # 80%命中率


async def test_performance_monitor():
    """测试性能监控器"""
    print("\n🔧 测试性能监控器")
    print("=" * 60)
    
    # 创建性能监控器
    monitor = PerformanceMonitor()
    
    # 启动监控（短间隔用于测试）
    await monitor.start_monitoring(interval=2)
    print("✅ 性能监控已启动")
    
    # 模拟一些请求
    for i in range(20):
        # 模拟不同的响应时间
        response_time = 0.1 + (i % 5) * 0.1
        success = i % 10 != 0  # 10%失败率
        
        monitor.record_request(response_time, success)
        await asyncio.sleep(0.1)
    
    # 等待收集一些数据
    await asyncio.sleep(5)
    
    # 获取性能报告
    report = monitor.get_performance_report(hours=1)
    
    print(f"\n📊 性能报告:")
    if "error" in report:
        print(f"❌ {report['error']}")
        return False
    
    print(f"  时间范围: {report['时间范围']}")
    print(f"  数据点数量: {report['数据点数量']}")
    
    if "系统资源" in report:
        sys_resources = report["系统资源"]
        print(f"  CPU使用率: {sys_resources['CPU使用率']['平均']}")
        print(f"  内存使用率: {sys_resources['内存使用率']['平均']}")
    
    if "应用性能" in report:
        app_perf = report["应用性能"]
        print(f"  平均响应时间: {app_perf['平均响应时间']}")
        print(f"  请求速率: {app_perf['请求速率']}")
        print(f"  错误率: {app_perf['错误率']}")
    
    print(f"  活跃告警: {report['活跃告警']}")
    print(f"  总告警数: {report['总告警数']}")
    
    # 停止监控
    await monitor.stop_monitoring()
    print("✅ 性能监控已停止")
    
    return report["数据点数量"] > 0


async def test_integrated_performance():
    """测试集成性能优化"""
    print("\n🔧 测试集成性能优化")
    print("=" * 60)
    
    # 创建组件
    lb = SmartLoadBalancer(strategy=LoadBalancingStrategy.ADAPTIVE)
    cache = SmartCacheManager(memory_cache_size=50)
    monitor = PerformanceMonitor(load_balancer=lb, cache_manager=cache)
    
    # 添加供应商
    providers = [
        MockProvider("fast_provider", response_time=0.2, success_rate=0.99),
        MockProvider("slow_provider", response_time=1.0, success_rate=0.95)
    ]
    
    for provider in providers:
        lb.add_provider(provider, weight=1.0)
    
    # 启动监控
    await lb.start_health_check()
    await monitor.start_monitoring(interval=1)
    
    print("✅ 集成系统已启动")
    
    # 模拟工作负载
    total_requests = 30
    cache_hits = 0
    successful_requests = 0
    
    for i in range(total_requests):
        try:
            # 生成缓存键
            cache_key = cache.generate_cache_key("text_gen", f"prompt_{i % 10}")
            
            # 尝试从缓存获取
            cached_result = await cache.get(cache_key)
            if cached_result:
                cache_hits += 1
                successful_requests += 1
                monitor.record_request(0.01, True)  # 缓存命中很快
                continue
            
            # 缓存未命中，使用负载均衡器
            provider = await lb.select_provider()
            if provider:
                lb.record_request_start(provider.provider_id)
                
                start_time = time.time()
                try:
                    result = await provider.generate_text(f"Test prompt {i}")
                    response_time = time.time() - start_time
                    
                    # 缓存结果
                    await cache.set(cache_key, result, ttl=1800)
                    
                    lb.record_request_end(provider.provider_id, response_time, True)
                    monitor.record_request(response_time, True)
                    successful_requests += 1
                    
                except Exception as e:
                    response_time = time.time() - start_time
                    lb.record_request_end(provider.provider_id, response_time, False)
                    monitor.record_request(response_time, False)
            
            await asyncio.sleep(0.1)  # 模拟请求间隔
            
        except Exception as e:
            print(f"❌ 请求处理错误: {e}")
    
    # 等待数据收集
    await asyncio.sleep(3)
    
    # 获取综合指标
    lb_metrics = lb.get_metrics_summary()
    cache_stats = cache.get_stats()
    perf_report = monitor.get_performance_report(hours=1)
    
    print(f"\n📊 集成性能指标:")
    print(f"  总请求: {total_requests}")
    print(f"  成功请求: {successful_requests}")
    print(f"  缓存命中: {cache_hits}")
    print(f"  缓存命中率: {cache_hits / total_requests:.2%}")
    print(f"  负载均衡策略: {lb_metrics['strategy']}")
    print(f"  健康供应商: {lb_metrics['healthy_providers']}/{lb_metrics['total_providers']}")
    
    # 清理
    await lb.stop_health_check()
    await monitor.stop_monitoring()
    await cache.close()
    
    # 评估性能
    success_rate = successful_requests / total_requests
    cache_hit_rate = cache_hits / total_requests
    
    print(f"\n✅ 性能评估:")
    print(f"  成功率: {success_rate:.2%}")
    print(f"  缓存效率: {cache_hit_rate:.2%}")
    
    return success_rate >= 0.8 and cache_hit_rate >= 0.3  # 期望指标


async def main():
    """主测试函数"""
    print("🚀 开始性能优化测试")
    print()
    
    tests = [
        ("负载均衡器", test_load_balancer),
        ("缓存管理器", test_cache_manager),
        ("性能监控器", test_performance_monitor),
        ("集成性能优化", test_integrated_performance),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"🧪 运行测试: {test_name}")
            print(f"{'='*60}")
            
            result = await test_func()
            
            if result:
                passed_tests += 1
                print(f"\n✅ 测试通过: {test_name}")
            else:
                print(f"\n❌ 测试失败: {test_name}")
                
        except Exception as e:
            print(f"\n❌ 测试异常: {test_name} - {e}")
            import traceback
            traceback.print_exc()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 性能优化测试结果总结")
    print("=" * 60)
    
    print(f"📈 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests >= total_tests - 1:  # 允许一个测试失败
        print("\n🎉 性能优化功能基本正常！")
        print("\n🚀 性能优化特性:")
        print("  • 智能负载均衡 - 多策略支持，自适应调整")
        print("  • 多级缓存系统 - 内存+Redis，智能压缩")
        print("  • 实时性能监控 - 自动告警，性能基线")
        print("  • 自动优化 - 基于监控数据自动调整")
        print("  • 健康检查 - 供应商状态监控，故障转移")
        print("\n📈 性能提升:")
        print("  • 响应时间优化 - 缓存加速，负载均衡")
        print("  • 并发处理能力 - 异步架构，连接池")
        print("  • 系统稳定性 - 故障转移，自动恢复")
        print("  • 资源利用率 - 智能调度，动态优化")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查实现")
        return False


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
