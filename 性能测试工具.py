#!/usr/bin/env python3
"""
AI Gen Hub 性能测试工具

用于测试系统性能，包括响应时间、并发处理能力、资源使用情况等
"""

import asyncio
import time
import json
import statistics
import psutil
import requests
import aiohttp
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    response_time: float
    status_code: int
    success: bool
    error_message: Optional[str] = None
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class SystemMetrics:
    """系统资源指标"""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


class PerformanceTester:
    """性能测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.session = None
        self.results: List[PerformanceMetrics] = []
        self.system_metrics: List[SystemMetrics] = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def test_api_availability(self) -> bool:
        """测试API可用性"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            return response.status_code == 200
        except:
            return False
    
    async def single_request_test(self, endpoint: str, method: str = "GET", 
                                 data: Dict = None, headers: Dict = None) -> PerformanceMetrics:
        """单个请求测试"""
        start_time = time.time()
        
        try:
            if method.upper() == "GET":
                async with self.session.get(f"{self.base_url}{endpoint}", 
                                          headers=headers) as response:
                    await response.text()
                    end_time = time.time()
                    return PerformanceMetrics(
                        response_time=end_time - start_time,
                        status_code=response.status,
                        success=response.status < 400
                    )
            else:
                async with self.session.post(f"{self.base_url}{endpoint}", 
                                           json=data, headers=headers) as response:
                    await response.text()
                    end_time = time.time()
                    return PerformanceMetrics(
                        response_time=end_time - start_time,
                        status_code=response.status,
                        success=response.status < 400
                    )
        except Exception as e:
            end_time = time.time()
            return PerformanceMetrics(
                response_time=end_time - start_time,
                status_code=0,
                success=False,
                error_message=str(e)
            )
    
    async def concurrent_test(self, endpoint: str, concurrent_users: int = 10, 
                            total_requests: int = 100, method: str = "GET",
                            data: Dict = None, headers: Dict = None) -> List[PerformanceMetrics]:
        """并发测试"""
        print(f"🚀 开始并发测试: {concurrent_users} 并发用户, {total_requests} 总请求")
        
        semaphore = asyncio.Semaphore(concurrent_users)
        tasks = []
        
        async def limited_request():
            async with semaphore:
                return await self.single_request_test(endpoint, method, data, headers)
        
        # 创建任务
        for _ in range(total_requests):
            tasks.append(limited_request())
        
        # 执行任务并收集结果
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤异常结果
        valid_results = []
        for result in results:
            if isinstance(result, PerformanceMetrics):
                valid_results.append(result)
            else:
                valid_results.append(PerformanceMetrics(
                    response_time=0,
                    status_code=0,
                    success=False,
                    error_message=str(result)
                ))
        
        return valid_results
    
    def monitor_system_resources(self, duration: int = 60, interval: int = 1) -> List[SystemMetrics]:
        """监控系统资源使用情况"""
        print(f"📊 开始监控系统资源: {duration}秒, 间隔{interval}秒")
        
        metrics = []
        start_time = time.time()
        
        while time.time() - start_time < duration:
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            
            metrics.append(SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / 1024 / 1024
            ))
            
            time.sleep(interval)
        
        return metrics
    
    def analyze_results(self, results: List[PerformanceMetrics]) -> Dict[str, Any]:
        """分析测试结果"""
        if not results:
            return {"error": "没有测试结果"}
        
        response_times = [r.response_time for r in results if r.success]
        success_count = sum(1 for r in results if r.success)
        total_count = len(results)
        
        if not response_times:
            return {"error": "没有成功的请求"}
        
        analysis = {
            "总请求数": total_count,
            "成功请求数": success_count,
            "失败请求数": total_count - success_count,
            "成功率": f"{success_count / total_count * 100:.2f}%",
            "平均响应时间": f"{statistics.mean(response_times):.3f}秒",
            "中位数响应时间": f"{statistics.median(response_times):.3f}秒",
            "最小响应时间": f"{min(response_times):.3f}秒",
            "最大响应时间": f"{max(response_times):.3f}秒",
            "95%分位数": f"{statistics.quantiles(response_times, n=20)[18]:.3f}秒" if len(response_times) > 20 else "N/A",
            "99%分位数": f"{statistics.quantiles(response_times, n=100)[98]:.3f}秒" if len(response_times) > 100 else "N/A",
            "QPS": f"{success_count / sum(response_times):.2f}" if response_times else "0"
        }
        
        return analysis
    
    def generate_report(self, results: List[PerformanceMetrics], 
                       system_metrics: List[SystemMetrics] = None) -> str:
        """生成性能测试报告"""
        analysis = self.analyze_results(results)
        
        report = f"""
# AI Gen Hub 性能测试报告

## 测试时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 测试结果概览
"""
        
        for key, value in analysis.items():
            report += f"- **{key}**: {value}\n"
        
        if system_metrics:
            cpu_usage = [m.cpu_percent for m in system_metrics]
            memory_usage = [m.memory_percent for m in system_metrics]
            
            report += f"""
## 系统资源使用情况
- **平均CPU使用率**: {statistics.mean(cpu_usage):.2f}%
- **最大CPU使用率**: {max(cpu_usage):.2f}%
- **平均内存使用率**: {statistics.mean(memory_usage):.2f}%
- **最大内存使用率**: {max(memory_usage):.2f}%
"""
        
        # 状态码分布
        status_codes = {}
        for result in results:
            code = result.status_code
            status_codes[code] = status_codes.get(code, 0) + 1
        
        report += "\n## HTTP状态码分布\n"
        for code, count in sorted(status_codes.items()):
            report += f"- **{code}**: {count}次\n"
        
        # 错误信息
        errors = [r.error_message for r in results if r.error_message]
        if errors:
            report += "\n## 错误信息\n"
            error_counts = {}
            for error in errors:
                error_counts[error] = error_counts.get(error, 0) + 1
            
            for error, count in error_counts.items():
                report += f"- **{error}**: {count}次\n"
        
        return report
    
    def save_results(self, results: List[PerformanceMetrics], filename: str = None):
        """保存测试结果"""
        if filename is None:
            filename = f"performance_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        data = []
        for result in results:
            data.append({
                "response_time": result.response_time,
                "status_code": result.status_code,
                "success": result.success,
                "error_message": result.error_message,
                "timestamp": result.timestamp
            })
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"📁 测试结果已保存到: {filename}")


async def run_comprehensive_test():
    """运行综合性能测试"""
    print("🚀 开始AI Gen Hub综合性能测试")
    print("=" * 60)
    
    async with PerformanceTester() as tester:
        # 检查API可用性
        if not tester.test_api_availability():
            print("❌ API服务不可用，请确保服务器正在运行")
            return
        
        print("✅ API服务可用，开始性能测试")
        
        # 测试场景
        test_scenarios = [
            {
                "name": "健康检查",
                "endpoint": "/health",
                "method": "GET",
                "concurrent_users": 50,
                "total_requests": 200
            },
            {
                "name": "获取指标",
                "endpoint": "/metrics",
                "method": "GET", 
                "concurrent_users": 20,
                "total_requests": 100
            }
        ]
        
        all_results = []
        
        for scenario in test_scenarios:
            print(f"\n📋 测试场景: {scenario['name']}")
            print("-" * 40)
            
            results = await tester.concurrent_test(
                endpoint=scenario["endpoint"],
                method=scenario["method"],
                concurrent_users=scenario["concurrent_users"],
                total_requests=scenario["total_requests"]
            )
            
            all_results.extend(results)
            
            # 分析单个场景结果
            analysis = tester.analyze_results(results)
            print("📊 测试结果:")
            for key, value in analysis.items():
                print(f"  {key}: {value}")
        
        # 生成综合报告
        print("\n" + "=" * 60)
        print("📋 综合性能测试报告")
        print("=" * 60)
        
        overall_analysis = tester.analyze_results(all_results)
        for key, value in overall_analysis.items():
            print(f"{key}: {value}")
        
        # 保存结果
        tester.save_results(all_results)
        
        # 生成详细报告
        report = tester.generate_report(all_results)
        report_filename = f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📄 详细报告已保存到: {report_filename}")
        
        return all_results


if __name__ == "__main__":
    try:
        results = asyncio.run(run_comprehensive_test())
        print("\n🎉 性能测试完成！")
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
