#!/usr/bin/env python3
"""
AI Gen Hub 修复版服务器启动脚本

解决依赖问题并正确启动服务器，应用所有修复。

使用方法:
    python start_server_fixed.py
"""

import asyncio
import logging
import os
import subprocess
import sys
import time
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class FixedServerStarter:
    """修复版服务器启动器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.src_path = self.project_root / "src"
        
        # 添加src目录到Python路径
        if str(self.src_path) not in sys.path:
            sys.path.insert(0, str(self.src_path))
    
    def install_system_dependencies(self) -> bool:
        """安装系统依赖"""
        logger.info("=== 安装系统依赖 ===")
        
        try:
            # 尝试安装Python包（使用--break-system-packages）
            packages = [
                'fastapi', 'uvicorn', 'pydantic', 'pydantic-settings', 
                'click', 'httpx', 'structlog', 'aioredis', 'jinja2', 
                'psutil', 'sse-starlette'
            ]
            
            for package in packages:
                try:
                    __import__(package.replace('-', '_'))
                    logger.info(f"✅ {package} 已安装")
                except ImportError:
                    logger.info(f"安装 {package}...")
                    result = subprocess.run([
                        sys.executable, '-m', 'pip', 'install', 
                        '--break-system-packages', package
                    ], capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        logger.info(f"✅ {package} 安装成功")
                    else:
                        logger.warning(f"⚠️  {package} 安装失败: {result.stderr}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 安装依赖失败: {e}")
            return False
    
    def check_environment(self) -> bool:
        """检查环境配置"""
        logger.info("=== 检查环境配置 ===")
        
        # 检查.env文件
        env_file = self.project_root / ".env"
        if not env_file.exists():
            logger.info("创建.env文件...")
            self.create_env_file()
        
        # 设置环境变量
        os.environ['DEBUG'] = 'true'
        os.environ['LOG_LEVEL'] = 'DEBUG'
        os.environ['ENVIRONMENT'] = 'development'
        
        # 检查Google AI API密钥
        google_ai_keys = os.environ.get('GOOGLE_AI_API_KEYS', '')
        if not google_ai_keys or 'your-google-ai-api-key' in google_ai_keys:
            logger.warning("⚠️  Google AI API密钥未正确配置")
            logger.warning("   请编辑.env文件，设置真实的GOOGLE_AI_API_KEYS")
            
            # 提供一个测试密钥提示
            logger.info("💡 如果只是测试，可以暂时使用任意字符串")
            
        logger.info("✅ 环境配置检查完成")
        return True
    
    def create_env_file(self):
        """创建.env文件"""
        env_content = """# AI Gen Hub 配置文件
DEBUG=true
LOG_LEVEL=DEBUG
ENVIRONMENT=development

# 服务配置
API_HOST=0.0.0.0
API_PORT=8001

# Google AI 配置
# 请将 your-google-ai-api-key 替换为实际的API密钥
GOOGLE_AI_API_KEYS=your-google-ai-api-key
GOOGLE_AI_ENABLED=true
GOOGLE_AI_TIMEOUT=120

# 其他供应商配置（可选）
# OPENAI_API_KEYS=your-openai-api-key
# ANTHROPIC_API_KEYS=your-anthropic-api-key

# 缓存配置（可选）
REDIS_URL=redis://localhost:6379/0
ENABLE_CACHING=false

# 监控配置
ENABLE_METRICS=true
HEALTH_CHECK_INTERVAL=60
"""
        
        env_file = self.project_root / ".env"
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        logger.info(f"✅ .env文件创建成功: {env_file}")
    
    def start_server_directly(self) -> bool:
        """直接启动服务器（不使用CLI）"""
        logger.info("=== 直接启动服务器 ===")
        
        try:
            # 导入必要的模块
            from ai_gen_hub.api.app import create_app
            from ai_gen_hub.core.logging import setup_logging
            import uvicorn
            
            # 设置日志
            setup_logging(debug=True)
            logger.info("✅ 日志系统初始化成功")
            
            # 创建FastAPI应用
            logger.info("创建FastAPI应用...")
            app = create_app()
            logger.info("✅ FastAPI应用创建成功")
            
            # 启动服务器
            logger.info("🚀 启动AI Gen Hub服务器...")
            logger.info("📍 地址: http://0.0.0.0:8001")
            logger.info("📖 API文档: http://localhost:8001/docs")
            logger.info("🏥 健康检查: http://localhost:8001/health")
            
            # 使用uvicorn启动
            uvicorn.run(
                app,
                host="0.0.0.0",
                port=8001,
                log_level="debug",
                access_log=True,
                reload=False  # 避免重载问题
            )
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 直接启动服务器失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def start_with_cli(self) -> bool:
        """使用CLI启动服务器"""
        logger.info("=== 使用CLI启动服务器 ===")
        
        try:
            # 导入CLI
            from ai_gen_hub.main import cli
            
            # 设置命令行参数
            original_argv = sys.argv.copy()
            sys.argv = ['ai_gen_hub', '--debug', 'serve', '--host', '0.0.0.0', '--port', '8001', '--reload']
            
            try:
                cli()
                return True
            finally:
                sys.argv = original_argv
                
        except Exception as e:
            logger.error(f"❌ CLI启动失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def verify_server(self) -> bool:
        """验证服务器功能"""
        logger.info("=== 验证服务器功能 ===")
        
        # 等待服务器启动
        await asyncio.sleep(3)
        
        try:
            import httpx
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                # 测试健康检查
                response = await client.get("http://localhost:8001/health")
                if response.status_code == 200:
                    logger.info("✅ 健康检查端点正常")
                else:
                    logger.warning(f"⚠️  健康检查返回状态码: {response.status_code}")
                
                # 测试API文档
                response = await client.get("http://localhost:8001/docs")
                if response.status_code == 200:
                    logger.info("✅ API文档端点正常")
                else:
                    logger.warning(f"⚠️  API文档返回状态码: {response.status_code}")
                
                return True
                
        except Exception as e:
            logger.error(f"❌ 服务器验证失败: {e}")
            return False
    
    def run_startup_sequence(self) -> bool:
        """运行启动序列"""
        logger.info("开始AI Gen Hub服务器启动序列...")
        
        try:
            # 1. 安装依赖
            if not self.install_system_dependencies():
                logger.error("依赖安装失败")
                return False
            
            # 2. 检查环境
            if not self.check_environment():
                logger.error("环境检查失败")
                return False
            
            # 3. 尝试启动服务器
            logger.info("尝试直接启动服务器...")
            if self.start_server_directly():
                return True
            
            logger.info("直接启动失败，尝试CLI启动...")
            if self.start_with_cli():
                return True
            
            logger.error("所有启动方式都失败了")
            return False
            
        except KeyboardInterrupt:
            logger.info("用户中断启动")
            return False
        except Exception as e:
            logger.error(f"启动序列异常: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    starter = FixedServerStarter()
    
    try:
        success = starter.run_startup_sequence()
        
        if success:
            logger.info("🎉 服务器启动成功！")
        else:
            logger.error("❌ 服务器启动失败")
            
            # 提供备用方案
            logger.info("💡 备用启动方案:")
            logger.info("1. 手动安装依赖:")
            logger.info("   pip install --break-system-packages fastapi uvicorn httpx structlog")
            logger.info("2. 直接运行应用:")
            logger.info("   python -c \"from src.ai_gen_hub.api.app import create_app; import uvicorn; uvicorn.run(create_app(), host='0.0.0.0', port=8001)\"")
            
            return 1
            
    except Exception as e:
        logger.error(f"主程序异常: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
