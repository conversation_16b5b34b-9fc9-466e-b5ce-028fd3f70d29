#!/usr/bin/env python3
"""
测试智能模型路由和A/B测试系统

验证模型注册、路由决策、A/B测试等功能
"""

import asyncio
import random
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

# 创建必要的模块
def create_mock_modules():
    """创建模拟模块"""
    # 创建基础接口
    class BaseRequest:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
            if not hasattr(self, 'id'):
                self.id = f"req_{int(time.time())}"
    
    class BaseResponse:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
    
    # 创建异常类
    class AIGenHubException(Exception):
        pass
    
    # 创建日志记录器
    class Logger:
        def info(self, msg, **kwargs):
            print(f"INFO: {msg}")
        
        def error(self, msg, **kwargs):
            print(f"ERROR: {msg}")
        
        def warning(self, msg, **kwargs):
            print(f"WARNING: {msg}")
    
    def get_logger(name):
        return Logger()
    
    return {
        'BaseRequest': BaseRequest,
        'BaseResponse': BaseResponse,
        'AIGenHubException': AIGenHubException,
        'get_logger': get_logger
    }

# 创建模拟模块
mock_modules = create_mock_modules()

# 简化的路由系统
from enum import Enum
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional
from datetime import datetime
import uuid
import hashlib
import statistics

class RoutingStrategy(Enum):
    ROUND_ROBIN = "round_robin"
    LEAST_LATENCY = "least_latency"
    LEAST_COST = "least_cost"
    ADAPTIVE = "adaptive"
    AB_TEST = "ab_test"

class ModelStatus(Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"

class ABTestStatus(Enum):
    DRAFT = "draft"
    RUNNING = "running"
    COMPLETED = "completed"

class MetricType(Enum):
    LATENCY = "latency"
    COST = "cost"
    QUALITY_SCORE = "quality_score"
    SUCCESS_RATE = "success_rate"

@dataclass
class ModelConfig:
    model_id: str
    provider: str
    model_name: str
    version: str = "latest"
    cost_per_token: float = 0.0
    quality_score: float = 0.0
    weight: float = 1.0
    status: ModelStatus = ModelStatus.ACTIVE

@dataclass
class ModelMetrics:
    model_id: str
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_latency: float = 0.0
    avg_cost_per_request: float = 0.0
    avg_quality_score: float = 0.0
    
    def calculate_success_rate(self) -> float:
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests

@dataclass
class RoutingDecision:
    request_id: str
    selected_model: ModelConfig
    strategy_used: RoutingStrategy
    decision_reason: str = ""
    ab_test_id: Optional[str] = None
    ab_test_group: Optional[str] = None

@dataclass
class ABTestGroup:
    group_id: str
    name: str
    model_configs: List[ModelConfig] = field(default_factory=list)
    traffic_percentage: float = 50.0
    target_metrics: List[MetricType] = field(default_factory=list)

@dataclass
class ABTest:
    test_id: str
    name: str
    control_group: ABTestGroup
    treatment_groups: List[ABTestGroup] = field(default_factory=list)
    status: ABTestStatus = ABTestStatus.DRAFT
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    total_traffic_percentage: float = 100.0
    
    def is_active(self) -> bool:
        return self.status == ABTestStatus.RUNNING


class SimpleModelRegistry:
    """简化的模型注册表"""
    
    def __init__(self):
        self.logger = mock_modules['get_logger'](__name__)
        self.models: Dict[str, ModelConfig] = {}
        self.metrics: Dict[str, ModelMetrics] = {}
    
    def register_model(self, model: ModelConfig):
        """注册模型"""
        self.models[model.model_id] = model
        self.metrics[model.model_id] = ModelMetrics(model.model_id)
        self.logger.info(f"注册模型: {model.model_id}")
    
    def get_active_models(self) -> List[ModelConfig]:
        """获取激活的模型"""
        return [model for model in self.models.values() if model.status == ModelStatus.ACTIVE]
    
    def update_model_metrics(self, model_id: str, latency: float, cost: float, success: bool, quality_score: float = None):
        """更新模型指标"""
        if model_id not in self.metrics:
            return
        
        metrics = self.metrics[model_id]
        metrics.total_requests += 1
        
        if success:
            metrics.successful_requests += 1
            # 更新平均值
            metrics.avg_latency = (metrics.avg_latency * (metrics.successful_requests - 1) + latency) / metrics.successful_requests
            metrics.avg_cost_per_request = (metrics.avg_cost_per_request * (metrics.successful_requests - 1) + cost) / metrics.successful_requests
            if quality_score is not None:
                metrics.avg_quality_score = (metrics.avg_quality_score * (metrics.successful_requests - 1) + quality_score) / metrics.successful_requests
        else:
            metrics.failed_requests += 1
    
    def get_model_metrics(self, model_id: str) -> Optional[ModelMetrics]:
        """获取模型指标"""
        return self.metrics.get(model_id)


class SimpleRoutingEngine:
    """简化的路由引擎"""
    
    def __init__(self, model_registry: SimpleModelRegistry):
        self.logger = mock_modules['get_logger'](__name__)
        self.model_registry = model_registry
        self.ab_tests: Dict[str, ABTest] = {}
        self.last_selected_index = 0
    
    async def route_request(self, request_context: Dict[str, Any], user_id: str = None) -> RoutingDecision:
        """路由请求"""
        request_id = f"req_{int(time.time())}"
        
        # 获取候选模型
        models = self.model_registry.get_active_models()
        if not models:
            raise mock_modules['AIGenHubException']("没有可用的模型")
        
        # 检查A/B测试
        ab_decision = self._check_ab_tests(request_context, user_id, models)
        if ab_decision:
            return ab_decision
        
        # 使用自适应策略
        selected_model = self._adaptive_select(models)
        
        return RoutingDecision(
            request_id=request_id,
            selected_model=selected_model,
            strategy_used=RoutingStrategy.ADAPTIVE,
            decision_reason="自适应路由策略"
        )
    
    def _check_ab_tests(self, request_context: Dict[str, Any], user_id: str, models: List[ModelConfig]) -> Optional[RoutingDecision]:
        """检查A/B测试"""
        for ab_test in self.ab_tests.values():
            if not ab_test.is_active():
                continue
            
            # 简单的用户分组逻辑
            if self._should_participate_in_test(user_id, ab_test):
                group = self._assign_test_group(user_id, ab_test)
                if group and group.model_configs:
                    selected_model = random.choice(group.model_configs)
                    return RoutingDecision(
                        request_id=f"req_{int(time.time())}",
                        selected_model=selected_model,
                        strategy_used=RoutingStrategy.AB_TEST,
                        decision_reason=f"A/B测试: {ab_test.name}",
                        ab_test_id=ab_test.test_id,
                        ab_test_group=group.group_id
                    )
        
        return None
    
    def _should_participate_in_test(self, user_id: str, ab_test: ABTest) -> bool:
        """判断用户是否参与测试"""
        if not user_id:
            return False
        
        # 基于用户ID哈希决定
        hash_value = int(hashlib.md5(f"{ab_test.test_id}:{user_id}".encode()).hexdigest(), 16)
        percentage = (hash_value % 100) + 1
        return percentage <= ab_test.total_traffic_percentage
    
    def _assign_test_group(self, user_id: str, ab_test: ABTest) -> Optional[ABTestGroup]:
        """分配测试组"""
        all_groups = [ab_test.control_group] + ab_test.treatment_groups
        
        # 基于用户ID哈希分组
        hash_value = int(hashlib.md5(f"{ab_test.test_id}:group:{user_id}".encode()).hexdigest(), 16)
        total_weight = sum(group.traffic_percentage for group in all_groups)
        
        if total_weight == 0:
            return None
        
        target = (hash_value % int(total_weight * 100)) / 100
        cumulative = 0
        
        for group in all_groups:
            cumulative += group.traffic_percentage
            if target <= cumulative:
                return group
        
        return all_groups[0]
    
    def _adaptive_select(self, models: List[ModelConfig]) -> ModelConfig:
        """自适应选择"""
        best_model = None
        best_score = -1
        
        for model in models:
            metrics = self.model_registry.get_model_metrics(model.model_id)
            if not metrics:
                continue
            
            # 计算综合分数
            latency_score = 1.0 / (1.0 + metrics.avg_latency) if metrics.avg_latency > 0 else 1.0
            cost_score = 1.0 / (1.0 + metrics.avg_cost_per_request) if metrics.avg_cost_per_request > 0 else 1.0
            quality_score = metrics.avg_quality_score / 10.0 if metrics.avg_quality_score > 0 else 0.5
            success_rate = metrics.calculate_success_rate()
            
            composite_score = (
                latency_score * 0.3 +
                cost_score * 0.2 +
                quality_score * 0.3 +
                success_rate * 0.2
            )
            
            if composite_score > best_score:
                best_score = composite_score
                best_model = model
        
        return best_model or models[0]


class SimpleABTestManager:
    """简化的A/B测试管理器"""
    
    def __init__(self):
        self.logger = mock_modules['get_logger'](__name__)
        self.tests: Dict[str, ABTest] = {}
        self.test_data: Dict[str, Dict[str, List[Dict[str, Any]]]] = {}
    
    def create_test(self, test: ABTest) -> ABTest:
        """创建A/B测试"""
        self.tests[test.test_id] = test
        self.test_data[test.test_id] = {}
        self.logger.info(f"创建A/B测试: {test.name}")
        return test
    
    def start_test(self, test_id: str) -> bool:
        """启动测试"""
        test = self.tests.get(test_id)
        if not test:
            return False
        
        test.status = ABTestStatus.RUNNING
        test.start_time = datetime.now()
        self.logger.info(f"启动A/B测试: {test.name}")
        return True
    
    def record_result(self, test_id: str, group_id: str, metrics: Dict[MetricType, float], user_id: str = None):
        """记录测试结果"""
        if test_id not in self.test_data:
            self.test_data[test_id] = {}
        
        if group_id not in self.test_data[test_id]:
            self.test_data[test_id][group_id] = []
        
        data_point = {
            'timestamp': datetime.now(),
            'user_id': user_id,
            'metrics': metrics
        }
        
        self.test_data[test_id][group_id].append(data_point)
    
    def get_test_summary(self, test_id: str) -> Dict[str, Any]:
        """获取测试摘要"""
        test = self.tests.get(test_id)
        if not test:
            return {}
        
        summary = {
            'test_id': test_id,
            'name': test.name,
            'status': test.status.value,
            'groups': []
        }
        
        # 计算各组统计
        for group in [test.control_group] + test.treatment_groups:
            group_data = self.test_data.get(test_id, {}).get(group.group_id, [])
            
            group_info = {
                'group_id': group.group_id,
                'name': group.name,
                'sample_size': len(group_data),
                'is_control': group.group_id == test.control_group.group_id
            }
            
            if group_data:
                # 计算平均指标
                metrics = {}
                for metric_type in group.target_metrics:
                    values = [point['metrics'].get(metric_type, 0) for point in group_data 
                             if metric_type in point['metrics']]
                    if values:
                        metrics[metric_type.value] = statistics.mean(values)
                
                group_info['metrics'] = metrics
            
            summary['groups'].append(group_info)
        
        return summary


async def test_model_registration():
    """测试模型注册"""
    print("🔧 测试模型注册")
    print("=" * 40)
    
    registry = SimpleModelRegistry()
    
    # 注册多个模型
    models = [
        ModelConfig("gpt-4", "openai", "gpt-4", cost_per_token=0.03, quality_score=9.5),
        ModelConfig("gpt-3.5", "openai", "gpt-3.5-turbo", cost_per_token=0.002, quality_score=8.0),
        ModelConfig("claude-3", "anthropic", "claude-3-sonnet", cost_per_token=0.015, quality_score=9.0),
        ModelConfig("gemini-pro", "google", "gemini-pro", cost_per_token=0.001, quality_score=8.5)
    ]
    
    for model in models:
        registry.register_model(model)
    
    active_models = registry.get_active_models()
    print(f"✅ 注册了 {len(models)} 个模型")
    print(f"✅ 激活模型数量: {len(active_models)}")
    
    return registry


async def test_routing_engine():
    """测试路由引擎"""
    print("\n🔧 测试路由引擎")
    print("=" * 40)
    
    registry = await test_model_registration()
    engine = SimpleRoutingEngine(registry)
    
    # 模拟一些请求
    for i in range(10):
        request_context = {"user_type": "premium" if i % 2 == 0 else "basic"}
        user_id = f"user_{i}"
        
        decision = await engine.route_request(request_context, user_id)
        
        # 模拟请求结果
        latency = random.uniform(0.5, 3.0)
        cost = random.uniform(0.001, 0.05)
        success = random.random() > 0.1  # 90%成功率
        quality_score = random.uniform(7.0, 10.0)
        
        # 更新指标
        registry.update_model_metrics(
            decision.selected_model.model_id,
            latency, cost, success, quality_score
        )
        
        print(f"  请求 {i+1}: {decision.selected_model.model_id} ({decision.strategy_used.value})")
    
    # 显示模型指标
    print("\n📊 模型性能指标:")
    for model in registry.get_active_models():
        metrics = registry.get_model_metrics(model.model_id)
        if metrics and metrics.total_requests > 0:
            print(f"  {model.model_id}: 请求数={metrics.total_requests}, "
                  f"成功率={metrics.calculate_success_rate():.2f}, "
                  f"平均延迟={metrics.avg_latency:.2f}s")
    
    return engine


async def test_ab_testing():
    """测试A/B测试"""
    print("\n🔧 测试A/B测试")
    print("=" * 40)
    
    registry = await test_model_registration()
    engine = SimpleRoutingEngine(registry)
    ab_manager = SimpleABTestManager()
    
    # 连接A/B测试管理器到路由引擎
    engine.ab_tests = ab_manager.tests
    
    # 创建A/B测试
    models = registry.get_active_models()
    
    control_group = ABTestGroup(
        group_id="control",
        name="对照组",
        model_configs=[models[0]],  # GPT-4
        traffic_percentage=50.0,
        target_metrics=[MetricType.LATENCY, MetricType.QUALITY_SCORE]
    )
    
    treatment_group = ABTestGroup(
        group_id="treatment",
        name="实验组",
        model_configs=[models[1]],  # GPT-3.5
        traffic_percentage=50.0,
        target_metrics=[MetricType.LATENCY, MetricType.QUALITY_SCORE]
    )
    
    ab_test = ABTest(
        test_id="test_001",
        name="GPT-4 vs GPT-3.5 性能对比",
        control_group=control_group,
        treatment_groups=[treatment_group],
        total_traffic_percentage=20.0  # 只有20%的用户参与测试
    )
    
    # 创建并启动测试
    ab_manager.create_test(ab_test)
    ab_manager.start_test("test_001")
    
    print(f"✅ 创建A/B测试: {ab_test.name}")
    
    # 模拟用户请求
    for i in range(50):
        user_id = f"user_{i}"
        request_context = {"feature": "chat"}
        
        decision = await engine.route_request(request_context, user_id)
        
        # 模拟请求结果
        latency = random.uniform(0.5, 3.0)
        quality_score = random.uniform(7.0, 10.0)
        
        # 如果是A/B测试请求，记录结果
        if decision.ab_test_id:
            metrics = {
                MetricType.LATENCY: latency,
                MetricType.QUALITY_SCORE: quality_score
            }
            ab_manager.record_result(decision.ab_test_id, decision.ab_test_group, metrics, user_id)
    
    # 获取测试结果
    summary = ab_manager.get_test_summary("test_001")
    
    print(f"✅ A/B测试完成，参与用户数: {sum(group['sample_size'] for group in summary['groups'])}")
    
    for group in summary['groups']:
        print(f"  {group['name']}: 样本数={group['sample_size']}")
        if 'metrics' in group:
            for metric, value in group['metrics'].items():
                print(f"    {metric}: {value:.3f}")
    
    return True


async def test_integrated_routing():
    """测试集成路由功能"""
    print("\n🔧 测试集成路由功能")
    print("=" * 40)
    
    # 创建完整的路由系统
    registry = SimpleModelRegistry()
    engine = SimpleRoutingEngine(registry)
    ab_manager = SimpleABTestManager()
    
    # 注册模型
    models = [
        ModelConfig("fast-model", "provider-a", "fast-model", cost_per_token=0.001, quality_score=7.0),
        ModelConfig("balanced-model", "provider-b", "balanced-model", cost_per_token=0.01, quality_score=8.5),
        ModelConfig("premium-model", "provider-c", "premium-model", cost_per_token=0.05, quality_score=9.5)
    ]
    
    for model in models:
        registry.register_model(model)
    
    print(f"✅ 注册了 {len(models)} 个模型")
    
    # 模拟不同场景的路由
    scenarios = [
        {"name": "快速响应场景", "context": {"priority": "speed"}, "users": 20},
        {"name": "成本敏感场景", "context": {"priority": "cost"}, "users": 15},
        {"name": "质量优先场景", "context": {"priority": "quality"}, "users": 25}
    ]
    
    routing_stats = {}
    
    for scenario in scenarios:
        print(f"\n📋 测试场景: {scenario['name']}")
        scenario_stats = {}
        
        for i in range(scenario['users']):
            user_id = f"{scenario['name']}_user_{i}"
            
            decision = await engine.route_request(scenario['context'], user_id)
            model_id = decision.selected_model.model_id
            
            scenario_stats[model_id] = scenario_stats.get(model_id, 0) + 1
            
            # 模拟请求结果
            latency = random.uniform(0.5, 3.0)
            cost = random.uniform(0.001, 0.05)
            success = random.random() > 0.05  # 95%成功率
            quality_score = random.uniform(7.0, 10.0)
            
            registry.update_model_metrics(model_id, latency, cost, success, quality_score)
        
        routing_stats[scenario['name']] = scenario_stats
        
        for model_id, count in scenario_stats.items():
            percentage = (count / scenario['users']) * 100
            print(f"  {model_id}: {count} 次 ({percentage:.1f}%)")
    
    print("\n📊 最终路由统计:")
    for scenario_name, stats in routing_stats.items():
        print(f"  {scenario_name}: {stats}")
    
    return True


async def main():
    """主测试函数"""
    print("🚀 开始智能模型路由和A/B测试系统测试")
    print()
    
    tests = [
        ("模型注册", test_model_registration),
        ("路由引擎", test_routing_engine),
        ("A/B测试", test_ab_testing),
        ("集成路由功能", test_integrated_routing),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed_tests += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 智能模型路由和A/B测试系统测试结果")
    print("=" * 50)
    
    print(f"📈 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 智能模型路由和A/B测试系统测试全部通过！")
        print("\n🚀 支持的功能:")
        print("  • 智能模型注册和管理")
        print("  • 多策略路由决策")
        print("  • A/B测试和统计分析")
        print("  • 实时性能监控")
        print("  • 自适应模型选择")
        print("\n📈 技术特性:")
        print("  • 多种路由策略支持")
        print("  • 统计显著性检验")
        print("  • 实时指标收集")
        print("  • 用户分组和流量控制")
        print("  • 自动化决策优化")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查实现")
        return False


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)
