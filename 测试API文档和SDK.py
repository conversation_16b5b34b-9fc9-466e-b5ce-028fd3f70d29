#!/usr/bin/env python3
"""
测试API文档和SDK功能

验证API文档的完整性和SDK的功能
"""

import os
import sys
import json
import requests
from pathlib import Path


def test_api_documentation():
    """测试API文档"""
    print("🔧 测试API文档")
    print("=" * 60)
    
    docs_path = Path("docs/api")
    
    # 检查文档文件是否存在
    required_docs = [
        "README.md",
        "text-generation.md"
    ]
    
    missing_docs = []
    for doc in required_docs:
        doc_path = docs_path / doc
        if not doc_path.exists():
            missing_docs.append(doc)
        else:
            print(f"✅ 找到文档: {doc}")
    
    if missing_docs:
        print(f"❌ 缺少文档: {missing_docs}")
        return False
    
    # 检查文档内容
    readme_path = docs_path / "README.md"
    with open(readme_path, 'r', encoding='utf-8') as f:
        readme_content = f.read()
    
    # 检查关键内容
    required_sections = [
        "基础URL",
        "认证方式",
        "请求格式",
        "响应格式",
        "HTTP状态码",
        "错误代码"
    ]
    
    missing_sections = []
    for section in required_sections:
        if section not in readme_content:
            missing_sections.append(section)
        else:
            print(f"✅ 包含章节: {section}")
    
    if missing_sections:
        print(f"❌ 缺少章节: {missing_sections}")
        return False
    
    print("✅ API文档检查通过")
    return True


def test_python_sdk_structure():
    """测试Python SDK结构"""
    print("\n🔧 测试Python SDK结构")
    print("=" * 60)
    
    sdk_path = Path("sdk/python")
    
    # 检查必要文件
    required_files = [
        "setup.py",
        "README.md",
        "ai_gen_hub_sdk/__init__.py",
        "ai_gen_hub_sdk/client.py",
        "ai_gen_hub_sdk/exceptions.py",
        "ai_gen_hub_sdk/models.py",
        "ai_gen_hub_sdk/services.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = sdk_path / file_path
        if not full_path.exists():
            missing_files.append(file_path)
        else:
            print(f"✅ 找到文件: {file_path}")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    # 检查setup.py内容
    setup_path = sdk_path / "setup.py"
    with open(setup_path, 'r', encoding='utf-8') as f:
        setup_content = f.read()
    
    required_setup_items = [
        "name=\"ai-gen-hub-sdk\"",
        "version=",
        "install_requires=",
        "httpx"
    ]
    
    for item in required_setup_items:
        if item in setup_content:
            print(f"✅ setup.py包含: {item}")
        else:
            print(f"❌ setup.py缺少: {item}")
            return False
    
    print("✅ Python SDK结构检查通过")
    return True


def test_javascript_sdk_structure():
    """测试JavaScript SDK结构"""
    print("\n🔧 测试JavaScript SDK结构")
    print("=" * 60)
    
    sdk_path = Path("sdk/javascript")
    
    # 检查必要文件
    required_files = [
        "package.json",
        "README.md",
        "src/index.ts",
        "src/client.ts",
        "src/types.ts",
        "src/exceptions.ts",
        "src/constants.ts",
        "src/services/text.ts",
        "src/services/image.ts",
        "src/services/auth.ts"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = sdk_path / file_path
        if not full_path.exists():
            missing_files.append(file_path)
        else:
            print(f"✅ 找到文件: {file_path}")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    # 检查package.json内容
    package_path = sdk_path / "package.json"
    with open(package_path, 'r', encoding='utf-8') as f:
        package_data = json.load(f)
    
    required_package_items = [
        ("name", "ai-gen-hub-sdk"),
        ("version", "1.0.0"),
        ("main", "dist/index.js"),
        ("types", "dist/index.d.ts")
    ]
    
    for key, expected_value in required_package_items:
        if key in package_data and package_data[key] == expected_value:
            print(f"✅ package.json包含: {key} = {expected_value}")
        else:
            print(f"❌ package.json缺少或错误: {key}")
            return False
    
    # 检查依赖
    dependencies = package_data.get("dependencies", {})
    required_deps = ["axios", "eventsource"]
    
    for dep in required_deps:
        if dep in dependencies:
            print(f"✅ 包含依赖: {dep}")
        else:
            print(f"❌ 缺少依赖: {dep}")
            return False
    
    print("✅ JavaScript SDK结构检查通过")
    return True


def test_sdk_examples():
    """测试SDK示例代码"""
    print("\n🔧 测试SDK示例代码")
    print("=" * 60)
    
    # 检查Python SDK示例
    python_readme = Path("sdk/python/README.md")
    if python_readme.exists():
        with open(python_readme, 'r', encoding='utf-8') as f:
            python_content = f.read()
        
        python_examples = [
            "from ai_gen_hub_sdk import AIGenHubClient",
            "client.text.generate",
            "client.image.generate",
            "async def",
            "await client.text.agenerate"
        ]
        
        for example in python_examples:
            if example in python_content:
                print(f"✅ Python示例包含: {example}")
            else:
                print(f"❌ Python示例缺少: {example}")
                return False
    
    # 检查JavaScript SDK示例
    js_readme = Path("sdk/javascript/README.md")
    if js_readme.exists():
        with open(js_readme, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        js_examples = [
            "import { AIGenHubClient }",
            "client.text.generate",
            "client.image.generate",
            "async function",
            "for await"
        ]
        
        for example in js_examples:
            if example in js_content:
                print(f"✅ JavaScript示例包含: {example}")
            else:
                print(f"❌ JavaScript示例缺少: {example}")
                return False
    
    print("✅ SDK示例代码检查通过")
    return True


def test_api_endpoints_documentation():
    """测试API端点文档"""
    print("\n🔧 测试API端点文档")
    print("=" * 60)
    
    text_gen_doc = Path("docs/api/text-generation.md")
    if not text_gen_doc.exists():
        print("❌ 文本生成API文档不存在")
        return False
    
    with open(text_gen_doc, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查必要的API端点
    required_endpoints = [
        "/api/v1/text/generate",
        "/api/v1/text/stream",
        "/api/v1/text/models",
        "/api/v1/text/chat",
        "/api/v1/text/complete"
    ]
    
    for endpoint in required_endpoints:
        if endpoint in content:
            print(f"✅ 文档包含端点: {endpoint}")
        else:
            print(f"❌ 文档缺少端点: {endpoint}")
            return False
    
    # 检查请求示例
    required_examples = [
        "curl",
        "python",
        "javascript",
        "POST",
        "Content-Type: application/json"
    ]
    
    for example in required_examples:
        if example.lower() in content.lower():
            print(f"✅ 文档包含示例: {example}")
        else:
            print(f"❌ 文档缺少示例: {example}")
            return False
    
    print("✅ API端点文档检查通过")
    return True


def test_openapi_compatibility():
    """测试OpenAPI兼容性"""
    print("\n🔧 测试OpenAPI兼容性")
    print("=" * 60)
    
    try:
        # 检查服务器是否运行
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code != 200:
            print("⚠️ 服务器未运行，跳过OpenAPI测试")
            return True
    except requests.exceptions.ConnectionError:
        print("⚠️ 无法连接到服务器，跳过OpenAPI测试")
        return True
    
    try:
        # 获取OpenAPI规范
        response = requests.get("http://localhost:8001/openapi.json", timeout=10)
        if response.status_code == 200:
            openapi_spec = response.json()
            
            # 检查基本结构
            required_fields = ["openapi", "info", "paths"]
            for field in required_fields:
                if field in openapi_spec:
                    print(f"✅ OpenAPI包含字段: {field}")
                else:
                    print(f"❌ OpenAPI缺少字段: {field}")
                    return False
            
            # 检查API路径
            paths = openapi_spec.get("paths", {})
            required_paths = [
                "/api/v1/text/generate",
                "/api/v1/image/generate",
                "/health"
            ]
            
            for path in required_paths:
                if path in paths:
                    print(f"✅ OpenAPI包含路径: {path}")
                else:
                    print(f"❌ OpenAPI缺少路径: {path}")
                    return False
            
            print("✅ OpenAPI兼容性检查通过")
            return True
        else:
            print(f"❌ 无法获取OpenAPI规范: {response.status_code}")
            return False
    
    except Exception as e:
        print(f"❌ OpenAPI测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始API文档和SDK测试")
    print()
    
    tests = [
        ("API文档", test_api_documentation),
        ("Python SDK结构", test_python_sdk_structure),
        ("JavaScript SDK结构", test_javascript_sdk_structure),
        ("SDK示例代码", test_sdk_examples),
        ("API端点文档", test_api_endpoints_documentation),
        ("OpenAPI兼容性", test_openapi_compatibility),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed_tests += 1
            print(f"{'✅ 通过' if result else '❌ 失败'}: {test_name}")
        except Exception as e:
            print(f"❌ 异常: {test_name} - {e}")
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 API文档和SDK测试结果总结")
    print("=" * 60)
    
    print(f"📈 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests >= total_tests - 1:  # 允许一个测试失败
        print("\n🎉 API文档和SDK基本功能正常！")
        print("\n📚 文档结构:")
        print("  • docs/api/ - API参考文档")
        print("  • sdk/python/ - Python SDK")
        print("  • sdk/javascript/ - JavaScript/TypeScript SDK")
        print("\n🛠️ SDK特性:")
        print("  • 完整的类型定义")
        print("  • 同步和异步支持")
        print("  • 流式输出")
        print("  • 错误处理")
        print("  • 重试机制")
        print("  • 多种认证方式")
        print("\n📖 使用方式:")
        print("  Python: pip install ./sdk/python")
        print("  JavaScript: npm install ./sdk/javascript")
        print("\n🌐 在线文档:")
        print("  API文档: http://localhost:8001/docs")
        print("  管理控制台: http://localhost:8001/admin/dashboard")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查实现")
        return False


if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
