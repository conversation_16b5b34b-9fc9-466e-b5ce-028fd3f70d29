#!/usr/bin/env python3
"""
AI Gen Hub 服务器启动脚本（修复版）

修复的问题：
1. 使用正确的CLI命令语法：--debug 在主命令级别
2. 修复StreamConfig的Pydantic验证错误
3. 提供详细的启动日志和错误处理

使用方法：
    python start_server_with_correct_cli.py
"""

import os
import sys
import subprocess
import time
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_environment():
    """设置环境变量"""
    logger.info("设置环境变量...")
    
    # 基础环境配置
    os.environ['DEBUG'] = 'true'
    os.environ['LOG_LEVEL'] = 'DEBUG'
    os.environ['ENVIRONMENT'] = 'development'
    
    # 禁用可能导致问题的功能
    os.environ['ENABLE_CACHING'] = 'false'
    os.environ['ENABLE_METRICS'] = 'false'
    
    # Google AI配置
    google_ai_keys = os.environ.get('GOOGLE_AI_API_KEYS', '')
    if not google_ai_keys or google_ai_keys == 'test-key-for-debugging':
        logger.warning("⚠️  需要设置真实的Google AI API密钥")
        logger.info("请设置环境变量: export GOOGLE_AI_API_KEYS='your-real-api-key'")
        logger.info("或者编辑.env文件")
        # 为了演示，我们仍然使用测试密钥，但会显示警告
        os.environ['GOOGLE_AI_API_KEYS'] = 'test-key-for-debugging'
    
    os.environ['GOOGLE_AI_ENABLED'] = 'true'
    
    logger.info("✅ 环境变量设置完成")

def check_dependencies():
    """检查必要的依赖"""
    logger.info("检查Python依赖...")
    
    required_packages = [
        'fastapi', 'uvicorn', 'httpx', 'structlog', 'pydantic',
        'prometheus_client', 'tenacity', 'PyJWT'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少依赖包: {missing_packages}")
        logger.info("正在安装缺少的依赖...")
        
        for package in missing_packages:
            try:
                subprocess.run([
                    sys.executable, '-m', 'pip', 'install', 
                    '--break-system-packages', package
                ], check=True, capture_output=True)
                logger.info(f"✅ 已安装: {package}")
            except subprocess.CalledProcessError as e:
                logger.error(f"❌ 安装失败: {package} - {e}")
                return False
    
    logger.info("✅ 所有依赖检查完成")
    return True

def start_server():
    """启动服务器"""
    logger.info("🚀 启动AI Gen Hub服务器...")
    
    # 使用正确的CLI命令语法
    cmd = [
        sys.executable, '-m', 'ai_gen_hub.main',
        '--debug',  # --debug 在主命令级别
        'serve',    # serve 子命令
        '--host', '0.0.0.0',
        '--port', '8001',
        '--reload'  # --reload 在serve子命令级别
    ]
    
    logger.info(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 启动服务器进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        logger.info("✅ 服务器进程已启动")
        logger.info("📍 服务器地址:")
        logger.info("   - 主页: http://localhost:8001")
        logger.info("   - API文档: http://localhost:8001/docs")
        logger.info("   - 健康检查: http://localhost:8001/health")
        logger.info("   - 调试页面: http://localhost:8001/debug")
        logger.info("")
        logger.info("按 Ctrl+C 停止服务器")
        logger.info("=" * 50)
        
        # 实时输出服务器日志
        try:
            for line in process.stdout:
                print(line.rstrip())
        except KeyboardInterrupt:
            logger.info("\n🛑 收到停止信号，正在关闭服务器...")
            process.terminate()
            process.wait()
            logger.info("✅ 服务器已停止")
        
        return process.returncode == 0
        
    except FileNotFoundError:
        logger.error("❌ 找不到ai_gen_hub模块")
        logger.info("请确保已正确安装AI Gen Hub")
        return False
    except Exception as e:
        logger.error(f"❌ 启动服务器失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("🚀 AI Gen Hub 服务器启动脚本（修复版）")
    logger.info("=" * 50)
    
    # 检查当前目录
    current_dir = Path.cwd()
    logger.info(f"当前目录: {current_dir}")
    
    # 设置环境
    setup_environment()
    
    # 检查依赖
    if not check_dependencies():
        logger.error("❌ 依赖检查失败，无法启动服务器")
        return False
    
    # 启动服务器
    success = start_server()
    
    if success:
        logger.info("🎉 服务器启动成功！")
    else:
        logger.error("❌ 服务器启动失败")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\n👋 用户取消启动")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ 启动脚本异常: {e}")
        sys.exit(1)
