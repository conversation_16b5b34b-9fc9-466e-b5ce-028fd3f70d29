#!/usr/bin/env python3
"""
直接测试Chat接口模块

通过直接导入文件来避免包级别的依赖问题。
"""

import sys
import os
import importlib.util

def load_module_from_file(module_name, file_path):
    """从文件路径直接加载模块"""
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    sys.modules[module_name] = module
    spec.loader.exec_module(module)
    return module

def test_chat_interfaces_direct():
    """直接测试Chat接口模块"""
    print("🚀 开始直接测试Chat接口...")
    
    try:
        # 直接加载chat_interfaces模块
        interfaces_path = os.path.join(os.path.dirname(__file__), 'src', 'ai_gen_hub', 'core', 'chat_interfaces.py')
        interfaces = load_module_from_file('chat_interfaces', interfaces_path)
        
        print("✅ Chat接口模块直接加载成功")
        
        # 测试枚举
        ChatMessageRole = interfaces.ChatMessageRole
        ChatFinishReason = interfaces.ChatFinishReason
        ChatStreamEventType = interfaces.ChatStreamEventType
        
        print(f"✅ 消息角色枚举: {list(ChatMessageRole)}")
        print(f"✅ 完成原因枚举: {list(ChatFinishReason)}")
        print(f"✅ 流式事件类型枚举: {list(ChatStreamEventType)}")
        
        # 测试数据模型
        ChatMessage = interfaces.ChatMessage
        ChatConfig = interfaces.ChatConfig
        ChatUsage = interfaces.ChatUsage
        
        # 创建消息
        message = ChatMessage(
            role=ChatMessageRole.USER,
            content="测试消息"
        )
        print(f"✅ 消息创建成功: {message.role} - {message.content}")
        
        # 创建配置
        config = ChatConfig(
            temperature=0.7,
            max_tokens=1000
        )
        print(f"✅ 配置创建成功: temperature={config.temperature}")
        
        # 创建使用量统计
        usage = ChatUsage(
            prompt_tokens=10,
            completion_tokens=5,
            total_tokens=15
        )
        print(f"✅ 使用量统计创建成功: {usage.total_tokens}")
        
        # 测试异常
        ChatError = interfaces.ChatError
        ChatValidationError = interfaces.ChatValidationError
        
        error = ChatValidationError("测试错误", provider="test")
        print(f"✅ 异常创建成功: {type(error).__name__}")
        
        print("🎉 Chat接口直接测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ Chat接口直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chat_base_direct():
    """直接测试Chat基础实现"""
    print("\n🚀 开始直接测试Chat基础实现...")
    
    try:
        # 先加载依赖的接口模块
        interfaces_path = os.path.join(os.path.dirname(__file__), 'src', 'ai_gen_hub', 'core', 'chat_interfaces.py')
        interfaces = load_module_from_file('ai_gen_hub.core.chat_interfaces', interfaces_path)
        
        # 加载基础实现模块
        base_path = os.path.join(os.path.dirname(__file__), 'src', 'ai_gen_hub', 'core', 'chat_base.py')
        base = load_module_from_file('chat_base', base_path)
        
        print("✅ Chat基础实现模块直接加载成功")
        
        # 测试流式累积器
        StreamAccumulator = base.StreamAccumulator
        accumulator = StreamAccumulator()
        
        # 创建测试块
        ChatStreamChunk = interfaces.ChatStreamChunk
        ChatStreamEventType = interfaces.ChatStreamEventType
        ChatFinishReason = interfaces.ChatFinishReason
        ChatUsage = interfaces.ChatUsage
        
        chunk1 = ChatStreamChunk(
            id="chunk_1",
            event_type=ChatStreamEventType.CONTENT_DELTA,
            delta_content="Hello"
        )
        
        chunk2 = ChatStreamChunk(
            id="chunk_2",
            event_type=ChatStreamEventType.CONTENT_DELTA,
            delta_content=" World!"
        )
        
        accumulator.add_chunk(chunk1)
        accumulator.add_chunk(chunk2)
        
        content = accumulator.get_accumulated_content()
        print(f"✅ 流式内容累积成功: '{content}'")
        
        print("🎉 Chat基础实现直接测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ Chat基础实现直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validation_logic():
    """测试验证逻辑"""
    print("\n🚀 开始测试验证逻辑...")
    
    try:
        # 加载接口模块
        interfaces_path = os.path.join(os.path.dirname(__file__), 'src', 'ai_gen_hub', 'core', 'chat_interfaces.py')
        interfaces = load_module_from_file('validation_interfaces', interfaces_path)
        
        ChatMessage = interfaces.ChatMessage
        ChatMessageRole = interfaces.ChatMessageRole
        ChatConfig = interfaces.ChatConfig
        
        # 测试消息验证
        messages = [
            ChatMessage(role=ChatMessageRole.SYSTEM, content="你是AI助手"),
            ChatMessage(role=ChatMessageRole.USER, content="你好"),
            ChatMessage(role=ChatMessageRole.ASSISTANT, content="你好！")
        ]
        print(f"✅ 创建了{len(messages)}条测试消息")
        
        # 测试配置验证
        configs = [
            ChatConfig(temperature=0.0),
            ChatConfig(temperature=1.0),
            ChatConfig(temperature=2.0),
            ChatConfig(max_tokens=1),
            ChatConfig(max_tokens=1000),
            ChatConfig(top_p=0.0),
            ChatConfig(top_p=1.0),
        ]
        print(f"✅ 创建了{len(configs)}个测试配置")
        
        # 测试边界值
        try:
            # 这些应该会失败
            ChatConfig(temperature=-0.1)
            print("❌ 温度验证失败：应该拒绝负值")
        except:
            print("✅ 温度负值验证正确")
        
        try:
            ChatConfig(temperature=2.1)
            print("❌ 温度验证失败：应该拒绝超出范围的值")
        except:
            print("✅ 温度超范围验证正确")
        
        print("🎉 验证逻辑测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 验证逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enum_values():
    """测试枚举值"""
    print("\n🚀 开始测试枚举值...")
    
    try:
        # 加载接口模块
        interfaces_path = os.path.join(os.path.dirname(__file__), 'src', 'ai_gen_hub', 'core', 'chat_interfaces.py')
        interfaces = load_module_from_file('enum_interfaces', interfaces_path)
        
        # 测试消息角色枚举
        ChatMessageRole = interfaces.ChatMessageRole
        expected_roles = ["system", "user", "assistant", "function", "tool"]
        actual_roles = [role.value for role in ChatMessageRole]
        
        for role in expected_roles:
            assert role in actual_roles, f"缺少角色: {role}"
        print(f"✅ 消息角色枚举完整: {actual_roles}")
        
        # 测试完成原因枚举
        ChatFinishReason = interfaces.ChatFinishReason
        expected_reasons = ["stop", "length", "content_filter", "tool_calls", "function_call", "error"]
        actual_reasons = [reason.value for reason in ChatFinishReason]
        
        for reason in expected_reasons:
            assert reason in actual_reasons, f"缺少完成原因: {reason}"
        print(f"✅ 完成原因枚举完整: {actual_reasons}")
        
        # 测试流式事件类型枚举
        ChatStreamEventType = interfaces.ChatStreamEventType
        expected_events = ["message_start", "content_delta", "tool_call_delta", "message_stop", "error"]
        actual_events = [event.value for event in ChatStreamEventType]
        
        for event in expected_events:
            assert event in actual_events, f"缺少事件类型: {event}"
        print(f"✅ 流式事件类型枚举完整: {actual_events}")
        
        print("🎉 枚举值测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 枚举值测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("AI Gen Hub 统一Chat接口 - 直接模块测试")
    print("=" * 60)
    
    all_passed = True
    
    # 运行各项测试
    tests = [
        test_chat_interfaces_direct,
        test_chat_base_direct,
        test_validation_logic,
        test_enum_values
    ]
    
    for test_func in tests:
        if not test_func():
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有直接测试通过！统一Chat接口核心功能正确。")
        print("✅ 验证完成的功能：")
        print("   - 数据模型创建和验证")
        print("   - 枚举定义和取值")
        print("   - 异常处理机制")
        print("   - 流式响应累积")
        print("   - 参数边界验证")
    else:
        print("❌ 部分测试失败，请检查实现。")
    print("=" * 60)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
