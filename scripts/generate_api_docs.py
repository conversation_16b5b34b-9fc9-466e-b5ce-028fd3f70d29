#!/usr/bin/env python3
"""
API文档生成脚本

自动生成AI Gen Hub认证系统的API文档，包括：
- OpenAPI规范文档
- 中文API参考文档
- 示例代码
- 错误码说明
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, Any

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi

from ai_gen_hub.auth.tokens import APITokenManager, JWTTokenManager
from ai_gen_hub.auth.rbac import RBACManager
from ai_gen_hub.auth.security import SecurityManager
from ai_gen_hub.api.auth import create_auth_router


class MockUserService:
    """模拟用户服务"""
    pass


def create_docs_app() -> FastAPI:
    """创建用于文档生成的FastAPI应用"""
    app = FastAPI(
        title="AI Gen Hub 认证系统 API",
        description="""
AI Gen Hub 身份认证和授权系统API文档

## 概述

AI Gen Hub 认证系统提供完整的身份认证和授权功能，支持：

- **OAuth2/OIDC登录**: 支持主流身份提供商（Auth0、Keycloak、Azure AD等）
- **API Token管理**: 长期API访问令牌的创建、管理和撤销
- **JWT令牌**: 短期访问令牌和刷新令牌
- **RBAC权限控制**: 基于角色的访问控制
- **安全特性**: 频率限制、审计日志、威胁检测

## 认证方式

### 1. JWT Bearer Token
用于控制台用户的短期访问：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 2. API Token
用于程序化访问的长期令牌：
```
Authorization: Bearer ak_1234567890abcdef1234567890abcdef
```

## 权限系统

系统使用基于角色的访问控制（RBAC），包含以下角色：

- **super_admin**: 超级管理员，拥有所有权限
- **admin**: 管理员，拥有管理权限
- **developer**: 开发者，拥有API访问和基本管理权限
- **user**: 普通用户，拥有基本API访问权限
- **readonly**: 只读用户，仅有查看权限

## 错误处理

API使用标准HTTP状态码，错误响应格式：

```json
{
  "error": "error_type",
  "message": "错误描述",
  "error_code": "ErrorCode",
  "details": {}
}
```

常见错误码：
- `401`: 认证失败
- `403`: 权限不足
- `429`: 频率限制超出
- `400`: 请求格式错误
- `500`: 服务器内部错误
        """,
        version="1.0.0",
        contact={
            "name": "AI Gen Hub Team",
            "email": "<EMAIL>",
        },
        license_info={
            "name": "MIT License",
            "url": "https://opensource.org/licenses/MIT",
        },
    )
    
    # 初始化组件
    jwt_manager = JWTTokenManager("dummy-secret")
    api_token_manager = APITokenManager("dummy-secret")
    rbac_manager = RBACManager()
    security_manager = SecurityManager()
    user_service = MockUserService()
    
    # 添加认证路由
    auth_router = create_auth_router(
        jwt_manager=jwt_manager,
        api_token_manager=api_token_manager,
        rbac_manager=rbac_manager,
        security_manager=security_manager,
        user_service=user_service
    )
    app.include_router(auth_router)
    
    return app


def generate_openapi_spec(app: FastAPI, output_path: str):
    """生成OpenAPI规范文档"""
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    
    # 添加安全定义
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "JWT访问令牌或API Token"
        }
    }
    
    # 为所有端点添加安全要求
    for path_item in openapi_schema["paths"].values():
        for operation in path_item.values():
            if isinstance(operation, dict) and "security" not in operation:
                # 排除登录相关端点
                if not any(tag in operation.get("tags", []) for tag in ["登录", "公开"]):
                    operation["security"] = [{"BearerAuth": []}]
    
    # 保存OpenAPI规范
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(openapi_schema, f, ensure_ascii=False, indent=2)
    
    print(f"✅ OpenAPI规范已生成: {output_path}")


def generate_markdown_docs(openapi_schema: Dict[str, Any], output_path: str):
    """生成Markdown格式的API文档"""
    markdown_content = f"""# {openapi_schema['info']['title']}

{openapi_schema['info']['description']}

## API端点

"""
    
    # 按标签分组端点
    endpoints_by_tag = {}
    for path, path_item in openapi_schema["paths"].items():
        for method, operation in path_item.items():
            if isinstance(operation, dict):
                tags = operation.get("tags", ["其他"])
                tag = tags[0] if tags else "其他"
                
                if tag not in endpoints_by_tag:
                    endpoints_by_tag[tag] = []
                
                endpoints_by_tag[tag].append({
                    "path": path,
                    "method": method.upper(),
                    "operation": operation
                })
    
    # 生成每个标签的文档
    for tag, endpoints in endpoints_by_tag.items():
        markdown_content += f"### {tag}\n\n"
        
        for endpoint in endpoints:
            operation = endpoint["operation"]
            markdown_content += f"#### {endpoint['method']} {endpoint['path']}\n\n"
            markdown_content += f"**{operation.get('summary', '无描述')}**\n\n"
            
            if operation.get('description'):
                markdown_content += f"{operation['description']}\n\n"
            
            # 请求参数
            if 'requestBody' in operation:
                markdown_content += "**请求体:**\n\n"
                markdown_content += "```json\n"
                # 这里可以添加请求体示例
                markdown_content += "{\n  // 请求参数\n}\n"
                markdown_content += "```\n\n"
            
            # 响应示例
            if 'responses' in operation:
                markdown_content += "**响应示例:**\n\n"
                for status_code, response in operation['responses'].items():
                    markdown_content += f"**{status_code}**: {response.get('description', '无描述')}\n\n"
                    markdown_content += "```json\n"
                    markdown_content += "{\n  // 响应数据\n}\n"
                    markdown_content += "```\n\n"
            
            markdown_content += "---\n\n"
    
    # 保存Markdown文档
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    
    print(f"✅ Markdown文档已生成: {output_path}")


def generate_code_examples(output_dir: str):
    """生成代码示例"""
    examples_dir = Path(output_dir) / "examples"
    examples_dir.mkdir(exist_ok=True)
    
    # Python示例
    python_example = '''"""
AI Gen Hub 认证系统 Python 客户端示例
"""

import requests
import json
from typing import Optional, Dict, Any

class AIGenHubAuthClient:
    """AI Gen Hub 认证客户端"""
    
    def __init__(self, base_url: str, api_token: Optional[str] = None):
        self.base_url = base_url.rstrip('/')
        self.api_token = api_token
        self.session = requests.Session()
        
        if api_token:
            self.session.headers.update({
                'Authorization': f'Bearer {api_token}',
                'Content-Type': 'application/json'
            })
    
    def login_with_oauth(self, idp_name: str = "auth0") -> Dict[str, str]:
        """发起OAuth登录"""
        response = self.session.post(
            f"{self.base_url}/auth/login",
            json={"idp_name": idp_name}
        )
        response.raise_for_status()
        return response.json()
    
    def create_api_token(
        self, 
        name: str, 
        scopes: list = None, 
        expires_in_days: int = 90
    ) -> Dict[str, Any]:
        """创建API令牌"""
        data = {
            "name": name,
            "scopes": scopes or ["api_access"],
            "expires_in_days": expires_in_days
        }
        
        response = self.session.post(
            f"{self.base_url}/auth/tokens",
            json=data
        )
        response.raise_for_status()
        return response.json()
    
    def get_user_info(self) -> Dict[str, Any]:
        """获取用户信息"""
        response = self.session.get(f"{self.base_url}/auth/me")
        response.raise_for_status()
        return response.json()

# 使用示例
if __name__ == "__main__":
    # 使用API Token初始化客户端
    client = AIGenHubAuthClient(
        base_url="https://api.your-domain.com",
        api_token="ak_your_api_token_here"
    )
    
    # 获取用户信息
    user_info = client.get_user_info()
    print(f"当前用户: {user_info['username']}")
    
    # 创建新的API令牌
    new_token = client.create_api_token(
        name="新令牌",
        scopes=["api_access"],
        expires_in_days=30
    )
    print(f"新令牌: {new_token['token']}")
'''
    
    with open(examples_dir / "python_client.py", 'w', encoding='utf-8') as f:
        f.write(python_example)
    
    # JavaScript示例
    javascript_example = '''/**
 * AI Gen Hub 认证系统 JavaScript 客户端示例
 */

class AIGenHubAuthClient {
    constructor(baseUrl, apiToken = null) {
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.apiToken = apiToken;
    }
    
    async request(method, endpoint, data = null) {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (this.apiToken) {
            headers['Authorization'] = `Bearer ${this.apiToken}`;
        }
        
        const config = {
            method,
            headers
        };
        
        if (data) {
            config.body = JSON.stringify(data);
        }
        
        const response = await fetch(`${this.baseUrl}${endpoint}`, config);
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || `HTTP ${response.status}`);
        }
        
        return response.json();
    }
    
    async loginWithOAuth(idpName = 'auth0') {
        return this.request('POST', '/auth/login', { idp_name: idpName });
    }
    
    async createApiToken(name, scopes = ['api_access'], expiresInDays = 90) {
        return this.request('POST', '/auth/tokens', {
            name,
            scopes,
            expires_in_days: expiresInDays
        });
    }
    
    async getUserInfo() {
        return this.request('GET', '/auth/me');
    }
}

// 使用示例
const client = new AIGenHubAuthClient(
    'https://api.your-domain.com',
    'ak_your_api_token_here'
);

// 获取用户信息
client.getUserInfo()
    .then(userInfo => console.log('当前用户:', userInfo.username))
    .catch(error => console.error('错误:', error));

// 创建API令牌
client.createApiToken('新令牌', ['api_access'], 30)
    .then(token => console.log('新令牌:', token.token))
    .catch(error => console.error('错误:', error));
'''
    
    with open(examples_dir / "javascript_client.js", 'w', encoding='utf-8') as f:
        f.write(javascript_example)
    
    # cURL示例
    curl_example = '''#!/bin/bash

# AI Gen Hub 认证系统 cURL 示例

# 设置变量
BASE_URL="https://api.your-domain.com"
API_TOKEN="ak_your_api_token_here"

# 获取用户信息
echo "获取用户信息..."
curl -X GET "$BASE_URL/auth/me" \\
  -H "Authorization: Bearer $API_TOKEN" \\
  -H "Content-Type: application/json"

echo -e "\\n\\n"

# 创建API令牌
echo "创建API令牌..."
curl -X POST "$BASE_URL/auth/tokens" \\
  -H "Authorization: Bearer $API_TOKEN" \\
  -H "Content-Type: application/json" \\
  -d '{
    "name": "测试令牌",
    "scopes": ["api_access"],
    "expires_in_days": 30
  }'

echo -e "\\n\\n"

# 列出API令牌
echo "列出API令牌..."
curl -X GET "$BASE_URL/auth/tokens" \\
  -H "Authorization: Bearer $API_TOKEN" \\
  -H "Content-Type: application/json"
'''
    
    with open(examples_dir / "curl_examples.sh", 'w', encoding='utf-8') as f:
        f.write(curl_example)
    
    # 设置执行权限
    os.chmod(examples_dir / "curl_examples.sh", 0o755)
    
    print(f"✅ 代码示例已生成: {examples_dir}")


def main():
    """主函数"""
    print("🚀 开始生成API文档...")
    
    # 创建输出目录
    docs_dir = Path(__file__).parent.parent / "docs" / "api"
    docs_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建FastAPI应用
    app = create_docs_app()
    
    # 生成OpenAPI规范
    openapi_path = docs_dir / "openapi.json"
    generate_openapi_spec(app, str(openapi_path))
    
    # 读取OpenAPI规范
    with open(openapi_path, 'r', encoding='utf-8') as f:
        openapi_schema = json.load(f)
    
    # 生成Markdown文档
    markdown_path = docs_dir / "api_reference.md"
    generate_markdown_docs(openapi_schema, str(markdown_path))
    
    # 生成代码示例
    generate_code_examples(str(docs_dir))
    
    print("✅ API文档生成完成!")
    print(f"   OpenAPI规范: {openapi_path}")
    print(f"   Markdown文档: {markdown_path}")
    print(f"   代码示例: {docs_dir / 'examples'}")


if __name__ == "__main__":
    main()
