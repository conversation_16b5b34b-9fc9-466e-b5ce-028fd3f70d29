#!/usr/bin/env python3
"""
AI Gen Hub 认证系统验证脚本

此脚本用于验证认证系统的完整性和功能正确性，包括：
- 配置验证
- 组件初始化测试
- 功能集成测试
- 安全性检查
"""

import asyncio
import os
import sys
import time
from typing import Dict, List, Any, Optional
from uuid import uuid4

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from ai_gen_hub.auth import (
    AuthUser,
    APIToken,
    TokenScope,
    Permission,
    UserRole,
    UserStatus,
    IdPConfig,
    IdPType,
    AuthConfig,
    create_idp_provider,
)
from ai_gen_hub.auth.tokens import APITokenManager, JWTTokenManager
from ai_gen_hub.auth.rbac import RBACManager
from ai_gen_hub.auth.security import SecurityManager, RateLimiter, AuditLogger
from ai_gen_hub.auth.exceptions import (
    AuthenticationError,
    AuthorizationError,
    TokenError,
    RateLimitError,
)
from ai_gen_hub.config.settings import get_settings


class AuthSystemValidator:
    """认证系统验证器"""
    
    def __init__(self):
        self.results: Dict[str, Dict[str, Any]] = {}
        self.settings = None
        self.jwt_manager = None
        self.api_token_manager = None
        self.rbac_manager = None
        self.security_manager = None
    
    def log_result(self, category: str, test_name: str, success: bool, message: str = "", details: Any = None):
        """记录测试结果"""
        if category not in self.results:
            self.results[category] = {}
        
        self.results[category][test_name] = {
            "success": success,
            "message": message,
            "details": details,
            "timestamp": time.time()
        }
        
        status = "✅" if success else "❌"
        print(f"{status} {category}.{test_name}: {message}")
    
    async def validate_configuration(self) -> bool:
        """验证配置"""
        print("\n🔧 验证配置...")
        
        try:
            # 加载配置
            self.settings = get_settings()
            self.log_result("config", "load_settings", True, "配置加载成功")
            
            # 验证JWT配置
            if not self.settings.auth.jwt_secret_key:
                self.log_result("config", "jwt_secret", False, "JWT密钥未设置")
                return False
            
            if len(self.settings.auth.jwt_secret_key) < 32:
                self.log_result("config", "jwt_secret_length", False, "JWT密钥长度不足32字符")
                return False
            
            self.log_result("config", "jwt_config", True, "JWT配置验证通过")
            
            # 验证IdP配置
            idp_count = len(self.settings.auth.idp_configs)
            if idp_count == 0:
                self.log_result("config", "idp_config", False, "未配置任何身份提供商")
            else:
                self.log_result("config", "idp_config", True, f"配置了 {idp_count} 个身份提供商")
            
            # 验证安全配置
            if not self.settings.auth.rate_limit_enabled:
                self.log_result("config", "rate_limit", False, "频率限制未启用")
            else:
                self.log_result("config", "rate_limit", True, "频率限制已启用")
            
            return True
            
        except Exception as e:
            self.log_result("config", "validation", False, f"配置验证失败: {str(e)}")
            return False
    
    async def validate_components(self) -> bool:
        """验证组件初始化"""
        print("\n🔧 验证组件初始化...")
        
        try:
            # 初始化JWT管理器
            self.jwt_manager = JWTTokenManager(
                secret_key=self.settings.auth.jwt_secret_key,
                access_token_expire_minutes=self.settings.auth.jwt_access_token_expire_minutes,
                refresh_token_expire_days=self.settings.auth.jwt_refresh_token_expire_days
            )
            self.log_result("components", "jwt_manager", True, "JWT管理器初始化成功")
            
            # 初始化API Token管理器
            self.api_token_manager = APITokenManager(self.settings.auth.jwt_secret_key)
            self.log_result("components", "api_token_manager", True, "API Token管理器初始化成功")
            
            # 初始化RBAC管理器
            self.rbac_manager = RBACManager()
            self.log_result("components", "rbac_manager", True, "RBAC管理器初始化成功")
            
            # 初始化安全管理器
            rate_limiter = RateLimiter(
                default_requests_per_minute=self.settings.auth.rate_limit_requests_per_minute,
                default_burst_size=self.settings.auth.rate_limit_burst_size
            )
            audit_logger = AuditLogger(retention_days=self.settings.auth.audit_log_retention_days)
            self.security_manager = SecurityManager(rate_limiter, audit_logger)
            self.log_result("components", "security_manager", True, "安全管理器初始化成功")
            
            return True
            
        except Exception as e:
            self.log_result("components", "initialization", False, f"组件初始化失败: {str(e)}")
            return False
    
    async def validate_jwt_functionality(self) -> bool:
        """验证JWT功能"""
        print("\n🔑 验证JWT功能...")
        
        try:
            # 创建测试用户
            test_user = AuthUser(
                username="test_user",
                email="<EMAIL>",
                role=UserRole.USER,
                permissions={Permission.API_TEXT_GENERATE}
            )
            
            # 创建访问令牌
            access_token = self.jwt_manager.create_access_token(test_user)
            self.log_result("jwt", "create_access_token", True, "访问令牌创建成功")
            
            # 验证访问令牌
            payload = self.jwt_manager.verify_token(access_token)
            if payload["sub"] == str(test_user.id):
                self.log_result("jwt", "verify_access_token", True, "访问令牌验证成功")
            else:
                self.log_result("jwt", "verify_access_token", False, "访问令牌验证失败")
                return False
            
            # 创建刷新令牌
            refresh_token = self.jwt_manager.create_refresh_token(test_user)
            self.log_result("jwt", "create_refresh_token", True, "刷新令牌创建成功")
            
            # 使用刷新令牌获取新的访问令牌
            new_access_token = self.jwt_manager.refresh_access_token(refresh_token, test_user)
            new_payload = self.jwt_manager.verify_token(new_access_token)
            if new_payload["sub"] == str(test_user.id):
                self.log_result("jwt", "refresh_token", True, "令牌刷新成功")
            else:
                self.log_result("jwt", "refresh_token", False, "令牌刷新失败")
                return False
            
            # 撤销刷新令牌
            revoke_success = self.jwt_manager.revoke_refresh_token(refresh_token)
            if revoke_success:
                self.log_result("jwt", "revoke_token", True, "令牌撤销成功")
            else:
                self.log_result("jwt", "revoke_token", False, "令牌撤销失败")
            
            return True
            
        except Exception as e:
            self.log_result("jwt", "functionality", False, f"JWT功能测试失败: {str(e)}")
            return False
    
    async def validate_api_token_functionality(self) -> bool:
        """验证API Token功能"""
        print("\n🔐 验证API Token功能...")
        
        try:
            test_user_id = uuid4()
            
            # 生成API令牌
            api_token, token_value = self.api_token_manager.generate_api_token(
                user_id=test_user_id,
                name="测试令牌",
                scopes={TokenScope.API_ACCESS},
                permissions={Permission.API_TEXT_GENERATE},
                expires_in_days=30
            )
            self.log_result("api_token", "generate", True, "API令牌生成成功")
            
            # 验证API令牌
            verified_token = self.api_token_manager.verify_api_token(token_value)
            if verified_token and verified_token.user_id == test_user_id:
                self.log_result("api_token", "verify", True, "API令牌验证成功")
            else:
                self.log_result("api_token", "verify", False, "API令牌验证失败")
                return False
            
            # 获取用户令牌
            user_tokens = self.api_token_manager.get_user_tokens(test_user_id)
            if len(user_tokens) == 1 and user_tokens[0].id == api_token.id:
                self.log_result("api_token", "get_user_tokens", True, "获取用户令牌成功")
            else:
                self.log_result("api_token", "get_user_tokens", False, "获取用户令牌失败")
                return False
            
            # 撤销API令牌
            revoke_success = self.api_token_manager.revoke_api_token(api_token.id)
            if revoke_success:
                self.log_result("api_token", "revoke", True, "API令牌撤销成功")
            else:
                self.log_result("api_token", "revoke", False, "API令牌撤销失败")
                return False
            
            # 验证撤销后的令牌
            revoked_token = self.api_token_manager.verify_api_token(token_value)
            if revoked_token is None:
                self.log_result("api_token", "verify_revoked", True, "撤销令牌验证正确")
            else:
                self.log_result("api_token", "verify_revoked", False, "撤销令牌仍然有效")
                return False
            
            return True
            
        except Exception as e:
            self.log_result("api_token", "functionality", False, f"API Token功能测试失败: {str(e)}")
            return False
    
    async def validate_rbac_functionality(self) -> bool:
        """验证RBAC功能"""
        print("\n🛡️ 验证RBAC功能...")
        
        try:
            test_user_id = uuid4()
            
            # 检查系统角色
            user_role = self.rbac_manager.get_role_by_name("user")
            if user_role:
                self.log_result("rbac", "system_roles", True, "系统角色加载成功")
            else:
                self.log_result("rbac", "system_roles", False, "系统角色加载失败")
                return False
            
            # 分配角色给用户
            assign_success = self.rbac_manager.assign_role_to_user(test_user_id, user_role.id)
            if assign_success:
                self.log_result("rbac", "assign_role", True, "角色分配成功")
            else:
                self.log_result("rbac", "assign_role", False, "角色分配失败")
                return False
            
            # 检查用户权限
            user_permissions = self.rbac_manager.get_user_permissions(test_user_id)
            if Permission.API_TEXT_GENERATE in user_permissions:
                self.log_result("rbac", "user_permissions", True, "用户权限获取成功")
            else:
                self.log_result("rbac", "user_permissions", False, "用户权限获取失败")
                return False
            
            # 权限检查
            has_permission = self.rbac_manager.check_permission(test_user_id, Permission.API_TEXT_GENERATE)
            if has_permission:
                self.log_result("rbac", "permission_check", True, "权限检查成功")
            else:
                self.log_result("rbac", "permission_check", False, "权限检查失败")
                return False
            
            # 检查没有的权限
            has_admin_permission = self.rbac_manager.check_permission(test_user_id, Permission.SYSTEM_ADMIN)
            if not has_admin_permission:
                self.log_result("rbac", "negative_permission_check", True, "负权限检查成功")
            else:
                self.log_result("rbac", "negative_permission_check", False, "负权限检查失败")
                return False
            
            return True
            
        except Exception as e:
            self.log_result("rbac", "functionality", False, f"RBAC功能测试失败: {str(e)}")
            return False
    
    async def validate_security_functionality(self) -> bool:
        """验证安全功能"""
        print("\n🔒 验证安全功能...")
        
        try:
            # 测试频率限制
            test_identifier = "test_user"
            
            # 正常请求应该通过
            allowed, _ = self.security_manager.rate_limiter.check_rate_limit(test_identifier, "user")
            if allowed:
                self.log_result("security", "rate_limit_normal", True, "正常频率限制检查通过")
            else:
                self.log_result("security", "rate_limit_normal", False, "正常频率限制检查失败")
                return False
            
            # 测试审计日志
            test_user_id = uuid4()
            self.security_manager.audit_logger.log_authentication_event(
                user_id=test_user_id,
                event_type="test_login",
                success=True,
                ip_address="127.0.0.1"
            )
            
            # 获取审计日志
            logs = self.security_manager.audit_logger.get_audit_logs(
                category="authentication",
                user_id=test_user_id,
                limit=1
            )
            
            if len(logs) == 1 and logs[0]["event_type"] == "test_login":
                self.log_result("security", "audit_log", True, "审计日志记录成功")
            else:
                self.log_result("security", "audit_log", False, "审计日志记录失败")
                return False
            
            # 测试失败认证跟踪
            identifier = "<EMAIL>"
            ip_address = "*************"
            
            # 记录多次失败尝试
            for _ in range(5):
                self.security_manager.record_failed_authentication(identifier, ip_address)
            
            # 检查账户是否被锁定
            is_locked = self.security_manager.is_account_locked(identifier)
            if is_locked:
                self.log_result("security", "account_lockout", True, "账户锁定功能正常")
            else:
                self.log_result("security", "account_lockout", False, "账户锁定功能异常")
                return False
            
            # 清除失败尝试
            self.security_manager.clear_failed_attempts(identifier)
            is_locked_after_clear = self.security_manager.is_account_locked(identifier)
            if not is_locked_after_clear:
                self.log_result("security", "clear_attempts", True, "清除失败尝试功能正常")
            else:
                self.log_result("security", "clear_attempts", False, "清除失败尝试功能异常")
                return False
            
            return True
            
        except Exception as e:
            self.log_result("security", "functionality", False, f"安全功能测试失败: {str(e)}")
            return False
    
    async def validate_idp_integration(self) -> bool:
        """验证IdP集成"""
        print("\n🌐 验证IdP集成...")
        
        try:
            # 测试创建IdP提供商
            test_config = IdPConfig(
                name="test_google",
                type=IdPType.GOOGLE_OAUTH,
                client_id="test_client_id",
                client_secret="test_client_secret",
                authorization_url="https://accounts.google.com/o/oauth2/v2/auth",
                token_url="https://oauth2.googleapis.com/token",
                userinfo_url="https://openidconnect.googleapis.com/v1/userinfo",
                redirect_uri="http://localhost:8000/auth/callback/google",
                scopes=["openid", "profile", "email"]
            )
            
            provider = create_idp_provider(test_config)
            if provider:
                self.log_result("idp", "create_provider", True, "IdP提供商创建成功")
            else:
                self.log_result("idp", "create_provider", False, "IdP提供商创建失败")
                return False
            
            # 测试生成授权URL
            await provider.initialize()
            auth_url, state, nonce = provider.generate_authorization_url()
            
            if auth_url and state and nonce:
                self.log_result("idp", "generate_auth_url", True, "授权URL生成成功")
            else:
                self.log_result("idp", "generate_auth_url", False, "授权URL生成失败")
                return False
            
            await provider.cleanup()
            
            return True
            
        except Exception as e:
            self.log_result("idp", "integration", False, f"IdP集成测试失败: {str(e)}")
            return False
    
    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "="*60)
        print("🎯 认证系统验证摘要")
        print("="*60)
        
        total_tests = 0
        passed_tests = 0
        
        for category, tests in self.results.items():
            print(f"\n📂 {category.upper()}:")
            category_passed = 0
            category_total = len(tests)
            
            for test_name, result in tests.items():
                status = "✅" if result["success"] else "❌"
                print(f"  {status} {test_name}: {result['message']}")
                
                if result["success"]:
                    category_passed += 1
                    passed_tests += 1
                
                total_tests += 1
            
            print(f"  📊 {category} 通过率: {category_passed}/{category_total} ({category_passed/category_total*100:.1f}%)")
        
        print(f"\n🎯 总体通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！认证系统验证成功！")
            return True
        else:
            print("⚠️ 部分测试失败，请检查上述错误信息。")
            return False
    
    async def run_validation(self) -> bool:
        """运行完整验证"""
        print("🚀 开始AI Gen Hub认证系统验证")
        print("="*60)
        
        # 验证步骤
        steps = [
            ("配置验证", self.validate_configuration),
            ("组件初始化", self.validate_components),
            ("JWT功能", self.validate_jwt_functionality),
            ("API Token功能", self.validate_api_token_functionality),
            ("RBAC功能", self.validate_rbac_functionality),
            ("安全功能", self.validate_security_functionality),
            ("IdP集成", self.validate_idp_integration),
        ]
        
        for step_name, step_func in steps:
            try:
                success = await step_func()
                if not success:
                    print(f"\n❌ {step_name} 验证失败，停止后续验证")
                    break
            except Exception as e:
                print(f"\n💥 {step_name} 验证过程中出现异常: {str(e)}")
                break
        
        # 打印摘要
        return self.print_summary()


async def main():
    """主函数"""
    validator = AuthSystemValidator()
    success = await validator.run_validation()
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
