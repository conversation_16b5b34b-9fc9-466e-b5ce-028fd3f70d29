#!/usr/bin/env python3
"""
测试多模态AI服务

验证图像生成、语音处理、图像理解、文件管理等多模态功能
"""

import asyncio
import base64
import io
import sys
import time
from pathlib import Path
from PIL import Image

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from ai_gen_hub.services.multimodal.image_enhanced import (
        EnhancedImageGenerationService,
        ImageEditRequest,
        ImageStyleTransferRequest,
        ImageUpscaleRequest
    )
    from ai_gen_hub.services.multimodal.speech_service import (
        SpeechService,
        TTSRequest,
        STTRequest,
        VoiceCloneRequest,
        AudioFormat,
        Language,
        VoiceType
    )
    from ai_gen_hub.services.multimodal.vision_service import (
        VisionService,
        VisionRequest,
        VisionTask,
        DetailLevel
    )
    from ai_gen_hub.services.multimodal.file_manager import (
        MultimodalFileManager,
        FileUploadRequest,
        FileType
    )
except ImportError as e:
    print(f"❌ 导入多模态服务模块失败: {e}")
    sys.exit(1)


def create_test_image() -> str:
    """创建测试图像"""
    # 创建一个简单的测试图像
    img = Image.new('RGB', (512, 512), color='blue')
    
    # 转换为Base64
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    image_data = base64.b64encode(buffer.getvalue()).decode()
    
    return image_data


def create_test_audio() -> str:
    """创建测试音频数据"""
    # 创建模拟音频数据
    audio_data = b"fake_audio_data_for_testing" * 100
    return base64.b64encode(audio_data).decode()


async def test_enhanced_image_service():
    """测试增强图像生成服务"""
    print("🔧 测试增强图像生成服务")
    print("=" * 60)
    
    service = EnhancedImageGenerationService()
    test_image = create_test_image()
    
    try:
        # 测试图像编辑
        edit_request = ImageEditRequest(
            prompt="Add a red circle to the image",
            image=test_image,
            edit_instruction="Add a red circle in the center",
            edit_type="inpaint",
            model="dall-e-3"
        )
        
        edit_response = await service.edit_image(edit_request)
        print(f"✅ 图像编辑成功: {edit_response.id}")
        
        # 测试风格转换
        style_request = ImageStyleTransferRequest(
            prompt="Style transfer",
            source_image=test_image,
            style_prompt="Van Gogh painting style",
            strength=0.8,
            model="stable-diffusion"
        )
        
        style_response = await service.transfer_style(style_request)
        print(f"✅ 风格转换成功: {style_response.id}")
        
        # 测试图像超分辨率
        upscale_request = ImageUpscaleRequest(
            prompt="Upscale image",
            image=test_image,
            scale_factor=2,
            enhance_details=True,
            model="real-esrgan"
        )
        
        upscale_response = await service.upscale_image(upscale_request)
        print(f"✅ 图像超分辨率成功: {upscale_response.id}")
        
        # 测试生成变体
        variations = await service.generate_image_variations(
            image=test_image,
            n=3,
            model="dall-e-3"
        )
        print(f"✅ 生成 {len(variations)} 个图像变体")
        
        return True
        
    except Exception as e:
        print(f"❌ 增强图像服务测试失败: {e}")
        return False


async def test_speech_service():
    """测试语音处理服务"""
    print("\n🔧 测试语音处理服务")
    print("=" * 60)
    
    service = SpeechService()
    
    try:
        # 测试文本转语音
        tts_request = TTSRequest(
            text="这是一个语音合成测试",
            voice="zh-CN-XiaoxiaoNeural",
            language=Language.ZH_CN,
            voice_type=VoiceType.FEMALE,
            speed=1.0,
            format=AudioFormat.MP3
        )
        
        tts_response = await service.text_to_speech(tts_request)
        print(f"✅ 文本转语音成功: {tts_response.id}, 时长: {tts_response.duration}秒")
        
        # 测试语音转文本
        test_audio = create_test_audio()
        stt_request = STTRequest(
            audio_data=test_audio,
            language=Language.ZH_CN,
            format=AudioFormat.WAV,
            enable_punctuation=True
        )
        
        stt_response = await service.speech_to_text(stt_request)
        print(f"✅ 语音转文本成功: {stt_response.text}, 置信度: {stt_response.confidence}")
        
        # 测试语音克隆
        clone_request = VoiceCloneRequest(
            reference_audio=test_audio,
            target_text="这是语音克隆测试文本",
            language=Language.ZH_CN,
            quality="high"
        )
        
        clone_response = await service.clone_voice(clone_request)
        print(f"✅ 语音克隆成功: {clone_response.id}, 时长: {clone_response.duration}秒")
        
        # 测试获取可用语音
        voices = await service.get_available_voices(Language.ZH_CN)
        print(f"✅ 获取可用语音: {len(voices.get('zh-CN', []))} 个中文语音")
        
        return True
        
    except Exception as e:
        print(f"❌ 语音服务测试失败: {e}")
        return False


async def test_vision_service():
    """测试图像理解服务"""
    print("\n🔧 测试图像理解服务")
    print("=" * 60)
    
    service = VisionService()
    test_image = create_test_image()
    
    try:
        # 测试图像描述
        description = await service.describe_image(
            image=test_image,
            detail_level=DetailLevel.MEDIUM,
            language="zh-CN"
        )
        print(f"✅ 图像描述: {description}")
        
        # 测试图像问答
        answer = await service.answer_image_question(
            image=test_image,
            question="这张图片的主要颜色是什么？",
            language="zh-CN"
        )
        print(f"✅ 图像问答: {answer}")
        
        # 测试OCR文字识别
        ocr_results = await service.extract_text(
            image=test_image,
            languages=["zh-CN", "en"]
        )
        print(f"✅ OCR识别: 找到 {len(ocr_results)} 个文字区域")
        
        # 测试物体检测
        objects = await service.detect_objects(
            image=test_image,
            max_objects=10,
            confidence_threshold=0.5,
            include_attributes=True
        )
        print(f"✅ 物体检测: 检测到 {len(objects)} 个物体")
        
        # 测试图像分类
        categories = await service.classify_image(image=test_image)
        print(f"✅ 图像分类: {len(categories)} 个分类结果")
        
        # 测试内容审核
        moderation = await service.moderate_content(image=test_image)
        print(f"✅ 内容审核: 安全性 - {moderation.get('safe', True)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 图像理解服务测试失败: {e}")
        return False


async def test_file_manager():
    """测试文件管理系统"""
    print("\n🔧 测试文件管理系统")
    print("=" * 60)
    
    manager = MultimodalFileManager()
    
    try:
        # 创建测试文件数据
        test_image = create_test_image()
        image_data = base64.b64decode(test_image)
        
        # 测试文件上传
        upload_request = FileUploadRequest(
            filename="test_image.png",
            content_type="image/png",
            file_data=image_data,
            generate_thumbnail=True,
            compress=True,
            extract_metadata=True,
            public=False
        )
        
        upload_response = await manager.upload_file(upload_request)
        file_id = upload_response.file_id
        print(f"✅ 文件上传成功: {file_id}")
        print(f"   文件大小: {upload_response.size} 字节")
        print(f"   文件类型: {upload_response.file_type.value}")
        print(f"   缩略图: {upload_response.thumbnail_url}")
        
        # 测试获取文件信息
        metadata = await manager.get_file(file_id)
        if metadata:
            print(f"✅ 获取文件信息成功")
            print(f"   文件名: {metadata.filename}")
            print(f"   尺寸: {metadata.width}x{metadata.height}")
            print(f"   校验和: {metadata.checksum[:16]}...")
        
        # 测试下载文件
        file_data = await manager.download_file(file_id)
        if file_data:
            print(f"✅ 文件下载成功: {len(file_data)} 字节")
        
        # 测试删除文件
        delete_success = await manager.delete_file(file_id)
        if delete_success:
            print(f"✅ 文件删除成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件管理测试失败: {e}")
        return False


async def test_integrated_multimodal_workflow():
    """测试集成多模态工作流"""
    print("\n🔧 测试集成多模态工作流")
    print("=" * 60)
    
    try:
        # 初始化服务
        image_service = EnhancedImageGenerationService()
        speech_service = SpeechService()
        vision_service = VisionService()
        file_manager = MultimodalFileManager()
        
        # 1. 创建测试图像
        test_image = create_test_image()
        print("✅ 步骤1: 创建测试图像")
        
        # 2. 上传图像到文件管理系统
        image_data = base64.b64decode(test_image)
        upload_request = FileUploadRequest(
            filename="workflow_test.png",
            content_type="image/png",
            file_data=image_data,
            generate_thumbnail=True
        )
        upload_response = await file_manager.upload_file(upload_request)
        print(f"✅ 步骤2: 图像上传成功 - {upload_response.file_id}")
        
        # 3. 使用视觉服务分析图像
        description = await vision_service.describe_image(
            image=test_image,
            detail_level=DetailLevel.HIGH,
            language="zh-CN"
        )
        print(f"✅ 步骤3: 图像分析 - {description}")
        
        # 4. 将描述转换为语音
        tts_request = TTSRequest(
            text=description,
            language=Language.ZH_CN,
            voice_type=VoiceType.FEMALE,
            format=AudioFormat.MP3
        )
        tts_response = await speech_service.text_to_speech(tts_request)
        print(f"✅ 步骤4: 语音合成 - 时长 {tts_response.duration}秒")
        
        # 5. 基于描述编辑图像
        edit_request = ImageEditRequest(
            prompt=f"Based on this description: {description}",
            image=test_image,
            edit_instruction="Enhance the image based on the description",
            edit_type="variation",
            model="dall-e-3"
        )
        edit_response = await image_service.edit_image(edit_request)
        print(f"✅ 步骤5: 图像编辑 - {edit_response.id}")
        
        # 6. 清理测试文件
        await file_manager.delete_file(upload_response.file_id)
        print("✅ 步骤6: 清理完成")
        
        print("\n🎉 集成多模态工作流测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 集成工作流测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始多模态AI服务测试")
    print()
    
    tests = [
        ("增强图像生成服务", test_enhanced_image_service),
        ("语音处理服务", test_speech_service),
        ("图像理解服务", test_vision_service),
        ("文件管理系统", test_file_manager),
        ("集成多模态工作流", test_integrated_multimodal_workflow),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"🧪 运行测试: {test_name}")
            print(f"{'='*60}")
            
            result = await test_func()
            
            if result:
                passed_tests += 1
                print(f"\n✅ 测试通过: {test_name}")
            else:
                print(f"\n❌ 测试失败: {test_name}")
                
        except Exception as e:
            print(f"\n❌ 测试异常: {test_name} - {e}")
            import traceback
            traceback.print_exc()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 多模态AI服务测试结果总结")
    print("=" * 60)
    
    print(f"📈 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests >= total_tests - 1:  # 允许一个测试失败
        print("\n🎉 多模态AI服务功能基本正常！")
        print("\n🚀 多模态功能特性:")
        print("  • 增强图像生成 - 编辑、风格转换、超分辨率")
        print("  • 语音处理 - TTS、STT、语音克隆")
        print("  • 图像理解 - 描述、问答、OCR、检测")
        print("  • 文件管理 - 上传、存储、处理、优化")
        print("  • 多模态工作流 - 服务间协作")
        print("\n📈 支持的模态:")
        print("  • 文本 - 生成、理解、转换")
        print("  • 图像 - 生成、编辑、分析")
        print("  • 语音 - 合成、识别、克隆")
        print("  • 文件 - 存储、处理、管理")
        print("\n🔧 技术特性:")
        print("  • 异步处理 - 高并发支持")
        print("  • 缓存优化 - 提升响应速度")
        print("  • 格式转换 - 多种格式支持")
        print("  • 安全检查 - 文件安全验证")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查实现")
        return False


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
