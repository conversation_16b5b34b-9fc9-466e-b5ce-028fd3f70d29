#!/usr/bin/env python3
"""
全面测试V2 API StreamConfig chunk_size修复
"""

import asyncio
import json
import logging
import httpx

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_chunk_size_value(chunk_size, description):
    """测试特定的chunk_size值"""
    base_url = "http://localhost:8001"
    
    test_request = {
        "messages": [{"role": "user", "content": "Hello"}],
        "model": "gemini-2.5-flash",
        "stream": {
            "enabled": False,
            "chunk_size": chunk_size,
            "include_usage": True
        }
    }
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        try:
            response = await client.post(
                f"{base_url}/api/v1/text/v2/generate",
                json=test_request,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                return "✅ 成功"
            elif response.status_code == 400:
                return "✅ 业务错误（验证通过）"
            elif response.status_code == 422:
                error_data = response.json()
                error_detail = str(error_data.get("detail", ""))
                if "streamconfig" in error_detail.lower():
                    return f"❌ StreamConfig验证错误"
                else:
                    return f"❌ 其他验证错误"
            else:
                return f"❌ 状态码: {response.status_code}"
                
        except Exception as e:
            return f"❌ 异常: {str(e)}"

async def main():
    """主函数"""
    logger.info("🚀 开始全面测试V2 API StreamConfig chunk_size修复...")
    
    # 等待服务器启动
    await asyncio.sleep(5)
    
    test_cases = [
        (0, "chunk_size=0 (之前失败的值)"),
        (None, "chunk_size=None"),
        (1, "chunk_size=1 (最小有效值)"),
        (100, "chunk_size=100 (中等值)"),
        (1000, "chunk_size=1000 (最大值)"),
        (-1, "chunk_size=-1 (负数，应该失败)"),
        (1001, "chunk_size=1001 (超过最大值，应该失败)"),
    ]
    
    logger.info("📊 测试结果:")
    logger.info("-" * 60)
    
    results = {}
    for chunk_size, description in test_cases:
        logger.info(f"测试 {description}...")
        result = await test_chunk_size_value(chunk_size, description)
        results[description] = result
        logger.info(f"  结果: {result}")
    
    logger.info("\n" + "="*60)
    logger.info("📋 测试总结:")
    
    success_count = sum(1 for result in results.values() if "✅" in result)
    total_count = len(results)
    
    for description, result in results.items():
        logger.info(f"  {description}: {result}")
    
    logger.info(f"\n成功率: {success_count}/{total_count}")
    
    # 检查关键修复是否生效
    key_fix_success = "✅" in results.get("chunk_size=0 (之前失败的值)", "")
    
    if key_fix_success:
        logger.info("🎉 关键修复成功：chunk_size=0 现在可以正常工作！")
    else:
        logger.error("💥 关键修复失败：chunk_size=0 仍然有问题")

if __name__ == "__main__":
    asyncio.run(main())
