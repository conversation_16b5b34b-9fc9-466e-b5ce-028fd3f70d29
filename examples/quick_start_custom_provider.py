"""
自定义 Provider 快速开始示例

这是一个最简化的自定义 provider 实现示例，展示核心概念和最少必要代码。
适合快速理解和原型开发。

使用步骤：
1. 复制此文件并重命名
2. 修改类名和配置
3. 实现 _perform_health_check 和 generate_text 方法
4. 测试和部署
"""

import json
import time
from typing import Any, AsyncIterator, Dict, List, Optional, Union
from uuid import uuid4

import httpx

from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.core.interfaces import (
    Message,
    MessageRole,
    ModelType,
    TextGenerationChoice,
    TextGenerationRequest,
    TextGenerationResponse,
    Usage,
)
from ai_gen_hub.providers.base import BaseProvider
from ai_gen_hub.utils.key_manager import KeyManager


class QuickCustomProvider(BaseProvider):
    """快速自定义 Provider 示例
    
    这是一个最简化的实现，只包含核心必要功能：
    - 基础配置
    - 健康检查
    - 文本生成
    """
    
    def __init__(self, config: ProviderConfig, key_manager: KeyManager):
        """初始化 Provider"""
        super().__init__("quick_custom", config, key_manager)
        
        # 🔧 配置 API 基础信息
        self.base_url = config.base_url or "https://api.your-ai-service.com/v1"
        
        # 🎯 定义支持的功能
        self._supported_model_types = [ModelType.TEXT_GENERATION]
        
        # 📝 定义支持的模型（根据您的 AI 服务修改）
        self._supported_models = [
            "your-model-v1",
            "your-model-v2",
            "your-fast-model",
        ]
        
        # 🔄 模型名称映射（可选）
        self._model_mapping = {
            "gpt-4": "your-model-v1",
            "gpt-3.5-turbo": "your-fast-model",
        }
    
    async def _perform_health_check(self, api_key: str) -> bool:
        """健康检查 - 验证 API 连接和密钥有效性"""
        try:
            headers = {"Authorization": f"Bearer {api_key}"}
            
            # 🏥 调用一个简单的 API 端点进行健康检查
            # 根据您的 AI 服务 API 文档修改此端点
            response = await self._client.get(
                f"{self.base_url}/models",  # 或 /health, /status 等
                headers=headers,
                timeout=10.0
            )
            
            return response.status_code == 200
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return False
    
    async def generate_text(self, request: TextGenerationRequest) -> TextGenerationResponse:
        """生成文本 - 核心功能实现"""
        
        # 🔑 获取 API 密钥
        api_key = await self.key_manager.get_key(self.name)
        if not api_key:
            from ai_gen_hub.core.exceptions import AuthenticationError
            raise AuthenticationError("未找到有效的 API 密钥")
        
        # 🏗️ 构建 API 请求
        request_data = self._build_api_request(request)
        
        # 📡 发送请求
        headers = {
            "Authorization": f"Bearer {api_key.key}",
            "Content-Type": "application/json"
        }
        
        try:
            response = await self._client.post(
                f"{self.base_url}/chat/completions",  # 根据您的 API 修改
                headers=headers,
                json=request_data,
                timeout=self.config.timeout
            )
            
            if response.status_code != 200:
                error_text = response.text
                self.logger.error(f"API 请求失败: {response.status_code} - {error_text}")
                from ai_gen_hub.core.exceptions import APIError
                raise APIError(f"API 请求失败: {response.status_code}")
            
            # 📋 解析响应
            response_data = response.json()
            return self._parse_api_response(response_data)
            
        except Exception as e:
            self.logger.error(f"文本生成失败: {e}")
            raise
    
    def _build_api_request(self, request: TextGenerationRequest) -> Dict[str, Any]:
        """构建 API 请求数据
        
        🔧 根据您的 AI 服务 API 格式修改此方法
        """
        # 映射模型名称
        model_name = self.map_model_name(request.model)
        
        # 转换消息格式
        messages = []
        for msg in request.messages:
            messages.append({
                "role": msg.role.value,
                "content": msg.content
            })
        
        # 构建请求数据（根据您的 API 格式调整）
        api_request = {
            "model": model_name,
            "messages": messages,
        }
        
        # 添加可选参数
        if request.temperature is not None:
            api_request["temperature"] = request.temperature
        
        if request.max_tokens is not None:
            api_request["max_tokens"] = request.max_tokens
        
        if request.top_p is not None:
            api_request["top_p"] = request.top_p
        
        return api_request
    
    def _parse_api_response(self, response_data: Dict[str, Any]) -> TextGenerationResponse:
        """解析 API 响应数据
        
        🔧 根据您的 AI 服务响应格式修改此方法
        """
        
        # 检查错误
        if "error" in response_data:
            error_msg = response_data["error"].get("message", "未知错误")
            from ai_gen_hub.core.exceptions import APIError
            raise APIError(f"API 返回错误: {error_msg}")
        
        # 解析选择项（根据您的 API 响应格式调整）
        choices = []
        for choice_data in response_data.get("choices", []):
            message_data = choice_data.get("message", {})
            choice = TextGenerationChoice(
                index=choice_data.get("index", 0),
                message=Message(
                    role=MessageRole(message_data.get("role", "assistant")),
                    content=message_data.get("content", "")
                ),
                finish_reason=choice_data.get("finish_reason")
            )
            choices.append(choice)
        
        # 解析使用统计
        usage_data = response_data.get("usage", {})
        usage = Usage(
            prompt_tokens=usage_data.get("prompt_tokens", 0),
            completion_tokens=usage_data.get("completion_tokens", 0),
            total_tokens=usage_data.get("total_tokens", 0)
        )
        
        return TextGenerationResponse(
            id=response_data.get("id", str(uuid4())),
            choices=choices,
            usage=usage,
            model=response_data.get("model", ""),
            created=response_data.get("created", int(time.time()))
        )


# 🧪 快速测试函数
async def quick_test():
    """快速测试自定义 provider"""
    print("🚀 开始快速测试...")
    
    # 创建配置
    config = ProviderConfig(
        api_keys=["your-api-key-here"],  # 🔑 替换为您的真实 API 密钥
        base_url="https://api.your-ai-service.com/v1",  # 🌐 替换为您的 API 地址
        enabled=True
    )
    
    # 创建 provider
    key_manager = KeyManager()
    provider = QuickCustomProvider(config, key_manager)
    
    try:
        # 初始化
        await provider.initialize()
        print("✅ Provider 初始化成功")
        
        # 健康检查
        health = await provider.health_check()
        print(f"🏥 健康检查: {'通过' if health else '失败'}")
        
        # 测试文本生成
        request = TextGenerationRequest(
            model="your-model-v1",  # 🎯 使用您支持的模型名称
            messages=[
                Message(role=MessageRole.USER, content="你好，请介绍一下你自己。")
            ],
            temperature=0.7,
            max_tokens=100
        )
        
        print("💬 开始文本生成...")
        response = await provider.generate_text(request)
        
        print("✅ 文本生成成功！")
        print(f"📝 响应内容: {response.choices[0].message.content}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    finally:
        # 清理资源
        await provider.cleanup()
        print("🧹 资源清理完成")


if __name__ == "__main__":
    """
    🚀 快速开始使用说明：
    
    1. 修改配置：
       - 替换 API 密钥
       - 修改 base_url
       - 调整支持的模型列表
    
    2. 适配 API 格式：
       - 修改 _build_api_request 方法
       - 修改 _parse_api_response 方法
       - 调整健康检查端点
    
    3. 运行测试：
       python quick_start_custom_provider.py
    """
    import asyncio
    asyncio.run(quick_test())
