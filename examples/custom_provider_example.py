"""
自定义 AI Provider 完整示例

本示例展示如何创建一个完整的自定义 AI provider，包括：
- 继承 BaseProvider 基类
- 实现必要的抽象方法
- 处理 API 请求和响应
- 错误处理和重试机制
- 流式输出支持
"""

import json
import time
from typing import Any, AsyncIterator, Dict, List, Optional, Union
from uuid import uuid4

import httpx

from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.core.interfaces import (
    ImageGenerationRequest,
    ImageGenerationResponse,
    Message,
    MessageRole,
    ModelType,
    TextGenerationChoice,
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
    Usage,
)
from ai_gen_hub.providers.base import BaseProvider
from ai_gen_hub.utils.key_manager import KeyManager


class CustomAIProvider(BaseProvider):
    """自定义 AI Provider 示例实现
    
    这是一个完整的自定义 provider 示例，展示了如何：
    - 实现文本生成功能
    - 处理流式和非流式响应
    - 实现健康检查
    - 处理错误和重试
    - 支持多种模型
    """
    
    def __init__(self, config: ProviderConfig, key_manager: KeyManager):
        """初始化自定义 AI Provider
        
        Args:
            config: Provider 配置对象
            key_manager: 密钥管理器
        """
        super().__init__("custom_ai", config, key_manager)
        
        # 设置 API 基础 URL
        self.base_url = config.base_url or "https://api.custom-ai.com/v1"
        
        # 定义支持的模型类型
        self._supported_model_types = [
            ModelType.TEXT_GENERATION,
            # 如果支持图像生成，可以添加：
            # ModelType.IMAGE_GENERATION,
        ]
        
        # 定义支持的模型列表
        self._supported_models = [
            # 文本生成模型
            "custom-gpt-4",
            "custom-gpt-3.5",
            "custom-claude",
            "custom-llama",
            
            # 专用模型
            "custom-code-model",
            "custom-chat-model",
            "custom-instruct-model",
        ]
        
        # 模型映射（将通用名称映射到 API 特定名称）
        self._model_mapping = {
            "gpt-4": "custom-gpt-4",
            "gpt-3.5-turbo": "custom-gpt-3.5",
            "claude": "custom-claude",
            "llama": "custom-llama",
        }
    
    async def _perform_health_check(self, api_key: str) -> bool:
        """执行健康检查
        
        Args:
            api_key: API 密钥
            
        Returns:
            bool: 健康状态
        """
        try:
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            # 调用模型列表 API 进行健康检查
            response = await self._client.get(
                f"{self.base_url}/models",
                headers=headers,
                timeout=10.0
            )
            
            if response.status_code == 200:
                self.logger.info("自定义 AI Provider 健康检查通过")
                return True
            else:
                self.logger.warning(f"健康检查失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"健康检查异常: {str(e)}")
            return False
    
    async def generate_text(
        self, 
        request: TextGenerationRequest
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """生成文本
        
        Args:
            request: 文本生成请求
            
        Returns:
            文本生成响应或流式响应迭代器
        """
        # 获取 API 密钥
        api_key = await self.key_manager.get_key(self.name)
        if not api_key:
            from ai_gen_hub.core.exceptions import AuthenticationError
            raise AuthenticationError("未找到有效的自定义 AI API 密钥")
        
        # 调用实际的生成实现
        return await self._generate_text_impl(request, api_key.key)
    
    async def _generate_text_impl(
        self,
        request: TextGenerationRequest,
        api_key: str
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """文本生成的核心实现
        
        Args:
            request: 文本生成请求
            api_key: API 密钥
            
        Returns:
            文本生成响应或流式响应迭代器
        """
        # 构建请求数据
        request_data = self._build_chat_request(request)
        
        # 设置请求头
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        
        # 根据是否流式输出选择不同的处理方式
        if request.stream:
            return self._generate_text_stream(request_data, headers)
        else:
            return await self._generate_text_sync(request_data, headers)
    
    def _build_chat_request(self, request: TextGenerationRequest) -> Dict[str, Any]:
        """构建符合自定义 AI API 格式的请求数据
        
        Args:
            request: 标准文本生成请求
            
        Returns:
            API 特定格式的请求数据
        """
        # 映射模型名称
        model_name = self.map_model_name(request.model)
        
        # 转换消息格式
        messages = []
        for msg in request.messages:
            messages.append({
                "role": msg.role.value,
                "content": msg.content
            })
        
        # 构建基础请求数据
        request_data = {
            "model": model_name,
            "messages": messages,
            "stream": request.stream or False,
        }
        
        # 添加可选参数
        if request.temperature is not None:
            request_data["temperature"] = request.temperature
        
        if request.max_tokens is not None:
            request_data["max_tokens"] = request.max_tokens
        
        if request.top_p is not None:
            request_data["top_p"] = request.top_p
        
        if request.frequency_penalty is not None:
            request_data["frequency_penalty"] = request.frequency_penalty
        
        if request.presence_penalty is not None:
            request_data["presence_penalty"] = request.presence_penalty
        
        return request_data
    
    async def _generate_text_sync(
        self,
        request_data: Dict[str, Any],
        headers: Dict[str, str]
    ) -> TextGenerationResponse:
        """同步文本生成
        
        Args:
            request_data: 请求数据
            headers: 请求头
            
        Returns:
            文本生成响应
        """
        try:
            # 发送请求
            response = await self._make_request(
                "POST",
                f"{self.base_url}/chat/completions",
                headers=headers,
                json_data=request_data
            )
            
            response_data = response.json()
            
            # 检查错误
            if "error" in response_data:
                error_msg = response_data["error"].get("message", "未知错误")
                self.logger.error(f"自定义 AI API 错误: {error_msg}")
                from ai_gen_hub.core.exceptions import APIError
                raise APIError(error_msg)
            
            # 解析响应
            return self._parse_text_response(response_data)
            
        except Exception as e:
            self.logger.error(f"文本生成失败: {str(e)}")
            raise
    
    async def _generate_text_stream(
        self,
        request_data: Dict[str, Any],
        headers: Dict[str, str]
    ) -> AsyncIterator[TextGenerationStreamChunk]:
        """流式文本生成
        
        Args:
            request_data: 请求数据
            headers: 请求头
            
        Yields:
            文本生成流式响应块
        """
        try:
            # 发送流式请求
            async with self._client.stream(
                "POST",
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=request_data
            ) as response:
                
                if response.status_code != 200:
                    error_text = await response.aread()
                    self.logger.error(f"流式请求失败: {response.status_code} - {error_text}")
                    from ai_gen_hub.core.exceptions import APIError
                    raise APIError(f"API 请求失败: {response.status_code}")
                
                # 处理流式响应
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]  # 移除 "data: " 前缀
                        
                        if data_str.strip() == "[DONE]":
                            break
                        
                        try:
                            data = json.loads(data_str)
                            chunk = self._parse_stream_chunk(data)
                            if chunk:
                                yield chunk
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            self.logger.error(f"流式文本生成失败: {str(e)}")
            raise
    
    def _parse_text_response(self, response_data: Dict[str, Any]) -> TextGenerationResponse:
        """解析文本生成响应
        
        Args:
            response_data: API 响应数据
            
        Returns:
            标准文本生成响应
        """
        choices = []
        for choice_data in response_data.get("choices", []):
            message = choice_data.get("message", {})
            choice = TextGenerationChoice(
                index=choice_data.get("index", 0),
                message=Message(
                    role=MessageRole(message.get("role", "assistant")),
                    content=message.get("content", "")
                ),
                finish_reason=choice_data.get("finish_reason")
            )
            choices.append(choice)
        
        # 解析使用统计
        usage_data = response_data.get("usage", {})
        usage = Usage(
            prompt_tokens=usage_data.get("prompt_tokens", 0),
            completion_tokens=usage_data.get("completion_tokens", 0),
            total_tokens=usage_data.get("total_tokens", 0)
        )
        
        return TextGenerationResponse(
            id=response_data.get("id", str(uuid4())),
            choices=choices,
            usage=usage,
            model=response_data.get("model", ""),
            created=response_data.get("created", int(time.time()))
        )
    
    def _parse_stream_chunk(self, chunk_data: Dict[str, Any]) -> Optional[TextGenerationStreamChunk]:
        """解析流式响应块
        
        Args:
            chunk_data: 流式响应块数据
            
        Returns:
            标准流式响应块或 None
        """
        choices = chunk_data.get("choices", [])
        if not choices:
            return None
        
        choice = choices[0]
        delta = choice.get("delta", {})
        
        return TextGenerationStreamChunk(
            id=chunk_data.get("id", str(uuid4())),
            choices=[{
                "index": choice.get("index", 0),
                "delta": {
                    "role": delta.get("role"),
                    "content": delta.get("content", "")
                },
                "finish_reason": choice.get("finish_reason")
            }],
            model=chunk_data.get("model", ""),
            created=chunk_data.get("created", int(time.time()))
        )
