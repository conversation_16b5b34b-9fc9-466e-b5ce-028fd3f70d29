"""
自定义 Provider 测试和使用示例

本脚本展示如何：
1. 配置和初始化自定义 provider
2. 注册到 provider manager
3. 执行各种测试
4. 验证功能正确性

使用方法：
1. 设置环境变量：
   export CUSTOM_AI_API_KEY="your-api-key"
   export CUSTOM_AI_BASE_URL="https://api.custom-ai.com/v1"

2. 运行测试：
   python test_custom_provider.py
"""

import asyncio
import os
from typing import List

from ai_gen_hub.config.settings import ProviderConfig, get_settings
from ai_gen_hub.core.interfaces import (
    Message,
    MessageRole,
    TextGenerationRequest,
)
from ai_gen_hub.services.provider_manager import ProviderManagerImpl
from ai_gen_hub.utils.key_manager import KeyManager

# 导入自定义 provider（需要根据实际路径调整）
from custom_provider_example import CustomAIProvider


class CustomProviderTester:
    """自定义 Provider 测试器"""
    
    def __init__(self):
        self.provider_manager = None
        self.custom_provider = None
    
    async def setup(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")
        
        # 创建配置
        config = ProviderConfig(
            api_keys=[os.getenv("CUSTOM_AI_API_KEY", "test-api-key")],
            base_url=os.getenv("CUSTOM_AI_BASE_URL", "https://api.custom-ai.com/v1"),
            enabled=True,
            timeout=120,
            max_retries=3
        )
        
        # 创建密钥管理器
        key_manager = KeyManager()
        
        # 创建自定义 provider
        self.custom_provider = CustomAIProvider(config, key_manager)
        
        # 创建 provider manager
        settings = get_settings()
        self.provider_manager = ProviderManagerImpl(settings, key_manager)
        
        print("✅ 测试环境设置完成")
    
    async def test_provider_initialization(self):
        """测试 provider 初始化"""
        print("\n🧪 测试 Provider 初始化...")
        
        try:
            await self.custom_provider.initialize()
            print("✅ Provider 初始化成功")
            
            # 检查基本属性
            assert self.custom_provider.name == "custom_ai"
            assert len(self.custom_provider._supported_models) > 0
            print(f"✅ 支持的模型: {self.custom_provider._supported_models}")
            
        except Exception as e:
            print(f"❌ Provider 初始化失败: {e}")
            raise
    
    async def test_health_check(self):
        """测试健康检查"""
        print("\n🏥 测试健康检查...")
        
        try:
            # 注意：这里需要有效的 API 密钥才能通过真实的健康检查
            health_status = await self.custom_provider.health_check()
            print(f"✅ 健康检查结果: {'健康' if health_status else '不健康'}")
            
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
    
    async def test_provider_registration(self):
        """测试 provider 注册"""
        print("\n📝 测试 Provider 注册...")
        
        try:
            # 注册到 provider manager
            await self.provider_manager.register_provider(self.custom_provider)
            print("✅ Provider 注册成功")
            
            # 验证注册
            registered_provider = await self.provider_manager.get_provider_by_name("custom_ai")
            assert registered_provider is not None
            print("✅ Provider 注册验证通过")
            
        except Exception as e:
            print(f"❌ Provider 注册失败: {e}")
            raise
    
    async def test_text_generation(self):
        """测试文本生成功能"""
        print("\n💬 测试文本生成...")
        
        try:
            # 创建测试请求
            request = TextGenerationRequest(
                model="custom-gpt-4",
                messages=[
                    Message(
                        role=MessageRole.SYSTEM,
                        content="你是一个有用的AI助手。"
                    ),
                    Message(
                        role=MessageRole.USER,
                        content="请简单介绍一下人工智能的发展历史。"
                    )
                ],
                temperature=0.7,
                max_tokens=500,
                stream=False
            )
            
            # 执行文本生成
            response = await self.custom_provider.generate_text(request)
            
            # 验证响应
            assert response is not None
            assert len(response.choices) > 0
            assert response.choices[0].message.content
            
            print("✅ 文本生成成功")
            print(f"📄 生成内容预览: {response.choices[0].message.content[:100]}...")
            
        except Exception as e:
            print(f"❌ 文本生成失败: {e}")
    
    async def test_stream_generation(self):
        """测试流式文本生成"""
        print("\n🌊 测试流式文本生成...")
        
        try:
            # 创建流式请求
            request = TextGenerationRequest(
                model="custom-gpt-4",
                messages=[
                    Message(
                        role=MessageRole.USER,
                        content="请用一段话描述春天的美景。"
                    )
                ],
                temperature=0.8,
                max_tokens=200,
                stream=True
            )
            
            # 执行流式生成
            stream = await self.custom_provider.generate_text(request)
            
            print("✅ 流式生成开始")
            print("📝 流式内容: ", end="")
            
            chunk_count = 0
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].get("delta", {}).get("content"):
                    content = chunk.choices[0]["delta"]["content"]
                    print(content, end="", flush=True)
                    chunk_count += 1
            
            print(f"\n✅ 流式生成完成，共收到 {chunk_count} 个数据块")
            
        except Exception as e:
            print(f"❌ 流式生成失败: {e}")
    
    async def test_model_support(self):
        """测试模型支持功能"""
        print("\n🎯 测试模型支持...")
        
        try:
            # 测试支持的模型
            supported_models = [
                "custom-gpt-4",
                "custom-gpt-3.5",
                "custom-claude",
            ]
            
            for model in supported_models:
                supports = self.custom_provider.supports_model(model)
                print(f"✅ 模型 {model}: {'支持' if supports else '不支持'}")
                assert supports, f"应该支持模型 {model}"
            
            # 测试模型映射
            mapped_model = self.custom_provider.map_model_name("gpt-4")
            print(f"✅ 模型映射 gpt-4 -> {mapped_model}")
            assert mapped_model == "custom-gpt-4"
            
        except Exception as e:
            print(f"❌ 模型支持测试失败: {e}")
    
    async def test_error_handling(self):
        """测试错误处理"""
        print("\n⚠️ 测试错误处理...")
        
        try:
            # 测试无效模型
            request = TextGenerationRequest(
                model="invalid-model",
                messages=[
                    Message(role=MessageRole.USER, content="测试")
                ]
            )
            
            try:
                await self.custom_provider.generate_text(request)
                print("⚠️ 预期应该抛出错误，但没有")
            except Exception as e:
                print(f"✅ 正确处理了无效模型错误: {type(e).__name__}")
            
        except Exception as e:
            print(f"❌ 错误处理测试失败: {e}")
    
    async def cleanup(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        
        try:
            if self.custom_provider:
                await self.custom_provider.cleanup()
            
            if self.provider_manager:
                await self.provider_manager.cleanup()
            
            print("✅ 清理完成")
            
        except Exception as e:
            print(f"⚠️ 清理过程中出现错误: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始自定义 Provider 测试")
        print("=" * 50)
        
        try:
            await self.setup()
            await self.test_provider_initialization()
            await self.test_health_check()
            await self.test_provider_registration()
            await self.test_model_support()
            await self.test_text_generation()
            await self.test_stream_generation()
            await self.test_error_handling()
            
            print("\n" + "=" * 50)
            print("🎉 所有测试完成！")
            
        except Exception as e:
            print(f"\n❌ 测试过程中出现错误: {e}")
            raise
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    # 检查环境变量
    if not os.getenv("CUSTOM_AI_API_KEY"):
        print("⚠️ 警告: 未设置 CUSTOM_AI_API_KEY 环境变量")
        print("   某些测试可能会失败，但基本功能测试仍会执行")
    
    # 运行测试
    tester = CustomProviderTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    # 设置示例环境变量（仅用于演示）
    os.environ.setdefault("CUSTOM_AI_API_KEY", "demo-api-key")
    os.environ.setdefault("CUSTOM_AI_BASE_URL", "https://api.custom-ai.com/v1")
    
    # 运行测试
    asyncio.run(main())
