#!/usr/bin/env python3
"""
简单的 Google AI Provider 测试
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

# 设置环境变量
os.environ["PYTHONPATH"] = str(project_root / "src")

async def test_google_ai_provider():
    """测试 Google AI Provider"""
    print("🧪 测试 Google AI Provider...")
    
    try:
        from ai_gen_hub.providers.google_ai_provider import GoogleAIProvider
        from ai_gen_hub.core.interfaces import TextGenerationRequest, Message, MessageRole
        
        # 创建 Provider
        provider = GoogleAIProvider()
        print("✅ Provider 创建成功")
        
        # 创建测试请求
        request = TextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="Say hello")],
            model="gemini-2.5-flash",
            max_tokens=50,
            temperature=0.7
        )
        print("✅ 请求创建成功")
        
        # 测试生成
        print("📡 发送请求到 Google AI...")
        response = await provider.generate_text(request)
        print(f"✅ 响应成功: {response.content[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 开始简单的 Google AI Provider 测试...")
    print("=" * 50)
    
    success = await test_google_ai_provider()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试成功！")
    else:
        print("💥 测试失败！")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
