"""
OpenAI 供应商适配器

实现OpenAI API的完整适配，包括：
- 文本生成（Chat Completions）
- 图像生成（DALL-E）
- 流式输出支持
- 错误处理和重试
"""

import json
import time
from typing import Any, AsyncIterator, Dict, List, Union
from uuid import uuid4

from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.core.interfaces import (
    ImageData,
    ImageGenerationRequest,
    ImageGenerationResponse,
    Message,
    ModelType,
    TextGenerationChoice,
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
    Usage,
    OptimizedTextGenerationRequest,
)
from ai_gen_hub.core.provider_adapter import ProviderAdapterMixin
from ai_gen_hub.providers.base import BaseProvider
from ai_gen_hub.utils.key_manager import KeyManager


class OpenAIProvider(BaseProvider, ProviderAdapterMixin):
    """OpenAI供应商适配器

    集成了优化版本支持，提供以下增强功能：
    - 支持OptimizedTextGenerationRequest格式
    - 自动供应商兼容性检查和参数验证
    - 保持对传统TextGenerationRequest的向后兼容
    """
    
    def __init__(self, config: ProviderConfig, key_manager: KeyManager):
        """初始化OpenAI适配器
        
        Args:
            config: OpenAI配置
            key_manager: 密钥管理器
        """
        super().__init__("openai", config, key_manager)
        
        # 设置基础URL
        self.base_url = config.base_url or "https://api.openai.com/v1"
        
        # 支持的模型类型
        self._supported_model_types = [
            ModelType.TEXT_GENERATION,
            ModelType.IMAGE_GENERATION
        ]
        
        # 支持的模型列表 - 更新至2024年最新版本
        self._supported_models = [
            # GPT-4o 系列 (最新旗舰模型)
            "gpt-4o",                    # 最新的 GPT-4o 模型
            "gpt-4o-2024-08-06",        # GPT-4o 特定版本
            "gpt-4o-2024-05-13",        # GPT-4o 早期版本
            "gpt-4o-mini",              # GPT-4o 轻量版本
            "gpt-4o-mini-2024-07-18",   # GPT-4o mini 特定版本

            # GPT-4 Turbo 系列
            "gpt-4-turbo",              # 最新的 GPT-4 Turbo
            "gpt-4-turbo-2024-04-09",   # GPT-4 Turbo 特定版本
            "gpt-4-turbo-preview",      # GPT-4 Turbo 预览版

            # GPT-4 传统系列 (保持向后兼容)
            "gpt-4",
            "gpt-4-0613",
            "gpt-4-32k",
            "gpt-4-32k-0613",
            "gpt-4-1106-preview",
            "gpt-4-0125-preview",
            "gpt-4-vision-preview",

            # GPT-3.5 系列 (保持向后兼容)
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-0613",
            "gpt-3.5-turbo-16k",
            "gpt-3.5-turbo-16k-0613",
            "gpt-3.5-turbo-1106",
            "gpt-3.5-turbo-0125",

            # DALL-E 系列
            "dall-e-2",
            "dall-e-3",
        ]
        
        # 模型映射 - 更新为最新的推荐模型
        self._model_mapping = {
            # 最新模型映射 - 指向当前最佳性能模型
            "gpt-4-latest": "gpt-4o",                    # 最新 GPT-4 指向 GPT-4o
            "gpt-4o-latest": "gpt-4o",                   # GPT-4o 最新版本
            "gpt-4-turbo-latest": "gpt-4-turbo",         # GPT-4 Turbo 最新版本
            "gpt-3.5-latest": "gpt-3.5-turbo-0125",     # 保持 GPT-3.5 映射

            # 向后兼容的别名映射
            "gpt-4-mini": "gpt-4o-mini",                 # 轻量版本映射
            "gpt-4-vision": "gpt-4-vision-preview",      # 视觉模型映射

            # DALL-E 模型映射
            "dalle-2": "dall-e-2",
            "dalle-3": "dall-e-3",
            "dalle-latest": "dall-e-3",                  # 最新图像生成模型
        }
    
    async def _perform_health_check(self, api_key: str) -> bool:
        """执行健康检查

        通过调用 OpenAI 模型列表 API 来验证 API 密钥的有效性

        Args:
            api_key: OpenAI API 密钥

        Returns:
            bool: 健康检查是否成功
        """
        try:
            # 发送一个简单的模型列表请求来验证 API 密钥
            headers = {"Authorization": f"Bearer {api_key}"}
            response = await self._make_request(
                "GET",
                f"{self.base_url}/models",
                headers=headers
            )
            return response.status_code == 200
        except Exception as e:
            self.logger.warning(f"OpenAI 健康检查失败: {e}")
            return False

    def _handle_openai_specific_errors(self, error_data: Dict[str, Any], status_code: int) -> None:
        """处理 OpenAI 特定的错误类型

        Args:
            error_data: 错误响应数据
            status_code: HTTP 状态码

        Raises:
            相应的异常类型
        """
        from ai_gen_hub.core.exceptions import (
            APIError, AuthenticationError, AuthorizationError,
            RateLimitError, QuotaExceededError, ModelNotSupportedError
        )

        error_message = self._extract_error_message(error_data)
        error_type = error_data.get("error", {}).get("type", "")
        error_code = error_data.get("error", {}).get("code", "")

        # OpenAI 特定错误类型处理
        if error_type == "invalid_api_key" or "api key" in error_message.lower():
            raise AuthenticationError(f"OpenAI API 密钥无效: {error_message}")
        elif error_type == "insufficient_quota" or "quota" in error_message.lower():
            raise QuotaExceededError(f"OpenAI 配额不足: {error_message}")
        elif error_type == "model_not_found" or "model" in error_message.lower():
            raise ModelNotSupportedError("", self.name, f"OpenAI 模型不支持: {error_message}")
        elif error_type == "rate_limit_exceeded":
            # 尝试从响应头获取重试时间
            retry_after = None
            if hasattr(error_data, 'headers'):
                retry_after = error_data.headers.get("Retry-After")
            raise RateLimitError(f"OpenAI 请求频率超限: {error_message}", retry_after)
        elif error_code == "context_length_exceeded":
            raise APIError(f"OpenAI 上下文长度超限: {error_message}", status_code=status_code, retryable=False)
        elif error_code == "content_filter":
            raise APIError(f"OpenAI 内容被过滤: {error_message}", status_code=status_code, retryable=False)
        else:
            # 默认处理
            raise APIError(f"OpenAI API 错误: {error_message}", status_code=status_code)
    
    async def _generate_text_impl(
        self,
        request: TextGenerationRequest,
        api_key: str
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """实现文本生成"""
        # 构建请求数据
        request_data = self._build_text_request(request)
        
        # 设置请求头
        headers = {"Authorization": f"Bearer {api_key}"}
        
        # 构建URL
        url = f"{self.base_url}/chat/completions"
        
        try:
            if request.stream:
                # 流式响应
                return self._handle_stream_response(request, headers, url, request_data)
            else:
                # 普通响应
                response = await self._make_request(
                    "POST",
                    url,
                    headers=headers,
                    json_data=request_data
                )

                response_data = response.json()
                return self._parse_text_response(response_data, request)
        except Exception as e:
            # 如果是 API 错误且包含错误数据，尝试 OpenAI 特定错误处理
            if hasattr(e, 'response_data') and e.response_data:
                self._handle_openai_specific_errors(e.response_data, getattr(e, 'status_code', 500))
            raise
    
    def _build_text_request(self, request: TextGenerationRequest) -> Dict[str, Any]:
        """构建文本生成请求数据"""
        # 转换消息格式
        messages = []
        for msg in request.messages:
            message_dict = {
                "role": msg.role,
                "content": msg.content
            }
            if msg.name:
                message_dict["name"] = msg.name
            if msg.function_call:
                message_dict["function_call"] = msg.function_call
            if msg.tool_calls:
                message_dict["tool_calls"] = msg.tool_calls
            messages.append(message_dict)
        
        # 构建基础请求
        request_data = {
            "model": self.map_model_name(request.model),
            "messages": messages,
            "stream": request.stream,
        }

        # 如果启用流式响应，添加流式选项以获取使用量信息
        if request.stream:
            request_data["stream_options"] = {"include_usage": True}
        
        # 添加可选参数
        if request.max_tokens is not None:
            request_data["max_tokens"] = request.max_tokens
        if request.temperature is not None:
            request_data["temperature"] = request.temperature
        if request.top_p is not None:
            request_data["top_p"] = request.top_p
        if request.frequency_penalty is not None:
            request_data["frequency_penalty"] = request.frequency_penalty
        if request.presence_penalty is not None:
            request_data["presence_penalty"] = request.presence_penalty
        if request.stop is not None:
            request_data["stop"] = request.stop
        if request.user is not None:
            request_data["user"] = request.user
        
        # 工具调用支持 (推荐使用 tools 而非已废弃的 functions)
        if request.tools:
            request_data["tools"] = request.tools
        if request.tool_choice:
            request_data["tool_choice"] = request.tool_choice

        # 向后兼容：支持传统的 functions 参数，但转换为 tools 格式
        if request.functions and not request.tools:
            # 将 functions 格式转换为 tools 格式
            tools = []
            for func in request.functions:
                tool = {
                    "type": "function",
                    "function": func
                }
                tools.append(tool)
            request_data["tools"] = tools

            # 转换 function_call 为 tool_choice
            if request.function_call:
                if request.function_call == "auto":
                    request_data["tool_choice"] = "auto"
                elif request.function_call == "none":
                    request_data["tool_choice"] = "none"
                elif isinstance(request.function_call, dict) and "name" in request.function_call:
                    request_data["tool_choice"] = {
                        "type": "function",
                        "function": {"name": request.function_call["name"]}
                    }
        
        # 供应商特定参数
        if request.provider_params:
            request_data.update(request.provider_params)
        
        return request_data
    
    def _parse_text_response(
        self,
        response_data: Dict[str, Any],
        request: TextGenerationRequest
    ) -> TextGenerationResponse:
        """解析文本生成响应"""
        # 解析选择
        choices = []
        for choice_data in response_data.get("choices", []):
            message_data = choice_data.get("message", {})
            message = Message(
                role=message_data.get("role", "assistant"),
                content=message_data.get("content", ""),
                function_call=message_data.get("function_call"),
                tool_calls=message_data.get("tool_calls")
            )
            
            choice = TextGenerationChoice(
                index=choice_data.get("index", 0),
                message=message,
                finish_reason=choice_data.get("finish_reason")
            )
            choices.append(choice)
        
        # 解析使用量
        usage_data = response_data.get("usage", {})
        usage = Usage(
            prompt_tokens=usage_data.get("prompt_tokens", 0),
            completion_tokens=usage_data.get("completion_tokens", 0),
            total_tokens=usage_data.get("total_tokens", 0)
        )
        
        return TextGenerationResponse(
            id=response_data.get("id", str(uuid4())),
            object=response_data.get("object", "chat.completion"),
            created=response_data.get("created", int(time.time())),
            model=response_data.get("model", request.model),
            choices=choices,
            usage=usage,
            provider=self.name,
            request_id=str(uuid4()),
            processing_time=0.0  # 将在调用层计算
        )
    
    async def _handle_stream_response(
        self,
        request: TextGenerationRequest,
        headers: Dict[str, str],
        url: str,
        request_data: Dict[str, Any]
    ) -> AsyncIterator[TextGenerationStreamChunk]:
        """处理流式响应"""
        async for chunk in self._make_request(
            "POST",
            url,
            headers=headers,
            json_data=request_data,
            stream=True
        ):
            # 解析SSE数据
            chunk_str = chunk.decode('utf-8')
            for line in chunk_str.split('\n'):
                line = line.strip()
                if line.startswith('data: '):
                    data_str = line[6:]  # 移除 'data: ' 前缀
                    
                    if data_str == '[DONE]':
                        return
                    
                    try:
                        data = json.loads(data_str)

                        # 构建流式响应块
                        chunk_response = TextGenerationStreamChunk(
                            id=data.get("id", str(uuid4())),
                            object=data.get("object", "chat.completion.chunk"),
                            created=data.get("created", int(time.time())),
                            model=data.get("model", request.model),
                            choices=data.get("choices", []),
                            provider=self.name,
                            request_id=str(uuid4())
                        )

                        # 如果包含使用量信息，添加到响应中
                        if "usage" in data:
                            chunk_response.usage = Usage(
                                prompt_tokens=data["usage"].get("prompt_tokens", 0),
                                completion_tokens=data["usage"].get("completion_tokens", 0),
                                total_tokens=data["usage"].get("total_tokens", 0)
                            )

                        yield chunk_response

                    except json.JSONDecodeError:
                        # 忽略无效的JSON数据，记录警告日志
                        self.logger.warning(f"无法解析流式响应数据: {data_str}")
                        continue
    
    async def _generate_image_impl(
        self,
        request: ImageGenerationRequest,
        api_key: str
    ) -> ImageGenerationResponse:
        """实现图像生成"""
        # 构建请求数据
        request_data = self._build_image_request(request)
        
        # 设置请求头
        headers = {"Authorization": f"Bearer {api_key}"}
        
        # 构建URL
        url = f"{self.base_url}/images/generations"
        
        # 发送请求
        response = await self._make_request(
            "POST",
            url,
            headers=headers,
            json_data=request_data
        )
        
        response_data = response.json()
        return self._parse_image_response(response_data)
    
    def _build_image_request(self, request: ImageGenerationRequest) -> Dict[str, Any]:
        """构建图像生成请求数据"""
        request_data = {
            "prompt": request.prompt,
            "n": request.n,
            "size": request.size,
            "response_format": request.response_format,
        }
        
        # 设置模型（DALL-E 3支持）
        if request.model:
            model = self.map_model_name(request.model)
            request_data["model"] = model
            
            # DALL-E 3特定参数
            if model == "dall-e-3":
                if request.quality:
                    request_data["quality"] = request.quality
                if request.style:
                    request_data["style"] = request.style
        
        if request.user:
            request_data["user"] = request.user
        
        # 供应商特定参数
        if request.provider_params:
            request_data.update(request.provider_params)
        
        return request_data
    
    def _parse_image_response(self, response_data: Dict[str, Any]) -> ImageGenerationResponse:
        """解析图像生成响应"""
        # 解析图像数据
        image_data_list = []
        for data in response_data.get("data", []):
            image_data = ImageData(
                url=data.get("url"),
                b64_json=data.get("b64_json"),
                revised_prompt=data.get("revised_prompt")
            )
            image_data_list.append(image_data)
        
        return ImageGenerationResponse(
            created=response_data.get("created", int(time.time())),
            data=image_data_list,
            provider=self.name,
            request_id=str(uuid4()),
            processing_time=0.0  # 将在调用层计算
        )

    # ========================================================================
    # 优化版本支持方法
    # ========================================================================

    async def _generate_text_with_params(
        self,
        params: Dict[str, Any],
        request: OptimizedTextGenerationRequest
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """使用供应商特定参数生成文本（优化版本实现）

        这个方法实现了ProviderAdapterMixin的抽象方法，
        用于处理OpenAI特定的API调用。

        Args:
            params: OpenAI API参数
            request: 原始的优化版本请求

        Returns:
            文本生成响应或流式响应迭代器
        """
        self.logger.info(
            "开始OpenAI文本生成（优化版本）",
            model=request.model,
            stream=request.stream.enabled,
            max_tokens=request.generation.max_tokens,
            temperature=request.generation.temperature
        )

        try:
            # 获取API密钥
            api_key = await self.key_manager.get_key(self.name)
            if not api_key:
                raise ValueError(f"未找到 {self.name} 的有效API密钥")

            # 设置请求头
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

            # 构建URL
            url = f"{self.base_url}/chat/completions"

            if request.stream.enabled:
                # 流式响应
                return self._handle_stream_response_optimized(headers, url, params, request)
            else:
                # 普通响应
                response = await self._make_request(
                    "POST",
                    url,
                    headers=headers,
                    json_data=params
                )

                response_data = response.json()
                return self._parse_text_response_optimized(response_data, request)

        except Exception as e:
            self.logger.error(f"OpenAI文本生成失败（优化版本）: {e}")
            raise

    def _parse_text_response_optimized(
        self,
        response_data: Dict[str, Any],
        request: OptimizedTextGenerationRequest
    ) -> TextGenerationResponse:
        """解析OpenAI文本生成响应（优化版本）

        Args:
            response_data: OpenAI API响应数据
            request: 原始的优化版本请求

        Returns:
            标准化的文本生成响应
        """
        # 解析选择
        choices = []
        for choice_data in response_data.get("choices", []):
            message_data = choice_data.get("message", {})

            # 创建消息对象
            message = Message(
                role=message_data.get("role", "assistant"),
                content=message_data.get("content", ""),
                function_call=message_data.get("function_call"),
                tool_calls=message_data.get("tool_calls")
            )

            choice = TextGenerationChoice(
                index=choice_data.get("index", 0),
                message=message,
                finish_reason=choice_data.get("finish_reason")
            )
            choices.append(choice)

        # 解析使用量
        usage_data = response_data.get("usage", {})
        usage = Usage(
            prompt_tokens=usage_data.get("prompt_tokens", 0),
            completion_tokens=usage_data.get("completion_tokens", 0),
            total_tokens=usage_data.get("total_tokens", 0)
        )

        return TextGenerationResponse(
            id=response_data.get("id", str(uuid4())),
            object=response_data.get("object", "chat.completion"),
            created=response_data.get("created", int(time.time())),
            model=response_data.get("model", request.model),
            choices=choices,
            usage=usage,
            provider=self.name,
            request_id=str(uuid4()),
            processing_time=0.0  # 将在调用层计算
        )

    async def _handle_stream_response_optimized(
        self,
        headers: Dict[str, str],
        url: str,
        params: Dict[str, Any],
        request: OptimizedTextGenerationRequest
    ) -> AsyncIterator[TextGenerationStreamChunk]:
        """处理OpenAI流式响应（优化版本）

        Args:
            headers: 请求头
            url: API URL
            params: 请求参数
            request: 原始的优化版本请求

        Yields:
            流式响应块
        """
        self.logger.info("开始OpenAI流式文本生成（优化版本）")

        # 设置流式参数
        params["stream"] = True
        if "stream_options" not in params:
            params["stream_options"] = {"include_usage": True}

        try:
            async with self.session.post(url, headers=headers, json=params) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"OpenAI API错误: {response.status} - {error_text}")

                async for line in response.content:
                    line = line.decode('utf-8').strip()

                    if line.startswith("data: "):
                        data = line[6:]  # 移除 "data: " 前缀

                        if data == "[DONE]":
                            break

                        try:
                            chunk_data = json.loads(data)

                            # 创建流式响应块
                            chunk = TextGenerationStreamChunk(
                                id=chunk_data.get("id", str(uuid4())),
                                object=chunk_data.get("object", "chat.completion.chunk"),
                                created=chunk_data.get("created", int(time.time())),
                                model=chunk_data.get("model", request.model),
                                choices=chunk_data.get("choices", []),
                                provider=self.name,
                                request_id=str(uuid4())
                            )

                            yield chunk

                        except json.JSONDecodeError:
                            self.logger.warning(f"无法解析流式数据: {data}")
                            continue

        except Exception as e:
            self.logger.error(f"OpenAI流式响应处理失败（优化版本）: {e}")
            raise
