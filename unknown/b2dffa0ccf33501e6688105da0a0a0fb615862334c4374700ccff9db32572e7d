#!/usr/bin/env python3
"""
AI Gen Hub 修复验证脚本

验证所有修复是否正确应用并正常工作。

使用方法:
    python verify_fixes.py
"""

import asyncio
import json
import logging
import sys
import time
from typing import Dict, Any

import httpx

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class FixVerifier:
    """修复验证器"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=300.0)
    
    async def test_health_endpoint_fix(self) -> Dict[str, Any]:
        """测试健康检查端点修复"""
        logger.info("=== 测试健康检查端点修复 ===")
        
        try:
            # 测试根路径健康检查（应该不再返回307）
            response = await self.client.get(f"{self.base_url}/health")
            
            logger.info(f"健康检查响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                health_data = response.json()
                logger.info(f"健康检查数据: {json.dumps(health_data, indent=2, ensure_ascii=False)}")
                
                # 验证响应格式
                if "status" in health_data and health_data["status"] == "healthy":
                    return {
                        "health_endpoint_fixed": True,
                        "status_code": response.status_code,
                        "health_data": health_data
                    }
                else:
                    return {
                        "health_endpoint_fixed": False,
                        "error": "健康检查响应格式不正确",
                        "health_data": health_data
                    }
            else:
                return {
                    "health_endpoint_fixed": False,
                    "status_code": response.status_code,
                    "error": response.text
                }
                
        except Exception as e:
            logger.error(f"健康检查端点测试失败: {e}")
            return {
                "health_endpoint_fixed": False,
                "error": str(e)
            }
    
    async def test_text_generation_timeout_fix(self) -> Dict[str, Any]:
        """测试文本生成超时修复"""
        logger.info("=== 测试文本生成超时修复 ===")
        
        request_data = {
            "model": "gemini-2.5-flash",
            "messages": [
                {
                    "role": "user",
                    "content": "请简单介绍一下人工智能的发展历史，不超过100字。"
                }
            ],
            "stream": False,
            "max_tokens": 150
        }
        
        try:
            logger.info("发送文本生成请求...")
            start_time = time.time()
            
            response = await self.client.post(
                f"{self.base_url}/api/v1/text/generate",
                json=request_data
            )
            
            elapsed_time = time.time() - start_time
            logger.info(f"文本生成响应状态码: {response.status_code}")
            logger.info(f"响应时间: {elapsed_time:.2f}秒")
            
            if response.status_code == 200:
                response_data = response.json()
                
                # 提取生成的内容
                content = ""
                if "choices" in response_data and response_data["choices"]:
                    choice = response_data["choices"][0]
                    if "message" in choice and "content" in choice["message"]:
                        content = choice["message"]["content"]
                        logger.info(f"生成的内容: {content}")
                
                return {
                    "text_generation_fixed": True,
                    "response_time": elapsed_time,
                    "generated_content": content,
                    "response_data": response_data
                }
            else:
                logger.error(f"文本生成请求失败: {response.text}")
                return {
                    "text_generation_fixed": False,
                    "status_code": response.status_code,
                    "error": response.text,
                    "response_time": elapsed_time
                }
                
        except Exception as e:
            logger.error(f"文本生成测试失败: {e}")
            return {
                "text_generation_fixed": False,
                "error": str(e)
            }
    
    async def test_streaming_generation_fix(self) -> Dict[str, Any]:
        """测试流式生成修复"""
        logger.info("=== 测试流式生成修复 ===")
        
        request_data = {
            "model": "gemini-2.5-flash",
            "messages": [
                {
                    "role": "user",
                    "content": "请写一首关于科技的短诗，4行即可。"
                }
            ],
            "stream": True,
            "max_tokens": 200
        }
        
        try:
            logger.info("发送流式生成请求...")
            start_time = time.time()
            
            chunk_count = 0
            total_content = ""
            error_count = 0
            
            async with self.client.stream(
                "POST",
                f"{self.base_url}/api/v1/text/generate",
                json=request_data
            ) as response:
                logger.info(f"流式响应状态码: {response.status_code}")
                
                if response.status_code != 200:
                    error_text = await response.aread()
                    logger.error(f"流式请求失败: {error_text.decode()}")
                    return {
                        "streaming_fixed": False,
                        "error": error_text.decode(),
                        "status_code": response.status_code
                    }
                
                async for chunk in response.aiter_bytes():
                    chunk_count += 1
                    chunk_str = chunk.decode('utf-8')
                    
                    # 解析SSE数据
                    for line in chunk_str.split('\n'):
                        line = line.strip()
                        if line.startswith('data: '):
                            data_str = line[6:]
                            if data_str and data_str != '[DONE]':
                                try:
                                    data = json.loads(data_str)
                                    
                                    # 检查是否是错误事件
                                    if 'event' in data and data['event'] == 'error':
                                        error_count += 1
                                        logger.error(f"收到错误事件: {data}")
                                        continue
                                    
                                    # 提取内容
                                    if 'choices' in data and data['choices']:
                                        choice = data['choices'][0]
                                        if 'delta' in choice and 'content' in choice['delta']:
                                            content = choice['delta']['content']
                                            total_content += content
                                            logger.debug(f"收到内容: {content}")
                                            
                                except json.JSONDecodeError as e:
                                    logger.warning(f"JSON解析失败: {e}, 数据: {data_str[:100]}...")
                                    continue
            
            elapsed_time = time.time() - start_time
            logger.info(f"流式请求完成，耗时: {elapsed_time:.2f}秒")
            logger.info(f"共收到 {chunk_count} 个数据块")
            logger.info(f"生成内容: {total_content}")
            
            return {
                "streaming_fixed": True,
                "elapsed_time": elapsed_time,
                "chunk_count": chunk_count,
                "content": total_content,
                "error_count": error_count
            }
            
        except Exception as e:
            logger.error(f"流式生成测试失败: {e}")
            return {
                "streaming_fixed": False,
                "error": str(e)
            }
    
    async def test_error_handling_improvement(self) -> Dict[str, Any]:
        """测试错误处理改进"""
        logger.info("=== 测试错误处理改进 ===")
        
        # 使用无效的模型名称测试错误处理
        request_data = {
            "model": "invalid-model-name-12345",
            "messages": [
                {
                    "role": "user",
                    "content": "测试错误处理"
                }
            ],
            "stream": False
        }
        
        try:
            logger.info("发送错误请求（无效模型）...")
            
            response = await self.client.post(
                f"{self.base_url}/api/v1/text/generate",
                json=request_data
            )
            
            logger.info(f"错误请求响应状态码: {response.status_code}")
            
            if response.status_code != 200:
                error_text = response.text
                logger.info(f"错误响应: {error_text}")
                
                # 检查错误信息是否有意义
                if error_text and len(error_text.strip()) > 0:
                    try:
                        error_data = response.json()
                        return {
                            "error_handling_improved": True,
                            "status_code": response.status_code,
                            "error_message": error_data.get("detail", error_text),
                            "has_meaningful_error": True
                        }
                    except:
                        return {
                            "error_handling_improved": True,
                            "status_code": response.status_code,
                            "error_message": error_text,
                            "has_meaningful_error": True
                        }
                else:
                    return {
                        "error_handling_improved": False,
                        "status_code": response.status_code,
                        "error_message": "空错误信息",
                        "has_meaningful_error": False
                    }
            else:
                logger.warning("错误请求意外成功")
                return {
                    "error_handling_improved": False,
                    "message": "错误请求意外成功"
                }
                
        except Exception as e:
            logger.error(f"错误处理测试异常: {e}")
            return {
                "error_handling_improved": False,
                "error": str(e)
            }
    
    async def run_all_verifications(self) -> Dict[str, Any]:
        """运行所有验证测试"""
        logger.info("开始修复验证测试...")
        
        results = {}
        
        # 1. 健康检查端点修复验证
        results["health_endpoint"] = await self.test_health_endpoint_fix()
        
        # 2. 文本生成超时修复验证
        results["text_generation"] = await self.test_text_generation_timeout_fix()
        
        # 3. 流式生成修复验证
        results["streaming_generation"] = await self.test_streaming_generation_fix()
        
        # 4. 错误处理改进验证
        results["error_handling"] = await self.test_error_handling_improvement()
        
        return results
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


async def main():
    """主函数"""
    verifier = FixVerifier()
    
    try:
        results = await verifier.run_all_verifications()
        
        # 输出验证结果摘要
        logger.info("=== 修复验证结果摘要 ===")
        
        total_tests = 0
        passed_tests = 0
        
        for test_name, result in results.items():
            total_tests += 1
            
            # 根据不同的测试类型检查成功标志
            success_flags = [
                "health_endpoint_fixed",
                "text_generation_fixed", 
                "streaming_fixed",
                "error_handling_improved"
            ]
            
            test_passed = any(result.get(flag, False) for flag in success_flags)
            
            if test_passed:
                passed_tests += 1
                status = "✅ 通过"
            else:
                status = "❌ 失败"
                if "error" in result:
                    logger.error(f"  错误: {result['error']}")
            
            logger.info(f"{test_name}: {status}")
        
        # 计算总体成功率
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        logger.info(f"总体成功率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        if success_rate >= 75:
            logger.info("🎉 修复验证成功！所有主要功能正常工作")
        elif success_rate >= 50:
            logger.warning("⚠️ 修复部分成功，仍有一些问题需要解决")
        else:
            logger.error("❌ 修复验证失败，需要进一步调试")
        
        return results
        
    finally:
        await verifier.close()


if __name__ == "__main__":
    asyncio.run(main())
