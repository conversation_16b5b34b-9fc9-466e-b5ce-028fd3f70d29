"""
数据库模型定义

定义所有数据库表的SQLAlchemy模型
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Set

from sqlalchemy import (
    Column, String, Integer, Float, Boolean, DateTime, Text, JSON,
    ForeignKey, Index, UniqueConstraint, CheckConstraint, TypeDecorator
)
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import json

# 创建基础模型类
Base = declarative_base()


class JSONArray(TypeDecorator):
    """兼容SQLite的数组类型，使用JSON存储"""
    impl = Text
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is not None:
            return json.dumps(value)
        return value

    def process_result_value(self, value, dialect):
        if value is not None:
            return json.loads(value)
        return value


def get_array_type():
    """根据数据库类型返回合适的数组类型"""
    # 在实际使用中，可以根据dialect判断
    # 这里为了简化，直接使用JSONArray
    return JSONArray()


class User(Base):
    """用户表"""
    __tablename__ = "users"
    
    # 主键和基本信息
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    full_name = Column(String(100), nullable=True)
    
    # 认证信息
    password_hash = Column(String(255), nullable=False)
    role = Column(String(20), nullable=False, default="user")
    status = Column(String(20), nullable=False, default="active")
    
    # 权限和配额
    permissions = Column(get_array_type(), nullable=False, default=list)
    api_quota = Column(Integer, nullable=False, default=1000)
    api_quota_used = Column(Integer, nullable=False, default=0)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    
    # 元数据
    metadata_json = Column(JSON, nullable=False, default=dict)
    
    # 关系
    api_keys = relationship("APIKey", back_populates="user", cascade="all, delete-orphan")
    usage_records = relationship("UserUsage", back_populates="user", cascade="all, delete-orphan")
    
    # 约束
    __table_args__ = (
        CheckConstraint("api_quota >= 0", name="check_api_quota_positive"),
        CheckConstraint("api_quota_used >= 0", name="check_api_quota_used_positive"),
        CheckConstraint("role IN ('admin', 'developer', 'user', 'guest')", name="check_valid_role"),
        CheckConstraint("status IN ('active', 'inactive', 'suspended', 'banned')", name="check_valid_status"),
        Index("idx_user_role_status", "role", "status"),
        Index("idx_user_created_at", "created_at"),
    )
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"


class APIKey(Base):
    """API密钥表"""
    __tablename__ = "api_keys"
    
    # 主键和关联
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # 密钥信息
    name = Column(String(100), nullable=False)
    key_hash = Column(String(255), nullable=False, unique=True)
    key_prefix = Column(String(20), nullable=False)
    
    # 权限和限制
    permissions = Column(get_array_type(), nullable=False, default=list)
    rate_limit = Column(Integer, nullable=False, default=100)
    
    # 状态和时间
    is_active = Column(Boolean, nullable=False, default=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    
    # 关系
    user = relationship("User", back_populates="api_keys")
    
    # 约束
    __table_args__ = (
        CheckConstraint("rate_limit > 0", name="check_rate_limit_positive"),
        Index("idx_api_key_user_id", "user_id"),
        Index("idx_api_key_active", "is_active"),
        Index("idx_api_key_expires", "expires_at"),
        UniqueConstraint("user_id", "name", name="uq_user_api_key_name"),
    )
    
    def __repr__(self):
        return f"<APIKey(id={self.id}, name='{self.name}', user_id={self.user_id})>"


class UserUsage(Base):
    """用户使用量统计表"""
    __tablename__ = "user_usage"
    
    # 主键和关联
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # 服务信息
    service_type = Column(String(50), nullable=False)  # text_generation, image_generation, etc.
    model = Column(String(100), nullable=False)
    provider = Column(String(50), nullable=False)
    
    # 使用量统计
    tokens_used = Column(Integer, nullable=False, default=0)
    requests_count = Column(Integer, nullable=False, default=1)
    cost = Column(Float, nullable=False, default=0.0)
    
    # 请求详情
    request_id = Column(String(100), nullable=True)
    response_time = Column(Float, nullable=True)  # 响应时间（秒）
    status = Column(String(20), nullable=False, default="success")  # success, error, timeout
    error_message = Column(Text, nullable=True)
    
    # 时间信息
    date = Column(DateTime(timezone=True), nullable=False, default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    user = relationship("User", back_populates="usage_records")
    
    # 约束
    __table_args__ = (
        CheckConstraint("tokens_used >= 0", name="check_tokens_used_positive"),
        CheckConstraint("requests_count >= 0", name="check_requests_count_positive"),
        CheckConstraint("cost >= 0", name="check_cost_positive"),
        CheckConstraint("status IN ('success', 'error', 'timeout')", name="check_valid_status"),
        Index("idx_usage_user_date", "user_id", "date"),
        Index("idx_usage_service_type", "service_type"),
        Index("idx_usage_model", "model"),
        Index("idx_usage_provider", "provider"),
        Index("idx_usage_status", "status"),
        Index("idx_usage_created_at", "created_at"),
    )
    
    def __repr__(self):
        return f"<UserUsage(id={self.id}, user_id={self.user_id}, service='{self.service_type}')>"


class SystemConfig(Base):
    """系统配置表"""
    __tablename__ = "system_config"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 配置信息
    key = Column(String(100), unique=True, nullable=False, index=True)
    value = Column(JSON, nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String(50), nullable=False, default="general")
    
    # 元数据
    is_public = Column(Boolean, nullable=False, default=False)  # 是否可以通过API访问
    is_encrypted = Column(Boolean, nullable=False, default=False)  # 值是否加密存储
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    
    # 约束
    __table_args__ = (
        Index("idx_config_category", "category"),
        Index("idx_config_public", "is_public"),
        Index("idx_config_updated_at", "updated_at"),
    )
    
    def __repr__(self):
        return f"<SystemConfig(key='{self.key}', category='{self.category}')>"


class ProviderStatus(Base):
    """供应商状态表"""
    __tablename__ = "provider_status"
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 供应商信息
    provider_name = Column(String(50), nullable=False, index=True)
    model_name = Column(String(100), nullable=False)
    
    # 状态信息
    is_available = Column(Boolean, nullable=False, default=True)
    last_check_at = Column(DateTime(timezone=True), server_default=func.now())
    last_success_at = Column(DateTime(timezone=True), nullable=True)
    last_error_at = Column(DateTime(timezone=True), nullable=True)
    last_error_message = Column(Text, nullable=True)
    
    # 性能指标
    avg_response_time = Column(Float, nullable=True)  # 平均响应时间（秒）
    success_rate = Column(Float, nullable=True)  # 成功率（0-1）
    total_requests = Column(Integer, nullable=False, default=0)
    failed_requests = Column(Integer, nullable=False, default=0)
    
    # 配置信息
    priority = Column(Integer, nullable=False, default=100)  # 优先级，数字越小优先级越高
    max_tokens_limit = Column(Integer, nullable=True)
    rate_limit = Column(Integer, nullable=True)  # 每分钟请求限制
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 约束
    __table_args__ = (
        CheckConstraint("success_rate >= 0 AND success_rate <= 1", name="check_success_rate_range"),
        CheckConstraint("priority > 0", name="check_priority_positive"),
        CheckConstraint("total_requests >= 0", name="check_total_requests_positive"),
        CheckConstraint("failed_requests >= 0", name="check_failed_requests_positive"),
        UniqueConstraint("provider_name", "model_name", name="uq_provider_model"),
        Index("idx_provider_available", "is_available"),
        Index("idx_provider_priority", "priority"),
        Index("idx_provider_updated_at", "updated_at"),
    )
    
    def __repr__(self):
        return f"<ProviderStatus(provider='{self.provider_name}', available={self.is_available})>"
