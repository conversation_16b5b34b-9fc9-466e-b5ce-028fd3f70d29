"""
数据库迁移脚本

提供数据库初始化、迁移、种子数据等功能
"""

import asyncio
from datetime import datetime
from typing import List, Dict, Any
from uuid import uuid4

from sqlalchemy.ext.asyncio import AsyncSession
from structlog import get_logger

from ai_gen_hub.database.connection import DatabaseManager
from ai_gen_hub.database.models import User, SystemConfig, ProviderStatus
from ai_gen_hub.database.repositories import UserRepository, SystemConfigRepository
from ai_gen_hub.core.models.auth import UserCreate, UserRole, Permission, RolePermissions
from ai_gen_hub.services.auth_service import AuthService


class DatabaseMigration:
    """数据库迁移管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.logger = get_logger(__name__)
    
    async def run_initial_migration(self):
        """运行初始迁移"""
        self.logger.info("开始数据库初始迁移")
        
        try:
            # 创建表
            await self.db_manager.create_tables()
            
            # 插入种子数据
            await self.insert_seed_data()
            
            self.logger.info("数据库初始迁移完成")
            
        except Exception as e:
            self.logger.error("数据库初始迁移失败", error=str(e))
            raise
    
    async def insert_seed_data(self):
        """插入种子数据"""
        async with self.db_manager.get_session() as session:
            await self._create_default_admin(session)
            await self._create_system_configs(session)
            await self._create_provider_status(session)
    
    async def _create_default_admin(self, session: AsyncSession):
        """创建默认管理员用户"""
        user_repo = UserRepository(session)
        
        # 检查是否已存在管理员
        existing_admin = await user_repo.get_user_by_username("admin")
        if existing_admin:
            self.logger.info("默认管理员用户已存在")
            return
        
        # 创建管理员用户
        auth_service = AuthService(
            jwt_secret_key="temp-key",
            jwt_algorithm="HS256"
        )
        
        admin_data = UserCreate(
            username="admin",
            email="<EMAIL>",
            password="admin123456",  # 生产环境应使用强密码
            full_name="系统管理员",
            role=UserRole.ADMIN,
            api_quota=10000
        )
        
        password_hash = auth_service.password_manager.hash_password(admin_data.password)
        admin_user = await user_repo.create_user(admin_data, password_hash)
        
        self.logger.info("默认管理员用户创建成功", user_id=str(admin_user.id))
    
    async def _create_system_configs(self, session: AsyncSession):
        """创建系统配置"""
        config_repo = SystemConfigRepository(session)
        
        default_configs = [
            {
                "key": "system.version",
                "value": "1.0.0",
                "description": "系统版本号",
                "category": "system",
                "is_public": True
            },
            {
                "key": "system.maintenance_mode",
                "value": False,
                "description": "维护模式开关",
                "category": "system",
                "is_public": False
            },
            {
                "key": "api.default_rate_limit",
                "value": 100,
                "description": "默认API速率限制（每分钟）",
                "category": "api",
                "is_public": False
            },
            {
                "key": "api.max_tokens_limit",
                "value": 4000,
                "description": "最大token数限制",
                "category": "api",
                "is_public": True
            },
            {
                "key": "user.default_quota",
                "value": 1000,
                "description": "新用户默认API配额",
                "category": "user",
                "is_public": False
            },
            {
                "key": "security.jwt_expire_minutes",
                "value": 30,
                "description": "JWT Token过期时间（分钟）",
                "category": "security",
                "is_public": False
            },
            {
                "key": "security.password_min_length",
                "value": 8,
                "description": "密码最小长度",
                "category": "security",
                "is_public": True
            },
            {
                "key": "monitoring.health_check_interval",
                "value": 60,
                "description": "健康检查间隔（秒）",
                "category": "monitoring",
                "is_public": False
            }
        ]
        
        for config_data in default_configs:
            existing = await config_repo.get_config(config_data["key"])
            if not existing:
                await config_repo.set_config(**config_data)
                self.logger.debug("系统配置创建成功", key=config_data["key"])
        
        self.logger.info("系统配置初始化完成")
    
    async def _create_provider_status(self, session: AsyncSession):
        """创建供应商状态记录"""
        providers = [
            {
                "provider_name": "openai",
                "model_name": "gpt-3.5-turbo",
                "priority": 10,
                "max_tokens_limit": 4000,
                "rate_limit": 60
            },
            {
                "provider_name": "openai",
                "model_name": "gpt-4",
                "priority": 20,
                "max_tokens_limit": 8000,
                "rate_limit": 20
            },
            {
                "provider_name": "google_ai",
                "model_name": "gemini-pro",
                "priority": 30,
                "max_tokens_limit": 30720,
                "rate_limit": 60
            },
            {
                "provider_name": "anthropic",
                "model_name": "claude-3-sonnet",
                "priority": 40,
                "max_tokens_limit": 4000,
                "rate_limit": 50
            }
        ]
        
        for provider_data in providers:
            provider_status = ProviderStatus(**provider_data)
            session.add(provider_status)
        
        await session.commit()
        self.logger.info("供应商状态记录初始化完成")
    
    async def migrate_from_memory_to_db(self, auth_service):
        """从内存数据迁移到数据库"""
        self.logger.info("开始从内存迁移数据到数据库")
        
        async with self.db_manager.get_session() as session:
            user_repo = UserRepository(session)
            
            # 迁移用户数据
            migrated_users = 0
            for user_id, user in auth_service._users.items():
                existing = await user_repo.get_user_by_id(user_id)
                if not existing:
                    # 创建用户记录
                    db_user = User(
                        id=user.id,
                        username=user.username,
                        email=user.email,
                        full_name=user.full_name,
                        password_hash=user.password_hash,
                        role=user.role.value,
                        status=user.status.value,
                        permissions=[p.value for p in user.permissions],
                        api_quota=user.api_quota,
                        api_quota_used=user.api_quota_used,
                        created_at=user.created_at,
                        updated_at=user.updated_at,
                        last_login_at=user.last_login_at,
                        metadata_json=user.metadata
                    )
                    session.add(db_user)
                    migrated_users += 1
            
            await session.commit()
            self.logger.info("用户数据迁移完成", migrated_count=migrated_users)
    
    async def backup_database(self, backup_path: str):
        """备份数据库"""
        # 这里可以实现数据库备份逻辑
        # 对于PostgreSQL，可以使用pg_dump
        self.logger.info("数据库备份功能待实现", backup_path=backup_path)
    
    async def restore_database(self, backup_path: str):
        """恢复数据库"""
        # 这里可以实现数据库恢复逻辑
        self.logger.info("数据库恢复功能待实现", backup_path=backup_path)


async def init_database_with_migration(db_manager: DatabaseManager):
    """初始化数据库并运行迁移"""
    migration = DatabaseMigration(db_manager)
    await migration.run_initial_migration()


# 命令行工具函数
async def create_admin_user(db_manager: DatabaseManager, username: str, 
                           email: str, password: str):
    """创建管理员用户（命令行工具）"""
    async with db_manager.get_session() as session:
        user_repo = UserRepository(session)
        
        # 检查用户是否已存在
        existing = await user_repo.get_user_by_username(username)
        if existing:
            print(f"用户 {username} 已存在")
            return
        
        # 创建用户
        auth_service = AuthService(
            jwt_secret_key="temp-key",
            jwt_algorithm="HS256"
        )
        
        admin_data = UserCreate(
            username=username,
            email=email,
            password=password,
            role=UserRole.ADMIN,
            api_quota=10000
        )
        
        password_hash = auth_service.password_manager.hash_password(password)
        admin_user = await user_repo.create_user(admin_data, password_hash)
        
        print(f"管理员用户创建成功: {admin_user.username} ({admin_user.email})")


async def reset_user_password(db_manager: DatabaseManager, username: str, 
                             new_password: str):
    """重置用户密码（命令行工具）"""
    async with db_manager.get_session() as session:
        user_repo = UserRepository(session)
        
        # 查找用户
        user = await user_repo.get_user_by_username(username)
        if not user:
            print(f"用户 {username} 不存在")
            return
        
        # 更新密码
        auth_service = AuthService(
            jwt_secret_key="temp-key",
            jwt_algorithm="HS256"
        )
        
        password_hash = auth_service.password_manager.hash_password(new_password)
        
        from ai_gen_hub.core.models.auth import UserUpdate
        update_data = UserUpdate(password_hash=password_hash)
        
        # 这里需要扩展UserUpdate模型来支持password_hash
        # 或者直接使用SQL更新
        from sqlalchemy import update
        stmt = (
            update(User)
            .where(User.id == user.id)
            .values(password_hash=password_hash, updated_at=datetime.utcnow())
        )
        
        await session.execute(stmt)
        await session.commit()
        
        print(f"用户 {username} 密码重置成功")


if __name__ == "__main__":
    import sys
    import os
    
    # 添加项目路径
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../.."))
    
    from ai_gen_hub.config.settings import DatabaseConfig
    from ai_gen_hub.database.connection import DatabaseManager
    
    async def main():
        # 示例：初始化数据库
        config = DatabaseConfig(
            url="postgresql+asyncpg://user:password@localhost/ai_gen_hub"
        )
        
        db_manager = DatabaseManager(config)
        await init_database_with_migration(db_manager)
        await db_manager.close()
    
    # asyncio.run(main())
