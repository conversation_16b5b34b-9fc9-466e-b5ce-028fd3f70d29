"""
数据访问层

实现Repository模式，提供数据访问接口
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from structlog import get_logger

from ai_gen_hub.database.models import <PERSON>r, APIKey, UserUsage, SystemConfig, ProviderStatus
from ai_gen_hub.core.models.auth import (
    UserCreate, UserUpdate, APIKeyCreate, UserRole, Permission, UserStatus
)


class BaseRepository:
    """基础仓库类"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.logger = get_logger(self.__class__.__name__)


class UserRepository(BaseRepository):
    """用户数据访问层"""
    
    async def create_user(self, user_data: UserCreate, password_hash: str) -> User:
        """创建用户"""
        user = User(
            username=user_data.username,
            email=user_data.email,
            full_name=user_data.full_name,
            password_hash=password_hash,
            role=user_data.role.value,
            api_quota=user_data.api_quota,
            permissions=[p.value for p in user_data.permissions]
        )
        
        self.session.add(user)
        await self.session.commit()
        await self.session.refresh(user)
        
        self.logger.info("用户创建成功", user_id=str(user.id), username=user.username)
        return user
    
    async def get_user_by_id(self, user_id: UUID) -> Optional[User]:
        """根据ID获取用户"""
        stmt = select(User).where(User.id == user_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        stmt = select(User).where(User.username == username)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        stmt = select(User).where(User.email == email)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def update_user(self, user_id: UUID, user_data: UserUpdate) -> Optional[User]:
        """更新用户信息"""
        update_data = user_data.dict(exclude_unset=True)
        
        # 处理权限列表
        if "permissions" in update_data:
            update_data["permissions"] = [p.value for p in update_data["permissions"]]
        
        # 处理角色
        if "role" in update_data:
            update_data["role"] = update_data["role"].value
        
        # 处理状态
        if "status" in update_data:
            update_data["status"] = update_data["status"].value
        
        update_data["updated_at"] = datetime.utcnow()
        
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(**update_data)
            .returning(User)
        )
        
        result = await self.session.execute(stmt)
        await self.session.commit()
        
        updated_user = result.scalar_one_or_none()
        if updated_user:
            self.logger.info("用户信息更新成功", user_id=str(user_id))
        
        return updated_user
    
    async def delete_user(self, user_id: UUID) -> bool:
        """删除用户"""
        stmt = delete(User).where(User.id == user_id)
        result = await self.session.execute(stmt)
        await self.session.commit()
        
        deleted = result.rowcount > 0
        if deleted:
            self.logger.info("用户删除成功", user_id=str(user_id))
        
        return deleted
    
    async def list_users(self, skip: int = 0, limit: int = 100, 
                        role: Optional[UserRole] = None,
                        status: Optional[UserStatus] = None) -> List[User]:
        """获取用户列表"""
        stmt = select(User)
        
        # 添加过滤条件
        if role:
            stmt = stmt.where(User.role == role.value)
        if status:
            stmt = stmt.where(User.status == status.value)
        
        # 添加分页
        stmt = stmt.offset(skip).limit(limit).order_by(User.created_at.desc())
        
        result = await self.session.execute(stmt)
        return result.scalars().all()
    
    async def update_last_login(self, user_id: UUID) -> bool:
        """更新最后登录时间"""
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(last_login_at=datetime.utcnow())
        )
        
        result = await self.session.execute(stmt)
        await self.session.commit()
        
        return result.rowcount > 0
    
    async def update_api_quota_used(self, user_id: UUID, increment: int = 1) -> bool:
        """更新API配额使用量"""
        stmt = (
            update(User)
            .where(User.id == user_id)
            .values(api_quota_used=User.api_quota_used + increment)
        )
        
        result = await self.session.execute(stmt)
        await self.session.commit()
        
        return result.rowcount > 0
    
    async def get_user_stats(self) -> Dict[str, int]:
        """获取用户统计信息"""
        # 总用户数
        total_stmt = select(func.count(User.id))
        total_result = await self.session.execute(total_stmt)
        total_users = total_result.scalar()
        
        # 活跃用户数
        active_stmt = select(func.count(User.id)).where(User.status == "active")
        active_result = await self.session.execute(active_stmt)
        active_users = active_result.scalar()
        
        # 管理员用户数
        admin_stmt = select(func.count(User.id)).where(User.role == "admin")
        admin_result = await self.session.execute(admin_stmt)
        admin_users = admin_result.scalar()
        
        return {
            "total_users": total_users,
            "active_users": active_users,
            "admin_users": admin_users
        }


class APIKeyRepository(BaseRepository):
    """API密钥数据访问层"""
    
    async def create_api_key(self, user_id: UUID, key_data: APIKeyCreate, 
                           key_hash: str, key_prefix: str) -> APIKey:
        """创建API密钥"""
        expires_at = None
        if key_data.expires_in_days:
            expires_at = datetime.utcnow() + timedelta(days=key_data.expires_in_days)
        
        api_key = APIKey(
            user_id=user_id,
            name=key_data.name,
            key_hash=key_hash,
            key_prefix=key_prefix,
            permissions=[p.value for p in key_data.permissions],
            rate_limit=key_data.rate_limit,
            expires_at=expires_at
        )
        
        self.session.add(api_key)
        await self.session.commit()
        await self.session.refresh(api_key)
        
        self.logger.info("API密钥创建成功", user_id=str(user_id), key_name=key_data.name)
        return api_key
    
    async def get_api_key_by_hash(self, key_hash: str) -> Optional[APIKey]:
        """根据哈希获取API密钥"""
        stmt = (
            select(APIKey)
            .options(selectinload(APIKey.user))
            .where(APIKey.key_hash == key_hash)
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def list_user_api_keys(self, user_id: UUID) -> List[APIKey]:
        """获取用户的API密钥列表"""
        stmt = (
            select(APIKey)
            .where(APIKey.user_id == user_id)
            .order_by(APIKey.created_at.desc())
        )
        result = await self.session.execute(stmt)
        return result.scalars().all()
    
    async def update_last_used(self, api_key_id: UUID) -> bool:
        """更新API密钥最后使用时间"""
        stmt = (
            update(APIKey)
            .where(APIKey.id == api_key_id)
            .values(last_used_at=datetime.utcnow())
        )
        
        result = await self.session.execute(stmt)
        await self.session.commit()
        
        return result.rowcount > 0
    
    async def delete_api_key(self, user_id: UUID, key_id: UUID) -> bool:
        """删除API密钥"""
        stmt = delete(APIKey).where(
            and_(APIKey.id == key_id, APIKey.user_id == user_id)
        )
        result = await self.session.execute(stmt)
        await self.session.commit()
        
        deleted = result.rowcount > 0
        if deleted:
            self.logger.info("API密钥删除成功", user_id=str(user_id), key_id=str(key_id))
        
        return deleted
    
    async def deactivate_expired_keys(self) -> int:
        """停用过期的API密钥"""
        stmt = (
            update(APIKey)
            .where(
                and_(
                    APIKey.expires_at < datetime.utcnow(),
                    APIKey.is_active == True
                )
            )
            .values(is_active=False)
        )
        
        result = await self.session.execute(stmt)
        await self.session.commit()
        
        deactivated_count = result.rowcount
        if deactivated_count > 0:
            self.logger.info("停用过期API密钥", count=deactivated_count)
        
        return deactivated_count


class UsageRepository(BaseRepository):
    """使用量统计数据访问层"""
    
    async def record_usage(self, user_id: UUID, service_type: str, model: str,
                          provider: str, tokens_used: int, cost: float = 0.0,
                          request_id: Optional[str] = None,
                          response_time: Optional[float] = None,
                          status: str = "success",
                          error_message: Optional[str] = None) -> UserUsage:
        """记录使用量"""
        usage = UserUsage(
            user_id=user_id,
            service_type=service_type,
            model=model,
            provider=provider,
            tokens_used=tokens_used,
            cost=cost,
            request_id=request_id,
            response_time=response_time,
            status=status,
            error_message=error_message
        )
        
        self.session.add(usage)
        await self.session.commit()
        await self.session.refresh(usage)
        
        return usage
    
    async def get_user_usage_stats(self, user_id: UUID, 
                                  start_date: Optional[datetime] = None,
                                  end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """获取用户使用量统计"""
        stmt = select(
            func.count(UserUsage.id).label("total_requests"),
            func.sum(UserUsage.tokens_used).label("total_tokens"),
            func.sum(UserUsage.cost).label("total_cost"),
            func.avg(UserUsage.response_time).label("avg_response_time")
        ).where(UserUsage.user_id == user_id)
        
        # 添加时间范围过滤
        if start_date:
            stmt = stmt.where(UserUsage.date >= start_date)
        if end_date:
            stmt = stmt.where(UserUsage.date <= end_date)
        
        result = await self.session.execute(stmt)
        row = result.first()
        
        return {
            "total_requests": row.total_requests or 0,
            "total_tokens": row.total_tokens or 0,
            "total_cost": float(row.total_cost or 0),
            "avg_response_time": float(row.avg_response_time or 0)
        }
    
    async def get_usage_by_service(self, user_id: UUID,
                                  start_date: Optional[datetime] = None,
                                  end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """按服务类型获取使用量统计"""
        stmt = select(
            UserUsage.service_type,
            func.count(UserUsage.id).label("requests"),
            func.sum(UserUsage.tokens_used).label("tokens"),
            func.sum(UserUsage.cost).label("cost")
        ).where(UserUsage.user_id == user_id)
        
        # 添加时间范围过滤
        if start_date:
            stmt = stmt.where(UserUsage.date >= start_date)
        if end_date:
            stmt = stmt.where(UserUsage.date <= end_date)
        
        stmt = stmt.group_by(UserUsage.service_type)
        
        result = await self.session.execute(stmt)
        
        return [
            {
                "service_type": row.service_type,
                "requests": row.requests,
                "tokens": row.tokens or 0,
                "cost": float(row.cost or 0)
            }
            for row in result
        ]


class SystemConfigRepository(BaseRepository):
    """系统配置数据访问层"""
    
    async def get_config(self, key: str) -> Optional[SystemConfig]:
        """获取配置项"""
        stmt = select(SystemConfig).where(SystemConfig.key == key)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def set_config(self, key: str, value: Any, description: Optional[str] = None,
                        category: str = "general", is_public: bool = False,
                        user_id: Optional[UUID] = None) -> SystemConfig:
        """设置配置项"""
        # 检查是否已存在
        existing = await self.get_config(key)
        
        if existing:
            # 更新现有配置
            stmt = (
                update(SystemConfig)
                .where(SystemConfig.key == key)
                .values(
                    value=value,
                    description=description or existing.description,
                    updated_at=datetime.utcnow(),
                    updated_by=user_id
                )
                .returning(SystemConfig)
            )
            result = await self.session.execute(stmt)
            config = result.scalar_one()
        else:
            # 创建新配置
            config = SystemConfig(
                key=key,
                value=value,
                description=description,
                category=category,
                is_public=is_public,
                created_by=user_id,
                updated_by=user_id
            )
            self.session.add(config)
        
        await self.session.commit()
        await self.session.refresh(config)
        
        return config
    
    async def list_configs(self, category: Optional[str] = None,
                          is_public: Optional[bool] = None) -> List[SystemConfig]:
        """获取配置列表"""
        stmt = select(SystemConfig)
        
        if category:
            stmt = stmt.where(SystemConfig.category == category)
        if is_public is not None:
            stmt = stmt.where(SystemConfig.is_public == is_public)
        
        stmt = stmt.order_by(SystemConfig.category, SystemConfig.key)
        
        result = await self.session.execute(stmt)
        return result.scalars().all()
    
    async def delete_config(self, key: str) -> bool:
        """删除配置项"""
        stmt = delete(SystemConfig).where(SystemConfig.key == key)
        result = await self.session.execute(stmt)
        await self.session.commit()
        
        return result.rowcount > 0
