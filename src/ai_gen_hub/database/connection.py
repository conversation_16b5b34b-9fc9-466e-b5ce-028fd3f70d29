"""
数据库连接管理

提供数据库连接池、会话管理等功能
"""

import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional

from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import (
    AsyncSession, AsyncEngine, create_async_engine, async_sessionmaker
)
from sqlalchemy.orm import sessionmaker
from structlog import get_logger

from ai_gen_hub.config.settings import DatabaseConfig
from ai_gen_hub.database.models import Base


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: DatabaseConfig):
        self.logger = get_logger(__name__)
        self.config = config
        self._engine: Optional[AsyncEngine] = None
        self._session_factory: Optional[async_sessionmaker] = None
        self._initialized = False
    
    async def initialize(self):
        """初始化数据库连接"""
        if self._initialized:
            return
        
        try:
            # 创建异步引擎
            self._engine = create_async_engine(
                self.config.url,
                pool_size=self.config.pool_size,
                max_overflow=self.config.max_overflow,
                pool_timeout=self.config.pool_timeout,
                echo=self.config.echo,
                future=True
            )
            
            # 创建会话工厂
            self._session_factory = async_sessionmaker(
                bind=self._engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # 测试连接
            await self._test_connection()
            
            self._initialized = True
            self.logger.info("数据库连接初始化成功", url=self._mask_url(self.config.url))
            
        except Exception as e:
            self.logger.error("数据库连接初始化失败", error=str(e))
            raise
    
    async def _test_connection(self):
        """测试数据库连接"""
        async with self._engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            assert result.scalar() == 1
    
    def _mask_url(self, url: str) -> str:
        """屏蔽URL中的敏感信息"""
        if "@" in url:
            parts = url.split("@")
            if len(parts) == 2:
                # 屏蔽用户名和密码
                protocol_user = parts[0].split("://")
                if len(protocol_user) == 2:
                    protocol = protocol_user[0]
                    return f"{protocol}://***:***@{parts[1]}"
        return url
    
    async def create_tables(self):
        """创建数据库表"""
        if not self._initialized:
            await self.initialize()
        
        try:
            async with self._engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            self.logger.info("数据库表创建成功")
        except Exception as e:
            self.logger.error("数据库表创建失败", error=str(e))
            raise
    
    async def drop_tables(self):
        """删除数据库表"""
        if not self._initialized:
            await self.initialize()
        
        try:
            async with self._engine.begin() as conn:
                await conn.run_sync(Base.metadata.drop_all)
            self.logger.info("数据库表删除成功")
        except Exception as e:
            self.logger.error("数据库表删除失败", error=str(e))
            raise
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取数据库会话"""
        if not self._initialized:
            await self.initialize()
        
        async with self._session_factory() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def close(self):
        """关闭数据库连接"""
        if self._engine:
            await self._engine.dispose()
            self._engine = None
            self._session_factory = None
            self._initialized = False
            self.logger.info("数据库连接已关闭")
    
    @property
    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self._initialized
    
    @property
    def engine(self) -> AsyncEngine:
        """获取数据库引擎"""
        if not self._engine:
            raise RuntimeError("数据库未初始化")
        return self._engine


# 全局数据库管理器实例
_database_manager: Optional[DatabaseManager] = None


def get_database_manager() -> Optional[DatabaseManager]:
    """获取数据库管理器实例"""
    return _database_manager


def set_database_manager(manager: DatabaseManager):
    """设置数据库管理器实例"""
    global _database_manager
    _database_manager = manager


async def init_database(config: DatabaseConfig) -> DatabaseManager:
    """初始化数据库"""
    manager = DatabaseManager(config)
    await manager.initialize()
    await manager.create_tables()
    set_database_manager(manager)
    return manager


async def close_database():
    """关闭数据库连接"""
    global _database_manager
    if _database_manager:
        await _database_manager.close()
        _database_manager = None


# FastAPI依赖注入
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话（FastAPI依赖）"""
    manager = get_database_manager()
    if not manager:
        raise RuntimeError("数据库未初始化")
    
    async with manager.get_session() as session:
        yield session


# 数据库健康检查
async def check_database_health() -> dict:
    """检查数据库健康状态"""
    manager = get_database_manager()
    if not manager:
        return {
            "status": "unhealthy",
            "message": "数据库未初始化"
        }
    
    try:
        async with manager.get_session() as session:
            result = await session.execute(text("SELECT 1"))
            assert result.scalar() == 1
        
        return {
            "status": "healthy",
            "message": "数据库连接正常",
            "initialized": manager.is_initialized
        }
    except Exception as e:
        return {
            "status": "unhealthy", 
            "message": f"数据库连接失败: {str(e)}"
        }
