"""
AI Gen Hub 统一Chat管理器

本模块实现统一的Chat管理器，负责管理多个AI供应商，提供统一的Chat服务入口。

主要功能：
- 供应商注册和管理
- 智能供应商选择和负载均衡
- 故障转移和重试机制
- 健康检查和状态监控
- 统一的错误处理和日志记录

设计特性：
- 高可用性：支持多供应商故障转移
- 负载均衡：根据供应商状态和负载分配请求
- 智能路由：根据模型类型和用户偏好选择最佳供应商
- 监控告警：实时监控供应商健康状态

作者：AI Gen Hub Team
创建时间：2025-08-24
"""

import asyncio
import logging as std_logging
import random
from typing import Any, AsyncIterator, Dict, List, Optional, Set
from datetime import datetime, timedelta
from collections import defaultdict

from ai_gen_hub.core.chat_interfaces import (
    ChatManager,
    ChatProvider,
    ChatMessage,
    ChatConfig,
    ChatResponse,
    ChatStreamChunk,
    Chat<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>rovider<PERSON>rror,
    ChatValidationError,
)

# 设置日志记录器
logger = std_logging.getLogger(__name__)


class ProviderStats:
    """供应商统计信息"""
    
    def __init__(self):
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.total_response_time = 0.0
        self.last_request_time: Optional[datetime] = None
        self.last_error_time: Optional[datetime] = None
        self.last_error_message: Optional[str] = None
        self.consecutive_failures = 0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests
    
    @property
    def average_response_time(self) -> float:
        """平均响应时间（秒）"""
        if self.successful_requests == 0:
            return 0.0
        return self.total_response_time / self.successful_requests
    
    def record_success(self, response_time: float):
        """记录成功请求"""
        self.total_requests += 1
        self.successful_requests += 1
        self.total_response_time += response_time
        self.last_request_time = datetime.now()
        self.consecutive_failures = 0
    
    def record_failure(self, error_message: str):
        """记录失败请求"""
        self.total_requests += 1
        self.failed_requests += 1
        self.last_request_time = datetime.now()
        self.last_error_time = datetime.now()
        self.last_error_message = error_message
        self.consecutive_failures += 1


class UnifiedChatManager(ChatManager):
    """统一Chat管理器实现
    
    管理多个Chat供应商，提供统一的服务入口。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化Chat管理器
        
        Args:
            config: 管理器配置，包含以下字段：
                - default_provider: 默认供应商名称（可选）
                - max_retries: 最大重试次数（默认3）
                - retry_delay: 重试延迟秒数（默认1.0）
                - health_check_interval: 健康检查间隔秒数（默认60）
                - failure_threshold: 故障阈值（连续失败次数，默认3）
                - recovery_time: 恢复时间秒数（默认300）
        """
        self.config = config
        self.default_provider = config.get("default_provider")
        self.max_retries = config.get("max_retries", 3)
        self.retry_delay = config.get("retry_delay", 1.0)
        self.health_check_interval = config.get("health_check_interval", 60)
        self.failure_threshold = config.get("failure_threshold", 3)
        self.recovery_time = config.get("recovery_time", 300)
        
        # 供应商管理
        self._providers: Dict[str, ChatProvider] = {}
        self._provider_stats: Dict[str, ProviderStats] = {}
        self._disabled_providers: Set[str] = set()
        self._last_health_check: Optional[datetime] = None
        
        # 模型到供应商的映射
        self._model_provider_mapping: Dict[str, str] = {}
        
        # 负载均衡权重
        self._provider_weights: Dict[str, float] = {}
        
        self.logger = std_logging.getLogger(f"{__name__}.UnifiedChatManager")
        self.logger.info("统一Chat管理器初始化完成")
    
    def register_provider(self, provider: ChatProvider, weight: float = 1.0):
        """注册Chat供应商
        
        Args:
            provider: Chat供应商实例
            weight: 负载均衡权重（默认1.0）
        """
        provider_name = provider.provider_name
        self._providers[provider_name] = provider
        self._provider_stats[provider_name] = ProviderStats()
        self._provider_weights[provider_name] = weight
        
        self.logger.info(f"注册Chat供应商: {provider_name}，权重: {weight}")
    
    def unregister_provider(self, provider_name: str):
        """注销Chat供应商
        
        Args:
            provider_name: 供应商名称
        """
        if provider_name in self._providers:
            del self._providers[provider_name]
            del self._provider_stats[provider_name]
            del self._provider_weights[provider_name]
            self._disabled_providers.discard(provider_name)
            
            self.logger.info(f"注销Chat供应商: {provider_name}")
    
    def set_model_provider_mapping(self, model_mappings: Dict[str, str]):
        """设置模型到供应商的映射
        
        Args:
            model_mappings: 模型名称到供应商名称的映射字典
        """
        self._model_provider_mapping.update(model_mappings)
        self.logger.info(f"更新模型供应商映射: {len(model_mappings)}个映射")
    
    async def _check_provider_health(self, provider_name: str) -> bool:
        """检查供应商健康状态
        
        Args:
            provider_name: 供应商名称
            
        Returns:
            bool: 是否健康
        """
        if provider_name not in self._providers:
            return False
        
        try:
            provider = self._providers[provider_name]
            is_healthy = await provider.health_check()
            
            if is_healthy:
                # 如果供应商恢复健康，从禁用列表中移除
                if provider_name in self._disabled_providers:
                    self._disabled_providers.remove(provider_name)
                    self.logger.info(f"供应商 {provider_name} 已恢复健康")
            
            return is_healthy
            
        except Exception as e:
            self.logger.warning(f"供应商 {provider_name} 健康检查失败: {e}")
            return False
    
    async def _perform_health_checks(self):
        """执行所有供应商的健康检查"""
        if not self._providers:
            return
        
        self.logger.debug("开始执行供应商健康检查")
        
        # 并发检查所有供应商
        tasks = []
        for provider_name in self._providers.keys():
            task = self._check_provider_health(provider_name)
            tasks.append((provider_name, task))
        
        results = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
        
        # 处理检查结果
        for (provider_name, _), result in zip(tasks, results):
            if isinstance(result, Exception):
                self.logger.warning(f"供应商 {provider_name} 健康检查异常: {result}")
                continue
            
            if not result:
                # 供应商不健康，检查是否需要禁用
                stats = self._provider_stats[provider_name]
                if stats.consecutive_failures >= self.failure_threshold:
                    if provider_name not in self._disabled_providers:
                        self._disabled_providers.add(provider_name)
                        self.logger.warning(f"供应商 {provider_name} 连续失败 {stats.consecutive_failures} 次，已禁用")
        
        self._last_health_check = datetime.now()
        self.logger.debug("供应商健康检查完成")
    
    def _get_available_providers(self) -> List[str]:
        """获取可用的供应商列表
        
        Returns:
            List[str]: 可用供应商名称列表
        """
        available = []
        current_time = datetime.now()
        
        for provider_name in self._providers.keys():
            # 跳过禁用的供应商
            if provider_name in self._disabled_providers:
                # 检查是否已过恢复时间
                stats = self._provider_stats[provider_name]
                if (stats.last_error_time and 
                    current_time - stats.last_error_time > timedelta(seconds=self.recovery_time)):
                    # 尝试恢复
                    self._disabled_providers.remove(provider_name)
                    self.logger.info(f"供应商 {provider_name} 恢复时间已到，重新启用")
                    available.append(provider_name)
                continue
            
            available.append(provider_name)
        
        return available
    
    def _select_provider(self, model: Optional[str] = None, preferred_provider: Optional[str] = None) -> str:
        """选择最佳供应商
        
        Args:
            model: 模型名称（可选）
            preferred_provider: 首选供应商（可选）
            
        Returns:
            str: 选择的供应商名称
            
        Raises:
            ChatProviderError: 没有可用的供应商
        """
        available_providers = self._get_available_providers()
        
        if not available_providers:
            raise ChatProviderError("没有可用的Chat供应商")
        
        # 如果指定了首选供应商且可用，直接使用
        if preferred_provider and preferred_provider in available_providers:
            return preferred_provider
        
        # 如果指定了模型，查找对应的供应商
        if model and model in self._model_provider_mapping:
            mapped_provider = self._model_provider_mapping[model]
            if mapped_provider in available_providers:
                return mapped_provider
        
        # 如果有默认供应商且可用，使用默认供应商
        if self.default_provider and self.default_provider in available_providers:
            return self.default_provider
        
        # 基于权重和性能指标选择供应商
        weighted_providers = []
        for provider_name in available_providers:
            stats = self._provider_stats[provider_name]
            base_weight = self._provider_weights.get(provider_name, 1.0)
            
            # 根据成功率调整权重
            success_rate_factor = stats.success_rate
            
            # 根据响应时间调整权重（响应时间越短权重越高）
            response_time_factor = 1.0
            if stats.average_response_time > 0:
                response_time_factor = 1.0 / (1.0 + stats.average_response_time)
            
            # 计算最终权重
            final_weight = base_weight * success_rate_factor * response_time_factor
            weighted_providers.append((provider_name, final_weight))
        
        # 按权重随机选择
        if weighted_providers:
            total_weight = sum(weight for _, weight in weighted_providers)
            if total_weight > 0:
                rand_val = random.uniform(0, total_weight)
                current_weight = 0
                for provider_name, weight in weighted_providers:
                    current_weight += weight
                    if rand_val <= current_weight:
                        return provider_name
        
        # 如果权重计算失败，随机选择一个可用供应商
        return random.choice(available_providers)
    
    async def _execute_with_retry(self, operation, *args, **kwargs):
        """带重试的操作执行
        
        Args:
            operation: 要执行的操作函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            操作结果
            
        Raises:
            ChatError: 所有重试都失败后抛出最后一个错误
        """
        last_error = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return await operation(*args, **kwargs)
            except ChatError as e:
                last_error = e
                
                # 记录失败统计
                if hasattr(e, 'provider') and e.provider:
                    if e.provider in self._provider_stats:
                        self._provider_stats[e.provider].record_failure(str(e))
                
                # 如果是最后一次尝试，直接抛出异常
                if attempt == self.max_retries:
                    break
                
                # 等待后重试
                if self.retry_delay > 0:
                    await asyncio.sleep(self.retry_delay)
                
                self.logger.warning(f"操作失败，第 {attempt + 1} 次重试: {e}")
        
        # 所有重试都失败，抛出最后一个错误
        if last_error:
            raise last_error
        else:
            raise ChatProviderError("操作执行失败，原因未知")
    
    async def chat(
        self,
        messages: List[ChatMessage],
        config: ChatConfig,
        provider: Optional[str] = None
    ) -> ChatResponse:
        """发送Chat请求
        
        Args:
            messages: 对话消息列表
            config: Chat配置参数
            provider: 指定供应商（可选，不指定则自动选择）
            
        Returns:
            ChatResponse: Chat响应
            
        Raises:
            ChatError: Chat相关错误
        """
        # 定期执行健康检查
        if (self._last_health_check is None or 
            datetime.now() - self._last_health_check > timedelta(seconds=self.health_check_interval)):
            await self._perform_health_checks()
        
        # 提取模型信息
        model = config.provider_params.get("model") if config.provider_params else None
        
        async def _chat_operation():
            # 选择供应商
            selected_provider = self._select_provider(model, provider)
            provider_instance = self._providers[selected_provider]
            
            # 记录开始时间
            start_time = datetime.now()
            
            try:
                # 执行Chat请求
                response = await provider_instance.chat(messages, config)
                
                # 记录成功统计
                response_time = (datetime.now() - start_time).total_seconds()
                self._provider_stats[selected_provider].record_success(response_time)
                
                self.logger.info(f"Chat请求成功，供应商: {selected_provider}，响应时间: {response_time:.2f}秒")
                return response
                
            except ChatError as e:
                # 为异常添加供应商信息
                e.provider = selected_provider
                raise e
        
        return await self._execute_with_retry(_chat_operation)

    async def chat_stream(
        self,
        messages: List[ChatMessage],
        config: ChatConfig,
        provider: Optional[str] = None
    ) -> AsyncIterator[ChatStreamChunk]:
        """发送Chat流式请求

        Args:
            messages: 对话消息列表
            config: Chat配置参数
            provider: 指定供应商（可选，不指定则自动选择）

        Yields:
            ChatStreamChunk: Chat流式响应块

        Raises:
            ChatError: Chat相关错误
        """
        # 定期执行健康检查
        if (self._last_health_check is None or
            datetime.now() - self._last_health_check > timedelta(seconds=self.health_check_interval)):
            await self._perform_health_checks()

        # 提取模型信息
        model = config.provider_params.get("model") if config.provider_params else None

        # 选择供应商
        selected_provider = self._select_provider(model, provider)
        provider_instance = self._providers[selected_provider]

        # 记录开始时间
        start_time = datetime.now()

        try:
            self.logger.info(f"开始Chat流式请求，供应商: {selected_provider}")

            # 执行流式Chat请求
            async for chunk in provider_instance.chat_stream(messages, config):
                yield chunk

            # 记录成功统计
            response_time = (datetime.now() - start_time).total_seconds()
            self._provider_stats[selected_provider].record_success(response_time)

            self.logger.info(f"Chat流式请求完成，供应商: {selected_provider}，总时间: {response_time:.2f}秒")

        except ChatError as e:
            # 记录失败统计
            self._provider_stats[selected_provider].record_failure(str(e))

            # 为异常添加供应商信息
            e.provider = selected_provider

            self.logger.error(f"Chat流式请求失败，供应商: {selected_provider}，错误: {e}")
            raise e
        except Exception as e:
            # 记录失败统计
            error_msg = f"未知错误: {str(e)}"
            self._provider_stats[selected_provider].record_failure(error_msg)

            self.logger.error(f"Chat流式请求异常，供应商: {selected_provider}，错误: {e}")
            raise ChatProviderError(error_msg, provider=selected_provider)

    async def get_available_providers(self) -> List[str]:
        """获取可用的供应商列表

        Returns:
            List[str]: 可用供应商名称列表
        """
        return self._get_available_providers()

    def get_provider_stats(self, provider_name: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """获取供应商统计信息

        Args:
            provider_name: 供应商名称（可选，不指定则返回所有供应商统计）

        Returns:
            Dict[str, Dict[str, Any]]: 供应商统计信息字典
        """
        if provider_name:
            if provider_name not in self._provider_stats:
                return {}

            stats = self._provider_stats[provider_name]
            return {
                provider_name: {
                    "total_requests": stats.total_requests,
                    "successful_requests": stats.successful_requests,
                    "failed_requests": stats.failed_requests,
                    "success_rate": stats.success_rate,
                    "average_response_time": stats.average_response_time,
                    "consecutive_failures": stats.consecutive_failures,
                    "last_request_time": stats.last_request_time.isoformat() if stats.last_request_time else None,
                    "last_error_time": stats.last_error_time.isoformat() if stats.last_error_time else None,
                    "last_error_message": stats.last_error_message,
                    "is_disabled": provider_name in self._disabled_providers,
                    "weight": self._provider_weights.get(provider_name, 1.0)
                }
            }

        # 返回所有供应商统计
        result = {}
        for name in self._provider_stats.keys():
            result.update(self.get_provider_stats(name))

        return result

    def reset_provider_stats(self, provider_name: Optional[str] = None):
        """重置供应商统计信息

        Args:
            provider_name: 供应商名称（可选，不指定则重置所有供应商统计）
        """
        if provider_name:
            if provider_name in self._provider_stats:
                self._provider_stats[provider_name] = ProviderStats()
                self._disabled_providers.discard(provider_name)
                self.logger.info(f"重置供应商 {provider_name} 的统计信息")
        else:
            for name in self._provider_stats.keys():
                self._provider_stats[name] = ProviderStats()
            self._disabled_providers.clear()
            self.logger.info("重置所有供应商的统计信息")

    async def validate_config(self, config: ChatConfig, provider_name: Optional[str] = None) -> bool:
        """验证配置参数是否有效

        Args:
            config: Chat配置参数
            provider_name: 供应商名称（可选）

        Returns:
            bool: 配置是否有效
        """
        try:
            if provider_name:
                if provider_name not in self._providers:
                    return False
                return await self._providers[provider_name].validate_config(config)
            else:
                # 选择一个可用供应商进行验证
                available_providers = self._get_available_providers()
                if not available_providers:
                    return False

                selected_provider = available_providers[0]
                return await self._providers[selected_provider].validate_config(config)
        except Exception as e:
            self.logger.warning(f"配置验证失败: {e}")
            return False

    async def get_supported_models(self, provider_name: Optional[str] = None) -> Dict[str, List[str]]:
        """获取支持的模型列表

        Args:
            provider_name: 供应商名称（可选，不指定则返回所有供应商的模型）

        Returns:
            Dict[str, List[str]]: 供应商名称到模型列表的映射
        """
        if provider_name:
            if provider_name not in self._providers:
                return {}

            try:
                models = await self._providers[provider_name].get_supported_models()
                return {provider_name: models}
            except Exception as e:
                self.logger.warning(f"获取供应商 {provider_name} 支持的模型失败: {e}")
                return {}

        # 返回所有供应商的模型
        result = {}
        for name, provider in self._providers.items():
            try:
                models = await provider.get_supported_models()
                result[name] = models
            except Exception as e:
                self.logger.warning(f"获取供应商 {name} 支持的模型失败: {e}")
                result[name] = []

        return result
