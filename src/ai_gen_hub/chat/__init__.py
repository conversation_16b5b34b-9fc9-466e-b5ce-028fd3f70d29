"""
AI Gen Hub 统一Chat接口模块

本模块提供统一的Chat接口，支持多个AI供应商的对话功能。

主要组件：
- 统一的Chat接口定义和数据模型
- 各供应商的Chat适配器实现
- 统一的Chat管理器
- 错误处理和异常定义

支持的供应商：
- OpenAI (GPT系列模型)
- Google AI (Gemini系列模型)
- Anthropic (Claude系列模型)

使用示例：
```python
from ai_gen_hub.chat import (
    UnifiedChatManager,
    OpenAIChatProvider,
    GoogleAIChatProvider,
    AnthropicChatProvider,
    ChatMessage,
    ChatConfig,
    ChatMessageRole
)

# 创建管理器
manager = UnifiedChatManager({})

# 注册供应商
openai_provider = OpenAIChatProvider({"api_key": "your-openai-key"})
manager.register_provider(openai_provider)

# 发送Chat请求
messages = [
    ChatMessage(role=ChatMessageRole.USER, content="你好！")
]
config = ChatConfig(temperature=0.7, max_tokens=1000)

response = await manager.chat(messages, config)
print(response.message.content)
```

作者：AI Gen Hub Team
创建时间：2025-08-24
"""

# 导入核心接口和数据模型
from ai_gen_hub.core.chat_interfaces import (
    # 数据模型
    ChatMessage,
    ChatConfig,
    ChatResponse,
    ChatStreamChunk,
    ChatUsage,
    
    # 枚举
    ChatMessageRole,
    ChatFinishReason,
    ChatStreamEventType,
    
    # 异常
    ChatError,
    ChatValidationError,
    ChatProviderError,
    ChatRateLimitError,
    ChatContentFilterError,
    
    # 接口
    ChatProvider,
    ChatManager,
)

# 导入基础实现
from ai_gen_hub.core.chat_base import (
    BaseChatProvider,
    StreamAccumulator,
)

# 导入供应商适配器
from ai_gen_hub.chat.providers.openai_chat import OpenAIChatProvider
from ai_gen_hub.chat.providers.google_ai_chat import GoogleAIChatProvider
from ai_gen_hub.chat.providers.anthropic_chat import AnthropicChatProvider

# 导入管理器
from ai_gen_hub.chat.chat_manager import UnifiedChatManager, ProviderStats

# 定义公开的API
__all__ = [
    # 数据模型
    "ChatMessage",
    "ChatConfig", 
    "ChatResponse",
    "ChatStreamChunk",
    "ChatUsage",
    
    # 枚举
    "ChatMessageRole",
    "ChatFinishReason", 
    "ChatStreamEventType",
    
    # 异常
    "ChatError",
    "ChatValidationError",
    "ChatProviderError",
    "ChatRateLimitError",
    "ChatContentFilterError",
    
    # 接口
    "ChatProvider",
    "ChatManager",
    
    # 基础实现
    "BaseChatProvider",
    "StreamAccumulator",
    
    # 供应商适配器
    "OpenAIChatProvider",
    "GoogleAIChatProvider", 
    "AnthropicChatProvider",
    
    # 管理器
    "UnifiedChatManager",
    "ProviderStats",
]

# 版本信息
__version__ = "1.0.0"
__author__ = "AI Gen Hub Team"
__description__ = "统一的AI Chat接口，支持多个供应商"

# 便捷函数
def create_chat_manager(config: dict = None) -> UnifiedChatManager:
    """创建统一Chat管理器的便捷函数
    
    Args:
        config: 管理器配置（可选）
        
    Returns:
        UnifiedChatManager: 统一Chat管理器实例
    """
    if config is None:
        config = {}
    
    return UnifiedChatManager(config)


def create_openai_provider(api_key: str, **kwargs) -> OpenAIChatProvider:
    """创建OpenAI Chat供应商的便捷函数
    
    Args:
        api_key: OpenAI API密钥
        **kwargs: 其他配置参数
        
    Returns:
        OpenAIChatProvider: OpenAI Chat供应商实例
    """
    config = {"api_key": api_key}
    config.update(kwargs)
    
    return OpenAIChatProvider(config)


def create_google_ai_provider(api_key: str, **kwargs) -> GoogleAIChatProvider:
    """创建Google AI Chat供应商的便捷函数
    
    Args:
        api_key: Google AI API密钥
        **kwargs: 其他配置参数
        
    Returns:
        GoogleAIChatProvider: Google AI Chat供应商实例
    """
    config = {"api_key": api_key}
    config.update(kwargs)
    
    return GoogleAIChatProvider(config)


def create_anthropic_provider(api_key: str, **kwargs) -> AnthropicChatProvider:
    """创建Anthropic Chat供应商的便捷函数
    
    Args:
        api_key: Anthropic API密钥
        **kwargs: 其他配置参数
        
    Returns:
        AnthropicChatProvider: Anthropic Chat供应商实例
    """
    config = {"api_key": api_key}
    config.update(kwargs)
    
    return AnthropicChatProvider(config)


# 添加便捷函数到__all__
__all__.extend([
    "create_chat_manager",
    "create_openai_provider", 
    "create_google_ai_provider",
    "create_anthropic_provider",
])
