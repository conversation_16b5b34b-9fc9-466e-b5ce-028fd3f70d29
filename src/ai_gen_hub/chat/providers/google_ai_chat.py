"""
Google AI Chat适配器

实现Google AI Gemini API的统一接口适配，支持Gemini系列模型的对话功能。

主要功能：
- 支持Gemini 2.5、2.0、1.5系列模型
- 完整的流式和非流式响应支持
- 多模态输入支持（文本、图像等）
- 系统指令和思考配置支持
- 安全设置和内容过滤

API兼容性：
- 兼容Google AI Gemini API v1beta
- 支持最新的Gemini 2.5 Pro/Flash模型
- 支持系统指令和多轮对话
- 支持工具调用和函数调用

特殊处理：
- 消息格式转换（OpenAI格式 -> Gemini contents格式）
- 参数名称映射（temperature -> generationConfig.temperature）
- 系统消息处理（role=system -> systemInstruction）
- 流式响应格式转换

作者：AI Gen Hub Team
创建时间：2025-08-24
"""

import json
import logging as std_logging
from typing import Any, AsyncIterator, Dict, List, Optional
from uuid import uuid4

import httpx

from ai_gen_hub.core.chat_interfaces import (
    ChatMessage,
    ChatConfig,
    ChatResponse,
    ChatStreamChunk,
    ChatUsage,
    ChatMessageRole,
    ChatFinishReason,
    ChatStreamEventType,
    ChatError,
    ChatValidationError,
    ChatProviderError,
)
from ai_gen_hub.core.chat_base import BaseChatProvider, StreamAccumulator

# 设置日志记录器
logger = std_logging.getLogger(__name__)


class GoogleAIChatProvider(BaseChatProvider):
    """Google AI Chat供应商适配器
    
    实现Google AI Gemini API的统一接口适配。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化Google AI Chat适配器
        
        Args:
            config: 配置字典，包含以下字段：
                - api_key: Google AI API密钥
                - base_url: API基础URL（可选，默认为官方URL）
                - timeout: 请求超时时间（可选，默认30秒）
                - max_retries: 最大重试次数（可选，默认3次）
        """
        # 设置默认配置
        config.setdefault("base_url", "https://generativelanguage.googleapis.com/v1beta")
        config.setdefault("timeout", 30.0)
        config.setdefault("max_retries", 3)
        
        super().__init__("google_ai", config)
        
        # 支持的模型列表
        self._supported_models = [
            "gemini-2.5-pro",
            "gemini-2.5-flash",
            "gemini-2.5-flash-lite",
            "gemini-2.0-flash",
            "gemini-1.5-pro",
            "gemini-1.5-flash",
            "gemini-pro",
            "gemini-pro-vision",
        ]
        
        # 参数映射（Google AI使用不同的参数结构）
        self._parameter_mapping = {
            "max_tokens": "maxOutputTokens",
            "stop_sequences": "stopSequences",
        }
        
        self.logger.info("Google AI Chat适配器初始化完成")
    
    def _get_headers(self) -> Dict[str, str]:
        """获取Google AI API请求头
        
        Returns:
            Dict[str, str]: 请求头字典
        """
        return {
            "Content-Type": "application/json",
            "x-goog-api-key": self.api_key,
            "User-Agent": "AI-Gen-Hub/Google-AI-Chat"
        }
    
    def _extract_system_instruction(self, messages: List[ChatMessage]) -> tuple[Optional[str], List[ChatMessage]]:
        """提取系统指令并返回剩余消息
        
        Args:
            messages: 原始消息列表
            
        Returns:
            tuple: (系统指令内容, 剩余消息列表)
        """
        system_instruction = None
        remaining_messages = []
        
        for message in messages:
            if message.role == ChatMessageRole.SYSTEM:
                # Google AI只支持一个系统指令，如果有多个则合并
                if system_instruction is None:
                    system_instruction = message.content
                else:
                    system_instruction += "\n\n" + message.content
            else:
                remaining_messages.append(message)
        
        return system_instruction, remaining_messages
    
    def _map_messages(self, messages: List[ChatMessage]) -> List[Dict[str, Any]]:
        """映射消息格式到Google AI格式
        
        Args:
            messages: 统一消息列表
            
        Returns:
            List[Dict[str, Any]]: Google AI格式的contents列表
        """
        contents = []
        
        for message in messages:
            # 映射角色
            role_mapping = {
                ChatMessageRole.USER: "user",
                ChatMessageRole.ASSISTANT: "model",  # Google AI使用"model"而不是"assistant"
                ChatMessageRole.FUNCTION: "function",
                ChatMessageRole.TOOL: "function",
            }
            
            role = role_mapping.get(message.role, "user")
            
            # 构建parts结构
            parts = [{"text": message.content}]
            
            # 处理工具调用（如果有）
            if message.tool_calls:
                for tool_call in message.tool_calls:
                    parts.append({
                        "functionCall": {
                            "name": tool_call.get("function", {}).get("name", ""),
                            "args": json.loads(tool_call.get("function", {}).get("arguments", "{}"))
                        }
                    })
            
            content = {
                "role": role,
                "parts": parts
            }
            
            contents.append(content)
        
        return contents
    
    def _map_parameters(self, config: ChatConfig) -> Dict[str, Any]:
        """映射配置参数到Google AI格式
        
        Args:
            config: 统一配置参数
            
        Returns:
            Dict[str, Any]: Google AI格式的参数字典
        """
        # Google AI使用generationConfig结构
        generation_config = {}
        
        # 基础参数
        if config.max_tokens is not None:
            generation_config["maxOutputTokens"] = config.max_tokens
        
        generation_config["temperature"] = config.temperature
        
        if config.top_p is not None:
            generation_config["topP"] = config.top_p
        
        if config.top_k is not None:
            generation_config["topK"] = config.top_k
        
        # 停止序列
        if config.stop_sequences:
            generation_config["stopSequences"] = config.stop_sequences
        
        params = {
            "generationConfig": generation_config
        }
        
        # 安全设置
        if config.safety_settings:
            params["safetySettings"] = config.safety_settings
        
        # 工具配置
        if config.tools:
            # 转换工具格式
            tools = []
            for tool in config.tools:
                if tool.get("type") == "function":
                    tools.append({
                        "functionDeclarations": [tool["function"]]
                    })
            if tools:
                params["tools"] = tools
        
        # 添加供应商特定参数
        if config.provider_params:
            params.update(config.provider_params)
        
        return params
    
    def _parse_response(self, response_data: Dict[str, Any], model: str) -> ChatResponse:
        """解析Google AI响应为统一格式
        
        Args:
            response_data: Google AI原始响应数据
            model: 使用的模型名称
            
        Returns:
            ChatResponse: 统一格式的响应
        """
        candidates = response_data.get("candidates", [])
        if not candidates:
            raise ChatProviderError("响应中没有候选结果", provider=self.provider_name)
        
        candidate = candidates[0]
        content = candidate.get("content", {})
        parts = content.get("parts", [])
        
        # 提取文本内容
        text_content = ""
        tool_calls = []
        
        for part in parts:
            if "text" in part:
                text_content += part["text"]
            elif "functionCall" in part:
                # 转换为OpenAI格式的工具调用
                func_call = part["functionCall"]
                tool_calls.append({
                    "id": str(uuid4()),
                    "type": "function",
                    "function": {
                        "name": func_call.get("name", ""),
                        "arguments": json.dumps(func_call.get("args", {}))
                    }
                })
        
        # 构建响应消息
        response_message = ChatMessage(
            role=ChatMessageRole.ASSISTANT,
            content=text_content,
            tool_calls=tool_calls if tool_calls else None
        )
        
        # 构建使用量统计
        usage_metadata = response_data.get("usageMetadata", {})
        usage = ChatUsage(
            prompt_tokens=usage_metadata.get("promptTokenCount", 0),
            completion_tokens=usage_metadata.get("candidatesTokenCount", 0),
            total_tokens=usage_metadata.get("totalTokenCount", 0)
        )
        
        # 映射完成原因
        finish_reason_map = {
            "STOP": ChatFinishReason.STOP,
            "MAX_TOKENS": ChatFinishReason.LENGTH,
            "SAFETY": ChatFinishReason.CONTENT_FILTER,
            "RECITATION": ChatFinishReason.CONTENT_FILTER,
            "OTHER": ChatFinishReason.ERROR,
        }
        
        finish_reason = finish_reason_map.get(
            candidate.get("finishReason", "STOP"),
            ChatFinishReason.STOP
        )
        
        return ChatResponse(
            id=str(uuid4()),  # Google AI不返回ID，生成一个
            model=model,
            provider=self.provider_name,
            message=response_message,
            finish_reason=finish_reason,
            usage=usage
        )
    
    def _parse_stream_chunk(self, chunk_data: Dict[str, Any], chunk_id: str) -> Optional[ChatStreamChunk]:
        """解析Google AI流式响应块
        
        Args:
            chunk_data: 流式响应块数据
            chunk_id: 块ID
            
        Returns:
            Optional[ChatStreamChunk]: 解析后的流式响应块
        """
        candidates = chunk_data.get("candidates", [])
        if not candidates:
            return None
        
        candidate = candidates[0]
        content = candidate.get("content", {})
        parts = content.get("parts", [])
        
        # 提取增量内容
        delta_content = ""
        delta_tool_calls = []
        
        for part in parts:
            if "text" in part:
                delta_content += part["text"]
            elif "functionCall" in part:
                func_call = part["functionCall"]
                delta_tool_calls.append({
                    "id": str(uuid4()),
                    "type": "function",
                    "function": {
                        "name": func_call.get("name", ""),
                        "arguments": json.dumps(func_call.get("args", {}))
                    }
                })
        
        # 确定事件类型
        if delta_content:
            event_type = ChatStreamEventType.CONTENT_DELTA
        elif delta_tool_calls:
            event_type = ChatStreamEventType.TOOL_CALL_DELTA
        elif candidate.get("finishReason"):
            event_type = ChatStreamEventType.MESSAGE_STOP
        else:
            return None
        
        # 构建流式响应块
        chunk = ChatStreamChunk(
            id=chunk_id,
            event_type=event_type,
            delta_content=delta_content if delta_content else None,
            delta_tool_calls=delta_tool_calls if delta_tool_calls else None
        )
        
        # 如果是结束块，添加完成信息
        if event_type == ChatStreamEventType.MESSAGE_STOP:
            finish_reason_map = {
                "STOP": ChatFinishReason.STOP,
                "MAX_TOKENS": ChatFinishReason.LENGTH,
                "SAFETY": ChatFinishReason.CONTENT_FILTER,
                "RECITATION": ChatFinishReason.CONTENT_FILTER,
                "OTHER": ChatFinishReason.ERROR,
            }
            chunk.finish_reason = finish_reason_map.get(
                candidate.get("finishReason"),
                ChatFinishReason.STOP
            )
            
            # 添加使用量统计（如果有）
            usage_metadata = chunk_data.get("usageMetadata")
            if usage_metadata:
                chunk.usage = ChatUsage(
                    prompt_tokens=usage_metadata.get("promptTokenCount", 0),
                    completion_tokens=usage_metadata.get("candidatesTokenCount", 0),
                    total_tokens=usage_metadata.get("totalTokenCount", 0)
                )
        
        return chunk
    
    async def chat(
        self,
        messages: List[ChatMessage],
        config: ChatConfig
    ) -> ChatResponse:
        """发送Chat请求并获取响应
        
        Args:
            messages: 对话消息列表
            config: Chat配置参数
            
        Returns:
            ChatResponse: Chat响应
            
        Raises:
            ChatError: Chat相关错误
        """
        # 验证输入
        self._validate_messages(messages)
        self._validate_config(config)
        
        # 提取系统指令
        system_instruction, remaining_messages = self._extract_system_instruction(messages)
        
        # 构建请求参数
        contents = self._map_messages(remaining_messages)
        params = self._map_parameters(config)
        params["contents"] = contents
        
        # 添加系统指令
        if system_instruction:
            params["systemInstruction"] = {
                "parts": [{"text": system_instruction}]
            }
        
        # 选择模型
        model = config.provider_params.get("model", "gemini-2.5-flash") if config.provider_params else "gemini-2.5-flash"
        
        try:
            client = await self._get_client()
            headers = self._get_headers()
            
            self.logger.info(f"发送Google AI Chat请求，模型: {model}，消息数: {len(remaining_messages)}")
            
            response = await client.post(
                f"{self.base_url}/models/{model}:generateContent",
                headers=headers,
                json=params
            )
            
            if response.status_code != 200:
                await self._handle_http_error(response)
            
            response_data = response.json()
            chat_response = self._parse_response(response_data, model)
            
            self.logger.info(f"Google AI Chat请求成功，使用token: {chat_response.usage.total_tokens}")
            return chat_response
            
        except httpx.RequestError as e:
            error_msg = f"网络请求失败: {str(e)}"
            self.logger.error(error_msg)
            raise ChatProviderError(error_msg, provider=self.provider_name)
        except json.JSONDecodeError as e:
            error_msg = f"响应解析失败: {str(e)}"
            self.logger.error(error_msg)
            raise ChatProviderError(error_msg, provider=self.provider_name)
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            self.logger.error(error_msg)
            raise ChatProviderError(error_msg, provider=self.provider_name)
    
    async def chat_stream(
        self,
        messages: List[ChatMessage],
        config: ChatConfig
    ) -> AsyncIterator[ChatStreamChunk]:
        """发送Chat请求并获取流式响应
        
        Args:
            messages: 对话消息列表
            config: Chat配置参数
            
        Yields:
            ChatStreamChunk: Chat流式响应块
            
        Raises:
            ChatError: Chat相关错误
        """
        # 验证输入
        self._validate_messages(messages)
        self._validate_config(config)
        
        # 提取系统指令
        system_instruction, remaining_messages = self._extract_system_instruction(messages)
        
        # 构建请求参数
        contents = self._map_messages(remaining_messages)
        params = self._map_parameters(config)
        params["contents"] = contents
        
        # 添加系统指令
        if system_instruction:
            params["systemInstruction"] = {
                "parts": [{"text": system_instruction}]
            }
        
        # 选择模型
        model = config.provider_params.get("model", "gemini-2.5-flash") if config.provider_params else "gemini-2.5-flash"
        
        try:
            client = await self._get_client()
            headers = self._get_headers()
            
            self.logger.info(f"发送Google AI Chat流式请求，模型: {model}，消息数: {len(remaining_messages)}")
            
            # 发送流式请求
            async with client.stream(
                "POST",
                f"{self.base_url}/models/{model}:streamGenerateContent",
                headers=headers,
                json=params
            ) as response:
                
                if response.status_code != 200:
                    await self._handle_http_error(response)
                
                # 处理流式响应
                chunk_id = str(uuid4())
                accumulator = StreamAccumulator()
                
                async for line in response.aiter_lines():
                    line = line.strip()
                    
                    # 跳过空行
                    if not line:
                        continue
                    
                    try:
                        chunk_data = json.loads(line)
                        chunk = self._parse_stream_chunk(chunk_data, chunk_id)
                        
                        if chunk:
                            # 累积内容
                            accumulator.add_chunk(chunk)
                            
                            # 添加累积内容到块中
                            chunk.accumulated_content = accumulator.get_accumulated_content()
                            
                            yield chunk
                            
                    except json.JSONDecodeError:
                        # 跳过无效的JSON数据
                        continue
                
                self.logger.info("Google AI Chat流式请求完成")
                
        except httpx.RequestError as e:
            error_msg = f"网络请求失败: {str(e)}"
            self.logger.error(error_msg)
            raise ChatProviderError(error_msg, provider=self.provider_name)
        except Exception as e:
            error_msg = f"流式请求处理失败: {str(e)}"
            self.logger.error(error_msg)
            raise ChatProviderError(error_msg, provider=self.provider_name)
