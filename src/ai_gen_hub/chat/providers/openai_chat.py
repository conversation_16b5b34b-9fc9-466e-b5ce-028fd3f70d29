"""
OpenAI Chat适配器

实现OpenAI Chat Completions API的统一接口适配，支持GPT系列模型的对话功能。

主要功能：
- 支持GPT-4、GPT-3.5等主流模型
- 完整的流式和非流式响应支持
- 工具调用和函数调用支持
- 完善的错误处理和重试机制
- 参数验证和格式转换

API兼容性：
- 完全兼容OpenAI Chat Completions API v1
- 支持最新的工具调用格式
- 支持系统消息、用户消息、助手消息
- 支持多轮对话上下文

作者：AI Gen Hub Team
创建时间：2025-08-24
"""

import json
import logging as std_logging
from typing import Any, AsyncIterator, Dict, List, Optional
from uuid import uuid4

import httpx

from ai_gen_hub.core.chat_interfaces import (
    ChatMessage,
    ChatConfig,
    ChatResponse,
    ChatStreamChunk,
    ChatUsage,
    ChatMessageRole,
    ChatFinishReason,
    ChatStreamEventType,
    ChatError,
    ChatValidationError,
    ChatProviderError,
)
from ai_gen_hub.core.chat_base import BaseChatProvider, StreamAccumulator

# 设置日志记录器
logger = std_logging.getLogger(__name__)


class OpenAIChatProvider(BaseChatProvider):
    """OpenAI Chat供应商适配器
    
    实现OpenAI Chat Completions API的统一接口适配。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化OpenAI Chat适配器
        
        Args:
            config: 配置字典，包含以下字段：
                - api_key: OpenAI API密钥
                - base_url: API基础URL（可选，默认为官方URL）
                - organization: 组织ID（可选）
                - timeout: 请求超时时间（可选，默认30秒）
                - max_retries: 最大重试次数（可选，默认3次）
        """
        # 设置默认配置
        config.setdefault("base_url", "https://api.openai.com/v1")
        config.setdefault("timeout", 30.0)
        config.setdefault("max_retries", 3)
        
        super().__init__("openai", config)
        
        # OpenAI特定配置
        self.organization = config.get("organization")
        
        # 支持的模型列表
        self._supported_models = [
            "gpt-4o",
            "gpt-4o-mini",
            "gpt-4-turbo",
            "gpt-4-turbo-preview",
            "gpt-4",
            "gpt-4-32k",
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k",
        ]
        
        # 参数映射（OpenAI使用标准参数名，无需映射）
        self._parameter_mapping = {}
        
        self.logger.info("OpenAI Chat适配器初始化完成")
    
    def _get_headers(self) -> Dict[str, str]:
        """获取OpenAI API请求头
        
        Returns:
            Dict[str, str]: 请求头字典
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "User-Agent": "AI-Gen-Hub/OpenAI-Chat"
        }
        
        # 添加组织ID（如果有）
        if self.organization:
            headers["OpenAI-Organization"] = self.organization
        
        return headers
    
    def _map_messages(self, messages: List[ChatMessage]) -> List[Dict[str, Any]]:
        """映射消息格式到OpenAI格式
        
        Args:
            messages: 统一消息列表
            
        Returns:
            List[Dict[str, Any]]: OpenAI格式的消息列表
        """
        openai_messages = []
        
        for message in messages:
            openai_message = {
                "role": message.role.value,
                "content": message.content
            }
            
            # 添加可选字段
            if message.name:
                openai_message["name"] = message.name
            
            if message.tool_calls:
                openai_message["tool_calls"] = message.tool_calls
            
            if message.tool_call_id:
                openai_message["tool_call_id"] = message.tool_call_id
            
            openai_messages.append(openai_message)
        
        return openai_messages
    
    def _map_parameters(self, config: ChatConfig) -> Dict[str, Any]:
        """映射配置参数到OpenAI格式
        
        Args:
            config: 统一配置参数
            
        Returns:
            Dict[str, Any]: OpenAI格式的参数字典
        """
        params = {}
        
        # 基础参数
        if config.max_tokens is not None:
            params["max_tokens"] = config.max_tokens
        
        params["temperature"] = config.temperature
        
        if config.top_p is not None:
            params["top_p"] = config.top_p
        
        params["frequency_penalty"] = config.frequency_penalty
        params["presence_penalty"] = config.presence_penalty
        
        # 停止序列
        if config.stop_sequences:
            params["stop"] = config.stop_sequences
        
        # 流式输出
        params["stream"] = config.stream
        
        # 工具和函数调用
        if config.tools:
            params["tools"] = config.tools
        
        if config.tool_choice:
            params["tool_choice"] = config.tool_choice
        
        # 添加供应商特定参数
        if config.provider_params:
            params.update(config.provider_params)
        
        return params
    
    def _parse_response(self, response_data: Dict[str, Any], model: str) -> ChatResponse:
        """解析OpenAI响应为统一格式
        
        Args:
            response_data: OpenAI原始响应数据
            model: 使用的模型名称
            
        Returns:
            ChatResponse: 统一格式的响应
        """
        choice = response_data["choices"][0]
        message_data = choice["message"]
        
        # 构建响应消息
        response_message = ChatMessage(
            role=ChatMessageRole.ASSISTANT,
            content=message_data.get("content", "") or "",
            tool_calls=message_data.get("tool_calls")
        )
        
        # 构建使用量统计
        usage_data = response_data["usage"]
        usage = ChatUsage(
            prompt_tokens=usage_data["prompt_tokens"],
            completion_tokens=usage_data["completion_tokens"],
            total_tokens=usage_data["total_tokens"]
        )
        
        # 映射完成原因
        finish_reason_map = {
            "stop": ChatFinishReason.STOP,
            "length": ChatFinishReason.LENGTH,
            "content_filter": ChatFinishReason.CONTENT_FILTER,
            "tool_calls": ChatFinishReason.TOOL_CALLS,
            "function_call": ChatFinishReason.TOOL_CALLS,  # 向后兼容
        }
        finish_reason = finish_reason_map.get(
            choice["finish_reason"],
            ChatFinishReason.STOP
        )
        
        return ChatResponse(
            id=response_data["id"],
            model=model,
            provider=self.provider_name,
            message=response_message,
            finish_reason=finish_reason,
            usage=usage
        )
    
    def _parse_stream_chunk(self, chunk_data: Dict[str, Any], chunk_id: str) -> Optional[ChatStreamChunk]:
        """解析OpenAI流式响应块
        
        Args:
            chunk_data: 流式响应块数据
            chunk_id: 块ID
            
        Returns:
            Optional[ChatStreamChunk]: 解析后的流式响应块
        """
        choices = chunk_data.get("choices", [])
        if not choices:
            return None
        
        choice = choices[0]
        delta = choice.get("delta", {})
        
        # 确定事件类型和内容
        delta_content = None
        delta_tool_calls = None
        event_type = ChatStreamEventType.CONTENT_DELTA
        
        if "content" in delta and delta["content"]:
            delta_content = delta["content"]
        elif "tool_calls" in delta:
            delta_tool_calls = delta["tool_calls"]
            event_type = ChatStreamEventType.TOOL_CALL_DELTA
        elif choice.get("finish_reason"):
            event_type = ChatStreamEventType.MESSAGE_STOP
        else:
            # 跳过空块
            return None
        
        # 构建流式响应块
        chunk = ChatStreamChunk(
            id=chunk_id,
            event_type=event_type,
            delta_content=delta_content,
            delta_tool_calls=delta_tool_calls
        )
        
        # 如果是结束块，添加完成信息
        if event_type == ChatStreamEventType.MESSAGE_STOP:
            finish_reason_map = {
                "stop": ChatFinishReason.STOP,
                "length": ChatFinishReason.LENGTH,
                "content_filter": ChatFinishReason.CONTENT_FILTER,
                "tool_calls": ChatFinishReason.TOOL_CALLS,
            }
            chunk.finish_reason = finish_reason_map.get(
                choice["finish_reason"],
                ChatFinishReason.STOP
            )
        
        return chunk
    
    async def chat(
        self,
        messages: List[ChatMessage],
        config: ChatConfig
    ) -> ChatResponse:
        """发送Chat请求并获取响应
        
        Args:
            messages: 对话消息列表
            config: Chat配置参数
            
        Returns:
            ChatResponse: Chat响应
            
        Raises:
            ChatError: Chat相关错误
        """
        # 验证输入
        self._validate_messages(messages)
        self._validate_config(config)
        
        # 构建请求参数
        openai_messages = self._map_messages(messages)
        params = self._map_parameters(config)
        params["messages"] = openai_messages
        
        # 确保非流式请求
        params["stream"] = False
        
        # 选择模型（如果配置中没有指定，使用默认模型）
        model = config.provider_params.get("model", "gpt-3.5-turbo") if config.provider_params else "gpt-3.5-turbo"
        params["model"] = model
        
        try:
            client = await self._get_client()
            headers = self._get_headers()
            
            self.logger.info(f"发送OpenAI Chat请求，模型: {model}，消息数: {len(messages)}")
            
            response = await client.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=params
            )
            
            if response.status_code != 200:
                await self._handle_http_error(response)
            
            response_data = response.json()
            chat_response = self._parse_response(response_data, model)
            
            self.logger.info(f"OpenAI Chat请求成功，使用token: {chat_response.usage.total_tokens}")
            return chat_response
            
        except httpx.RequestError as e:
            error_msg = f"网络请求失败: {str(e)}"
            self.logger.error(error_msg)
            raise ChatProviderError(error_msg, provider=self.provider_name)
        except json.JSONDecodeError as e:
            error_msg = f"响应解析失败: {str(e)}"
            self.logger.error(error_msg)
            raise ChatProviderError(error_msg, provider=self.provider_name)
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            self.logger.error(error_msg)
            raise ChatProviderError(error_msg, provider=self.provider_name)
    
    async def chat_stream(
        self,
        messages: List[ChatMessage],
        config: ChatConfig
    ) -> AsyncIterator[ChatStreamChunk]:
        """发送Chat请求并获取流式响应
        
        Args:
            messages: 对话消息列表
            config: Chat配置参数
            
        Yields:
            ChatStreamChunk: Chat流式响应块
            
        Raises:
            ChatError: Chat相关错误
        """
        # 验证输入
        self._validate_messages(messages)
        self._validate_config(config)
        
        # 构建请求参数
        openai_messages = self._map_messages(messages)
        params = self._map_parameters(config)
        params["messages"] = openai_messages
        
        # 确保流式请求
        params["stream"] = True
        
        # 选择模型
        model = config.provider_params.get("model", "gpt-3.5-turbo") if config.provider_params else "gpt-3.5-turbo"
        params["model"] = model
        
        try:
            client = await self._get_client()
            headers = self._get_headers()
            
            self.logger.info(f"发送OpenAI Chat流式请求，模型: {model}，消息数: {len(messages)}")
            
            # 发送流式请求
            async with client.stream(
                "POST",
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=params
            ) as response:
                
                if response.status_code != 200:
                    await self._handle_http_error(response)
                
                # 处理流式响应
                chunk_id = str(uuid4())
                accumulator = StreamAccumulator()
                
                async for line in response.aiter_lines():
                    line = line.strip()
                    
                    # 跳过空行和非数据行
                    if not line or not line.startswith("data: "):
                        continue
                    
                    # 提取数据部分
                    data_str = line[6:]  # 去掉 "data: " 前缀
                    
                    # 检查是否为结束标记
                    if data_str == "[DONE]":
                        # 发送最终的使用量统计（如果有累积的话）
                        if accumulator.usage:
                            final_chunk = ChatStreamChunk(
                                id=chunk_id,
                                event_type=ChatStreamEventType.MESSAGE_STOP,
                                finish_reason=accumulator.finish_reason or ChatFinishReason.STOP,
                                usage=accumulator.usage,
                                accumulated_content=accumulator.get_accumulated_content()
                            )
                            yield final_chunk
                        break
                    
                    try:
                        chunk_data = json.loads(data_str)
                        chunk = self._parse_stream_chunk(chunk_data, chunk_id)
                        
                        if chunk:
                            # 累积内容
                            accumulator.add_chunk(chunk)
                            
                            # 添加累积内容到块中
                            chunk.accumulated_content = accumulator.get_accumulated_content()
                            
                            yield chunk
                            
                    except json.JSONDecodeError:
                        # 跳过无效的JSON数据
                        continue
                
                self.logger.info("OpenAI Chat流式请求完成")
                
        except httpx.RequestError as e:
            error_msg = f"网络请求失败: {str(e)}"
            self.logger.error(error_msg)
            raise ChatProviderError(error_msg, provider=self.provider_name)
        except Exception as e:
            error_msg = f"流式请求处理失败: {str(e)}"
            self.logger.error(error_msg)
            raise ChatProviderError(error_msg, provider=self.provider_name)
