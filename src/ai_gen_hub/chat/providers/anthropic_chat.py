"""
Anthropic Chat适配器

实现Anthropic Claude API的统一接口适配，支持Claude系列模型的对话功能。

主要功能：
- 支持Claude 3.5、Claude 3系列模型
- 完整的流式和非流式响应支持
- 工具调用和函数调用支持
- 完善的错误处理和重试机制
- 系统消息和多轮对话支持

API兼容性：
- 兼容Anthropic Messages API v1
- 支持最新的Claude 3.5 Sonnet、Claude 3 Opus等模型
- 支持系统消息、用户消息、助手消息
- 支持工具调用和结构化输出

特殊处理：
- 系统消息处理（role=system -> system参数）
- 消息格式验证（必须以用户消息开始）
- 流式响应事件处理（message_start、content_block_delta等）
- 参数名称映射（max_tokens -> max_tokens，但有不同的默认值）

作者：AI Gen Hub Team
创建时间：2025-08-24
"""

import json
import logging as std_logging
from typing import Any, AsyncIterator, Dict, List, Optional
from uuid import uuid4

import httpx

from ai_gen_hub.core.chat_interfaces import (
    ChatMessage,
    ChatConfig,
    ChatResponse,
    ChatStreamChunk,
    ChatUsage,
    ChatMessageRole,
    ChatFinishReason,
    ChatStreamEventType,
    ChatError,
    ChatValidationError,
    ChatProviderError,
)
from ai_gen_hub.core.chat_base import BaseChatProvider, StreamAccumulator

# 设置日志记录器
logger = std_logging.getLogger(__name__)


class AnthropicChatProvider(BaseChatProvider):
    """Anthropic Chat供应商适配器
    
    实现Anthropic Claude API的统一接口适配。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化Anthropic Chat适配器
        
        Args:
            config: 配置字典，包含以下字段：
                - api_key: Anthropic API密钥
                - base_url: API基础URL（可选，默认为官方URL）
                - timeout: 请求超时时间（可选，默认30秒）
                - max_retries: 最大重试次数（可选，默认3次）
        """
        # 设置默认配置
        config.setdefault("base_url", "https://api.anthropic.com")
        config.setdefault("timeout", 30.0)
        config.setdefault("max_retries", 3)
        
        super().__init__("anthropic", config)
        
        # Anthropic特定配置
        self.api_version = "2023-06-01"
        
        # 支持的模型列表
        self._supported_models = [
            "claude-3-5-sonnet-20241022",
            "claude-3-5-sonnet-20240620",
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307",
            "claude-2.1",
            "claude-2.0",
            "claude-instant-1.2",
        ]
        
        # 参数映射（Anthropic使用不同的参数名）
        self._parameter_mapping = {
            "stop_sequences": "stop_sequences",  # 相同
        }
        
        self.logger.info("Anthropic Chat适配器初始化完成")
    
    def _get_headers(self) -> Dict[str, str]:
        """获取Anthropic API请求头
        
        Returns:
            Dict[str, str]: 请求头字典
        """
        return {
            "Content-Type": "application/json",
            "x-api-key": self.api_key,
            "anthropic-version": self.api_version,
            "User-Agent": "AI-Gen-Hub/Anthropic-Chat"
        }
    
    def _extract_system_message(self, messages: List[ChatMessage]) -> tuple[Optional[str], List[ChatMessage]]:
        """提取系统消息并返回剩余消息
        
        Args:
            messages: 原始消息列表
            
        Returns:
            tuple: (系统消息内容, 剩余消息列表)
        """
        system_content = None
        remaining_messages = []
        
        for message in messages:
            if message.role == ChatMessageRole.SYSTEM:
                # Anthropic只支持一个系统消息，如果有多个则合并
                if system_content is None:
                    system_content = message.content
                else:
                    system_content += "\n\n" + message.content
            else:
                remaining_messages.append(message)
        
        return system_content, remaining_messages
    
    def _validate_anthropic_messages(self, messages: List[ChatMessage]) -> None:
        """验证Anthropic特定的消息格式要求
        
        Args:
            messages: 消息列表
            
        Raises:
            ChatValidationError: 消息验证失败
        """
        if not messages:
            raise ChatValidationError("消息列表不能为空", provider=self.provider_name)
        
        # Anthropic要求第一条消息必须是用户消息
        if messages[0].role != ChatMessageRole.USER:
            raise ChatValidationError(
                "Anthropic API要求第一条消息必须是用户消息",
                provider=self.provider_name
            )
        
        # 检查消息角色交替（用户和助手消息应该交替出现）
        for i in range(1, len(messages)):
            current_role = messages[i].role
            prev_role = messages[i-1].role
            
            # 跳过系统消息和工具消息的检查
            if current_role in [ChatMessageRole.SYSTEM, ChatMessageRole.TOOL, ChatMessageRole.FUNCTION]:
                continue
            if prev_role in [ChatMessageRole.SYSTEM, ChatMessageRole.TOOL, ChatMessageRole.FUNCTION]:
                continue
            
            # 用户和助手消息应该交替
            if current_role == prev_role:
                self.logger.warning(f"检测到连续的{current_role.value}消息，这可能导致API错误")
    
    def _map_messages(self, messages: List[ChatMessage]) -> List[Dict[str, Any]]:
        """映射消息格式到Anthropic格式
        
        Args:
            messages: 统一消息列表
            
        Returns:
            List[Dict[str, Any]]: Anthropic格式的消息列表
        """
        anthropic_messages = []
        
        for message in messages:
            # 映射角色
            role_mapping = {
                ChatMessageRole.USER: "user",
                ChatMessageRole.ASSISTANT: "assistant",
                ChatMessageRole.TOOL: "user",  # 工具响应作为用户消息
                ChatMessageRole.FUNCTION: "user",  # 函数响应作为用户消息
            }
            
            role = role_mapping.get(message.role, "user")
            
            anthropic_message = {
                "role": role,
                "content": message.content
            }
            
            # 处理工具调用
            if message.tool_calls:
                # Anthropic的工具调用格式
                content_blocks = [{"type": "text", "text": message.content}]
                
                for tool_call in message.tool_calls:
                    content_blocks.append({
                        "type": "tool_use",
                        "id": tool_call.get("id", str(uuid4())),
                        "name": tool_call.get("function", {}).get("name", ""),
                        "input": json.loads(tool_call.get("function", {}).get("arguments", "{}"))
                    })
                
                anthropic_message["content"] = content_blocks
            
            # 处理工具响应
            if message.tool_call_id:
                anthropic_message["content"] = [{
                    "type": "tool_result",
                    "tool_use_id": message.tool_call_id,
                    "content": message.content
                }]
            
            anthropic_messages.append(anthropic_message)
        
        return anthropic_messages
    
    def _map_parameters(self, config: ChatConfig) -> Dict[str, Any]:
        """映射配置参数到Anthropic格式
        
        Args:
            config: 统一配置参数
            
        Returns:
            Dict[str, Any]: Anthropic格式的参数字典
        """
        params = {}
        
        # 基础参数
        if config.max_tokens is not None:
            params["max_tokens"] = config.max_tokens
        else:
            # Anthropic要求必须指定max_tokens
            params["max_tokens"] = 1024
        
        params["temperature"] = config.temperature
        
        if config.top_p is not None:
            params["top_p"] = config.top_p
        
        # 停止序列
        if config.stop_sequences:
            params["stop_sequences"] = config.stop_sequences
        
        # 流式输出
        params["stream"] = config.stream
        
        # 工具配置
        if config.tools:
            # 转换工具格式
            tools = []
            for tool in config.tools:
                if tool.get("type") == "function":
                    func_def = tool["function"]
                    tools.append({
                        "name": func_def["name"],
                        "description": func_def.get("description", ""),
                        "input_schema": func_def.get("parameters", {})
                    })
            if tools:
                params["tools"] = tools
        
        # 添加供应商特定参数
        if config.provider_params:
            params.update(config.provider_params)
        
        return params
    
    def _parse_response(self, response_data: Dict[str, Any], model: str) -> ChatResponse:
        """解析Anthropic响应为统一格式
        
        Args:
            response_data: Anthropic原始响应数据
            model: 使用的模型名称
            
        Returns:
            ChatResponse: 统一格式的响应
        """
        content_blocks = response_data.get("content", [])
        
        # 提取文本内容和工具调用
        text_content = ""
        tool_calls = []
        
        for block in content_blocks:
            if block.get("type") == "text":
                text_content += block.get("text", "")
            elif block.get("type") == "tool_use":
                # 转换为OpenAI格式的工具调用
                tool_calls.append({
                    "id": block.get("id", str(uuid4())),
                    "type": "function",
                    "function": {
                        "name": block.get("name", ""),
                        "arguments": json.dumps(block.get("input", {}))
                    }
                })
        
        # 构建响应消息
        response_message = ChatMessage(
            role=ChatMessageRole.ASSISTANT,
            content=text_content,
            tool_calls=tool_calls if tool_calls else None
        )
        
        # 构建使用量统计
        usage_data = response_data.get("usage", {})
        usage = ChatUsage(
            prompt_tokens=usage_data.get("input_tokens", 0),
            completion_tokens=usage_data.get("output_tokens", 0),
            total_tokens=usage_data.get("input_tokens", 0) + usage_data.get("output_tokens", 0)
        )
        
        # 映射完成原因
        finish_reason_map = {
            "end_turn": ChatFinishReason.STOP,
            "max_tokens": ChatFinishReason.LENGTH,
            "stop_sequence": ChatFinishReason.STOP,
            "tool_use": ChatFinishReason.TOOL_CALLS,
        }
        
        finish_reason = finish_reason_map.get(
            response_data.get("stop_reason", "end_turn"),
            ChatFinishReason.STOP
        )
        
        return ChatResponse(
            id=response_data.get("id", str(uuid4())),
            model=model,
            provider=self.provider_name,
            message=response_message,
            finish_reason=finish_reason,
            usage=usage
        )
    
    def _parse_stream_chunk(self, chunk_data: Dict[str, Any], chunk_id: str) -> Optional[ChatStreamChunk]:
        """解析Anthropic流式响应块
        
        Args:
            chunk_data: 流式响应块数据
            chunk_id: 块ID
            
        Returns:
            Optional[ChatStreamChunk]: 解析后的流式响应块
        """
        event_type_str = chunk_data.get("type", "")
        
        # 处理不同类型的事件
        if event_type_str == "content_block_delta":
            delta = chunk_data.get("delta", {})
            if delta.get("type") == "text_delta":
                return ChatStreamChunk(
                    id=chunk_id,
                    event_type=ChatStreamEventType.CONTENT_DELTA,
                    delta_content=delta.get("text", "")
                )
            elif delta.get("type") == "input_json_delta":
                # 工具调用的JSON增量
                return ChatStreamChunk(
                    id=chunk_id,
                    event_type=ChatStreamEventType.TOOL_CALL_DELTA,
                    delta_tool_calls=[{
                        "partial_json": delta.get("partial_json", "")
                    }]
                )
        
        elif event_type_str == "message_delta":
            delta = chunk_data.get("delta", {})
            usage_data = chunk_data.get("usage", {})
            
            # 映射完成原因
            finish_reason_map = {
                "end_turn": ChatFinishReason.STOP,
                "max_tokens": ChatFinishReason.LENGTH,
                "stop_sequence": ChatFinishReason.STOP,
                "tool_use": ChatFinishReason.TOOL_CALLS,
            }
            
            finish_reason = finish_reason_map.get(
                delta.get("stop_reason"),
                ChatFinishReason.STOP
            )
            
            usage = None
            if usage_data:
                usage = ChatUsage(
                    prompt_tokens=0,  # Anthropic在delta中不提供输入token
                    completion_tokens=usage_data.get("output_tokens", 0),
                    total_tokens=usage_data.get("output_tokens", 0)
                )
            
            return ChatStreamChunk(
                id=chunk_id,
                event_type=ChatStreamEventType.MESSAGE_STOP,
                finish_reason=finish_reason,
                usage=usage
            )
        
        elif event_type_str == "message_stop":
            return ChatStreamChunk(
                id=chunk_id,
                event_type=ChatStreamEventType.MESSAGE_STOP,
                finish_reason=ChatFinishReason.STOP
            )
        
        # 跳过其他类型的事件（如message_start、content_block_start等）
        return None
    
    async def chat(
        self,
        messages: List[ChatMessage],
        config: ChatConfig
    ) -> ChatResponse:
        """发送Chat请求并获取响应
        
        Args:
            messages: 对话消息列表
            config: Chat配置参数
            
        Returns:
            ChatResponse: Chat响应
            
        Raises:
            ChatError: Chat相关错误
        """
        # 验证输入
        self._validate_messages(messages)
        self._validate_config(config)
        
        # 提取系统消息
        system_content, remaining_messages = self._extract_system_message(messages)
        
        # Anthropic特定验证
        self._validate_anthropic_messages(remaining_messages)
        
        # 构建请求参数
        anthropic_messages = self._map_messages(remaining_messages)
        params = self._map_parameters(config)
        params["messages"] = anthropic_messages
        
        # 添加系统消息
        if system_content:
            params["system"] = system_content
        
        # 确保非流式请求
        params["stream"] = False
        
        # 选择模型
        model = config.provider_params.get("model", "claude-3-5-sonnet-20241022") if config.provider_params else "claude-3-5-sonnet-20241022"
        params["model"] = model
        
        try:
            client = await self._get_client()
            headers = self._get_headers()
            
            self.logger.info(f"发送Anthropic Chat请求，模型: {model}，消息数: {len(remaining_messages)}")
            
            response = await client.post(
                f"{self.base_url}/v1/messages",
                headers=headers,
                json=params
            )
            
            if response.status_code != 200:
                await self._handle_http_error(response)
            
            response_data = response.json()
            chat_response = self._parse_response(response_data, model)
            
            self.logger.info(f"Anthropic Chat请求成功，使用token: {chat_response.usage.total_tokens}")
            return chat_response
            
        except httpx.RequestError as e:
            error_msg = f"网络请求失败: {str(e)}"
            self.logger.error(error_msg)
            raise ChatProviderError(error_msg, provider=self.provider_name)
        except json.JSONDecodeError as e:
            error_msg = f"响应解析失败: {str(e)}"
            self.logger.error(error_msg)
            raise ChatProviderError(error_msg, provider=self.provider_name)
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            self.logger.error(error_msg)
            raise ChatProviderError(error_msg, provider=self.provider_name)

    async def chat_stream(
        self,
        messages: List[ChatMessage],
        config: ChatConfig
    ) -> AsyncIterator[ChatStreamChunk]:
        """发送Chat请求并获取流式响应

        Args:
            messages: 对话消息列表
            config: Chat配置参数

        Yields:
            ChatStreamChunk: Chat流式响应块

        Raises:
            ChatError: Chat相关错误
        """
        # 验证输入
        self._validate_messages(messages)
        self._validate_config(config)

        # 提取系统消息
        system_content, remaining_messages = self._extract_system_message(messages)

        # Anthropic特定验证
        self._validate_anthropic_messages(remaining_messages)

        # 构建请求参数
        anthropic_messages = self._map_messages(remaining_messages)
        params = self._map_parameters(config)
        params["messages"] = anthropic_messages

        # 添加系统消息
        if system_content:
            params["system"] = system_content

        # 确保流式请求
        params["stream"] = True

        # 选择模型
        model = config.provider_params.get("model", "claude-3-5-sonnet-20241022") if config.provider_params else "claude-3-5-sonnet-20241022"
        params["model"] = model

        try:
            client = await self._get_client()
            headers = self._get_headers()

            self.logger.info(f"发送Anthropic Chat流式请求，模型: {model}，消息数: {len(remaining_messages)}")

            # 发送流式请求
            async with client.stream(
                "POST",
                f"{self.base_url}/v1/messages",
                headers=headers,
                json=params
            ) as response:

                if response.status_code != 200:
                    await self._handle_http_error(response)

                # 处理流式响应
                chunk_id = str(uuid4())
                accumulator = StreamAccumulator()

                async for line in response.aiter_lines():
                    line = line.strip()

                    # 跳过空行和非事件行
                    if not line or not line.startswith("data: "):
                        continue

                    # 提取数据部分
                    data_str = line[6:]  # 去掉 "data: " 前缀

                    # 跳过ping事件
                    if data_str == '{"type": "ping"}':
                        continue

                    try:
                        chunk_data = json.loads(data_str)
                        chunk = self._parse_stream_chunk(chunk_data, chunk_id)

                        if chunk:
                            # 累积内容
                            accumulator.add_chunk(chunk)

                            # 添加累积内容到块中
                            chunk.accumulated_content = accumulator.get_accumulated_content()

                            yield chunk

                    except json.JSONDecodeError:
                        # 跳过无效的JSON数据
                        continue

                self.logger.info("Anthropic Chat流式请求完成")

        except httpx.RequestError as e:
            error_msg = f"网络请求失败: {str(e)}"
            self.logger.error(error_msg)
            raise ChatProviderError(error_msg, provider=self.provider_name)
        except Exception as e:
            error_msg = f"流式请求处理失败: {str(e)}"
            self.logger.error(error_msg)
            raise ChatProviderError(error_msg, provider=self.provider_name)
