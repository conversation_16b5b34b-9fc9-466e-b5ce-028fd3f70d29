<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Gen Hub - 高级分析仪表板</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .analytics-card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 10px;
            transition: transform 0.2s;
        }
        
        .analytics-card:hover {
            transform: translateY(-2px);
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .trend-indicator {
            font-size: 0.8rem;
            padding: 2px 8px;
            border-radius: 12px;
        }
        
        .trend-up {
            background-color: #d4edda;
            color: #155724;
        }
        
        .trend-down {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .trend-stable {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .anomaly-item {
            border-left: 4px solid #dc3545;
            padding: 10px;
            margin: 5px 0;
            background-color: #f8f9fa;
            border-radius: 0 5px 5px 0;
        }
        
        .severity-critical {
            border-left-color: #dc3545;
        }
        
        .severity-high {
            border-left-color: #fd7e14;
        }
        
        .severity-medium {
            border-left-color: #ffc107;
        }
        
        .severity-low {
            border-left-color: #28a745;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 5px;
            margin: 2px 0;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar p-3">
                <h4 class="mb-4">
                    <i class="fas fa-chart-line me-2"></i>
                    分析中心
                </h4>
                
                <nav class="nav flex-column">
                    <a class="nav-link active" href="#overview" data-section="overview">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        概览
                    </a>
                    <a class="nav-link" href="#trends" data-section="trends">
                        <i class="fas fa-trending-up me-2"></i>
                        趋势分析
                    </a>
                    <a class="nav-link" href="#anomalies" data-section="anomalies">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        异常检测
                    </a>
                    <a class="nav-link" href="#reports" data-section="reports">
                        <i class="fas fa-file-alt me-2"></i>
                        报告管理
                    </a>
                    <a class="nav-link" href="#export" data-section="export">
                        <i class="fas fa-download me-2"></i>
                        数据导出
                    </a>
                </nav>
            </div>
            
            <!-- 主内容区 -->
            <div class="col-md-10 p-4">
                <!-- 页面标题 -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>高级分析仪表板</h2>
                    <div>
                        <button class="btn btn-outline-primary me-2" onclick="refreshData()">
                            <i class="fas fa-sync-alt"></i> 刷新数据
                        </button>
                        <button class="btn btn-primary" onclick="generateReport()">
                            <i class="fas fa-file-export"></i> 生成报告
                        </button>
                    </div>
                </div>
                
                <!-- 概览部分 -->
                <div id="overview-section" class="section-content">
                    <!-- 关键指标卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card analytics-card">
                                <div class="card-body text-center">
                                    <div class="metric-value text-primary" id="total-requests">
                                        <span class="loading-spinner"></span>
                                    </div>
                                    <div class="metric-label">总请求数</div>
                                    <div class="trend-indicator trend-up mt-2" id="requests-trend">
                                        <i class="fas fa-arrow-up"></i> +12.5%
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card analytics-card">
                                <div class="card-body text-center">
                                    <div class="metric-value text-success" id="avg-response-time">
                                        <span class="loading-spinner"></span>
                                    </div>
                                    <div class="metric-label">平均响应时间 (ms)</div>
                                    <div class="trend-indicator trend-down mt-2" id="response-time-trend">
                                        <i class="fas fa-arrow-down"></i> -8.3%
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card analytics-card">
                                <div class="card-body text-center">
                                    <div class="metric-value text-warning" id="error-rate">
                                        <span class="loading-spinner"></span>
                                    </div>
                                    <div class="metric-label">错误率 (%)</div>
                                    <div class="trend-indicator trend-stable mt-2" id="error-rate-trend">
                                        <i class="fas fa-minus"></i> 0.0%
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card analytics-card">
                                <div class="card-body text-center">
                                    <div class="metric-value text-info" id="cache-hit-rate">
                                        <span class="loading-spinner"></span>
                                    </div>
                                    <div class="metric-label">缓存命中率 (%)</div>
                                    <div class="trend-indicator trend-up mt-2" id="cache-hit-trend">
                                        <i class="fas fa-arrow-up"></i> +5.2%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 趋势图表 -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card analytics-card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-line me-2"></i>
                                        性能趋势
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="trendChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card analytics-card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        最近异常
                                    </h5>
                                </div>
                                <div class="card-body" id="recent-anomalies">
                                    <div class="text-center">
                                        <span class="loading-spinner"></span>
                                        <p class="mt-2">加载异常数据...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 趋势分析部分 -->
                <div id="trends-section" class="section-content" style="display: none;">
                    <div class="card analytics-card">
                        <div class="card-header">
                            <h5 class="mb-0">趋势分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <select class="form-select" id="metric-selector">
                                        <option value="request_total">请求总数</option>
                                        <option value="response_time_avg">平均响应时间</option>
                                        <option value="error_rate">错误率</option>
                                        <option value="cache_hit_rate">缓存命中率</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <select class="form-select" id="time-range-selector">
                                        <option value="7">过去7天</option>
                                        <option value="30">过去30天</option>
                                        <option value="90">过去90天</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-primary" onclick="analyzeTrend()">
                                        <i class="fas fa-search"></i> 分析趋势
                                    </button>
                                </div>
                            </div>
                            
                            <div id="trend-analysis-result">
                                <div class="text-center text-muted">
                                    <i class="fas fa-chart-line fa-3x mb-3"></i>
                                    <p>选择指标和时间范围，然后点击"分析趋势"开始分析</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 异常检测部分 -->
                <div id="anomalies-section" class="section-content" style="display: none;">
                    <div class="card analytics-card">
                        <div class="card-header">
                            <h5 class="mb-0">异常检测</h5>
                        </div>
                        <div class="card-body">
                            <div id="anomaly-detection-result">
                                <div class="text-center text-muted">
                                    <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                                    <p>异常检测功能正在加载...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 报告管理部分 -->
                <div id="reports-section" class="section-content" style="display: none;">
                    <div class="card analytics-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">报告管理</h5>
                            <button class="btn btn-primary btn-sm" onclick="createNewReport()">
                                <i class="fas fa-plus"></i> 新建报告
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="reports-list">
                                <div class="text-center text-muted">
                                    <i class="fas fa-file-alt fa-3x mb-3"></i>
                                    <p>报告列表正在加载...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 数据导出部分 -->
                <div id="export-section" class="section-content" style="display: none;">
                    <div class="card analytics-card">
                        <div class="card-header">
                            <h5 class="mb-0">数据导出</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>选择导出数据</h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="export-requests" checked>
                                        <label class="form-check-label" for="export-requests">
                                            请求数据
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="export-performance" checked>
                                        <label class="form-check-label" for="export-performance">
                                            性能数据
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="export-errors">
                                        <label class="form-check-label" for="export-errors">
                                            错误日志
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6>导出格式</h6>
                                    <select class="form-select mb-3" id="export-format">
                                        <option value="csv">CSV</option>
                                        <option value="excel">Excel</option>
                                        <option value="json">JSON</option>
                                    </select>
                                    
                                    <button class="btn btn-success" onclick="exportData()">
                                        <i class="fas fa-download"></i> 导出数据
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 全局变量
        let trendChart = null;
        let currentSection = 'overview';
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
            setupNavigation();
            loadOverviewData();
        });
        
        // 初始化仪表板
        function initializeDashboard() {
            console.log('初始化高级分析仪表板');
            initializeTrendChart();
        }
        
        // 设置导航
        function setupNavigation() {
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 更新活跃状态
                    navLinks.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 显示对应部分
                    const section = this.getAttribute('data-section');
                    showSection(section);
                });
            });
        }
        
        // 显示指定部分
        function showSection(section) {
            // 隐藏所有部分
            document.querySelectorAll('.section-content').forEach(el => {
                el.style.display = 'none';
            });
            
            // 显示指定部分
            const sectionElement = document.getElementById(section + '-section');
            if (sectionElement) {
                sectionElement.style.display = 'block';
                currentSection = section;
                
                // 根据部分加载相应数据
                switch(section) {
                    case 'overview':
                        loadOverviewData();
                        break;
                    case 'trends':
                        loadTrendsData();
                        break;
                    case 'anomalies':
                        loadAnomaliesData();
                        break;
                    case 'reports':
                        loadReportsData();
                        break;
                    case 'export':
                        // 导出部分不需要额外加载数据
                        break;
                }
            }
        }
        
        // 初始化趋势图表
        function initializeTrendChart() {
            const ctx = document.getElementById('trendChart').getContext('2d');
            
            trendChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [
                        {
                            label: '请求数',
                            data: [],
                            borderColor: 'rgb(54, 162, 235)',
                            backgroundColor: 'rgba(54, 162, 235, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: '响应时间',
                            data: [],
                            borderColor: 'rgb(255, 99, 132)',
                            backgroundColor: 'rgba(255, 99, 132, 0.1)',
                            tension: 0.4,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    }
                }
            });
        }
        
        // 加载概览数据
        async function loadOverviewData() {
            try {
                const response = await fetch('/api/v1/analytics/summary');
                const data = await response.json();
                
                // 更新指标卡片
                updateMetricCard('total-requests', data.total_requests || 0, 'requests-trend', data.requests_trend || 0);
                updateMetricCard('avg-response-time', data.avg_response_time || 0, 'response-time-trend', data.response_time_trend || 0);
                updateMetricCard('error-rate', data.error_rate || 0, 'error-rate-trend', data.error_rate_trend || 0);
                updateMetricCard('cache-hit-rate', data.cache_hit_rate || 0, 'cache-hit-trend', data.cache_hit_trend || 0);
                
                // 更新趋势图表
                updateTrendChart(data.trend_data || []);
                
                // 更新异常列表
                updateRecentAnomalies(data.recent_anomalies || []);
                
            } catch (error) {
                console.error('加载概览数据失败:', error);
                showError('加载数据失败，请稍后重试');
            }
        }
        
        // 更新指标卡片
        function updateMetricCard(valueId, value, trendId, trendValue) {
            const valueElement = document.getElementById(valueId);
            const trendElement = document.getElementById(trendId);
            
            if (valueElement) {
                valueElement.textContent = formatMetricValue(value);
            }
            
            if (trendElement && trendValue !== undefined) {
                const isPositive = trendValue > 0;
                const isNegative = trendValue < 0;
                
                trendElement.className = 'trend-indicator mt-2 ' + 
                    (isPositive ? 'trend-up' : isNegative ? 'trend-down' : 'trend-stable');
                
                const icon = isPositive ? 'fa-arrow-up' : isNegative ? 'fa-arrow-down' : 'fa-minus';
                const sign = isPositive ? '+' : '';
                
                trendElement.innerHTML = `<i class="fas ${icon}"></i> ${sign}${trendValue.toFixed(1)}%`;
            }
        }
        
        // 格式化指标值
        function formatMetricValue(value) {
            if (typeof value === 'number') {
                if (value >= 1000000) {
                    return (value / 1000000).toFixed(1) + 'M';
                } else if (value >= 1000) {
                    return (value / 1000).toFixed(1) + 'K';
                } else {
                    return value.toFixed(1);
                }
            }
            return value;
        }
        
        // 更新趋势图表
        function updateTrendChart(data) {
            if (!trendChart || !data.length) return;
            
            const labels = data.map(item => new Date(item.timestamp).toLocaleTimeString());
            const requestData = data.map(item => item.requests || 0);
            const responseTimeData = data.map(item => item.response_time || 0);
            
            trendChart.data.labels = labels;
            trendChart.data.datasets[0].data = requestData;
            trendChart.data.datasets[1].data = responseTimeData;
            
            trendChart.update();
        }
        
        // 更新最近异常
        function updateRecentAnomalies(anomalies) {
            const container = document.getElementById('recent-anomalies');
            
            if (!anomalies.length) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-check-circle fa-2x mb-2 text-success"></i>
                        <p>暂无异常检测到</p>
                    </div>
                `;
                return;
            }
            
            const anomaliesHtml = anomalies.map(anomaly => `
                <div class="anomaly-item severity-${anomaly.severity}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <strong>${anomaly.metric_name}</strong>
                            <p class="mb-1 small">${anomaly.description}</p>
                            <small class="text-muted">${new Date(anomaly.timestamp).toLocaleString()}</small>
                        </div>
                        <span class="badge bg-${getSeverityColor(anomaly.severity)}">${anomaly.severity}</span>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = anomaliesHtml;
        }
        
        // 获取严重程度颜色
        function getSeverityColor(severity) {
            const colors = {
                'critical': 'danger',
                'high': 'warning',
                'medium': 'info',
                'low': 'success'
            };
            return colors[severity] || 'secondary';
        }
        
        // 刷新数据
        function refreshData() {
            console.log('刷新数据');
            if (currentSection === 'overview') {
                loadOverviewData();
            }
        }
        
        // 生成报告
        function generateReport() {
            console.log('生成报告');
            // 这里可以实现报告生成逻辑
            alert('报告生成功能正在开发中');
        }
        
        // 显示错误信息
        function showError(message) {
            // 这里可以实现更好的错误显示
            console.error(message);
        }
        
        // 其他功能函数
        function loadTrendsData() {
            console.log('加载趋势数据');
        }
        
        function loadAnomaliesData() {
            console.log('加载异常数据');
        }
        
        function loadReportsData() {
            console.log('加载报告数据');
        }
        
        function analyzeTrend() {
            console.log('分析趋势');
        }
        
        function createNewReport() {
            console.log('创建新报告');
        }
        
        function exportData() {
            console.log('导出数据');
        }
    </script>
</body>
</html>
