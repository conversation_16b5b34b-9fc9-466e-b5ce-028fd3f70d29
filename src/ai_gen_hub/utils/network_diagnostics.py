"""
网络连接诊断工具

提供网络连接诊断、代理检测、连接测试等功能，帮助识别和解决网络连接问题。
"""

import asyncio
import socket
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from urllib.parse import urlparse
import aiohttp
import dns.resolver
from structlog import get_logger


@dataclass
class NetworkDiagnosticResult:
    """网络诊断结果"""
    success: bool
    message: str
    details: Dict[str, Any]
    suggestions: List[str]
    duration_ms: float


@dataclass
class ProxyConfig:
    """代理配置"""
    http_proxy: Optional[str] = None
    https_proxy: Optional[str] = None
    no_proxy: Optional[str] = None
    
    @classmethod
    def from_env(cls) -> 'ProxyConfig':
        """从环境变量创建代理配置"""
        import os
        return cls(
            http_proxy=os.getenv('HTTP_PROXY') or os.getenv('http_proxy'),
            https_proxy=os.getenv('HTTPS_PROXY') or os.getenv('https_proxy'),
            no_proxy=os.getenv('NO_PROXY') or os.getenv('no_proxy')
        )


class NetworkDiagnostics:
    """网络诊断工具"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.proxy_config = ProxyConfig.from_env()
    
    async def diagnose_connection(self, url: str, timeout: float = 10.0) -> NetworkDiagnosticResult:
        """诊断到指定URL的连接
        
        Args:
            url: 要诊断的URL
            timeout: 超时时间（秒）
            
        Returns:
            NetworkDiagnosticResult: 诊断结果
        """
        start_time = time.time()
        parsed_url = urlparse(url)
        host = parsed_url.hostname
        port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)
        
        details = {
            'url': url,
            'host': host,
            'port': port,
            'scheme': parsed_url.scheme
        }
        
        suggestions = []
        
        try:
            # 1. DNS解析测试
            dns_result = await self._test_dns_resolution(host)
            details['dns'] = dns_result
            
            if not dns_result['success']:
                suggestions.extend(dns_result['suggestions'])
                return NetworkDiagnosticResult(
                    success=False,
                    message=f"DNS解析失败: {dns_result['error']}",
                    details=details,
                    suggestions=suggestions,
                    duration_ms=(time.time() - start_time) * 1000
                )
            
            # 2. TCP连接测试
            tcp_result = await self._test_tcp_connection(host, port, timeout)
            details['tcp'] = tcp_result
            
            if not tcp_result['success']:
                suggestions.extend(tcp_result['suggestions'])
                return NetworkDiagnosticResult(
                    success=False,
                    message=f"TCP连接失败: {tcp_result['error']}",
                    details=details,
                    suggestions=suggestions,
                    duration_ms=(time.time() - start_time) * 1000
                )
            
            # 3. HTTP连接测试
            http_result = await self._test_http_connection(url, timeout)
            details['http'] = http_result
            
            if not http_result['success']:
                suggestions.extend(http_result['suggestions'])
                return NetworkDiagnosticResult(
                    success=False,
                    message=f"HTTP连接失败: {http_result['error']}",
                    details=details,
                    suggestions=suggestions,
                    duration_ms=(time.time() - start_time) * 1000
                )
            
            # 4. 代理检测
            proxy_result = await self._detect_proxy_settings()
            details['proxy'] = proxy_result
            
            return NetworkDiagnosticResult(
                success=True,
                message="网络连接正常",
                details=details,
                suggestions=["连接正常，无需额外配置"],
                duration_ms=(time.time() - start_time) * 1000
            )
            
        except Exception as e:
            self.logger.error("网络诊断过程中发生错误", error=str(e))
            return NetworkDiagnosticResult(
                success=False,
                message=f"诊断过程中发生错误: {str(e)}",
                details=details,
                suggestions=["请检查网络配置或联系系统管理员"],
                duration_ms=(time.time() - start_time) * 1000
            )
    
    async def _test_dns_resolution(self, host: str) -> Dict[str, Any]:
        """测试DNS解析"""
        try:
            resolver = dns.resolver.Resolver()
            resolver.timeout = 5.0
            resolver.lifetime = 5.0
            
            answers = resolver.resolve(host, 'A')
            ips = [str(answer) for answer in answers]
            
            return {
                'success': True,
                'ips': ips,
                'resolver': str(resolver.nameservers),
                'suggestions': []
            }
            
        except dns.resolver.NXDOMAIN:
            return {
                'success': False,
                'error': '域名不存在',
                'suggestions': [
                    '检查域名拼写是否正确',
                    '尝试使用不同的DNS服务器（如*******）'
                ]
            }
        except dns.resolver.Timeout:
            return {
                'success': False,
                'error': 'DNS查询超时',
                'suggestions': [
                    '检查网络连接',
                    '尝试使用不同的DNS服务器',
                    '检查防火墙设置'
                ]
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'suggestions': ['检查DNS配置']
            }
    
    async def _test_tcp_connection(self, host: str, port: int, timeout: float) -> Dict[str, Any]:
        """测试TCP连接"""
        try:
            # 尝试建立TCP连接
            future = asyncio.open_connection(host, port)
            reader, writer = await asyncio.wait_for(future, timeout=timeout)
            
            # 获取连接信息
            sock = writer.get_extra_info('socket')
            local_addr = sock.getsockname()
            remote_addr = sock.getpeername()
            
            writer.close()
            await writer.wait_closed()
            
            return {
                'success': True,
                'local_address': local_addr,
                'remote_address': remote_addr,
                'suggestions': []
            }
            
        except asyncio.TimeoutError:
            return {
                'success': False,
                'error': '连接超时',
                'suggestions': [
                    '检查网络连接',
                    '检查防火墙设置',
                    '尝试使用代理服务器',
                    '联系网络管理员检查网络策略'
                ]
            }
        except ConnectionRefusedError:
            return {
                'success': False,
                'error': '连接被拒绝',
                'suggestions': [
                    '检查目标服务是否正在运行',
                    '检查端口是否正确',
                    '检查防火墙设置'
                ]
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'suggestions': [
                    '检查网络配置',
                    '尝试使用代理服务器'
                ]
            }
    
    async def _test_http_connection(self, url: str, timeout: float) -> Dict[str, Any]:
        """测试HTTP连接"""
        try:
            # 配置代理
            connector_kwargs = {}
            if self.proxy_config.https_proxy and url.startswith('https'):
                connector_kwargs['proxy'] = self.proxy_config.https_proxy
            elif self.proxy_config.http_proxy and url.startswith('http'):
                connector_kwargs['proxy'] = self.proxy_config.http_proxy
            
            timeout_config = aiohttp.ClientTimeout(total=timeout)
            
            async with aiohttp.ClientSession(timeout=timeout_config) as session:
                async with session.head(url, **connector_kwargs) as response:
                    return {
                        'success': True,
                        'status_code': response.status,
                        'headers': dict(response.headers),
                        'proxy_used': connector_kwargs.get('proxy'),
                        'suggestions': []
                    }
                    
        except aiohttp.ClientConnectorError as e:
            return {
                'success': False,
                'error': f'连接错误: {str(e)}',
                'suggestions': [
                    '检查网络连接',
                    '尝试配置HTTP代理',
                    '检查防火墙设置',
                    '联系网络管理员'
                ]
            }
        except asyncio.TimeoutError:
            return {
                'success': False,
                'error': 'HTTP请求超时',
                'suggestions': [
                    '增加超时时间',
                    '检查网络速度',
                    '尝试使用代理服务器'
                ]
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'suggestions': ['检查HTTP配置']
            }
    
    async def _detect_proxy_settings(self) -> Dict[str, Any]:
        """检测代理设置"""
        return {
            'http_proxy': self.proxy_config.http_proxy,
            'https_proxy': self.proxy_config.https_proxy,
            'no_proxy': self.proxy_config.no_proxy,
            'proxy_detected': bool(self.proxy_config.http_proxy or self.proxy_config.https_proxy)
        }
    
    async def diagnose_ai_providers(self) -> Dict[str, NetworkDiagnosticResult]:
        """诊断所有AI供应商的网络连接"""
        providers = {
            'OpenAI': 'https://api.openai.com/v1/models',
            'Google AI': 'https://generativelanguage.googleapis.com/v1beta/models',
            'Anthropic': 'https://api.anthropic.com/v1/messages',
            'Azure OpenAI': 'https://management.azure.com/',
        }
        
        results = {}
        for name, url in providers.items():
            self.logger.info(f"诊断{name}连接", url=url)
            results[name] = await self.diagnose_connection(url)
        
        return results
    
    def generate_diagnostic_report(self, results: Dict[str, NetworkDiagnosticResult]) -> str:
        """生成诊断报告"""
        report = ["# AI Gen Hub 网络连接诊断报告\n"]
        
        # 总体状态
        successful_connections = sum(1 for result in results.values() if result.success)
        total_connections = len(results)
        
        report.append(f"## 总体状态")
        report.append(f"- 成功连接: {successful_connections}/{total_connections}")
        report.append(f"- 连接成功率: {successful_connections/total_connections*100:.1f}%\n")
        
        # 详细结果
        report.append("## 详细诊断结果\n")
        
        for provider, result in results.items():
            status = "✅ 正常" if result.success else "❌ 失败"
            report.append(f"### {provider} {status}")
            report.append(f"- 状态: {result.message}")
            report.append(f"- 耗时: {result.duration_ms:.1f}ms")
            
            if result.details.get('dns'):
                dns_info = result.details['dns']
                if dns_info.get('success'):
                    report.append(f"- DNS解析: ✅ {', '.join(dns_info.get('ips', []))}")
                else:
                    report.append(f"- DNS解析: ❌ {dns_info.get('error', '未知错误')}")
            
            if result.details.get('tcp'):
                tcp_info = result.details['tcp']
                if tcp_info.get('success'):
                    report.append(f"- TCP连接: ✅")
                else:
                    report.append(f"- TCP连接: ❌ {tcp_info.get('error', '未知错误')}")
            
            if result.details.get('http'):
                http_info = result.details['http']
                if http_info.get('success'):
                    report.append(f"- HTTP连接: ✅ (状态码: {http_info.get('status_code')})")
                else:
                    report.append(f"- HTTP连接: ❌ {http_info.get('error', '未知错误')}")
            
            if result.suggestions:
                report.append("- 建议:")
                for suggestion in result.suggestions:
                    report.append(f"  - {suggestion}")
            
            report.append("")
        
        # 代理信息
        if results:
            first_result = next(iter(results.values()))
            proxy_info = first_result.details.get('proxy', {})
            if proxy_info:
                report.append("## 代理配置")
                report.append(f"- HTTP代理: {proxy_info.get('http_proxy', '未配置')}")
                report.append(f"- HTTPS代理: {proxy_info.get('https_proxy', '未配置')}")
                report.append(f"- 代理检测: {'已检测到代理' if proxy_info.get('proxy_detected') else '未检测到代理'}")
                report.append("")
        
        # 总体建议
        if successful_connections < total_connections:
            report.append("## 总体建议")
            report.append("- 检查网络连接和防火墙设置")
            report.append("- 考虑配置HTTP/HTTPS代理")
            report.append("- 联系网络管理员检查网络策略")
            report.append("- 尝试使用VPN或其他网络连接方式")
        
        return "\n".join(report)


# 便捷函数
async def quick_diagnose(url: str) -> NetworkDiagnosticResult:
    """快速诊断单个URL的连接"""
    diagnostics = NetworkDiagnostics()
    return await diagnostics.diagnose_connection(url)


async def diagnose_all_providers() -> Dict[str, NetworkDiagnosticResult]:
    """诊断所有AI供应商的连接"""
    diagnostics = NetworkDiagnostics()
    return await diagnostics.diagnose_ai_providers()
