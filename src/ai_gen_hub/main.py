"""
AI Gen Hub 主入口文件

提供命令行接口和应用程序启动功能
"""

import asyncio
import logging
import sys
from typing import Optional

import click
import uvicorn
from fastapi import FastAPI

from ai_gen_hub.api.app import create_app
from ai_gen_hub.config.settings import get_settings
from ai_gen_hub.core.logging import setup_logging


@click.group()
@click.option("--debug", is_flag=True, help="启用调试模式")
@click.option("--config", type=str, help="配置文件路径")
@click.pass_context
def cli(ctx: click.Context, debug: bool, config: Optional[str]) -> None:
    """AI Gen Hub - 高性能AI服务聚合平台"""
    ctx.ensure_object(dict)
    ctx.obj["debug"] = debug
    ctx.obj["config"] = config
    
    # 设置日志
    setup_logging(debug=debug)


@cli.command()
@click.option("--host", default="0.0.0.0", help="服务器主机地址")
@click.option("--port", default=8001, type=int, help="服务器端口")
@click.option("--workers", default=1, type=int, help="工作进程数量")
@click.option("--reload", is_flag=True, help="启用自动重载（开发模式）")
@click.option("--debug", is_flag=True, help="启用调试模式")
@click.pass_context
def serve(
    ctx: click.Context,
    host: str,
    port: int,
    workers: int,
    reload: bool,
    debug: bool
) -> None:
    """启动API服务器"""
    # 优先使用命令行参数，然后是全局参数
    debug = debug or ctx.obj.get("debug", False)
    config_path = ctx.obj.get("config")
    
    # 加载配置（配置会在create_app内部自动加载）
    # settings = get_settings(config_path)

    # 创建FastAPI应用
    app = create_app()
    
    # 配置uvicorn
    uvicorn_config = {
        "app": app,
        "host": host,
        "port": port,
        "log_level": "debug" if debug else "info",
        "access_log": True,
        "reload": reload or debug,
    }
    
    if not reload and workers > 1:
        uvicorn_config["workers"] = workers
    
    click.echo(f"🚀 启动AI Gen Hub服务器...")
    click.echo(f"📍 地址: http://{host}:{port}")
    click.echo(f"🔧 调试模式: {'开启' if debug else '关闭'}")
    click.echo(f"👥 工作进程: {workers if not reload else 1}")
    
    try:
        uvicorn.run(**uvicorn_config)
    except KeyboardInterrupt:
        click.echo("\n👋 服务器已停止")


@cli.command()
@click.option("--provider", type=str, help="指定供应商进行健康检查")
@click.pass_context
def health_check(ctx: click.Context, provider: Optional[str]) -> None:
    """执行健康检查"""
    async def _health_check():
        from ai_gen_hub.services.hub import AIGenHub
        
        settings = get_settings(ctx.obj.get("config"))
        hub = AIGenHub(settings)
        
        try:
            await hub.initialize()
            
            if provider:
                # 检查特定供应商
                result = await hub.health_check_provider(provider)
                if result:
                    click.echo(f"✅ 供应商 {provider} 健康状态正常")
                    return 0
                else:
                    click.echo(f"❌ 供应商 {provider} 健康状态异常")
                    return 1
            else:
                # 检查所有供应商
                results = await hub.health_check_all()
                healthy_count = sum(1 for r in results.values() if r)
                total_count = len(results)
                
                click.echo(f"🏥 健康检查结果: {healthy_count}/{total_count} 个供应商正常")
                
                for name, status in results.items():
                    status_icon = "✅" if status else "❌"
                    click.echo(f"  {status_icon} {name}")
                
                return 0 if healthy_count == total_count else 1
                
        except Exception as e:
            click.echo(f"❌ 健康检查失败: {e}")
            return 1
        finally:
            await hub.cleanup()
    
    exit_code = asyncio.run(_health_check())
    sys.exit(exit_code)


@cli.command()
@click.option("--format", type=click.Choice(["json", "yaml"]), default="yaml", help="配置文件格式")
@click.option("--output", type=str, help="输出文件路径")
def generate_config(format: str, output: Optional[str]) -> None:
    """生成配置文件模板"""
    from ai_gen_hub.config.generator import generate_config_template
    
    try:
        config_content = generate_config_template(format)
        
        if output:
            with open(output, "w", encoding="utf-8") as f:
                f.write(config_content)
            click.echo(f"✅ 配置文件模板已生成: {output}")
        else:
            click.echo(config_content)
            
    except Exception as e:
        click.echo(f"❌ 生成配置文件失败: {e}")
        sys.exit(1)


def main() -> None:
    """主入口函数"""
    cli()


if __name__ == "__main__":
    main()
