"""
AI Gen Hub 配置管理

提供统一的配置管理功能，支持环境变量、配置文件、动态配置等多种配置源。
使用Pydantic进行配置验证和类型检查。
"""

import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, validator
from pydantic_settings import BaseSettings, SettingsConfigDict

# 导入认证相关配置
from ai_gen_hub.auth.models import AuthConfig, IdPConfig, IdPType

# 获取项目根目录
def get_project_root() -> Path:
    """获取项目根目录路径"""
    current_file = Path(__file__)
    # 从 src/ai_gen_hub/config/settings.py 向上找到项目根目录
    project_root = current_file.parent.parent.parent.parent
    return project_root

PROJECT_ROOT = get_project_root()


class DatabaseConfig(BaseModel):
    """数据库配置"""
    url: str = Field(..., description="数据库连接URL")
    pool_size: int = Field(10, description="连接池大小")
    max_overflow: int = Field(20, description="连接池最大溢出")
    pool_timeout: int = Field(30, description="连接池超时时间")
    echo: bool = Field(False, description="是否打印SQL语句")


class RedisConfig(BaseModel):
    """Redis配置"""
    url: str = Field("redis://localhost:6379/0", description="Redis连接URL")
    password: Optional[str] = Field(None, description="Redis密码")
    db: int = Field(0, description="Redis数据库编号")
    pool_size: int = Field(10, description="连接池大小")
    pool_timeout: int = Field(10, description="连接池超时时间")
    decode_responses: bool = Field(True, description="是否解码响应")


class ProviderConfig(BaseModel):
    """AI供应商配置"""
    api_keys: List[str] = Field(default_factory=list, description="API密钥列表")
    base_url: Optional[str] = Field(None, description="API基础URL")
    timeout: int = Field(120, description="请求超时时间（秒）- 增加到120秒以适应AI模型响应时间")
    max_retries: int = Field(3, description="最大重试次数")
    retry_delay: float = Field(1.0, description="重试延迟时间")
    rate_limit: Optional[int] = Field(None, description="速率限制（请求/分钟）")
    enabled: bool = Field(True, description="是否启用该供应商")
    priority: int = Field(1, description="供应商优先级")
    weight: float = Field(1.0, description="负载均衡权重")
    
    @validator('api_keys', pre=True)
    def parse_api_keys(cls, v):
        """解析API密钥列表"""
        if isinstance(v, str):
            return [key.strip() for key in v.split(',') if key.strip()]
        return v


class CacheConfig(BaseModel):
    """缓存配置"""
    # 内存缓存配置
    memory_cache_size: int = Field(1000, description="内存缓存大小")
    memory_cache_ttl: int = Field(3600, description="内存缓存TTL（秒）")
    
    # Redis缓存配置
    redis_cache_ttl: int = Field(7200, description="Redis缓存TTL（秒）")
    redis_cache_prefix: str = Field("ai_gen_hub:cache:", description="Redis缓存键前缀")
    
    # 缓存策略
    enable_memory_cache: bool = Field(True, description="是否启用内存缓存")
    enable_redis_cache: bool = Field(True, description="是否启用Redis缓存")
    cache_compression: bool = Field(True, description="是否启用缓存压缩")


class MonitoringConfig(BaseModel):
    """监控配置"""
    # 日志配置
    log_level: str = Field("INFO", description="日志级别")
    log_format: str = Field("json", description="日志格式")
    log_file: Optional[str] = Field(None, description="日志文件路径")
    
    # Prometheus配置
    prometheus_enabled: bool = Field(True, description="是否启用Prometheus监控")
    prometheus_port: int = Field(9090, description="Prometheus端口")
    
    # 健康检查配置
    health_check_interval: int = Field(60, description="健康检查间隔（秒）")
    health_check_timeout: int = Field(10, description="健康检查超时（秒）")
    
    # 指标收集
    collect_detailed_metrics: bool = Field(True, description="是否收集详细指标")
    metrics_retention_days: int = Field(30, description="指标保留天数")


class SecurityConfig(BaseModel):
    """安全配置"""
    # JWT配置
    jwt_secret_key: str = Field("ai-gen-hub-default-secret-key-change-in-production", description="JWT密钥")
    jwt_algorithm: str = Field("HS256", description="JWT算法")
    jwt_expire_minutes: int = Field(1440, description="JWT过期时间（分钟）")
    
    # API密钥
    api_key: Optional[str] = Field(None, description="API密钥")
    
    # CORS配置
    cors_origins: List[str] = Field(default_factory=list, description="CORS允许的源")
    cors_allow_credentials: bool = Field(True, description="CORS是否允许凭证")
    
    @validator('cors_origins', pre=True)
    def parse_cors_origins(cls, v):
        """解析CORS源列表"""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',') if origin.strip()]
        return v


class StorageConfig(BaseModel):
    """存储配置"""
    # 本地存储
    local_storage_path: str = Field("./storage", description="本地存储路径")
    
    # S3配置
    s3_bucket: Optional[str] = Field(None, description="S3存储桶")
    s3_region: Optional[str] = Field(None, description="S3区域")
    s3_access_key: Optional[str] = Field(None, description="S3访问密钥")
    s3_secret_key: Optional[str] = Field(None, description="S3密钥")
    s3_endpoint_url: Optional[str] = Field(None, description="S3端点URL")
    
    # MinIO配置
    minio_endpoint: Optional[str] = Field(None, description="MinIO端点")
    minio_access_key: Optional[str] = Field(None, description="MinIO访问密钥")
    minio_secret_key: Optional[str] = Field(None, description="MinIO密钥")
    minio_bucket: Optional[str] = Field(None, description="MinIO存储桶")
    minio_secure: bool = Field(True, description="MinIO是否使用HTTPS")


class PerformanceConfig(BaseModel):
    """性能配置"""
    # 请求限制
    rate_limit_requests: int = Field(100, description="速率限制请求数")
    rate_limit_window: int = Field(60, description="速率限制时间窗口（秒）")
    
    # 超时配置
    request_timeout: int = Field(300, description="请求超时时间（秒）")
    stream_timeout: int = Field(600, description="流式请求超时时间（秒）")
    
    # 并发配置
    max_concurrent_requests: int = Field(100, description="最大并发请求数")
    max_queue_size: int = Field(1000, description="最大队列大小")
    
    # 重试配置
    default_max_retries: int = Field(3, description="默认最大重试次数")
    default_retry_delay: float = Field(1.0, description="默认重试延迟（秒）")
    exponential_backoff: bool = Field(True, description="是否使用指数退避")


class FeatureFlags(BaseModel):
    """功能开关"""
    enable_text_generation: bool = Field(True, description="启用文本生成")
    enable_image_generation: bool = Field(True, description="启用图像生成")
    enable_streaming: bool = Field(True, description="启用流式输出")
    enable_caching: bool = Field(True, description="启用缓存")
    enable_monitoring: bool = Field(True, description="启用监控")
    enable_rate_limiting: bool = Field(True, description="启用速率限制")
    enable_load_balancing: bool = Field(True, description="启用负载均衡")
    enable_circuit_breaker: bool = Field(True, description="启用熔断器")


class Settings(BaseSettings):
    """主配置类"""
    model_config = SettingsConfigDict(
        env_file=[
            PROJECT_ROOT / ".env",  # 项目根目录的 .env 文件
            ".env",  # 当前目录的 .env 文件（备用）
        ],
        env_file_encoding="utf-8",
        env_nested_delimiter="_",  # 支持嵌套环境变量，如 GOOGLE_AI_API_KEYS
        case_sensitive=False,
        extra="ignore"
    )
    
    # 基础配置
    environment: str = Field("development", env="ENVIRONMENT", description="运行环境")
    app_name: str = Field("AI Gen Hub", env="APP_NAME", description="应用名称")
    app_version: str = Field("0.1.0", env="APP_VERSION", description="应用版本")
    debug: bool = Field(False, env="DEBUG", description="调试模式")
    
    # API配置
    api_host: str = Field("0.0.0.0", env="API_HOST", description="API主机")
    api_port: int = Field(8001, env="API_PORT", description="API端口")
    api_workers: int = Field(1, env="API_WORKERS", description="API工作进程数")
    
    # 数据库配置
    database: Optional[DatabaseConfig] = None
    
    # Redis配置
    redis: RedisConfig = Field(default_factory=RedisConfig)
    
    # AI供应商配置 - 直接环境变量字段
    openai_api_keys: str = Field("", env="OPENAI_API_KEYS", description="OpenAI API密钥")
    openai_base_url: Optional[str] = Field(None, env="OPENAI_BASE_URL", description="OpenAI API基础URL")
    openai_timeout: int = Field(60, env="OPENAI_TIMEOUT", description="OpenAI请求超时时间")
    openai_max_retries: int = Field(3, env="OPENAI_MAX_RETRIES", description="OpenAI最大重试次数")
    openai_enabled: bool = Field(True, env="OPENAI_ENABLED", description="是否启用OpenAI")

    google_ai_api_keys: str = Field("", env="GOOGLE_AI_API_KEYS", description="Google AI API密钥")
    google_ai_base_url: Optional[str] = Field(None, env="GOOGLE_AI_BASE_URL", description="Google AI API基础URL")
    google_ai_timeout: int = Field(120, env="GOOGLE_AI_TIMEOUT", description="Google AI请求超时时间（秒）- 增加到120秒")
    google_ai_max_retries: int = Field(3, env="GOOGLE_AI_MAX_RETRIES", description="Google AI最大重试次数")
    google_ai_enabled: bool = Field(True, env="GOOGLE_AI_ENABLED", description="是否启用Google AI")

    anthropic_api_keys: str = Field("", env="ANTHROPIC_API_KEYS", description="Anthropic API密钥")
    anthropic_base_url: Optional[str] = Field(None, env="ANTHROPIC_BASE_URL", description="Anthropic API基础URL")
    anthropic_timeout: int = Field(60, env="ANTHROPIC_TIMEOUT", description="Anthropic请求超时时间")
    anthropic_max_retries: int = Field(3, env="ANTHROPIC_MAX_RETRIES", description="Anthropic最大重试次数")
    anthropic_enabled: bool = Field(True, env="ANTHROPIC_ENABLED", description="是否启用Anthropic")

    dashscope_api_keys: str = Field("", env="DASHSCOPE_API_KEYS", description="DashScope API密钥")
    dashscope_base_url: Optional[str] = Field(None, env="DASHSCOPE_BASE_URL", description="DashScope API基础URL")
    dashscope_timeout: int = Field(60, env="DASHSCOPE_TIMEOUT", description="DashScope请求超时时间")
    dashscope_max_retries: int = Field(3, env="DASHSCOPE_MAX_RETRIES", description="DashScope最大重试次数")
    dashscope_enabled: bool = Field(True, env="DASHSCOPE_ENABLED", description="是否启用DashScope")

    azure_api_keys: str = Field("", env="AZURE_API_KEYS", description="Azure API密钥")
    azure_base_url: Optional[str] = Field(None, env="AZURE_BASE_URL", description="Azure API基础URL")
    azure_timeout: int = Field(60, env="AZURE_TIMEOUT", description="Azure请求超时时间")
    azure_max_retries: int = Field(3, env="AZURE_MAX_RETRIES", description="Azure最大重试次数")
    azure_enabled: bool = Field(True, env="AZURE_ENABLED", description="是否启用Azure")

    # 认证系统配置 - 环境变量字段
    auth_jwt_secret_key: str = Field("", env="AUTH_JWT_SECRET_KEY", description="JWT密钥")
    auth_jwt_algorithm: str = Field("HS256", env="AUTH_JWT_ALGORITHM", description="JWT算法")
    auth_jwt_access_token_expire_minutes: int = Field(30, env="AUTH_JWT_ACCESS_TOKEN_EXPIRE_MINUTES", description="访问令牌过期时间（分钟）")
    auth_jwt_refresh_token_expire_days: int = Field(7, env="AUTH_JWT_REFRESH_TOKEN_EXPIRE_DAYS", description="刷新令牌过期时间（天）")

    auth_session_expire_hours: int = Field(24, env="AUTH_SESSION_EXPIRE_HOURS", description="会话过期时间（小时）")
    auth_session_cookie_name: str = Field("ai_gen_hub_session", env="AUTH_SESSION_COOKIE_NAME", description="会话Cookie名称")
    auth_session_cookie_secure: bool = Field(True, env="AUTH_SESSION_COOKIE_SECURE", description="会话Cookie安全标志")

    auth_rate_limit_enabled: bool = Field(True, env="AUTH_RATE_LIMIT_ENABLED", description="是否启用频率限制")
    auth_rate_limit_requests_per_minute: int = Field(100, env="AUTH_RATE_LIMIT_REQUESTS_PER_MINUTE", description="每分钟请求限制")
    auth_rate_limit_burst_size: int = Field(10, env="AUTH_RATE_LIMIT_BURST_SIZE", description="突发请求大小")

    auth_audit_log_enabled: bool = Field(True, env="AUTH_AUDIT_LOG_ENABLED", description="是否启用审计日志")
    auth_audit_log_retention_days: int = Field(90, env="AUTH_AUDIT_LOG_RETENTION_DAYS", description="审计日志保留天数")

    auth_allow_registration: bool = Field(False, env="AUTH_ALLOW_REGISTRATION", description="是否允许用户注册")
    auth_require_email_verification: bool = Field(True, env="AUTH_REQUIRE_EMAIL_VERIFICATION", description="是否需要邮箱验证")

    # IdP配置环境变量
    auth_default_idp: Optional[str] = Field(None, env="AUTH_DEFAULT_IDP", description="默认身份提供商")

    # Auth0配置
    auth0_enabled: bool = Field(False, env="AUTH0_ENABLED", description="是否启用Auth0")
    auth0_domain: Optional[str] = Field(None, env="AUTH0_DOMAIN", description="Auth0域名")
    auth0_client_id: Optional[str] = Field(None, env="AUTH0_CLIENT_ID", description="Auth0客户端ID")
    auth0_client_secret: Optional[str] = Field(None, env="AUTH0_CLIENT_SECRET", description="Auth0客户端密钥")
    auth0_audience: Optional[str] = Field(None, env="AUTH0_AUDIENCE", description="Auth0受众")

    # Keycloak配置
    keycloak_enabled: bool = Field(False, env="KEYCLOAK_ENABLED", description="是否启用Keycloak")
    keycloak_base_url: Optional[str] = Field(None, env="KEYCLOAK_BASE_URL", description="Keycloak基础URL")
    keycloak_realm: Optional[str] = Field(None, env="KEYCLOAK_REALM", description="Keycloak域")
    keycloak_client_id: Optional[str] = Field(None, env="KEYCLOAK_CLIENT_ID", description="Keycloak客户端ID")
    keycloak_client_secret: Optional[str] = Field(None, env="KEYCLOAK_CLIENT_SECRET", description="Keycloak客户端密钥")

    # Azure AD配置
    azure_ad_enabled: bool = Field(False, env="AZURE_AD_ENABLED", description="是否启用Azure AD")
    azure_ad_tenant_id: Optional[str] = Field(None, env="AZURE_AD_TENANT_ID", description="Azure AD租户ID")
    azure_ad_client_id: Optional[str] = Field(None, env="AZURE_AD_CLIENT_ID", description="Azure AD客户端ID")
    azure_ad_client_secret: Optional[str] = Field(None, env="AZURE_AD_CLIENT_SECRET", description="Azure AD客户端密钥")

    # Google OAuth配置
    google_oauth_enabled: bool = Field(False, env="GOOGLE_OAUTH_ENABLED", description="是否启用Google OAuth")
    google_oauth_client_id: Optional[str] = Field(None, env="GOOGLE_OAUTH_CLIENT_ID", description="Google OAuth客户端ID")
    google_oauth_client_secret: Optional[str] = Field(None, env="GOOGLE_OAUTH_CLIENT_SECRET", description="Google OAuth客户端密钥")

    # GitHub配置
    github_enabled: bool = Field(False, env="GITHUB_ENABLED", description="是否启用GitHub")
    github_client_id: Optional[str] = Field(None, env="GITHUB_CLIENT_ID", description="GitHub客户端ID")
    github_client_secret: Optional[str] = Field(None, env="GITHUB_CLIENT_SECRET", description="GitHub客户端密钥")

    # AI供应商配置对象（从环境变量构建）
    openai: ProviderConfig = Field(default_factory=ProviderConfig)
    google_ai: ProviderConfig = Field(default_factory=ProviderConfig)
    anthropic: ProviderConfig = Field(default_factory=ProviderConfig)
    dashscope: ProviderConfig = Field(default_factory=ProviderConfig)
    azure: ProviderConfig = Field(default_factory=ProviderConfig)

    # 认证配置对象（从环境变量构建）
    auth: AuthConfig = Field(default_factory=AuthConfig)
    
    # 其他配置
    cache: CacheConfig = Field(default_factory=CacheConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    storage: StorageConfig = Field(default_factory=StorageConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    features: FeatureFlags = Field(default_factory=FeatureFlags)
    
    @validator('database', pre=True)
    def parse_database_config(cls, v):
        """解析数据库配置"""
        if isinstance(v, str):
            return DatabaseConfig(url=v)
        elif isinstance(v, dict):
            return DatabaseConfig(**v)
        return v

    def __init__(self, **kwargs):
        """初始化配置，构建供应商配置对象"""
        super().__init__(**kwargs)

        # 从环境变量构建供应商配置
        self._build_provider_configs()

        # 从环境变量构建认证配置
        self._build_auth_config()

    def _build_provider_configs(self):
        """从环境变量构建供应商配置对象

        优先从系统环境变量获取配置，确保配置的安全性和灵活性。
        对每个供应商进行配置验证和安全性检查。
        """
        # OpenAI配置
        self.openai = self._build_single_provider_config(
            "openai",
            self.openai_api_keys,
            self.openai_base_url,
            self.openai_timeout,
            self.openai_max_retries,
            self.openai_enabled
        )

        # Google AI配置
        self.google_ai = self._build_single_provider_config(
            "google_ai",
            self.google_ai_api_keys,
            self.google_ai_base_url,
            self.google_ai_timeout,
            self.google_ai_max_retries,
            self.google_ai_enabled
        )

        # Anthropic配置
        self.anthropic = self._build_single_provider_config(
            "anthropic",
            self.anthropic_api_keys,
            self.anthropic_base_url,
            self.anthropic_timeout,
            self.anthropic_max_retries,
            self.anthropic_enabled
        )

        # DashScope配置
        self.dashscope = self._build_single_provider_config(
            "dashscope",
            self.dashscope_api_keys,
            self.dashscope_base_url,
            self.dashscope_timeout,
            self.dashscope_max_retries,
            self.dashscope_enabled
        )

        # Azure配置
        self.azure = self._build_single_provider_config(
            "azure",
            self.azure_api_keys,
            self.azure_base_url,
            self.azure_timeout,
            self.azure_max_retries,
            self.azure_enabled
        )

    def _build_single_provider_config(
        self,
        provider_name: str,
        api_keys: str,
        base_url: Optional[str],
        timeout: int,
        max_retries: int,
        enabled: bool
    ) -> ProviderConfig:
        """构建单个供应商的配置对象

        Args:
            provider_name: 供应商名称
            api_keys: API密钥字符串
            base_url: API基础URL
            timeout: 请求超时时间
            max_retries: 最大重试次数
            enabled: 是否启用

        Returns:
            供应商配置对象
        """
        # 验证和处理API密钥
        validated_api_keys = self._validate_api_keys(provider_name, api_keys)

        # 验证基础URL
        validated_base_url = self._validate_base_url(provider_name, base_url)

        # 验证超时和重试配置
        validated_timeout = max(1, min(timeout, 600))  # 限制在1-600秒之间
        validated_max_retries = max(0, min(max_retries, 10))  # 限制在0-10次之间

        # 确定是否真正启用（需要有API密钥且配置启用）
        is_enabled = enabled and bool(validated_api_keys)

        return ProviderConfig(
            api_keys=validated_api_keys,
            base_url=validated_base_url,
            timeout=validated_timeout,
            max_retries=validated_max_retries,
            enabled=is_enabled
        )

    def _validate_api_keys(self, provider_name: str, api_keys: str) -> List[str]:
        """验证和处理API密钥

        Args:
            provider_name: 供应商名称
            api_keys: API密钥字符串

        Returns:
            验证后的API密钥列表
        """
        if not api_keys or not api_keys.strip():
            return []

        # 分割多个API密钥
        keys = [key.strip() for key in api_keys.split(',') if key.strip()]

        # 验证API密钥格式
        validated_keys = []
        for key in keys:
            if self._is_valid_api_key(provider_name, key):
                validated_keys.append(key)
            else:
                # 记录警告但不抛出异常，允许系统继续运行
                print(f"⚠️ 警告: {provider_name} 的API密钥格式可能不正确: {key[:8]}...")

        return validated_keys

    def _is_valid_api_key(self, provider_name: str, api_key: str) -> bool:
        """验证API密钥格式

        Args:
            provider_name: 供应商名称
            api_key: API密钥

        Returns:
            是否有效
        """
        if not api_key or len(api_key) < 8:
            return False

        # 基本的格式验证
        provider_patterns = {
            "openai": lambda k: k.startswith("sk-") and len(k) > 20,
            "google_ai": lambda k: k.startswith("AIza") and len(k) > 30,
            "anthropic": lambda k: k.startswith("sk-ant-") and len(k) > 20,
            "dashscope": lambda k: len(k) > 20,  # DashScope密钥格式较灵活
            "azure": lambda k: len(k) > 20,  # Azure密钥格式较灵活
        }

        validator = provider_patterns.get(provider_name)
        if validator:
            return validator(api_key)

        # 默认验证：长度大于8
        return len(api_key) > 8

    def _validate_base_url(self, provider_name: str, base_url: Optional[str]) -> Optional[str]:
        """验证基础URL

        Args:
            provider_name: 供应商名称
            base_url: 基础URL

        Returns:
            验证后的基础URL
        """
        if not base_url:
            return None

        # 基本的URL格式验证
        if not (base_url.startswith("http://") or base_url.startswith("https://")):
            print(f"⚠️ 警告: {provider_name} 的基础URL格式可能不正确: {base_url}")
            return base_url  # 仍然返回，允许用户自定义

        return base_url.rstrip("/")  # 移除末尾的斜杠

    def get_provider_config(self, provider_name: str) -> Optional[ProviderConfig]:
        """获取指定供应商的配置
        
        Args:
            provider_name: 供应商名称
            
        Returns:
            供应商配置或None
        """
        provider_configs = {
            "openai": self.openai,
            "google_ai": self.google_ai,
            "anthropic": self.anthropic,
            "dashscope": self.dashscope,
            "azure": self.azure,
        }
        return provider_configs.get(provider_name.lower())
    
    def get_enabled_providers(self) -> List[str]:
        """获取启用的供应商列表
        
        Returns:
            启用的供应商名称列表
        """
        enabled_providers = []
        for name, config in [
            ("openai", self.openai),
            ("google_ai", self.google_ai),
            ("anthropic", self.anthropic),
            ("dashscope", self.dashscope),
            ("azure", self.azure),
        ]:
            if config.enabled and config.api_keys:
                enabled_providers.append(name)
        return enabled_providers

    def validate_all_configs(self) -> Dict[str, Any]:
        """验证所有配置的有效性

        Returns:
            验证结果摘要
        """
        validation_result = {
            "valid": True,
            "warnings": [],
            "errors": [],
            "providers": {},
            "security": {},
            "summary": {}
        }

        # 验证AI供应商配置
        for provider_name in ["openai", "google_ai", "anthropic", "dashscope", "azure"]:
            provider_config = self.get_provider_config(provider_name)
            if provider_config:
                provider_validation = self._validate_provider_config(provider_name, provider_config)
                validation_result["providers"][provider_name] = provider_validation

                if not provider_validation["valid"]:
                    validation_result["valid"] = False

                validation_result["warnings"].extend(provider_validation.get("warnings", []))
                validation_result["errors"].extend(provider_validation.get("errors", []))

        # 验证安全配置
        security_validation = self._validate_security_config()
        validation_result["security"] = security_validation
        validation_result["warnings"].extend(security_validation.get("warnings", []))
        validation_result["errors"].extend(security_validation.get("errors", []))

        # 生成摘要
        enabled_providers = self.get_enabled_providers()
        validation_result["summary"] = {
            "enabled_providers_count": len(enabled_providers),
            "enabled_providers": enabled_providers,
            "total_warnings": len(validation_result["warnings"]),
            "total_errors": len(validation_result["errors"]),
            "environment": self.environment,
            "debug_mode": self.debug
        }

        return validation_result

    def _validate_provider_config(self, provider_name: str, config: ProviderConfig) -> Dict[str, Any]:
        """验证单个供应商配置

        Args:
            provider_name: 供应商名称
            config: 供应商配置

        Returns:
            验证结果
        """
        result = {
            "valid": True,
            "warnings": [],
            "errors": [],
            "api_keys_count": len(config.api_keys) if config.api_keys else 0,
            "enabled": config.enabled
        }

        # 检查API密钥
        if config.enabled and not config.api_keys:
            result["valid"] = False
            result["errors"].append(f"{provider_name}: 已启用但缺少API密钥")

        # 检查超时配置
        if config.timeout < 10:
            result["warnings"].append(f"{provider_name}: 超时时间过短 ({config.timeout}秒)")
        elif config.timeout > 300:
            result["warnings"].append(f"{provider_name}: 超时时间过长 ({config.timeout}秒)")

        # 检查重试配置
        if config.max_retries > 5:
            result["warnings"].append(f"{provider_name}: 重试次数过多 ({config.max_retries}次)")

        return result

    def _build_auth_config(self) -> None:
        """从环境变量构建认证配置对象"""
        # 构建IdP配置
        idp_configs = {}

        # Auth0配置
        if self.auth0_enabled and self.auth0_client_id and self.auth0_client_secret:
            idp_configs["auth0"] = IdPConfig(
                name="auth0",
                type=IdPType.AUTH0,
                enabled=self.auth0_enabled,
                client_id=self.auth0_client_id,
                client_secret=self.auth0_client_secret,
                authorization_url=f"https://{self.auth0_domain}/authorize" if self.auth0_domain else "",
                token_url=f"https://{self.auth0_domain}/oauth/token" if self.auth0_domain else "",
                userinfo_url=f"https://{self.auth0_domain}/userinfo" if self.auth0_domain else "",
                jwks_url=f"https://{self.auth0_domain}/.well-known/jwks.json" if self.auth0_domain else "",
                issuer=f"https://{self.auth0_domain}/" if self.auth0_domain else "",
                audience=self.auth0_audience,
                redirect_uri=f"{self._get_base_url()}/auth/callback/auth0",
                metadata={"domain": self.auth0_domain}
            )

        # Keycloak配置
        if self.keycloak_enabled and self.keycloak_client_id and self.keycloak_client_secret:
            idp_configs["keycloak"] = IdPConfig(
                name="keycloak",
                type=IdPType.KEYCLOAK,
                enabled=self.keycloak_enabled,
                client_id=self.keycloak_client_id,
                client_secret=self.keycloak_client_secret,
                redirect_uri=f"{self._get_base_url()}/auth/callback/keycloak",
                metadata={
                    "base_url": self.keycloak_base_url,
                    "realm": self.keycloak_realm or "master"
                }
            )

        # Azure AD配置
        if self.azure_ad_enabled and self.azure_ad_client_id and self.azure_ad_client_secret:
            idp_configs["azure_ad"] = IdPConfig(
                name="azure_ad",
                type=IdPType.AZURE_AD,
                enabled=self.azure_ad_enabled,
                client_id=self.azure_ad_client_id,
                client_secret=self.azure_ad_client_secret,
                redirect_uri=f"{self._get_base_url()}/auth/callback/azure_ad",
                metadata={"tenant_id": self.azure_ad_tenant_id or "common"}
            )

        # Google OAuth配置
        if self.google_oauth_enabled and self.google_oauth_client_id and self.google_oauth_client_secret:
            idp_configs["google_oauth"] = IdPConfig(
                name="google_oauth",
                type=IdPType.GOOGLE_OAUTH,
                enabled=self.google_oauth_enabled,
                client_id=self.google_oauth_client_id,
                client_secret=self.google_oauth_client_secret,
                redirect_uri=f"{self._get_base_url()}/auth/callback/google",
                scopes=["openid", "profile", "email"]
            )

        # GitHub配置
        if self.github_enabled and self.github_client_id and self.github_client_secret:
            idp_configs["github"] = IdPConfig(
                name="github",
                type=IdPType.GITHUB,
                enabled=self.github_enabled,
                client_id=self.github_client_id,
                client_secret=self.github_client_secret,
                authorization_url="https://github.com/login/oauth/authorize",
                token_url="https://github.com/login/oauth/access_token",
                userinfo_url="https://api.github.com/user",
                redirect_uri=f"{self._get_base_url()}/auth/callback/github",
                scopes=["user:email"]
            )

        # 构建认证配置
        self.auth = AuthConfig(
            jwt_secret_key=self.auth_jwt_secret_key or self._generate_default_jwt_secret(),
            jwt_algorithm=self.auth_jwt_algorithm,
            jwt_access_token_expire_minutes=self.auth_jwt_access_token_expire_minutes,
            jwt_refresh_token_expire_days=self.auth_jwt_refresh_token_expire_days,
            session_expire_hours=self.auth_session_expire_hours,
            session_cookie_name=self.auth_session_cookie_name,
            session_cookie_secure=self.auth_session_cookie_secure,
            rate_limit_enabled=self.auth_rate_limit_enabled,
            rate_limit_requests_per_minute=self.auth_rate_limit_requests_per_minute,
            rate_limit_burst_size=self.auth_rate_limit_burst_size,
            audit_log_enabled=self.auth_audit_log_enabled,
            audit_log_retention_days=self.auth_audit_log_retention_days,
            idp_configs=idp_configs,
            default_idp=self.auth_default_idp,
            allow_registration=self.auth_allow_registration,
            require_email_verification=self.auth_require_email_verification
        )

    def _get_base_url(self) -> str:
        """获取应用基础URL"""
        protocol = "https" if self.environment == "production" else "http"
        return f"{protocol}://{self.api_host}:{self.api_port}"

    def _generate_default_jwt_secret(self) -> str:
        """生成默认JWT密钥"""
        import secrets
        return secrets.token_urlsafe(32)

    def _validate_security_config(self) -> Dict[str, Any]:
        """验证安全配置

        Returns:
            验证结果
        """
        result = {
            "valid": True,
            "warnings": [],
            "errors": []
        }

        # 检查JWT密钥
        if self.security.jwt_secret_key == "ai-gen-hub-default-secret-key-change-in-production":
            if self.environment == "production":
                result["valid"] = False
                result["errors"].append("生产环境必须更改默认JWT密钥")
            else:
                result["warnings"].append("建议更改默认JWT密钥")

        # 检查API密钥
        if not self.security.api_key and self.environment == "production":
            result["warnings"].append("生产环境建议设置API密钥")

        return result

    def get_config_security_summary(self) -> Dict[str, Any]:
        """获取配置安全摘要（脱敏处理）

        Returns:
            安全摘要信息
        """
        def mask_value(value: str, show_chars: int = 4) -> str:
            """脱敏处理"""
            if not value:
                return "未设置"
            if len(value) <= show_chars * 2:
                return "*" * len(value)
            return value[:show_chars] + "*" * (len(value) - show_chars * 2) + value[-show_chars:]

        summary = {
            "environment": self.environment,
            "debug_mode": self.debug,
            "security": {
                "jwt_secret_key": mask_value(self.security.jwt_secret_key),
                "api_key": mask_value(self.security.api_key or ""),
                "cors_origins_count": len(self.security.cors_origins)
            },
            "providers": {}
        }

        # 添加供应商配置摘要
        for provider_name in ["openai", "google_ai", "anthropic", "dashscope", "azure"]:
            config = self.get_provider_config(provider_name)
            if config:
                summary["providers"][provider_name] = {
                    "enabled": config.enabled,
                    "api_keys_count": len(config.api_keys) if config.api_keys else 0,
                    "base_url": config.base_url or "默认",
                    "timeout": config.timeout,
                    "max_retries": config.max_retries
                }

        return summary


# 全局配置实例
_settings: Optional[Settings] = None


def get_settings(config_path: Optional[str] = None, debug_logging: bool = False) -> Settings:
    """获取配置实例

    Args:
        config_path: 配置文件路径
        debug_logging: 是否启用调试日志

    Returns:
        配置实例
    """
    global _settings

    if _settings is None:
        if debug_logging:
            print(f"🔧 正在加载配置...")
            print(f"   项目根目录: {PROJECT_ROOT}")
            print(f"   .env 文件路径: {PROJECT_ROOT / '.env'}")
            print(f"   .env 文件存在: {(PROJECT_ROOT / '.env').exists()}")
            if config_path:
                print(f"   指定配置文件: {config_path}")

        if config_path and Path(config_path).exists():
            # 从配置文件加载
            if debug_logging:
                print(f"   从指定配置文件加载: {config_path}")
            _settings = Settings(_env_file=config_path)
        else:
            # 从环境变量和默认 .env 文件加载
            if debug_logging:
                print(f"   从默认配置加载")
            _settings = Settings()

        if debug_logging:
            print(f"✅ 配置加载完成:")
            print(f"   环境: {_settings.environment}")
            print(f"   调试模式: {_settings.debug}")
            print(f"   应用名称: {_settings.app_name}")

    return _settings


def reload_settings(config_path: Optional[str] = None, debug_logging: bool = False) -> Settings:
    """重新加载配置

    Args:
        config_path: 配置文件路径
        debug_logging: 是否启用调试日志

    Returns:
        新的配置实例
    """
    global _settings
    _settings = None
    return get_settings(config_path, debug_logging)
