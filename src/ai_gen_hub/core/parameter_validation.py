"""
参数验证工具模块

提供文本生成API参数的预验证功能，在发送到外部API之前检查参数有效性，
避免不必要的API调用并提供更友好的错误消息。
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)


def validate_function_name(name: str) -> <PERSON>ple[bool, Optional[str]]:
    """验证函数名称是否符合Google AI API要求
    
    Google AI API要求：
    - 必须以字母或下划线开头
    - 只能包含字母数字(a-z, A-Z, 0-9)、下划线(_)、点(.)、破折号(-)
    - 最大长度64字符
    - 不能为空
    
    Args:
        name: 函数名称
        
    Returns:
        Tuple[bool, Optional[str]]: (是否有效, 错误消息)
    """
    if not name:
        return False, "函数名称不能为空"
    
    if not isinstance(name, str):
        return False, "函数名称必须是字符串"
    
    if len(name) > 64:
        return False, f"函数名称长度不能超过64字符，当前长度: {len(name)}"
    
    # 检查是否以字母或下划线开头
    if not re.match(r'^[a-zA-Z_]', name):
        return False, "函数名称必须以字母(a-z, A-Z)或下划线(_)开头"
    
    # 检查是否只包含允许的字符
    if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_.-]*$', name):
        return False, "函数名称只能包含字母数字(a-z, A-Z, 0-9)、下划线(_)、点(.)、破折号(-)"
    
    return True, None


def is_empty_or_placeholder_dict(obj: Any) -> bool:
    """检查对象是否为空字典或占位符字典
    
    识别以下模式：
    - {}
    - {"additionalProp1": {}}
    - {"additionalProp1": {}, "additionalProp2": {}}
    - 只包含空值的字典
    
    Args:
        obj: 要检查的对象
        
    Returns:
        bool: 是否为空或占位符字典
    """
    if not isinstance(obj, dict):
        return False
    
    if not obj:  # 空字典
        return True
    
    # 检查是否只包含additionalProp*键且值为空字典
    for key, value in obj.items():
        if key.startswith('additionalProp'):
            if not isinstance(value, dict) or value:
                return False
        else:
            # 如果有非additionalProp的键，则不是占位符
            return False
    
    return True


def validate_function_definition(func: Dict[str, Any]) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
    """验证单个函数定义
    
    Args:
        func: 函数定义字典
        
    Returns:
        Tuple[bool, Optional[str], Optional[Dict[str, Any]]]: 
        (是否有效, 错误消息, 清理后的函数定义)
    """
    if is_empty_or_placeholder_dict(func):
        return False, "空的函数定义", None
    
    # 检查必需字段
    if 'name' not in func:
        return False, "函数定义缺少'name'字段", None
    
    # 验证函数名称
    name_valid, name_error = validate_function_name(func['name'])
    if not name_valid:
        return False, f"函数'{func['name']}'名称无效: {name_error}", None
    
    # 创建清理后的函数定义
    cleaned_func = {
        'name': func['name'].strip(),
        'description': func.get('description', '').strip() or f"函数 {func['name']}"
    }
    
    # 保留参数定义（如果存在且有效）
    if 'parameters' in func and func['parameters'] and not is_empty_or_placeholder_dict(func['parameters']):
        cleaned_func['parameters'] = func['parameters']
    
    return True, None, cleaned_func


def clean_and_validate_functions(functions: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[str]]:
    """清理和验证函数列表
    
    Args:
        functions: 函数定义列表
        
    Returns:
        Tuple[List[Dict[str, Any]], List[str]]: (有效的函数列表, 错误消息列表)
    """
    if not functions:
        return [], []
    
    valid_functions = []
    errors = []
    
    for i, func in enumerate(functions):
        try:
            is_valid, error_msg, cleaned_func = validate_function_definition(func)
            
            if is_valid and cleaned_func:
                # 检查函数名称是否重复
                existing_names = [f['name'] for f in valid_functions]
                if cleaned_func['name'] in existing_names:
                    errors.append(f"函数[{i}]: 函数名称'{cleaned_func['name']}'重复")
                    continue
                
                valid_functions.append(cleaned_func)
                logger.debug(f"函数[{i}]: '{cleaned_func['name']}' 验证通过")
            else:
                errors.append(f"函数[{i}]: {error_msg}")
                logger.warning(f"函数[{i}]: {error_msg}")
                
        except Exception as e:
            errors.append(f"函数[{i}]: 验证时发生异常: {str(e)}")
            logger.error(f"函数[{i}]: 验证异常: {e}")
    
    return valid_functions, errors


def validate_tool_definition(tool: Dict[str, Any]) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
    """验证单个工具定义
    
    Args:
        tool: 工具定义字典
        
    Returns:
        Tuple[bool, Optional[str], Optional[Dict[str, Any]]]: 
        (是否有效, 错误消息, 清理后的工具定义)
    """
    if is_empty_or_placeholder_dict(tool):
        return False, "空的工具定义", None
    
    # 工具可能包含function定义
    if 'function' in tool:
        func_valid, func_error, cleaned_func = validate_function_definition(tool['function'])
        if not func_valid:
            return False, f"工具中的函数定义无效: {func_error}", None
        
        cleaned_tool = {
            'type': tool.get('type', 'function'),
            'function': cleaned_func
        }
        return True, None, cleaned_tool
    
    # 其他类型的工具（如内置工具）
    if 'type' in tool and tool['type'] in ['code_execution', 'google_search']:
        return True, None, tool
    
    return False, "不支持的工具类型或缺少必需字段", None


def clean_and_validate_tools(tools: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[str]]:
    """清理和验证工具列表
    
    Args:
        tools: 工具定义列表
        
    Returns:
        Tuple[List[Dict[str, Any]], List[str]]: (有效的工具列表, 错误消息列表)
    """
    if not tools:
        return [], []
    
    valid_tools = []
    errors = []
    
    for i, tool in enumerate(tools):
        try:
            is_valid, error_msg, cleaned_tool = validate_tool_definition(tool)
            
            if is_valid and cleaned_tool:
                valid_tools.append(cleaned_tool)
                logger.debug(f"工具[{i}]: 验证通过")
            else:
                errors.append(f"工具[{i}]: {error_msg}")
                logger.warning(f"工具[{i}]: {error_msg}")
                
        except Exception as e:
            errors.append(f"工具[{i}]: 验证时发生异常: {str(e)}")
            logger.error(f"工具[{i}]: 验证异常: {e}")
    
    return valid_tools, errors


def validate_parameters_for_provider(
    functions: Optional[List[Dict[str, Any]]] = None,
    tools: Optional[List[Dict[str, Any]]] = None,
    provider: str = "google_ai"
) -> Tuple[bool, List[str], Dict[str, Any]]:
    """为特定供应商验证参数
    
    Args:
        functions: 函数列表
        tools: 工具列表  
        provider: 供应商名称
        
    Returns:
        Tuple[bool, List[str], Dict[str, Any]]: 
        (是否全部有效, 错误消息列表, 清理后的参数)
    """
    all_errors = []
    cleaned_params = {}
    
    # 验证函数
    if functions:
        valid_functions, func_errors = clean_and_validate_functions(functions)
        if func_errors:
            all_errors.extend(func_errors)
        if valid_functions:
            cleaned_params['functions'] = valid_functions
            logger.info(f"清理后保留 {len(valid_functions)} 个有效函数")
    
    # 验证工具
    if tools:
        valid_tools, tool_errors = clean_and_validate_tools(tools)
        if tool_errors:
            all_errors.extend(tool_errors)
        if valid_tools:
            cleaned_params['tools'] = valid_tools
            logger.info(f"清理后保留 {len(valid_tools)} 个有效工具")
    
    is_valid = len(all_errors) == 0
    return is_valid, all_errors, cleaned_params
