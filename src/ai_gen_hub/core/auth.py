"""
认证与授权核心模块

提供权限装饰器、依赖注入、认证工具等功能
"""

from functools import wraps
from typing import List, Optional, Callable, Any
from uuid import UUID

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from ai_gen_hub.core.models.auth import User, Permission, TokenPayload
from ai_gen_hub.core.exceptions import AuthenticationError, AuthorizationError
from ai_gen_hub.services.auth_service import AuthService


# =============================================================================
# 全局认证服务实例
# =============================================================================

_auth_service: Optional[AuthService] = None


def get_auth_service() -> AuthService:
    """获取认证服务实例"""
    global _auth_service
    if _auth_service is None:
        # 这里应该从配置中获取参数
        _auth_service = AuthService(
            jwt_secret_key="dev-secret-key-change-in-production",
            jwt_algorithm="HS256",
            access_token_expire_minutes=30,
            refresh_token_expire_days=7
        )
    return _auth_service


def set_auth_service(auth_service: AuthService):
    """设置认证服务实例"""
    global _auth_service
    _auth_service = auth_service


# =============================================================================
# FastAPI依赖注入
# =============================================================================

security = HTTPBearer(auto_error=False)


async def get_current_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
) -> User:
    """获取当前用户（依赖注入）"""
    
    # 首先检查请求状态中是否已有用户信息（来自中间件）
    if hasattr(request.state, "user"):
        user_info = request.state.user
        if user_info and user_info.get("type") == "jwt":
            user_id = user_info.get("user_id")
            if user_id:
                user = auth_service.get_user_by_id(UUID(user_id))
                if user and user.is_active():
                    return user
    
    # 如果没有凭证，抛出认证错误
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要认证",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # 验证JWT Token
        payload = auth_service.verify_token(credentials.credentials)
        
        # 获取用户
        user = auth_service.get_user_by_id(UUID(payload.sub))
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if not user.is_active():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户未激活",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return user
        
    except (AuthenticationError, ValueError) as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """获取当前活跃用户"""
    if not current_user.is_active():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户未激活"
        )
    return current_user


async def get_current_admin_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """获取当前管理员用户"""
    if not current_user.is_admin():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


def get_optional_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
) -> Optional[User]:
    """获取可选用户（不强制认证）"""
    try:
        # 检查请求状态中的用户信息
        if hasattr(request.state, "user"):
            user_info = request.state.user
            if user_info and user_info.get("type") in ["jwt", "api_key"]:
                user_id = user_info.get("user_id")
                if user_id:
                    user = auth_service.get_user_by_id(UUID(user_id))
                    if user and user.is_active():
                        return user
        
        # 尝试从JWT Token获取用户
        if credentials:
            payload = auth_service.verify_token(credentials.credentials)
            user = auth_service.get_user_by_id(UUID(payload.sub))
            if user and user.is_active():
                return user
        
        return None
        
    except Exception:
        return None


# =============================================================================
# 权限检查依赖
# =============================================================================

def require_permission(permission: Permission):
    """要求特定权限的依赖"""
    def permission_dependency(
        current_user: User = Depends(get_current_active_user),
        auth_service: AuthService = Depends(get_auth_service)
    ) -> User:
        if not auth_service.check_permission(current_user, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"需要权限: {permission.value}"
            )
        return current_user
    
    return permission_dependency


def require_any_permission(permissions: List[Permission]):
    """要求任意权限的依赖"""
    def permission_dependency(
        current_user: User = Depends(get_current_active_user),
        auth_service: AuthService = Depends(get_auth_service)
    ) -> User:
        if not current_user.has_any_permission(permissions):
            perm_names = [p.value for p in permissions]
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"需要以下任意权限: {', '.join(perm_names)}"
            )
        return current_user
    
    return permission_dependency


def require_all_permissions(permissions: List[Permission]):
    """要求所有权限的依赖"""
    def permission_dependency(
        current_user: User = Depends(get_current_active_user),
        auth_service: AuthService = Depends(get_auth_service)
    ) -> User:
        if not current_user.has_all_permissions(permissions):
            perm_names = [p.value for p in permissions]
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"需要以下所有权限: {', '.join(perm_names)}"
            )
        return current_user
    
    return permission_dependency


def require_quota():
    """要求用户有可用配额的依赖"""
    def quota_dependency(
        current_user: User = Depends(get_current_active_user),
        auth_service: AuthService = Depends(get_auth_service)
    ) -> User:
        if not auth_service.check_quota(current_user.id):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="API配额已用完"
            )
        return current_user
    
    return quota_dependency


# =============================================================================
# 权限装饰器
# =============================================================================

def permission_required(permission: Permission):
    """权限装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取用户
            user = kwargs.get('current_user')
            if not user:
                raise AuthorizationError("未找到用户信息")
            
            auth_service = get_auth_service()
            if not auth_service.check_permission(user, permission):
                raise AuthorizationError(f"需要权限: {permission.value}")
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def admin_required(func: Callable) -> Callable:
    """管理员权限装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        user = kwargs.get('current_user')
        if not user:
            raise AuthorizationError("未找到用户信息")
        
        if not user.is_admin():
            raise AuthorizationError("需要管理员权限")
        
        return await func(*args, **kwargs)
    return wrapper


def quota_required(func: Callable) -> Callable:
    """配额检查装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        user = kwargs.get('current_user')
        if not user:
            raise AuthorizationError("未找到用户信息")
        
        auth_service = get_auth_service()
        if not auth_service.check_quota(user.id):
            raise AuthorizationError("API配额已用完")
        
        return await func(*args, **kwargs)
    return wrapper


# =============================================================================
# 工具函数
# =============================================================================

def get_user_id_from_request(request: Request) -> Optional[str]:
    """从请求中获取用户ID"""
    if hasattr(request.state, "user"):
        user_info = request.state.user
        return user_info.get("user_id") if user_info else None
    return None


def get_user_permissions_from_request(request: Request) -> List[Permission]:
    """从请求中获取用户权限"""
    if hasattr(request.state, "user"):
        user_info = request.state.user
        if user_info and user_info.get("type") == "jwt":
            payload = user_info.get("payload", {})
            permissions = payload.get("permissions", [])
            return [Permission(p) for p in permissions if p in Permission.__members__.values()]
    return []


def check_permission_in_request(request: Request, permission: Permission) -> bool:
    """检查请求中的用户是否有指定权限"""
    user_permissions = get_user_permissions_from_request(request)
    return permission in user_permissions


# =============================================================================
# 认证中间件辅助函数
# =============================================================================

def create_user_context(user: User) -> dict:
    """创建用户上下文信息"""
    return {
        "type": "user",
        "user_id": str(user.id),
        "username": user.username,
        "role": user.role.value,
        "permissions": [p.value for p in user.permissions],
        "is_active": user.is_active(),
        "is_admin": user.is_admin()
    }


def create_api_key_context(api_key, user: User) -> dict:
    """创建API密钥上下文信息"""
    return {
        "type": "api_key",
        "user_id": str(user.id),
        "username": user.username,
        "api_key_id": str(api_key.id),
        "api_key_name": api_key.name,
        "permissions": [p.value for p in api_key.permissions],
        "rate_limit": api_key.rate_limit
    }
