"""
AI工作流执行引擎

负责工作流的执行、调度和状态管理
"""

import asyncio
import time
import traceback
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

from ai_gen_hub.core.logging import get_logger
from ai_gen_hub.core.exceptions import AIGenHubException
from .models import (
    Workflow,
    WorkflowNode,
    WorkflowExecution,
    NodeStatus,
    WorkflowStatus,
    NodeType
)
from .nodes import NodeExecutorFactory


class WorkflowEngine:
    """工作流执行引擎"""
    
    def __init__(self, max_concurrent_executions: int = 10):
        self.logger = get_logger(__name__)
        self.max_concurrent_executions = max_concurrent_executions
        self.executor_factory = NodeExecutorFactory()
        
        # 执行状态管理
        self.running_executions: Dict[str, WorkflowExecution] = {}
        self.execution_semaphore = asyncio.Semaphore(max_concurrent_executions)
        
        # 事件回调
        self.event_callbacks: Dict[str, List[Callable]] = {
            'workflow_started': [],
            'workflow_completed': [],
            'workflow_failed': [],
            'node_started': [],
            'node_completed': [],
            'node_failed': []
        }
    
    def add_event_callback(self, event: str, callback: Callable):
        """添加事件回调"""
        if event in self.event_callbacks:
            self.event_callbacks[event].append(callback)
    
    async def execute_workflow(
        self,
        workflow: Workflow,
        input_data: Dict[str, Any],
        context: Dict[str, Any] = None,
        executor_id: str = None
    ) -> WorkflowExecution:
        """执行工作流"""
        # 验证工作流
        validation_errors = workflow.validate()
        if validation_errors:
            raise AIGenHubException(f"工作流验证失败: {', '.join(validation_errors)}")
        
        # 创建执行实例
        execution = WorkflowExecution(
            id=f"exec_{int(time.time())}_{workflow.id[:8]}",
            workflow_id=workflow.id,
            status=WorkflowStatus.RUNNING,
            start_time=datetime.now(),
            input_data=input_data,
            context=context or {},
            executor_id=executor_id
        )
        
        # 获取执行信号量
        async with self.execution_semaphore:
            try:
                self.running_executions[execution.id] = execution
                
                # 触发工作流开始事件
                await self._trigger_event('workflow_started', execution, workflow)
                
                self.logger.info(
                    "开始执行工作流",
                    workflow_id=workflow.id,
                    execution_id=execution.id,
                    workflow_name=workflow.metadata.name
                )
                
                # 执行工作流
                await self._execute_workflow_internal(workflow, execution)
                
                # 设置完成状态
                execution.end_time = datetime.now()
                execution.calculate_duration()
                
                if execution.status == WorkflowStatus.RUNNING:
                    execution.status = WorkflowStatus.COMPLETED
                    await self._trigger_event('workflow_completed', execution, workflow)
                
                self.logger.info(
                    "工作流执行完成",
                    workflow_id=workflow.id,
                    execution_id=execution.id,
                    duration=execution.duration,
                    status=execution.status.value
                )
                
            except Exception as e:
                execution.status = WorkflowStatus.FAILED
                execution.error_message = str(e)
                execution.error_details = {
                    'traceback': traceback.format_exc(),
                    'timestamp': datetime.now().isoformat()
                }
                execution.end_time = datetime.now()
                execution.calculate_duration()
                
                await self._trigger_event('workflow_failed', execution, workflow)
                
                self.logger.error(
                    "工作流执行失败",
                    workflow_id=workflow.id,
                    execution_id=execution.id,
                    error=str(e)
                )
                
            finally:
                # 清理执行状态
                if execution.id in self.running_executions:
                    del self.running_executions[execution.id]
        
        return execution
    
    async def _execute_workflow_internal(self, workflow: Workflow, execution: WorkflowExecution):
        """内部工作流执行逻辑"""
        # 初始化节点状态
        for node in workflow.nodes:
            node.status = NodeStatus.PENDING
            node.input_data = None
            node.output_data = None
            node.start_time = None
            node.end_time = None
            node.error_message = None
        
        # 获取输入节点
        input_nodes = workflow.get_input_nodes()
        
        # 设置输入数据
        for input_node in input_nodes:
            input_node.input_data = execution.input_data
            input_node.output_data = execution.input_data
            input_node.status = NodeStatus.COMPLETED
        
        # 执行节点（拓扑排序）
        executed_nodes = set()
        
        while len(executed_nodes) < len(workflow.nodes):
            # 找到可以执行的节点
            ready_nodes = []
            for node in workflow.nodes:
                if node.id not in executed_nodes and self._is_node_ready(workflow, node, executed_nodes):
                    ready_nodes.append(node)
            
            if not ready_nodes:
                # 检查是否有失败的节点
                failed_nodes = [node for node in workflow.nodes if node.status == NodeStatus.FAILED]
                if failed_nodes:
                    raise AIGenHubException(f"节点执行失败: {failed_nodes[0].config.name}")
                
                # 检查是否有循环等待
                pending_nodes = [node for node in workflow.nodes if node.id not in executed_nodes]
                if pending_nodes:
                    raise AIGenHubException("工作流存在循环依赖或无法执行的节点")
                
                break
            
            # 并行执行准备好的节点
            tasks = []
            for node in ready_nodes:
                task = asyncio.create_task(self._execute_node(workflow, node, execution))
                tasks.append((node, task))
            
            # 等待所有节点完成
            for node, task in tasks:
                try:
                    await task
                    executed_nodes.add(node.id)
                except Exception as e:
                    node.status = NodeStatus.FAILED
                    node.error_message = str(e)
                    executed_nodes.add(node.id)
                    
                    # 如果是关键节点失败，停止整个工作流
                    if not node.config.parameters.get('continue_on_error', False):
                        raise
        
        # 收集输出数据
        output_nodes = workflow.get_output_nodes()
        output_data = {}
        
        for output_node in output_nodes:
            if output_node.output_data:
                output_data[output_node.config.name] = output_node.output_data
        
        execution.output_data = output_data
    
    def _is_node_ready(self, workflow: Workflow, node: WorkflowNode, executed_nodes: set) -> bool:
        """检查节点是否准备好执行"""
        if node.status != NodeStatus.PENDING:
            return False
        
        # 输入节点总是准备好的
        if node.type == NodeType.INPUT:
            return True
        
        # 检查所有前置节点是否已完成
        prev_nodes = workflow.get_previous_nodes(node.id)
        for prev_node in prev_nodes:
            if prev_node.id not in executed_nodes or prev_node.status not in [NodeStatus.COMPLETED, NodeStatus.SKIPPED]:
                return False
        
        return True
    
    async def _execute_node(self, workflow: Workflow, node: WorkflowNode, execution: WorkflowExecution):
        """执行单个节点"""
        node.status = NodeStatus.RUNNING
        node.start_time = datetime.now()
        
        # 触发节点开始事件
        await self._trigger_event('node_started', execution, workflow, node)
        
        self.logger.info(
            "开始执行节点",
            node_id=node.id,
            node_name=node.config.name,
            node_type=node.type.value,
            execution_id=execution.id
        )
        
        try:
            # 准备输入数据
            input_data = await self._prepare_node_input(workflow, node)
            node.input_data = input_data
            
            # 获取节点执行器
            executor = self.executor_factory.get_executor(node.type)
            if not executor:
                raise AIGenHubException(f"不支持的节点类型: {node.type}")
            
            # 执行节点
            output_data = await executor.execute(node, input_data, execution.context)
            node.output_data = output_data
            node.status = NodeStatus.COMPLETED
            
            # 更新执行统计
            node.execution_count += 1
            if node.end_time and node.start_time:
                duration = (node.end_time - node.start_time).total_seconds()
                node.total_duration += duration
            
            # 触发节点完成事件
            await self._trigger_event('node_completed', execution, workflow, node)
            
            self.logger.info(
                "节点执行完成",
                node_id=node.id,
                node_name=node.config.name,
                execution_id=execution.id
            )
            
        except Exception as e:
            node.status = NodeStatus.FAILED
            node.error_message = str(e)
            
            # 触发节点失败事件
            await self._trigger_event('node_failed', execution, workflow, node)
            
            self.logger.error(
                "节点执行失败",
                node_id=node.id,
                node_name=node.config.name,
                error=str(e),
                execution_id=execution.id
            )
            
            raise
        
        finally:
            node.end_time = datetime.now()
            
            # 记录节点执行信息
            execution.set_node_execution(node.id, {
                'status': node.status.value,
                'start_time': node.start_time.isoformat() if node.start_time else None,
                'end_time': node.end_time.isoformat() if node.end_time else None,
                'duration': (node.end_time - node.start_time).total_seconds() if node.start_time and node.end_time else None,
                'error_message': node.error_message,
                'input_data': node.input_data,
                'output_data': node.output_data
            })
    
    async def _prepare_node_input(self, workflow: Workflow, node: WorkflowNode) -> Dict[str, Any]:
        """准备节点输入数据"""
        input_data = {}
        
        # 获取前置节点的输出
        prev_nodes = workflow.get_previous_nodes(node.id)
        
        if not prev_nodes:
            # 没有前置节点，使用空输入
            return input_data
        
        # 合并前置节点的输出
        for prev_node in prev_nodes:
            if prev_node.output_data:
                # 根据连接配置决定如何合并数据
                connection = None
                for conn in workflow.connections:
                    if conn.source_node_id == prev_node.id and conn.target_node_id == node.id:
                        connection = conn
                        break
                
                if connection and connection.source_port != "output":
                    # 使用指定的输出端口
                    port_data = prev_node.output_data.get(connection.source_port)
                    if port_data is not None:
                        input_data[connection.target_port] = port_data
                else:
                    # 使用全部输出数据
                    if isinstance(prev_node.output_data, dict):
                        input_data.update(prev_node.output_data)
                    else:
                        input_data['input'] = prev_node.output_data
        
        return input_data
    
    async def _trigger_event(self, event: str, execution: WorkflowExecution, workflow: Workflow, node: WorkflowNode = None):
        """触发事件回调"""
        callbacks = self.event_callbacks.get(event, [])
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(execution, workflow, node)
                else:
                    callback(execution, workflow, node)
            except Exception as e:
                self.logger.error(f"事件回调执行失败: {event} - {e}")
    
    async def cancel_execution(self, execution_id: str) -> bool:
        """取消工作流执行"""
        execution = self.running_executions.get(execution_id)
        if not execution:
            return False
        
        execution.status = WorkflowStatus.CANCELLED
        execution.end_time = datetime.now()
        execution.calculate_duration()
        
        self.logger.info(f"工作流执行已取消: {execution_id}")
        return True
    
    def get_execution_status(self, execution_id: str) -> Optional[WorkflowExecution]:
        """获取执行状态"""
        return self.running_executions.get(execution_id)
    
    def get_running_executions(self) -> List[WorkflowExecution]:
        """获取正在运行的执行实例"""
        return list(self.running_executions.values())
