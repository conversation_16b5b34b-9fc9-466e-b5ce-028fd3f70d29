"""
工作流节点执行器

定义各种类型节点的执行逻辑
"""

import asyncio
import json
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from ai_gen_hub.core.logging import get_logger
from ai_gen_hub.core.exceptions import AIGenHubException
from .models import WorkflowNode, NodeType


class BaseNodeExecutor(ABC):
    """节点执行器基类"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    @abstractmethod
    async def execute(self, node: WorkflowNode, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """执行节点"""
        pass
    
    def validate_input(self, node: WorkflowNode, input_data: Dict[str, Any]):
        """验证输入数据"""
        if node.config.input_schema:
            # 这里可以添加JSON Schema验证
            pass
    
    def validate_output(self, node: WorkflowNode, output_data: Dict[str, Any]):
        """验证输出数据"""
        if node.config.output_schema:
            # 这里可以添加JSON Schema验证
            pass


class InputNodeExecutor(BaseNodeExecutor):
    """输入节点执行器"""
    
    async def execute(self, node: WorkflowNode, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """输入节点直接返回输入数据"""
        return input_data


class OutputNodeExecutor(BaseNodeExecutor):
    """输出节点执行器"""
    
    async def execute(self, node: WorkflowNode, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """输出节点直接返回输入数据"""
        return input_data


class TextGenerationNodeExecutor(BaseNodeExecutor):
    """文本生成节点执行器"""
    
    async def execute(self, node: WorkflowNode, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """执行文本生成"""
        # 获取参数
        prompt = input_data.get('prompt') or node.config.parameters.get('prompt', '')
        model = node.config.parameters.get('model', 'gpt-3.5-turbo')
        max_tokens = node.config.parameters.get('max_tokens', 1000)
        temperature = node.config.parameters.get('temperature', 0.7)
        
        self.logger.info(f"执行文本生成: {prompt[:100]}...")
        
        # 模拟文本生成
        await asyncio.sleep(2)
        
        generated_text = f"Generated response for: {prompt[:50]}... (using {model})"
        
        return {
            'text': generated_text,
            'model': model,
            'tokens_used': len(generated_text.split()),
            'prompt': prompt
        }


class ImageGenerationNodeExecutor(BaseNodeExecutor):
    """图像生成节点执行器"""
    
    async def execute(self, node: WorkflowNode, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """执行图像生成"""
        # 获取参数
        prompt = input_data.get('prompt') or node.config.parameters.get('prompt', '')
        model = node.config.parameters.get('model', 'dall-e-3')
        size = node.config.parameters.get('size', '1024x1024')
        quality = node.config.parameters.get('quality', 'standard')
        
        self.logger.info(f"执行图像生成: {prompt[:100]}...")
        
        # 模拟图像生成
        await asyncio.sleep(3)
        
        return {
            'image_url': f"https://example.com/generated_image_{int(time.time())}.png",
            'prompt': prompt,
            'model': model,
            'size': size,
            'quality': quality
        }


class ImageEditNodeExecutor(BaseNodeExecutor):
    """图像编辑节点执行器"""
    
    async def execute(self, node: WorkflowNode, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """执行图像编辑"""
        # 获取参数
        image_url = input_data.get('image_url') or input_data.get('image')
        instruction = input_data.get('instruction') or node.config.parameters.get('instruction', '')
        edit_type = node.config.parameters.get('edit_type', 'inpaint')
        
        if not image_url:
            raise AIGenHubException("图像编辑节点需要输入图像")
        
        self.logger.info(f"执行图像编辑: {instruction}")
        
        # 模拟图像编辑
        await asyncio.sleep(4)
        
        return {
            'edited_image_url': f"https://example.com/edited_image_{int(time.time())}.png",
            'original_image_url': image_url,
            'instruction': instruction,
            'edit_type': edit_type
        }


class TransformNodeExecutor(BaseNodeExecutor):
    """数据转换节点执行器"""
    
    async def execute(self, node: WorkflowNode, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """执行数据转换"""
        transform_script = node.config.parameters.get('transform_script', '')
        
        if not transform_script:
            # 如果没有转换脚本，直接返回输入数据
            return input_data
        
        try:
            # 创建安全的执行环境
            safe_globals = {
                '__builtins__': {},
                'len': len,
                'str': str,
                'int': int,
                'float': float,
                'bool': bool,
                'list': list,
                'dict': dict,
                'json': json,
                'input_data': input_data,
                'context': context
            }
            
            # 执行转换脚本
            exec(transform_script, safe_globals)
            
            # 获取输出数据
            output_data = safe_globals.get('output_data', input_data)
            
            return output_data
            
        except Exception as e:
            raise AIGenHubException(f"数据转换执行失败: {e}")


class FilterNodeExecutor(BaseNodeExecutor):
    """数据过滤节点执行器"""
    
    async def execute(self, node: WorkflowNode, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """执行数据过滤"""
        filter_condition = node.config.parameters.get('filter_condition', 'True')
        
        try:
            # 创建安全的执行环境
            safe_globals = {
                '__builtins__': {},
                'len': len,
                'str': str,
                'int': int,
                'float': float,
                'bool': bool,
                'input_data': input_data,
                'context': context
            }
            
            # 评估过滤条件
            result = eval(filter_condition, safe_globals)
            
            if result:
                return input_data
            else:
                # 过滤条件不满足，返回空数据
                return {}
                
        except Exception as e:
            raise AIGenHubException(f"数据过滤执行失败: {e}")


class ConditionNodeExecutor(BaseNodeExecutor):
    """条件节点执行器"""
    
    async def execute(self, node: WorkflowNode, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """执行条件判断"""
        conditions = node.config.conditions or []
        
        for condition in conditions:
            condition_expr = condition.get('condition', 'False')
            output_port = condition.get('output_port', 'default')
            
            try:
                # 创建安全的执行环境
                safe_globals = {
                    '__builtins__': {},
                    'len': len,
                    'str': str,
                    'int': int,
                    'float': float,
                    'bool': bool,
                    'input_data': input_data,
                    'context': context
                }
                
                # 评估条件
                result = eval(condition_expr, safe_globals)
                
                if result:
                    return {
                        'condition_result': True,
                        'output_port': output_port,
                        'data': input_data
                    }
                    
            except Exception as e:
                self.logger.error(f"条件评估失败: {condition_expr} - {e}")
        
        # 所有条件都不满足
        return {
            'condition_result': False,
            'output_port': 'default',
            'data': input_data
        }


class MergeNodeExecutor(BaseNodeExecutor):
    """数据合并节点执行器"""
    
    async def execute(self, node: WorkflowNode, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """执行数据合并"""
        merge_strategy = node.config.parameters.get('merge_strategy', 'merge')
        
        if merge_strategy == 'merge':
            # 合并所有输入数据
            merged_data = {}
            if isinstance(input_data, dict):
                merged_data.update(input_data)
            
            return merged_data
            
        elif merge_strategy == 'array':
            # 将输入数据组成数组
            if isinstance(input_data, dict):
                return {'items': list(input_data.values())}
            else:
                return {'items': [input_data]}
                
        else:
            return input_data


class HTTPRequestNodeExecutor(BaseNodeExecutor):
    """HTTP请求节点执行器"""
    
    async def execute(self, node: WorkflowNode, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """执行HTTP请求"""
        import aiohttp
        
        url = node.config.parameters.get('url', '')
        method = node.config.parameters.get('method', 'GET').upper()
        headers = node.config.parameters.get('headers', {})
        timeout = node.config.parameters.get('timeout', 30)
        
        if not url:
            raise AIGenHubException("HTTP请求节点需要指定URL")
        
        # 准备请求数据
        request_data = None
        if method in ['POST', 'PUT', 'PATCH']:
            request_data = input_data
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
                async with session.request(
                    method=method,
                    url=url,
                    json=request_data,
                    headers=headers
                ) as response:
                    response_data = await response.text()
                    
                    try:
                        response_json = await response.json()
                    except:
                        response_json = None
                    
                    return {
                        'status_code': response.status,
                        'headers': dict(response.headers),
                        'text': response_data,
                        'json': response_json,
                        'url': url,
                        'method': method
                    }
                    
        except Exception as e:
            raise AIGenHubException(f"HTTP请求失败: {e}")


class NodeExecutorFactory:
    """节点执行器工厂"""
    
    def __init__(self):
        self.executors = {
            NodeType.INPUT: InputNodeExecutor(),
            NodeType.OUTPUT: OutputNodeExecutor(),
            NodeType.TEXT_GENERATION: TextGenerationNodeExecutor(),
            NodeType.IMAGE_GENERATION: ImageGenerationNodeExecutor(),
            NodeType.IMAGE_EDIT: ImageEditNodeExecutor(),
            NodeType.TRANSFORM: TransformNodeExecutor(),
            NodeType.FILTER: FilterNodeExecutor(),
            NodeType.CONDITION: ConditionNodeExecutor(),
            NodeType.MERGE: MergeNodeExecutor(),
            NodeType.HTTP_REQUEST: HTTPRequestNodeExecutor(),
        }
    
    def get_executor(self, node_type: NodeType) -> Optional[BaseNodeExecutor]:
        """获取节点执行器"""
        return self.executors.get(node_type)
    
    def register_executor(self, node_type: NodeType, executor: BaseNodeExecutor):
        """注册自定义节点执行器"""
        self.executors[node_type] = executor
