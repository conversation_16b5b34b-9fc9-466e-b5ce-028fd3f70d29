"""
AI Gen Hub 增强错误处理系统

提供统一的错误处理、错误分类、错误恢复和用户友好的错误信息。
"""

import asyncio
import traceback
from typing import Any, Dict, List, Optional, Tuple, Type, Union
from datetime import datetime
from enum import Enum

from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse

from ai_gen_hub.core.exceptions import (
    AIGenHubException,
    AuthenticationError,
    AuthorizationError,
    RateLimitError,
    QuotaExceededError,
    ProviderError,
    ProviderUnavailableError,
    TimeoutError,
    InvalidRequestError,
    ModelNotSupportedError,
    CacheError,
    ConfigurationError
)
from ai_gen_hub.core.logging import LoggerMixin


class ErrorSeverity(str, Enum):
    """错误严重程度"""
    LOW = "low"          # 低级错误，不影响核心功能
    MEDIUM = "medium"    # 中级错误，影响部分功能
    HIGH = "high"        # 高级错误，影响核心功能
    CRITICAL = "critical"  # 严重错误，系统不可用


class ErrorCategory(str, Enum):
    """错误分类"""
    AUTHENTICATION = "authentication"    # 认证相关
    AUTHORIZATION = "authorization"      # 授权相关
    VALIDATION = "validation"           # 参数验证
    PROVIDER = "provider"               # 供应商相关
    NETWORK = "network"                 # 网络相关
    TIMEOUT = "timeout"                 # 超时相关
    RATE_LIMIT = "rate_limit"          # 限流相关
    QUOTA = "quota"                    # 配额相关
    CONFIGURATION = "configuration"    # 配置相关
    CACHE = "cache"                    # 缓存相关
    INTERNAL = "internal"              # 内部错误
    UNKNOWN = "unknown"                # 未知错误


class ErrorContext:
    """错误上下文信息"""
    
    def __init__(
        self,
        request_id: Optional[str] = None,
        user_id: Optional[str] = None,
        provider: Optional[str] = None,
        model: Optional[str] = None,
        endpoint: Optional[str] = None,
        timestamp: Optional[datetime] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ):
        self.request_id = request_id
        self.user_id = user_id
        self.provider = provider
        self.model = model
        self.endpoint = endpoint
        self.timestamp = timestamp or datetime.utcnow()
        self.additional_data = additional_data or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "request_id": self.request_id,
            "user_id": self.user_id,
            "provider": self.provider,
            "model": self.model,
            "endpoint": self.endpoint,
            "timestamp": self.timestamp.isoformat(),
            "additional_data": self.additional_data
        }


class ErrorInfo:
    """错误信息封装"""
    
    def __init__(
        self,
        error_code: str,
        message: str,
        category: ErrorCategory,
        severity: ErrorSeverity,
        retryable: bool = False,
        retry_after: Optional[int] = None,
        suggestions: Optional[List[str]] = None,
        context: Optional[ErrorContext] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.error_code = error_code
        self.message = message
        self.category = category
        self.severity = severity
        self.retryable = retryable
        self.retry_after = retry_after
        self.suggestions = suggestions or []
        self.context = context
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "error_code": self.error_code,
            "message": self.message,
            "category": self.category.value,
            "severity": self.severity.value,
            "retryable": self.retryable,
            "details": self.details
        }
        
        if self.retry_after:
            result["retry_after"] = self.retry_after
        
        if self.suggestions:
            result["suggestions"] = self.suggestions
        
        if self.context:
            result["context"] = self.context.to_dict()
        
        return result


class EnhancedErrorHandler(LoggerMixin):
    """增强的错误处理器"""
    
    def __init__(self):
        """初始化错误处理器"""
        self.error_mappings = self._build_error_mappings()
        self.error_stats = {}  # 错误统计
    
    def _build_error_mappings(self) -> Dict[Type[Exception], Tuple[ErrorCategory, ErrorSeverity]]:
        """构建异常类型到错误分类的映射"""
        return {
            # 认证和授权错误
            AuthenticationError: (ErrorCategory.AUTHENTICATION, ErrorSeverity.MEDIUM),
            AuthorizationError: (ErrorCategory.AUTHORIZATION, ErrorSeverity.MEDIUM),
            
            # 限流和配额错误
            RateLimitError: (ErrorCategory.RATE_LIMIT, ErrorSeverity.MEDIUM),
            QuotaExceededError: (ErrorCategory.QUOTA, ErrorSeverity.HIGH),
            
            # 供应商错误
            ProviderError: (ErrorCategory.PROVIDER, ErrorSeverity.HIGH),
            ProviderUnavailableError: (ErrorCategory.PROVIDER, ErrorSeverity.HIGH),
            
            # 请求错误
            InvalidRequestError: (ErrorCategory.VALIDATION, ErrorSeverity.LOW),
            ModelNotSupportedError: (ErrorCategory.VALIDATION, ErrorSeverity.LOW),
            
            # 超时错误
            TimeoutError: (ErrorCategory.TIMEOUT, ErrorSeverity.MEDIUM),
            asyncio.TimeoutError: (ErrorCategory.TIMEOUT, ErrorSeverity.MEDIUM),
            
            # 配置错误
            ConfigurationError: (ErrorCategory.CONFIGURATION, ErrorSeverity.HIGH),
            
            # 缓存错误
            CacheError: (ErrorCategory.CACHE, ErrorSeverity.LOW),
            
            # 通用错误
            ValueError: (ErrorCategory.VALIDATION, ErrorSeverity.LOW),
            TypeError: (ErrorCategory.VALIDATION, ErrorSeverity.LOW),
            KeyError: (ErrorCategory.INTERNAL, ErrorSeverity.MEDIUM),
            AttributeError: (ErrorCategory.INTERNAL, ErrorSeverity.MEDIUM),
            ConnectionError: (ErrorCategory.NETWORK, ErrorSeverity.HIGH),
            Exception: (ErrorCategory.UNKNOWN, ErrorSeverity.CRITICAL),
        }
    
    def classify_error(self, exception: Exception) -> Tuple[ErrorCategory, ErrorSeverity]:
        """分类错误"""
        exception_type = type(exception)
        
        # 精确匹配
        if exception_type in self.error_mappings:
            return self.error_mappings[exception_type]
        
        # 继承匹配
        for exc_type, (category, severity) in self.error_mappings.items():
            if isinstance(exception, exc_type):
                return category, severity
        
        # 默认分类
        return ErrorCategory.UNKNOWN, ErrorSeverity.CRITICAL
    
    def generate_suggestions(self, exception: Exception, context: Optional[ErrorContext] = None) -> List[str]:
        """生成错误修复建议"""
        suggestions = []
        
        if isinstance(exception, AuthenticationError):
            suggestions.extend([
                "检查API密钥是否正确配置",
                "确认API密钥是否已过期",
                "验证请求头中的认证信息格式"
            ])
        
        elif isinstance(exception, AuthorizationError):
            suggestions.extend([
                "检查用户权限配置",
                "确认API密钥是否有足够的权限",
                "联系管理员获取必要的访问权限"
            ])
        
        elif isinstance(exception, RateLimitError):
            suggestions.extend([
                "降低请求频率",
                "实施请求队列机制",
                "考虑升级到更高的配额计划"
            ])
        
        elif isinstance(exception, QuotaExceededError):
            suggestions.extend([
                "检查当前配额使用情况",
                "升级到更高的配额计划",
                "优化请求以减少token使用"
            ])
        
        elif isinstance(exception, ProviderUnavailableError):
            suggestions.extend([
                "稍后重试请求",
                "切换到其他可用的AI供应商",
                "检查供应商服务状态页面"
            ])
        
        elif isinstance(exception, TimeoutError) or isinstance(exception, asyncio.TimeoutError):
            suggestions.extend([
                "增加请求超时时间",
                "检查网络连接状况",
                "尝试分批处理大型请求"
            ])
        
        elif isinstance(exception, InvalidRequestError):
            suggestions.extend([
                "检查请求参数格式",
                "参考API文档确认参数要求",
                "验证必需参数是否完整"
            ])
        
        elif isinstance(exception, ModelNotSupportedError):
            suggestions.extend([
                "使用支持的模型名称",
                "查看可用模型列表",
                "检查供应商是否支持该模型"
            ])
        
        elif isinstance(exception, ConfigurationError):
            suggestions.extend([
                "检查配置文件格式",
                "确认所有必需的配置项",
                "重新加载配置文件"
            ])
        
        elif isinstance(exception, CacheError):
            suggestions.extend([
                "检查Redis连接状态",
                "清理缓存数据",
                "重启缓存服务"
            ])
        
        else:
            suggestions.extend([
                "检查系统日志获取详细信息",
                "稍后重试操作",
                "联系技术支持获取帮助"
            ])
        
        return suggestions
    
    def create_error_info(
        self,
        exception: Exception,
        context: Optional[ErrorContext] = None
    ) -> ErrorInfo:
        """创建错误信息对象"""
        category, severity = self.classify_error(exception)
        suggestions = self.generate_suggestions(exception, context)
        
        # 处理AI Gen Hub自定义异常
        if isinstance(exception, AIGenHubException):
            return ErrorInfo(
                error_code=exception.error_code,
                message=exception.message,
                category=category,
                severity=severity,
                retryable=exception.retryable,
                suggestions=suggestions,
                context=context,
                details=exception.details
            )
        
        # 处理特殊异常
        retry_after = None
        if isinstance(exception, RateLimitError) and hasattr(exception, 'retry_after'):
            retry_after = exception.retry_after
        
        # 生成错误码
        error_code = self._generate_error_code(exception, category)
        
        # 生成用户友好的错误消息
        user_message = self._generate_user_message(exception, category)
        
        return ErrorInfo(
            error_code=error_code,
            message=user_message,
            category=category,
            severity=severity,
            retryable=self._is_retryable(exception, category),
            retry_after=retry_after,
            suggestions=suggestions,
            context=context,
            details={"original_error": str(exception)}
        )
    
    def _generate_error_code(self, exception: Exception, category: ErrorCategory) -> str:
        """生成错误码"""
        exception_name = type(exception).__name__
        
        # 特殊映射
        code_mappings = {
            "TimeoutError": "TIMEOUT_ERROR",
            "ConnectionError": "CONNECTION_ERROR",
            "ValueError": "VALIDATION_ERROR",
            "TypeError": "TYPE_ERROR",
            "KeyError": "KEY_ERROR",
            "AttributeError": "ATTRIBUTE_ERROR",
        }
        
        if exception_name in code_mappings:
            return code_mappings[exception_name]
        
        # 基于分类生成
        category_prefixes = {
            ErrorCategory.AUTHENTICATION: "AUTH",
            ErrorCategory.AUTHORIZATION: "AUTHZ",
            ErrorCategory.VALIDATION: "VALIDATION",
            ErrorCategory.PROVIDER: "PROVIDER",
            ErrorCategory.NETWORK: "NETWORK",
            ErrorCategory.TIMEOUT: "TIMEOUT",
            ErrorCategory.RATE_LIMIT: "RATE_LIMIT",
            ErrorCategory.QUOTA: "QUOTA",
            ErrorCategory.CONFIGURATION: "CONFIG",
            ErrorCategory.CACHE: "CACHE",
            ErrorCategory.INTERNAL: "INTERNAL",
            ErrorCategory.UNKNOWN: "UNKNOWN",
        }
        
        prefix = category_prefixes.get(category, "UNKNOWN")
        return f"{prefix}_ERROR"
    
    def _generate_user_message(self, exception: Exception, category: ErrorCategory) -> str:
        """生成用户友好的错误消息"""
        if isinstance(exception, AIGenHubException):
            return exception.message
        
        # 基于分类生成友好消息
        category_messages = {
            ErrorCategory.AUTHENTICATION: "认证失败，请检查API密钥",
            ErrorCategory.AUTHORIZATION: "权限不足，请联系管理员",
            ErrorCategory.VALIDATION: "请求参数验证失败",
            ErrorCategory.PROVIDER: "AI服务提供商暂时不可用",
            ErrorCategory.NETWORK: "网络连接异常",
            ErrorCategory.TIMEOUT: "请求处理超时",
            ErrorCategory.RATE_LIMIT: "请求频率超限，请稍后重试",
            ErrorCategory.QUOTA: "配额已用完，请升级计划",
            ErrorCategory.CONFIGURATION: "系统配置错误",
            ErrorCategory.CACHE: "缓存服务异常",
            ErrorCategory.INTERNAL: "系统内部错误",
            ErrorCategory.UNKNOWN: "未知错误，请联系技术支持",
        }
        
        base_message = category_messages.get(category, "系统错误")
        
        # 添加具体错误信息（如果有用）
        error_str = str(exception)
        if error_str and len(error_str) < 100:  # 只有简短的错误信息才添加
            return f"{base_message}: {error_str}"
        
        return base_message
    
    def _is_retryable(self, exception: Exception, category: ErrorCategory) -> bool:
        """判断错误是否可重试"""
        if isinstance(exception, AIGenHubException):
            return exception.retryable
        
        # 基于分类判断
        retryable_categories = {
            ErrorCategory.PROVIDER,
            ErrorCategory.NETWORK,
            ErrorCategory.TIMEOUT,
            ErrorCategory.RATE_LIMIT,
            ErrorCategory.CACHE,
        }
        
        non_retryable_categories = {
            ErrorCategory.AUTHENTICATION,
            ErrorCategory.AUTHORIZATION,
            ErrorCategory.VALIDATION,
            ErrorCategory.QUOTA,
            ErrorCategory.CONFIGURATION,
        }
        
        if category in retryable_categories:
            return True
        elif category in non_retryable_categories:
            return False
        else:
            # 对于未知错误，保守地设为不可重试
            return False
    
    def handle_exception(
        self,
        exception: Exception,
        request: Optional[Request] = None,
        context: Optional[ErrorContext] = None
    ) -> JSONResponse:
        """处理异常并返回JSON响应"""
        # 创建上下文（如果没有提供）
        if not context and request:
            context = ErrorContext(
                request_id=getattr(request.state, "request_id", None),
                endpoint=request.url.path,
                additional_data={
                    "method": request.method,
                    "user_agent": request.headers.get("user-agent"),
                }
            )
        
        # 创建错误信息
        error_info = self.create_error_info(exception, context)
        
        # 记录错误日志
        self._log_error(exception, error_info, context)
        
        # 更新错误统计
        self._update_error_stats(error_info)
        
        # 确定HTTP状态码
        status_code = self._get_http_status_code(exception, error_info.category)
        
        # 构建响应
        response_data = {
            "error": error_info.to_dict(),
            "timestamp": datetime.utcnow().isoformat(),
        }
        
        if context and context.request_id:
            response_data["request_id"] = context.request_id
        
        return JSONResponse(
            status_code=status_code,
            content=response_data
        )
    
    def _log_error(
        self,
        exception: Exception,
        error_info: ErrorInfo,
        context: Optional[ErrorContext]
    ) -> None:
        """记录错误日志"""
        log_data = {
            "error_code": error_info.error_code,
            "category": error_info.category.value,
            "severity": error_info.severity.value,
            "retryable": error_info.retryable,
            "exception_type": type(exception).__name__,
            "exception_message": str(exception),
        }
        
        if context:
            log_data.update(context.to_dict())
        
        # 根据严重程度选择日志级别
        if error_info.severity == ErrorSeverity.CRITICAL:
            self.logger.critical("严重错误", **log_data, exc_info=True)
        elif error_info.severity == ErrorSeverity.HIGH:
            self.logger.error("高级错误", **log_data, exc_info=True)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            self.logger.warning("中级错误", **log_data)
        else:
            self.logger.info("低级错误", **log_data)
    
    def _update_error_stats(self, error_info: ErrorInfo) -> None:
        """更新错误统计"""
        key = f"{error_info.category.value}:{error_info.error_code}"
        if key not in self.error_stats:
            self.error_stats[key] = {
                "count": 0,
                "first_seen": datetime.utcnow(),
                "last_seen": datetime.utcnow(),
            }
        
        self.error_stats[key]["count"] += 1
        self.error_stats[key]["last_seen"] = datetime.utcnow()
    
    def _get_http_status_code(self, exception: Exception, category: ErrorCategory) -> int:
        """获取HTTP状态码"""
        if isinstance(exception, HTTPException):
            return exception.status_code
        
        if isinstance(exception, AIGenHubException) and hasattr(exception, 'details'):
            status_code = exception.details.get('status_code')
            if status_code:
                return status_code
        
        # 基于分类映射状态码
        status_mappings = {
            ErrorCategory.AUTHENTICATION: 401,
            ErrorCategory.AUTHORIZATION: 403,
            ErrorCategory.VALIDATION: 400,
            ErrorCategory.RATE_LIMIT: 429,
            ErrorCategory.QUOTA: 429,
            ErrorCategory.TIMEOUT: 504,
            ErrorCategory.PROVIDER: 502,
            ErrorCategory.NETWORK: 502,
            ErrorCategory.CONFIGURATION: 500,
            ErrorCategory.CACHE: 500,
            ErrorCategory.INTERNAL: 500,
            ErrorCategory.UNKNOWN: 500,
        }
        
        return status_mappings.get(category, 500)
    
    def get_error_stats(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        return {
            "total_errors": sum(stat["count"] for stat in self.error_stats.values()),
            "error_types": len(self.error_stats),
            "errors_by_category": self.error_stats.copy(),
        }


# 全局错误处理器实例
error_handler = EnhancedErrorHandler()
