"""
AI Gen Hub 统一Chat接口定义

本模块定义了统一的Chat接口抽象层，用于屏蔽不同AI供应商的API差异，
提供一致的对话功能接口。

主要功能：
- 统一的消息格式和数据结构
- 标准化的Chat接口定义
- 流式和非流式响应支持
- 错误处理和异常定义
- 供应商无关的配置参数

设计原则：
- 简单易用：提供直观的API接口
- 高度兼容：支持主流AI供应商的核心功能
- 可扩展性：便于添加新的供应商支持
- 类型安全：使用完整的类型注解

作者：AI Gen Hub Team
创建时间：2025-08-24
"""

import logging as std_logging
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Any, AsyncIterator, Dict, List, Optional, Union
from uuid import uuid4

from pydantic import BaseModel, Field

# 设置日志记录器
logger = std_logging.getLogger(__name__)


# =============================================================================
# 枚举定义
# =============================================================================

class ChatMessageRole(str, Enum):
    """Chat消息角色枚举"""
    SYSTEM = "system"      # 系统消息，用于设置AI助手的行为
    USER = "user"          # 用户消息
    ASSISTANT = "assistant"  # AI助手消息
    FUNCTION = "function"   # 函数调用结果消息（已弃用，使用TOOL）
    TOOL = "tool"          # 工具调用结果消息


class ChatFinishReason(str, Enum):
    """Chat完成原因枚举"""
    STOP = "stop"                    # 自然停止（模型认为对话已完成）
    LENGTH = "length"                # 达到最大长度限制
    CONTENT_FILTER = "content_filter"  # 内容被安全过滤器阻止
    TOOL_CALLS = "tool_calls"        # 模型请求调用工具
    FUNCTION_CALL = "function_call"   # 模型请求调用函数（已弃用）
    ERROR = "error"                  # 发生错误


class ChatStreamEventType(str, Enum):
    """Chat流式事件类型枚举"""
    MESSAGE_START = "message_start"    # 消息开始
    CONTENT_DELTA = "content_delta"    # 内容增量更新
    TOOL_CALL_DELTA = "tool_call_delta"  # 工具调用增量更新
    MESSAGE_STOP = "message_stop"      # 消息结束
    ERROR = "error"                    # 错误事件


# =============================================================================
# 数据模型定义
# =============================================================================

class ChatMessage(BaseModel):
    """统一的Chat消息模型
    
    兼容所有主流AI供应商的消息格式，提供统一的数据结构。
    """
    role: ChatMessageRole = Field(..., description="消息角色")
    content: str = Field(..., description="消息内容")
    name: Optional[str] = Field(None, description="消息发送者名称（可选）")
    tool_calls: Optional[List[Dict[str, Any]]] = Field(None, description="工具调用信息")
    tool_call_id: Optional[str] = Field(None, description="工具调用ID（用于工具响应消息）")
    
    # 扩展字段，用于支持多模态内容
    attachments: Optional[List[Dict[str, Any]]] = Field(None, description="附件信息（图片、文件等）")
    metadata: Optional[Dict[str, Any]] = Field(None, description="消息元数据")

    class Config:
        """Pydantic配置"""
        use_enum_values = True


class ChatConfig(BaseModel):
    """Chat配置参数模型
    
    统一的配置参数，会根据不同供应商进行适配和映射。
    """
    # 基础生成参数
    max_tokens: Optional[int] = Field(None, ge=1, le=32768, description="最大生成token数")
    temperature: float = Field(0.7, ge=0.0, le=2.0, description="生成温度，控制随机性")
    top_p: Optional[float] = Field(None, ge=0.0, le=1.0, description="核采样参数")
    top_k: Optional[int] = Field(None, ge=1, description="Top-K采样参数")
    
    # 惩罚参数
    frequency_penalty: float = Field(0.0, ge=-2.0, le=2.0, description="频率惩罚")
    presence_penalty: float = Field(0.0, ge=-2.0, le=2.0, description="存在惩罚")
    
    # 停止条件
    stop_sequences: Optional[List[str]] = Field(None, description="停止序列列表")
    
    # 流式配置
    stream: bool = Field(False, description="是否启用流式输出")
    
    # 工具和函数调用
    tools: Optional[List[Dict[str, Any]]] = Field(None, description="可用工具列表")
    tool_choice: Optional[Union[str, Dict[str, Any]]] = Field(None, description="工具选择策略")
    
    # 安全和过滤
    safety_settings: Optional[List[Dict[str, Any]]] = Field(None, description="安全设置")
    
    # 供应商特定参数（透传）
    provider_params: Optional[Dict[str, Any]] = Field(None, description="供应商特定参数")

    class Config:
        """Pydantic配置"""
        extra = "allow"  # 允许额外字段


class ChatUsage(BaseModel):
    """Chat使用量统计模型"""
    prompt_tokens: int = Field(0, description="输入token数量")
    completion_tokens: int = Field(0, description="输出token数量")
    total_tokens: int = Field(0, description="总token数量")
    
    # 扩展统计信息
    cache_creation_input_tokens: Optional[int] = Field(None, description="缓存创建输入token数")
    cache_read_input_tokens: Optional[int] = Field(None, description="缓存读取输入token数")

    def __post_init__(self):
        """确保总token数量的一致性"""
        if self.total_tokens == 0:
            self.total_tokens = self.prompt_tokens + self.completion_tokens


class ChatResponse(BaseModel):
    """Chat响应模型"""
    id: str = Field(default_factory=lambda: str(uuid4()), description="响应唯一标识")
    created: datetime = Field(default_factory=datetime.now, description="创建时间")
    model: str = Field(..., description="使用的模型名称")
    provider: str = Field(..., description="供应商名称")
    
    # 响应内容
    message: ChatMessage = Field(..., description="AI助手的回复消息")
    finish_reason: ChatFinishReason = Field(..., description="完成原因")
    
    # 使用量统计
    usage: ChatUsage = Field(..., description="token使用量统计")
    
    # 元数据
    metadata: Optional[Dict[str, Any]] = Field(None, description="响应元数据")

    class Config:
        """Pydantic配置"""
        use_enum_values = True


class ChatStreamChunk(BaseModel):
    """Chat流式响应块模型"""
    id: str = Field(..., description="响应唯一标识")
    event_type: ChatStreamEventType = Field(..., description="事件类型")
    
    # 增量内容
    delta_content: Optional[str] = Field(None, description="内容增量")
    delta_tool_calls: Optional[List[Dict[str, Any]]] = Field(None, description="工具调用增量")
    
    # 完成信息（仅在MESSAGE_STOP事件中）
    finish_reason: Optional[ChatFinishReason] = Field(None, description="完成原因")
    usage: Optional[ChatUsage] = Field(None, description="使用量统计")
    
    # 累积内容（可选，用于客户端方便获取完整内容）
    accumulated_content: Optional[str] = Field(None, description="累积的完整内容")
    
    # 元数据
    metadata: Optional[Dict[str, Any]] = Field(None, description="块元数据")

    class Config:
        """Pydantic配置"""
        use_enum_values = True


# =============================================================================
# 异常定义
# =============================================================================

class ChatError(Exception):
    """Chat相关错误的基类"""
    def __init__(self, message: str, error_code: Optional[str] = None, provider: Optional[str] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.provider = provider


class ChatValidationError(ChatError):
    """Chat参数验证错误"""
    pass


class ChatProviderError(ChatError):
    """Chat供应商错误"""
    pass


class ChatRateLimitError(ChatError):
    """Chat速率限制错误"""
    pass


class ChatContentFilterError(ChatError):
    """Chat内容过滤错误"""
    pass


# =============================================================================
# 核心接口定义
# =============================================================================

class ChatProvider(ABC):
    """Chat供应商抽象基类
    
    定义了所有Chat供应商必须实现的核心接口。
    每个具体的供应商适配器都应该继承这个类并实现相应的方法。
    """
    
    def __init__(self, provider_name: str, config: Dict[str, Any]):
        """初始化Chat供应商
        
        Args:
            provider_name: 供应商名称
            config: 供应商配置
        """
        self.provider_name = provider_name
        self.config = config
        self.logger = std_logging.getLogger(f"{__name__}.{provider_name}")
    
    @abstractmethod
    async def chat(
        self,
        messages: List[ChatMessage],
        config: ChatConfig
    ) -> ChatResponse:
        """发送Chat请求并获取响应
        
        Args:
            messages: 对话消息列表
            config: Chat配置参数
            
        Returns:
            ChatResponse: Chat响应
            
        Raises:
            ChatError: Chat相关错误
        """
        pass
    
    @abstractmethod
    async def chat_stream(
        self,
        messages: List[ChatMessage],
        config: ChatConfig
    ) -> AsyncIterator[ChatStreamChunk]:
        """发送Chat请求并获取流式响应
        
        Args:
            messages: 对话消息列表
            config: Chat配置参数
            
        Yields:
            ChatStreamChunk: Chat流式响应块
            
        Raises:
            ChatError: Chat相关错误
        """
        pass
    
    @abstractmethod
    async def validate_config(self, config: ChatConfig) -> bool:
        """验证配置参数是否有效
        
        Args:
            config: Chat配置参数
            
        Returns:
            bool: 配置是否有效
        """
        pass
    
    @abstractmethod
    async def get_supported_models(self) -> List[str]:
        """获取支持的模型列表
        
        Returns:
            List[str]: 支持的模型名称列表
        """
        pass
    
    async def health_check(self) -> bool:
        """健康检查
        
        Returns:
            bool: 供应商是否健康
        """
        try:
            # 默认实现：尝试获取支持的模型列表
            models = await self.get_supported_models()
            return len(models) > 0
        except Exception as e:
            self.logger.warning(f"健康检查失败: {e}")
            return False


class ChatManager(ABC):
    """Chat管理器抽象基类
    
    负责管理多个Chat供应商，提供统一的Chat服务入口。
    """
    
    @abstractmethod
    async def chat(
        self,
        messages: List[ChatMessage],
        config: ChatConfig,
        provider: Optional[str] = None
    ) -> ChatResponse:
        """发送Chat请求
        
        Args:
            messages: 对话消息列表
            config: Chat配置参数
            provider: 指定供应商（可选，不指定则自动选择）
            
        Returns:
            ChatResponse: Chat响应
        """
        pass
    
    @abstractmethod
    async def chat_stream(
        self,
        messages: List[ChatMessage],
        config: ChatConfig,
        provider: Optional[str] = None
    ) -> AsyncIterator[ChatStreamChunk]:
        """发送Chat流式请求
        
        Args:
            messages: 对话消息列表
            config: Chat配置参数
            provider: 指定供应商（可选，不指定则自动选择）
            
        Yields:
            ChatStreamChunk: Chat流式响应块
        """
        pass
    
    @abstractmethod
    async def get_available_providers(self) -> List[str]:
        """获取可用的供应商列表
        
        Returns:
            List[str]: 可用供应商名称列表
        """
        pass
