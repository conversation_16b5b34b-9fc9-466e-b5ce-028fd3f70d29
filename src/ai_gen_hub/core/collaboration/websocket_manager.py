"""
WebSocket连接管理器

负责WebSocket连接的管理、消息路由和实时通信
"""

import asyncio
import json
import time
from typing import Any, Dict, List, Optional, Set
from datetime import datetime
from collections import defaultdict

from fastapi import WebSocket, WebSocketDisconnect
from ai_gen_hub.core.logging import get_logger
from ai_gen_hub.core.exceptions import AIGenHubException
from .models import UserPresence, PresenceStatus


class WebSocketConnection:
    """WebSocket连接封装"""
    
    def __init__(self, websocket: WebSocket, user_id: str, session_id: str):
        self.websocket = websocket
        self.user_id = user_id
        self.session_id = session_id
        self.connection_id = f"{user_id}_{session_id}_{int(time.time())}"
        self.connected_at = datetime.now()
        self.last_ping = datetime.now()
        self.is_alive = True
    
    async def send_message(self, message: Dict[str, Any]):
        """发送消息"""
        try:
            await self.websocket.send_text(json.dumps(message))
        except Exception as e:
            self.is_alive = False
            raise e
    
    async def send_ping(self):
        """发送心跳"""
        try:
            await self.send_message({
                "type": "ping",
                "timestamp": datetime.now().isoformat()
            })
            self.last_ping = datetime.now()
        except Exception:
            self.is_alive = False
    
    def update_last_ping(self):
        """更新最后心跳时间"""
        self.last_ping = datetime.now()


class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # 连接管理
        self.connections: Dict[str, WebSocketConnection] = {}
        self.session_connections: Dict[str, Set[str]] = defaultdict(set)
        self.user_connections: Dict[str, Set[str]] = defaultdict(set)
        
        # 消息队列
        self.message_queue: asyncio.Queue = asyncio.Queue()
        
        # 心跳管理
        self.heartbeat_interval = 30  # 秒
        self.connection_timeout = 60  # 秒
        
        # 启动后台任务
        self._start_background_tasks()
    
    def _start_background_tasks(self):
        """启动后台任务"""
        asyncio.create_task(self._heartbeat_task())
        asyncio.create_task(self._cleanup_task())
        asyncio.create_task(self._message_processor())
    
    async def connect(self, websocket: WebSocket, user_id: str, session_id: str) -> str:
        """建立WebSocket连接"""
        await websocket.accept()
        
        connection = WebSocketConnection(websocket, user_id, session_id)
        connection_id = connection.connection_id
        
        # 存储连接
        self.connections[connection_id] = connection
        self.session_connections[session_id].add(connection_id)
        self.user_connections[user_id].add(connection_id)
        
        self.logger.info(f"WebSocket连接建立: {connection_id}")
        
        # 发送连接确认
        await connection.send_message({
            "type": "connected",
            "connection_id": connection_id,
            "user_id": user_id,
            "session_id": session_id,
            "timestamp": datetime.now().isoformat()
        })
        
        # 通知其他用户
        await self.broadcast_to_session(session_id, {
            "type": "user_joined",
            "user_id": user_id,
            "connection_id": connection_id,
            "timestamp": datetime.now().isoformat()
        }, exclude_connections={connection_id})
        
        return connection_id
    
    async def disconnect(self, connection_id: str):
        """断开WebSocket连接"""
        connection = self.connections.get(connection_id)
        if not connection:
            return
        
        user_id = connection.user_id
        session_id = connection.session_id
        
        # 移除连接
        del self.connections[connection_id]
        self.session_connections[session_id].discard(connection_id)
        self.user_connections[user_id].discard(connection_id)
        
        # 清理空集合
        if not self.session_connections[session_id]:
            del self.session_connections[session_id]
        if not self.user_connections[user_id]:
            del self.user_connections[user_id]
        
        self.logger.info(f"WebSocket连接断开: {connection_id}")
        
        # 通知其他用户
        await self.broadcast_to_session(session_id, {
            "type": "user_left",
            "user_id": user_id,
            "connection_id": connection_id,
            "timestamp": datetime.now().isoformat()
        })
    
    async def send_to_connection(self, connection_id: str, message: Dict[str, Any]):
        """发送消息到指定连接"""
        connection = self.connections.get(connection_id)
        if connection and connection.is_alive:
            try:
                await connection.send_message(message)
            except Exception as e:
                self.logger.error(f"发送消息失败: {connection_id} - {e}")
                await self.disconnect(connection_id)
    
    async def send_to_user(self, user_id: str, message: Dict[str, Any]):
        """发送消息到用户的所有连接"""
        connection_ids = self.user_connections.get(user_id, set()).copy()
        for connection_id in connection_ids:
            await self.send_to_connection(connection_id, message)
    
    async def broadcast_to_session(
        self, 
        session_id: str, 
        message: Dict[str, Any], 
        exclude_connections: Optional[Set[str]] = None
    ):
        """广播消息到会话中的所有连接"""
        exclude_connections = exclude_connections or set()
        connection_ids = self.session_connections.get(session_id, set()).copy()
        
        for connection_id in connection_ids:
            if connection_id not in exclude_connections:
                await self.send_to_connection(connection_id, message)
    
    async def handle_message(self, connection_id: str, message: str):
        """处理接收到的消息"""
        try:
            data = json.loads(message)
            message_type = data.get("type")
            
            connection = self.connections.get(connection_id)
            if not connection:
                return
            
            # 更新连接活跃时间
            connection.update_last_ping()
            
            # 处理不同类型的消息
            if message_type == "pong":
                # 心跳响应
                pass
            elif message_type == "operation":
                # 协作操作
                await self._handle_operation_message(connection, data)
            elif message_type == "cursor":
                # 光标位置更新
                await self._handle_cursor_message(connection, data)
            elif message_type == "presence":
                # 在线状态更新
                await self._handle_presence_message(connection, data)
            elif message_type == "chat":
                # 聊天消息
                await self._handle_chat_message(connection, data)
            else:
                self.logger.warning(f"未知消息类型: {message_type}")
                
        except json.JSONDecodeError:
            self.logger.error(f"无效的JSON消息: {message}")
        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")
    
    async def _handle_operation_message(self, connection: WebSocketConnection, data: Dict[str, Any]):
        """处理协作操作消息"""
        # 将操作消息加入队列，由协作服务处理
        await self.message_queue.put({
            "type": "operation",
            "connection_id": connection.connection_id,
            "user_id": connection.user_id,
            "session_id": connection.session_id,
            "data": data
        })
    
    async def _handle_cursor_message(self, connection: WebSocketConnection, data: Dict[str, Any]):
        """处理光标位置消息"""
        # 广播光标位置到会话中的其他用户
        await self.broadcast_to_session(connection.session_id, {
            "type": "cursor_update",
            "user_id": connection.user_id,
            "position": data.get("position"),
            "selection_start": data.get("selection_start"),
            "selection_end": data.get("selection_end"),
            "timestamp": datetime.now().isoformat()
        }, exclude_connections={connection.connection_id})
    
    async def _handle_presence_message(self, connection: WebSocketConnection, data: Dict[str, Any]):
        """处理在线状态消息"""
        # 广播在线状态到会话中的其他用户
        await self.broadcast_to_session(connection.session_id, {
            "type": "presence_update",
            "user_id": connection.user_id,
            "status": data.get("status", "online"),
            "timestamp": datetime.now().isoformat()
        }, exclude_connections={connection.connection_id})
    
    async def _handle_chat_message(self, connection: WebSocketConnection, data: Dict[str, Any]):
        """处理聊天消息"""
        # 广播聊天消息到会话中的所有用户
        await self.broadcast_to_session(connection.session_id, {
            "type": "chat_message",
            "user_id": connection.user_id,
            "username": data.get("username", ""),
            "message": data.get("message", ""),
            "timestamp": datetime.now().isoformat()
        })
    
    async def _heartbeat_task(self):
        """心跳任务"""
        while True:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                
                # 发送心跳到所有连接
                connection_ids = list(self.connections.keys())
                for connection_id in connection_ids:
                    connection = self.connections.get(connection_id)
                    if connection and connection.is_alive:
                        try:
                            await connection.send_ping()
                        except Exception:
                            await self.disconnect(connection_id)
                            
            except Exception as e:
                self.logger.error(f"心跳任务错误: {e}")
    
    async def _cleanup_task(self):
        """清理任务"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟清理一次
                
                now = datetime.now()
                timeout_connections = []
                
                # 检查超时连接
                for connection_id, connection in self.connections.items():
                    if (now - connection.last_ping).total_seconds() > self.connection_timeout:
                        timeout_connections.append(connection_id)
                
                # 断开超时连接
                for connection_id in timeout_connections:
                    await self.disconnect(connection_id)
                    
            except Exception as e:
                self.logger.error(f"清理任务错误: {e}")
    
    async def _message_processor(self):
        """消息处理器"""
        while True:
            try:
                # 从队列获取消息
                message = await self.message_queue.get()
                
                # 这里可以将消息转发给协作服务处理
                # 暂时只记录日志
                self.logger.info(f"处理消息: {message['type']} from {message['user_id']}")
                
            except Exception as e:
                self.logger.error(f"消息处理错误: {e}")
    
    def get_session_participants(self, session_id: str) -> List[Dict[str, Any]]:
        """获取会话参与者"""
        participants = []
        connection_ids = self.session_connections.get(session_id, set())
        
        user_connections = defaultdict(list)
        for connection_id in connection_ids:
            connection = self.connections.get(connection_id)
            if connection:
                user_connections[connection.user_id].append(connection)
        
        for user_id, connections in user_connections.items():
            # 取最新的连接信息
            latest_connection = max(connections, key=lambda c: c.connected_at)
            participants.append({
                "user_id": user_id,
                "connection_count": len(connections),
                "connected_at": latest_connection.connected_at.isoformat(),
                "last_ping": latest_connection.last_ping.isoformat(),
                "is_online": latest_connection.is_alive
            })
        
        return participants
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计"""
        return {
            "total_connections": len(self.connections),
            "total_sessions": len(self.session_connections),
            "total_users": len(self.user_connections),
            "active_connections": sum(1 for c in self.connections.values() if c.is_alive)
        }
