"""
操作转换引擎

实现协作编辑中的操作转换算法，解决并发编辑冲突
"""

import copy
from typing import List, Tuple, Optional
from dataclasses import dataclass

from ai_gen_hub.core.logging import get_logger
from .models import RealtimeOperation, OperationType


@dataclass
class TextOperation:
    """文本操作"""
    type: OperationType
    position: int
    content: str = ""
    length: int = 0
    
    def __post_init__(self):
        if self.type == OperationType.DELETE and self.length == 0:
            self.length = len(self.content)
        elif self.type == OperationType.INSERT and self.length == 0:
            self.length = len(self.content)


class OperationTransformEngine:
    """操作转换引擎"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def transform_operations(
        self, 
        op1: RealtimeOperation, 
        op2: RealtimeOperation
    ) -> Tuple[RealtimeOperation, RealtimeOperation]:
        """
        转换两个并发操作
        
        Args:
            op1: 第一个操作
            op2: 第二个操作
            
        Returns:
            转换后的操作对 (op1', op2')
        """
        # 转换为内部操作格式
        text_op1 = self._to_text_operation(op1)
        text_op2 = self._to_text_operation(op2)
        
        # 执行操作转换
        transformed_op1, transformed_op2 = self._transform_text_operations(text_op1, text_op2)
        
        # 转换回原始格式
        result_op1 = self._from_text_operation(transformed_op1, op1)
        result_op2 = self._from_text_operation(transformed_op2, op2)
        
        # 标记为已转换
        result_op1.transformed = True
        result_op1.original_position = op1.position
        result_op2.transformed = True
        result_op2.original_position = op2.position
        
        return result_op1, result_op2
    
    def _transform_text_operations(
        self, 
        op1: TextOperation, 
        op2: TextOperation
    ) -> Tuple[TextOperation, TextOperation]:
        """转换两个文本操作"""
        
        # 插入 vs 插入
        if op1.type == OperationType.INSERT and op2.type == OperationType.INSERT:
            return self._transform_insert_insert(op1, op2)
        
        # 插入 vs 删除
        elif op1.type == OperationType.INSERT and op2.type == OperationType.DELETE:
            return self._transform_insert_delete(op1, op2)
        
        # 删除 vs 插入
        elif op1.type == OperationType.DELETE and op2.type == OperationType.INSERT:
            op2_prime, op1_prime = self._transform_insert_delete(op2, op1)
            return op1_prime, op2_prime
        
        # 删除 vs 删除
        elif op1.type == OperationType.DELETE and op2.type == OperationType.DELETE:
            return self._transform_delete_delete(op1, op2)
        
        # 替换操作转换为删除+插入
        elif op1.type == OperationType.REPLACE or op2.type == OperationType.REPLACE:
            return self._transform_replace_operations(op1, op2)
        
        # 其他操作类型暂不支持转换
        else:
            return op1, op2
    
    def _transform_insert_insert(
        self, 
        op1: TextOperation, 
        op2: TextOperation
    ) -> Tuple[TextOperation, TextOperation]:
        """转换两个插入操作"""
        op1_prime = copy.deepcopy(op1)
        op2_prime = copy.deepcopy(op2)
        
        if op1.position <= op2.position:
            # op1在op2之前或同位置，op2需要向后移动
            op2_prime.position += op1.length
        else:
            # op2在op1之前，op1需要向后移动
            op1_prime.position += op2.length
        
        return op1_prime, op2_prime
    
    def _transform_insert_delete(
        self, 
        insert_op: TextOperation, 
        delete_op: TextOperation
    ) -> Tuple[TextOperation, TextOperation]:
        """转换插入和删除操作"""
        insert_prime = copy.deepcopy(insert_op)
        delete_prime = copy.deepcopy(delete_op)
        
        if insert_op.position <= delete_op.position:
            # 插入在删除之前，删除位置需要向后移动
            delete_prime.position += insert_op.length
        elif insert_op.position >= delete_op.position + delete_op.length:
            # 插入在删除之后，插入位置需要向前移动
            insert_prime.position -= delete_op.length
        else:
            # 插入在删除范围内，插入位置移动到删除开始位置
            insert_prime.position = delete_op.position
        
        return insert_prime, delete_prime
    
    def _transform_delete_delete(
        self, 
        op1: TextOperation, 
        op2: TextOperation
    ) -> Tuple[TextOperation, TextOperation]:
        """转换两个删除操作"""
        op1_prime = copy.deepcopy(op1)
        op2_prime = copy.deepcopy(op2)
        
        # 计算删除范围
        op1_end = op1.position + op1.length
        op2_end = op2.position + op2.length
        
        if op1_end <= op2.position:
            # op1完全在op2之前
            op2_prime.position -= op1.length
        elif op2_end <= op1.position:
            # op2完全在op1之前
            op1_prime.position -= op2.length
        else:
            # 有重叠，需要计算重叠部分
            overlap_start = max(op1.position, op2.position)
            overlap_end = min(op1_end, op2_end)
            overlap_length = max(0, overlap_end - overlap_start)
            
            if op1.position < op2.position:
                # op1开始位置更早
                op1_prime.length -= overlap_length
                op2_prime.position = op1.position
                op2_prime.length -= overlap_length
            else:
                # op2开始位置更早
                op2_prime.length -= overlap_length
                op1_prime.position = op2.position
                op1_prime.length -= overlap_length
        
        return op1_prime, op2_prime
    
    def _transform_replace_operations(
        self, 
        op1: TextOperation, 
        op2: TextOperation
    ) -> Tuple[TextOperation, TextOperation]:
        """转换替换操作"""
        # 将替换操作分解为删除+插入
        if op1.type == OperationType.REPLACE:
            delete_op1 = TextOperation(OperationType.DELETE, op1.position, "", op1.length)
            insert_op1 = TextOperation(OperationType.INSERT, op1.position, op1.content)
            
            if op2.type == OperationType.REPLACE:
                delete_op2 = TextOperation(OperationType.DELETE, op2.position, "", op2.length)
                insert_op2 = TextOperation(OperationType.INSERT, op2.position, op2.content)
                
                # 先转换删除操作
                delete_op1_prime, delete_op2_prime = self._transform_delete_delete(delete_op1, delete_op2)
                
                # 再转换插入操作
                insert_op1_prime, insert_op2_prime = self._transform_insert_insert(insert_op1, insert_op2)
                
                # 合并回替换操作
                op1_prime = copy.deepcopy(op1)
                op1_prime.position = delete_op1_prime.position
                
                op2_prime = copy.deepcopy(op2)
                op2_prime.position = delete_op2_prime.position
                
                return op1_prime, op2_prime
            else:
                # op1是替换，op2是其他操作
                # 简化处理：先删除再插入
                delete_prime, op2_prime = self._transform_text_operations(delete_op1, op2)
                insert_prime, op2_prime = self._transform_text_operations(insert_op1, op2_prime)
                
                op1_prime = copy.deepcopy(op1)
                op1_prime.position = delete_prime.position
                
                return op1_prime, op2_prime
        
        # 如果只有op2是替换操作
        op2_prime, op1_prime = self._transform_replace_operations(op2, op1)
        return op1_prime, op2_prime
    
    def _to_text_operation(self, op: RealtimeOperation) -> TextOperation:
        """转换为文本操作"""
        return TextOperation(
            type=op.operation_type,
            position=op.position,
            content=op.content,
            length=op.length
        )
    
    def _from_text_operation(self, text_op: TextOperation, original_op: RealtimeOperation) -> RealtimeOperation:
        """从文本操作转换回原始操作"""
        result = copy.deepcopy(original_op)
        result.operation_type = text_op.type
        result.position = text_op.position
        result.content = text_op.content
        result.length = text_op.length
        return result
    
    def apply_operation(self, text: str, operation: RealtimeOperation) -> str:
        """应用操作到文本"""
        try:
            if operation.operation_type == OperationType.INSERT:
                return text[:operation.position] + operation.content + text[operation.position:]
            
            elif operation.operation_type == OperationType.DELETE:
                end_pos = operation.position + operation.length
                return text[:operation.position] + text[end_pos:]
            
            elif operation.operation_type == OperationType.REPLACE:
                end_pos = operation.position + operation.length
                return text[:operation.position] + operation.content + text[end_pos:]
            
            else:
                self.logger.warning(f"不支持的操作类型: {operation.operation_type}")
                return text
                
        except Exception as e:
            self.logger.error(f"应用操作失败: {e}")
            return text
    
    def validate_operation(self, text: str, operation: RealtimeOperation) -> bool:
        """验证操作是否有效"""
        try:
            text_length = len(text)
            
            # 检查位置是否有效
            if operation.position < 0 or operation.position > text_length:
                return False
            
            # 检查删除/替换操作的长度
            if operation.operation_type in [OperationType.DELETE, OperationType.REPLACE]:
                if operation.position + operation.length > text_length:
                    return False
            
            return True
            
        except Exception:
            return False
    
    def compose_operations(self, op1: RealtimeOperation, op2: RealtimeOperation) -> Optional[RealtimeOperation]:
        """组合两个连续的操作"""
        # 简单的组合逻辑：如果两个操作位置相邻且类型相同，可以组合
        if (op1.operation_type == op2.operation_type == OperationType.INSERT and 
            op1.position + len(op1.content) == op2.position):
            
            # 组合两个插入操作
            composed = copy.deepcopy(op1)
            composed.content += op2.content
            composed.length = len(composed.content)
            return composed
        
        elif (op1.operation_type == op2.operation_type == OperationType.DELETE and 
              op1.position == op2.position):
            
            # 组合两个删除操作
            composed = copy.deepcopy(op1)
            composed.length += op2.length
            return composed
        
        return None
    
    def invert_operation(self, operation: RealtimeOperation, original_text: str) -> Optional[RealtimeOperation]:
        """生成操作的逆操作"""
        try:
            if operation.operation_type == OperationType.INSERT:
                # 插入的逆操作是删除
                inverted = copy.deepcopy(operation)
                inverted.operation_type = OperationType.DELETE
                inverted.length = len(operation.content)
                inverted.content = ""
                return inverted
            
            elif operation.operation_type == OperationType.DELETE:
                # 删除的逆操作是插入
                deleted_content = original_text[operation.position:operation.position + operation.length]
                inverted = copy.deepcopy(operation)
                inverted.operation_type = OperationType.INSERT
                inverted.content = deleted_content
                inverted.length = 0
                return inverted
            
            elif operation.operation_type == OperationType.REPLACE:
                # 替换的逆操作是替换回原内容
                original_content = original_text[operation.position:operation.position + operation.length]
                inverted = copy.deepcopy(operation)
                inverted.content = original_content
                inverted.length = len(operation.content)
                return inverted
            
            return None
            
        except Exception as e:
            self.logger.error(f"生成逆操作失败: {e}")
            return None
