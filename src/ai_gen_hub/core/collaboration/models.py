"""
实时协作和共享功能 - 核心模型

定义协作会话、共享内容、版本控制等核心数据模型
"""

import time
import uuid
from typing import Any, Dict, List, Optional, Union
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime

from ai_gen_hub.core.interfaces import BaseRequest, BaseResponse


class CollaborationRole(Enum):
    """协作角色"""
    OWNER = "owner"          # 所有者
    EDITOR = "editor"        # 编辑者
    VIEWER = "viewer"        # 查看者
    COMMENTER = "commenter"  # 评论者


class SessionStatus(Enum):
    """会话状态"""
    ACTIVE = "active"        # 活跃
    PAUSED = "paused"        # 暂停
    ENDED = "ended"          # 结束
    ARCHIVED = "archived"    # 已归档


class ContentType(Enum):
    """内容类型"""
    TEXT = "text"            # 文本
    DOCUMENT = "document"    # 文档
    IMAGE = "image"          # 图像
    AUDIO = "audio"          # 音频
    VIDEO = "video"          # 视频
    WORKFLOW = "workflow"    # 工作流
    DATASET = "dataset"      # 数据集


class ShareScope(Enum):
    """共享范围"""
    PRIVATE = "private"      # 私有
    TEAM = "team"           # 团队
    ORGANIZATION = "organization"  # 组织
    PUBLIC = "public"        # 公开
    LINK = "link"           # 链接共享


class OperationType(Enum):
    """操作类型"""
    INSERT = "insert"        # 插入
    DELETE = "delete"        # 删除
    REPLACE = "replace"      # 替换
    FORMAT = "format"        # 格式化
    MOVE = "move"           # 移动
    COMMENT = "comment"      # 评论


class PresenceStatus(Enum):
    """在线状态"""
    ONLINE = "online"        # 在线
    AWAY = "away"           # 离开
    BUSY = "busy"           # 忙碌
    OFFLINE = "offline"      # 离线


@dataclass
class UserPresence:
    """用户在线状态"""
    user_id: str
    username: str
    avatar_url: Optional[str] = None
    status: PresenceStatus = PresenceStatus.ONLINE
    
    # 编辑状态
    cursor_position: Optional[int] = None
    selection_start: Optional[int] = None
    selection_end: Optional[int] = None
    editing_section: Optional[str] = None
    
    # 时间信息
    last_seen: datetime = field(default_factory=datetime.now)
    joined_at: datetime = field(default_factory=datetime.now)
    
    # 客户端信息
    client_id: Optional[str] = None
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None


@dataclass
class CollaborationPermission:
    """协作权限"""
    user_id: str
    role: CollaborationRole
    granted_by: str
    granted_at: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    
    # 细粒度权限
    can_read: bool = True
    can_write: bool = False
    can_comment: bool = False
    can_share: bool = False
    can_manage: bool = False
    
    def __post_init__(self):
        """根据角色设置默认权限"""
        if self.role == CollaborationRole.OWNER:
            self.can_read = True
            self.can_write = True
            self.can_comment = True
            self.can_share = True
            self.can_manage = True
        elif self.role == CollaborationRole.EDITOR:
            self.can_read = True
            self.can_write = True
            self.can_comment = True
            self.can_share = False
            self.can_manage = False
        elif self.role == CollaborationRole.VIEWER:
            self.can_read = True
            self.can_write = False
            self.can_comment = False
            self.can_share = False
            self.can_manage = False
        elif self.role == CollaborationRole.COMMENTER:
            self.can_read = True
            self.can_write = False
            self.can_comment = True
            self.can_share = False
            self.can_manage = False


@dataclass
class RealtimeOperation:
    """实时操作"""
    operation_id: str
    session_id: str
    user_id: str
    
    # 操作信息
    operation_type: OperationType
    position: int
    content: str = ""
    length: int = 0
    
    # 元数据
    timestamp: datetime = field(default_factory=datetime.now)
    sequence_number: int = 0
    parent_operation_id: Optional[str] = None
    
    # 操作转换信息
    transformed: bool = False
    original_position: Optional[int] = None
    transform_context: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.operation_id:
            self.operation_id = str(uuid.uuid4())


@dataclass
class ContentVersion:
    """内容版本"""
    version_id: str
    content_id: str
    version_number: int
    
    # 版本信息
    title: str = ""
    description: str = ""
    content_snapshot: str = ""
    content_delta: Optional[str] = None
    
    # 作者信息
    author_id: str = ""
    author_name: str = ""
    commit_message: str = ""
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    
    # 版本关系
    parent_version_id: Optional[str] = None
    branch_name: str = "main"
    is_merge: bool = False
    merged_from: Optional[str] = None
    
    # 统计信息
    changes_count: int = 0
    additions: int = 0
    deletions: int = 0
    
    def __post_init__(self):
        if not self.version_id:
            self.version_id = str(uuid.uuid4())


@dataclass
class SharedContent:
    """共享内容"""
    content_id: str
    title: str
    content_type: ContentType
    
    # 内容数据
    content_data: str = ""
    content_url: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 所有权
    owner_id: str = ""
    owner_name: str = ""
    
    # 共享设置
    share_scope: ShareScope = ShareScope.PRIVATE
    share_token: Optional[str] = None
    share_url: Optional[str] = None
    
    # 权限管理
    permissions: List[CollaborationPermission] = field(default_factory=list)
    default_role: CollaborationRole = CollaborationRole.VIEWER
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    last_accessed_at: Optional[datetime] = None
    
    # 版本控制
    current_version: int = 1
    versions: List[ContentVersion] = field(default_factory=list)
    
    # 统计信息
    view_count: int = 0
    edit_count: int = 0
    comment_count: int = 0
    
    def __post_init__(self):
        if not self.content_id:
            self.content_id = str(uuid.uuid4())
        
        if self.share_scope == ShareScope.LINK and not self.share_token:
            self.share_token = str(uuid.uuid4())
    
    def get_user_permission(self, user_id: str) -> Optional[CollaborationPermission]:
        """获取用户权限"""
        for permission in self.permissions:
            if permission.user_id == user_id:
                return permission
        return None
    
    def has_permission(self, user_id: str, action: str) -> bool:
        """检查用户权限"""
        if user_id == self.owner_id:
            return True
        
        permission = self.get_user_permission(user_id)
        if not permission:
            return False
        
        return getattr(permission, f"can_{action}", False)


@dataclass
class CollaborationSession:
    """协作会话"""
    session_id: str
    content_id: str
    title: str
    description: str = ""
    
    # 会话状态
    status: SessionStatus = SessionStatus.ACTIVE
    
    # 参与者
    participants: List[UserPresence] = field(default_factory=list)
    max_participants: int = 50
    
    # 创建信息
    created_by: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    
    # 活动信息
    last_activity_at: datetime = field(default_factory=datetime.now)
    auto_end_after: Optional[int] = None  # 分钟
    
    # 会话设置
    allow_anonymous: bool = False
    require_approval: bool = False
    enable_chat: bool = True
    enable_voice: bool = False
    enable_video: bool = False
    
    # 操作历史
    operations: List[RealtimeOperation] = field(default_factory=list)
    operation_sequence: int = 0
    
    def __post_init__(self):
        if not self.session_id:
            self.session_id = str(uuid.uuid4())
    
    def get_participant(self, user_id: str) -> Optional[UserPresence]:
        """获取参与者"""
        for participant in self.participants:
            if participant.user_id == user_id:
                return participant
        return None
    
    def add_participant(self, user_presence: UserPresence) -> bool:
        """添加参与者"""
        if len(self.participants) >= self.max_participants:
            return False
        
        # 检查是否已存在
        existing = self.get_participant(user_presence.user_id)
        if existing:
            # 更新现有参与者信息
            existing.status = user_presence.status
            existing.last_seen = datetime.now()
            existing.client_id = user_presence.client_id
            return True
        
        self.participants.append(user_presence)
        return True
    
    def remove_participant(self, user_id: str) -> bool:
        """移除参与者"""
        for i, participant in enumerate(self.participants):
            if participant.user_id == user_id:
                del self.participants[i]
                return True
        return False
    
    def get_online_participants(self) -> List[UserPresence]:
        """获取在线参与者"""
        return [p for p in self.participants if p.status == PresenceStatus.ONLINE]


@dataclass
class Comment:
    """评论"""
    comment_id: str
    content_id: str
    session_id: Optional[str] = None
    
    # 评论内容
    text: str = ""
    position: Optional[int] = None  # 评论位置
    selection_start: Optional[int] = None
    selection_end: Optional[int] = None
    
    # 作者信息
    author_id: str = ""
    author_name: str = ""
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None
    
    # 评论状态
    is_resolved: bool = False
    resolved_by: Optional[str] = None
    resolved_at: Optional[datetime] = None
    
    # 回复
    parent_comment_id: Optional[str] = None
    replies: List['Comment'] = field(default_factory=list)
    
    def __post_init__(self):
        if not self.comment_id:
            self.comment_id = str(uuid.uuid4())


# 请求和响应模型
@dataclass
class CreateSessionRequest(BaseRequest):
    """创建协作会话请求"""
    content_id: str
    title: str
    description: str = ""
    max_participants: int = 50
    allow_anonymous: bool = False
    require_approval: bool = False


@dataclass
class JoinSessionRequest(BaseRequest):
    """加入会话请求"""
    session_id: str
    user_id: str
    username: str
    avatar_url: Optional[str] = None


@dataclass
class ShareContentRequest(BaseRequest):
    """共享内容请求"""
    content_id: str
    share_scope: str = "private"
    permissions: List[Dict[str, Any]] = field(default_factory=list)
    expires_at: Optional[datetime] = None


@dataclass
class OperationRequest(BaseRequest):
    """操作请求"""
    session_id: str
    operation_type: str
    position: int
    content: str = ""
    length: int = 0


@dataclass
class SessionResponse(BaseResponse):
    """会话响应"""
    session: CollaborationSession


@dataclass
class ContentResponse(BaseResponse):
    """内容响应"""
    content: SharedContent


@dataclass
class OperationResponse(BaseResponse):
    """操作响应"""
    operation: RealtimeOperation
    transformed_operations: List[RealtimeOperation] = field(default_factory=list)
