"""
AI Gen Hub 异常定义

定义了系统中使用的所有自定义异常类，提供了详细的错误分类和处理机制。
这些异常类支持错误码、重试策略和详细的错误信息。
"""

from typing import Any, Dict, Optional


class AIGenHubException(Exception):
    """AI Gen Hub 基础异常类
    
    所有自定义异常的基类，提供统一的错误处理接口。
    """
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        retryable: bool = False
    ):
        """初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误码
            details: 错误详情
            retryable: 是否可重试
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.retryable = retryable
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details,
            "retryable": self.retryable
        }


# =============================================================================
# 配置相关异常
# =============================================================================

class ConfigurationError(AIGenHubException):
    """配置错误异常"""
    
    def __init__(self, message: str, config_key: Optional[str] = None):
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            details={"config_key": config_key} if config_key else {},
            retryable=False
        )


class InvalidConfigurationError(ConfigurationError):
    """无效配置异常"""
    
    def __init__(self, message: str, config_key: Optional[str] = None):
        super().__init__(message, config_key)
        self.error_code = "INVALID_CONFIGURATION"


class MissingConfigurationError(ConfigurationError):
    """缺失配置异常"""
    
    def __init__(self, config_key: str):
        super().__init__(
            message=f"缺少必需的配置项: {config_key}",
            config_key=config_key
        )
        self.error_code = "MISSING_CONFIGURATION"


# =============================================================================
# 供应商相关异常
# =============================================================================

class ProviderError(AIGenHubException):
    """供应商错误异常"""
    
    def __init__(
        self,
        message: str,
        provider_name: Optional[str] = None,
        retryable: bool = True
    ):
        super().__init__(
            message=message,
            error_code="PROVIDER_ERROR",
            details={"provider": provider_name} if provider_name else {},
            retryable=retryable
        )


class ProviderNotFoundError(ProviderError):
    """供应商未找到异常"""
    
    def __init__(self, provider_name: str):
        super().__init__(
            message=f"未找到供应商: {provider_name}",
            provider_name=provider_name,
            retryable=False
        )
        self.error_code = "PROVIDER_NOT_FOUND"


class ProviderUnavailableError(ProviderError):
    """供应商不可用异常"""
    
    def __init__(self, provider_name: str, reason: Optional[str] = None):
        message = f"供应商 {provider_name} 不可用"
        if reason:
            message += f": {reason}"
        
        super().__init__(
            message=message,
            provider_name=provider_name,
            retryable=True
        )
        self.error_code = "PROVIDER_UNAVAILABLE"


class ProviderInitializationError(ProviderError):
    """供应商初始化异常"""
    
    def __init__(self, provider_name: str, reason: str):
        super().__init__(
            message=f"供应商 {provider_name} 初始化失败: {reason}",
            provider_name=provider_name,
            retryable=False
        )
        self.error_code = "PROVIDER_INITIALIZATION_ERROR"


# =============================================================================
# API相关异常
# =============================================================================

class APIError(AIGenHubException):
    """API错误异常"""
    
    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
        retryable: bool = True
    ):
        details = {}
        if status_code:
            details["status_code"] = status_code
        if response_data:
            details["response_data"] = response_data
        
        super().__init__(
            message=message,
            error_code="API_ERROR",
            details=details,
            retryable=retryable
        )


class AuthenticationError(APIError):
    """认证错误异常"""
    
    def __init__(self, message: str = "认证失败"):
        super().__init__(
            message=message,
            status_code=401,
            retryable=False
        )
        self.error_code = "AUTHENTICATION_ERROR"


class AuthorizationError(APIError):
    """授权错误异常"""
    
    def __init__(self, message: str = "权限不足"):
        super().__init__(
            message=message,
            status_code=403,
            retryable=False
        )
        self.error_code = "AUTHORIZATION_ERROR"


class RateLimitError(APIError):
    """速率限制异常"""
    
    def __init__(
        self,
        message: str = "请求频率超限",
        retry_after: Optional[int] = None
    ):
        details = {}
        if retry_after:
            details["retry_after"] = retry_after
        
        super().__init__(
            message=message,
            status_code=429,
            retryable=True
        )
        self.error_code = "RATE_LIMIT_ERROR"
        self.details.update(details)


class QuotaExceededError(APIError):
    """配额超限异常"""
    
    def __init__(self, message: str = "配额已用完"):
        super().__init__(
            message=message,
            status_code=429,
            retryable=False
        )
        self.error_code = "QUOTA_EXCEEDED_ERROR"


# =============================================================================
# 请求相关异常
# =============================================================================

class RequestError(AIGenHubException):
    """请求错误异常"""
    
    def __init__(self, message: str, retryable: bool = False):
        super().__init__(
            message=message,
            error_code="REQUEST_ERROR",
            retryable=retryable
        )


class InvalidRequestError(RequestError):
    """无效请求异常"""
    
    def __init__(self, message: str, field: Optional[str] = None):
        super().__init__(message, retryable=False)
        self.error_code = "INVALID_REQUEST"
        if field:
            self.details["field"] = field


class ModelNotSupportedError(RequestError):
    """模型不支持异常"""
    
    def __init__(self, model: str, provider: Optional[str] = None):
        message = f"模型 {model} 不受支持"
        if provider:
            message += f" (供应商: {provider})"
        
        super().__init__(message, retryable=False)
        self.error_code = "MODEL_NOT_SUPPORTED"
        self.details.update({"model": model, "provider": provider})


class TimeoutError(RequestError):
    """超时异常"""
    
    def __init__(self, message: str = "请求超时", timeout: Optional[float] = None):
        super().__init__(message, retryable=True)
        self.error_code = "TIMEOUT_ERROR"
        if timeout:
            self.details["timeout"] = timeout


# =============================================================================
# 缓存相关异常
# =============================================================================

class CacheError(AIGenHubException):
    """缓存错误异常"""
    
    def __init__(self, message: str, retryable: bool = True):
        super().__init__(
            message=message,
            error_code="CACHE_ERROR",
            retryable=retryable
        )


class CacheConnectionError(CacheError):
    """缓存连接错误异常"""
    
    def __init__(self, message: str = "缓存连接失败"):
        super().__init__(message, retryable=True)
        self.error_code = "CACHE_CONNECTION_ERROR"


# =============================================================================
# 监控相关异常
# =============================================================================

class MonitoringError(AIGenHubException):
    """监控错误异常"""
    
    def __init__(self, message: str):
        super().__init__(
            message=message,
            error_code="MONITORING_ERROR",
            retryable=False
        )


class MetricsCollectionError(MonitoringError):
    """指标收集错误异常"""
    
    def __init__(self, message: str = "指标收集失败"):
        super().__init__(message)
        self.error_code = "METRICS_COLLECTION_ERROR"


# =============================================================================
# 存储相关异常
# =============================================================================

class StorageError(AIGenHubException):
    """存储错误异常"""
    
    def __init__(self, message: str, retryable: bool = True):
        super().__init__(
            message=message,
            error_code="STORAGE_ERROR",
            retryable=retryable
        )


class FileNotFoundError(StorageError):
    """文件未找到异常"""
    
    def __init__(self, file_path: str):
        super().__init__(
            message=f"文件未找到: {file_path}",
            retryable=False
        )
        self.error_code = "FILE_NOT_FOUND"
        self.details["file_path"] = file_path


class FileUploadError(StorageError):
    """文件上传错误异常"""

    def __init__(self, message: str = "文件上传失败"):
        super().__init__(message, retryable=True)
        self.error_code = "FILE_UPLOAD_ERROR"


# =============================================================================
# 验证相关异常
# =============================================================================

class ValidationError(AIGenHubException):
    """数据验证错误异常"""

    def __init__(self, message: str = "数据验证失败", field: Optional[str] = None):
        super().__init__(message, retryable=False)
        self.error_code = "VALIDATION_ERROR"
        if field:
            self.details["field"] = field
