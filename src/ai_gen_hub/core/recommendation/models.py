"""
AI内容个性化推荐系统 - 核心模型

定义用户行为、内容特征、推荐算法等核心数据模型
"""

import time
from typing import Any, Dict, List, Optional, Union
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime

from ai_gen_hub.core.interfaces import BaseRequest, BaseResponse


class UserActionType(Enum):
    """用户行为类型"""
    VIEW = "view"                    # 查看
    LIKE = "like"                    # 点赞
    DISLIKE = "dislike"              # 不喜欢
    SHARE = "share"                  # 分享
    COMMENT = "comment"              # 评论
    BOOKMARK = "bookmark"            # 收藏
    DOWNLOAD = "download"            # 下载
    COPY = "copy"                    # 复制
    EDIT = "edit"                    # 编辑
    DELETE = "delete"                # 删除
    SEARCH = "search"                # 搜索
    GENERATE = "generate"            # 生成
    REGENERATE = "regenerate"        # 重新生成


class ContentCategory(Enum):
    """内容类别"""
    TEXT = "text"                    # 文本
    IMAGE = "image"                  # 图像
    AUDIO = "audio"                  # 音频
    VIDEO = "video"                  # 视频
    CODE = "code"                    # 代码
    DOCUMENT = "document"            # 文档
    PRESENTATION = "presentation"    # 演示文稿
    WORKFLOW = "workflow"            # 工作流
    TEMPLATE = "template"            # 模板
    DATASET = "dataset"              # 数据集


class RecommendationType(Enum):
    """推荐类型"""
    CONTENT_BASED = "content_based"          # 基于内容
    COLLABORATIVE = "collaborative"          # 协同过滤
    HYBRID = "hybrid"                       # 混合推荐
    TRENDING = "trending"                   # 热门推荐
    PERSONALIZED = "personalized"          # 个性化推荐
    SIMILAR_USERS = "similar_users"        # 相似用户
    CONTEXTUAL = "contextual"              # 上下文推荐


class PreferenceType(Enum):
    """偏好类型"""
    EXPLICIT = "explicit"            # 显式偏好（用户主动表达）
    IMPLICIT = "implicit"            # 隐式偏好（从行为推断）
    INFERRED = "inferred"            # 推断偏好（算法推断）


@dataclass
class UserBehavior:
    """用户行为记录"""
    behavior_id: str
    user_id: str
    content_id: str
    action_type: UserActionType
    
    # 行为上下文
    session_id: Optional[str] = None
    device_type: Optional[str] = None
    platform: Optional[str] = None
    location: Optional[str] = None
    
    # 行为详情
    duration: Optional[float] = None  # 持续时间（秒）
    scroll_depth: Optional[float] = None  # 滚动深度（百分比）
    interaction_count: int = 0  # 交互次数
    
    # 时间信息
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.behavior_id:
            self.behavior_id = f"behavior_{int(time.time())}"


@dataclass
class ContentFeature:
    """内容特征"""
    content_id: str
    title: str
    category: ContentCategory
    
    # 基本属性
    content_type: str = ""
    description: str = ""
    tags: List[str] = field(default_factory=list)
    keywords: List[str] = field(default_factory=list)
    
    # 内容特征
    language: str = "zh"
    length: int = 0  # 内容长度
    complexity: float = 0.0  # 复杂度评分 (0-1)
    quality_score: float = 0.0  # 质量评分 (0-10)
    
    # 创作信息
    author_id: str = ""
    author_name: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    # 统计信息
    view_count: int = 0
    like_count: int = 0
    share_count: int = 0
    comment_count: int = 0
    download_count: int = 0
    
    # 向量特征
    embedding_vector: Optional[List[float]] = None
    feature_vector: Optional[List[float]] = None
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class UserPreference:
    """用户偏好"""
    user_id: str
    preference_type: PreferenceType
    
    # 类别偏好
    category_preferences: Dict[ContentCategory, float] = field(default_factory=dict)
    
    # 标签偏好
    tag_preferences: Dict[str, float] = field(default_factory=dict)
    
    # 作者偏好
    author_preferences: Dict[str, float] = field(default_factory=dict)
    
    # 时间偏好
    time_preferences: Dict[str, float] = field(default_factory=dict)  # 如：工作日、周末、早晨、晚上
    
    # 复杂度偏好
    complexity_preference: float = 0.5  # 0-1，0表示喜欢简单内容，1表示喜欢复杂内容
    
    # 质量偏好
    quality_threshold: float = 5.0  # 质量阈值
    
    # 新颖性偏好
    novelty_preference: float = 0.5  # 0-1，0表示喜欢熟悉内容，1表示喜欢新颖内容
    
    # 多样性偏好
    diversity_preference: float = 0.5  # 0-1，0表示喜欢相似内容，1表示喜欢多样内容
    
    # 更新时间
    last_updated: datetime = field(default_factory=datetime.now)
    
    # 置信度
    confidence: float = 0.0  # 偏好的置信度 (0-1)


@dataclass
class UserProfile:
    """用户画像"""
    user_id: str
    username: str = ""
    
    # 基本信息
    age_group: Optional[str] = None  # 年龄段
    gender: Optional[str] = None
    location: Optional[str] = None
    occupation: Optional[str] = None
    education: Optional[str] = None
    
    # 使用习惯
    active_hours: List[int] = field(default_factory=list)  # 活跃时间段
    preferred_devices: List[str] = field(default_factory=list)
    usage_frequency: str = "medium"  # low, medium, high
    
    # 技能水平
    skill_levels: Dict[str, float] = field(default_factory=dict)  # 各领域技能水平
    
    # 兴趣标签
    interests: List[str] = field(default_factory=list)
    
    # 行为统计
    total_actions: int = 0
    total_content_created: int = 0
    total_content_consumed: int = 0
    avg_session_duration: float = 0.0
    
    # 偏好信息
    preferences: Optional[UserPreference] = None
    
    # 相似用户
    similar_users: List[str] = field(default_factory=list)
    
    # 更新时间
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class RecommendationItem:
    """推荐项目"""
    content_id: str
    content_feature: ContentFeature
    
    # 推荐分数
    score: float = 0.0
    confidence: float = 0.0
    
    # 推荐原因
    reason: str = ""
    reason_details: Dict[str, Any] = field(default_factory=dict)
    
    # 推荐类型
    recommendation_type: RecommendationType = RecommendationType.HYBRID
    
    # 排序位置
    rank: int = 0
    
    # 预测指标
    predicted_rating: Optional[float] = None
    predicted_engagement: Optional[float] = None
    predicted_completion: Optional[float] = None


@dataclass
class RecommendationRequest:
    """推荐请求"""
    user_id: str
    
    # 推荐参数
    count: int = 10
    recommendation_types: List[RecommendationType] = field(default_factory=lambda: [RecommendationType.HYBRID])
    
    # 过滤条件
    categories: Optional[List[ContentCategory]] = None
    tags: Optional[List[str]] = None
    min_quality: Optional[float] = None
    max_complexity: Optional[float] = None
    
    # 排除内容
    exclude_content_ids: List[str] = field(default_factory=list)
    exclude_authors: List[str] = field(default_factory=list)
    
    # 上下文信息
    context: Dict[str, Any] = field(default_factory=dict)
    
    # 多样性控制
    diversity_factor: float = 0.3  # 多样性因子 (0-1)
    novelty_factor: float = 0.2   # 新颖性因子 (0-1)


@dataclass
class RecommendationResponse:
    """推荐响应"""
    user_id: str
    request_id: str
    
    # 推荐结果
    recommendations: List[RecommendationItem] = field(default_factory=list)
    
    # 推荐统计
    total_candidates: int = 0
    filtered_count: int = 0
    
    # 算法信息
    algorithms_used: List[str] = field(default_factory=list)
    computation_time: float = 0.0
    
    # 元数据
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RecommendationFeedback:
    """推荐反馈"""
    feedback_id: str
    user_id: str
    content_id: str
    recommendation_id: str
    
    # 反馈类型
    feedback_type: UserActionType
    
    # 反馈分数
    rating: Optional[float] = None  # 1-5分
    
    # 反馈详情
    is_relevant: Optional[bool] = None
    is_helpful: Optional[bool] = None
    is_novel: Optional[bool] = None
    
    # 时间信息
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TrendingContent:
    """热门内容"""
    content_id: str
    content_feature: ContentFeature
    
    # 热度指标
    trending_score: float = 0.0
    velocity: float = 0.0  # 热度增长速度
    
    # 时间窗口
    time_window: str = "24h"  # 1h, 24h, 7d, 30d
    
    # 统计信息
    recent_views: int = 0
    recent_likes: int = 0
    recent_shares: int = 0
    recent_comments: int = 0
    
    # 排名信息
    rank: int = 0
    previous_rank: Optional[int] = None
    rank_change: int = 0
    
    # 更新时间
    calculated_at: datetime = field(default_factory=datetime.now)


@dataclass
class SimilarityScore:
    """相似度分数"""
    item1_id: str
    item2_id: str
    similarity_type: str  # content, user, behavior
    score: float = 0.0
    
    # 相似度详情
    feature_similarities: Dict[str, float] = field(default_factory=dict)
    
    # 计算信息
    algorithm: str = ""
    calculated_at: datetime = field(default_factory=datetime.now)


# 请求和响应模型
@dataclass
class GetRecommendationsRequest(BaseRequest):
    """获取推荐请求"""
    user_id: str
    count: int = 10
    recommendation_types: List[str] = field(default_factory=list)
    categories: Optional[List[str]] = None
    context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RecordBehaviorRequest(BaseRequest):
    """记录行为请求"""
    user_id: str
    content_id: str
    action_type: str
    duration: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class UpdatePreferencesRequest(BaseRequest):
    """更新偏好请求"""
    user_id: str
    category_preferences: Optional[Dict[str, float]] = None
    tag_preferences: Optional[Dict[str, float]] = None
    complexity_preference: Optional[float] = None
    quality_threshold: Optional[float] = None


@dataclass
class GetTrendingRequest(BaseRequest):
    """获取热门内容请求"""
    time_window: str = "24h"
    category: Optional[str] = None
    count: int = 20


@dataclass
class RecommendationStatsResponse(BaseResponse):
    """推荐统计响应"""
    total_recommendations: int = 0
    total_feedback: int = 0
    avg_rating: float = 0.0
    click_through_rate: float = 0.0
    conversion_rate: float = 0.0
    diversity_score: float = 0.0
    novelty_score: float = 0.0
