"""
推荐算法引擎

实现多种推荐算法，包括协同过滤、内容推荐、混合推荐等
"""

import math
import random
from typing import Dict, List, Tuple, Optional
from collections import defaultdict, Counter
from datetime import datetime, timedelta

from ai_gen_hub.core.logging import get_logger
from .models import (
    UserBehavior,
    ContentFeature,
    UserPreference,
    UserProfile,
    RecommendationItem,
    RecommendationRequest,
    TrendingContent,
    SimilarityScore,
    UserActionType,
    ContentCategory,
    RecommendationType
)


class ContentBasedRecommender:
    """基于内容的推荐算法"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def recommend(
        self, 
        user_profile: UserProfile,
        content_features: List[ContentFeature],
        request: RecommendationRequest
    ) -> List[RecommendationItem]:
        """基于内容的推荐"""
        recommendations = []
        
        if not user_profile.preferences:
            return recommendations
        
        preferences = user_profile.preferences
        
        for content in content_features:
            if content.content_id in request.exclude_content_ids:
                continue
            
            # 计算内容匹配分数
            score = self._calculate_content_score(content, preferences)
            
            if score > 0:
                item = RecommendationItem(
                    content_id=content.content_id,
                    content_feature=content,
                    score=score,
                    recommendation_type=RecommendationType.CONTENT_BASED,
                    reason="基于您的内容偏好推荐",
                    reason_details={
                        "category_match": self._get_category_match_score(content, preferences),
                        "tag_match": self._get_tag_match_score(content, preferences),
                        "quality_match": self._get_quality_match_score(content, preferences)
                    }
                )
                recommendations.append(item)
        
        # 按分数排序
        recommendations.sort(key=lambda x: x.score, reverse=True)
        
        return recommendations[:request.count]
    
    def _calculate_content_score(self, content: ContentFeature, preferences: UserPreference) -> float:
        """计算内容匹配分数"""
        score = 0.0
        
        # 类别匹配
        category_score = preferences.category_preferences.get(content.category, 0.0)
        score += category_score * 0.3
        
        # 标签匹配
        tag_score = 0.0
        for tag in content.tags:
            tag_score += preferences.tag_preferences.get(tag, 0.0)
        if content.tags:
            tag_score /= len(content.tags)
        score += tag_score * 0.3
        
        # 质量匹配
        if content.quality_score >= preferences.quality_threshold:
            quality_score = min(content.quality_score / 10.0, 1.0)
            score += quality_score * 0.2
        
        # 复杂度匹配
        complexity_diff = abs(content.complexity - preferences.complexity_preference)
        complexity_score = 1.0 - complexity_diff
        score += complexity_score * 0.2
        
        return min(score, 1.0)
    
    def _get_category_match_score(self, content: ContentFeature, preferences: UserPreference) -> float:
        """获取类别匹配分数"""
        return preferences.category_preferences.get(content.category, 0.0)
    
    def _get_tag_match_score(self, content: ContentFeature, preferences: UserPreference) -> float:
        """获取标签匹配分数"""
        if not content.tags:
            return 0.0
        
        total_score = sum(preferences.tag_preferences.get(tag, 0.0) for tag in content.tags)
        return total_score / len(content.tags)
    
    def _get_quality_match_score(self, content: ContentFeature, preferences: UserPreference) -> float:
        """获取质量匹配分数"""
        if content.quality_score >= preferences.quality_threshold:
            return min(content.quality_score / 10.0, 1.0)
        return 0.0


class CollaborativeFilteringRecommender:
    """协同过滤推荐算法"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.user_similarities: Dict[Tuple[str, str], float] = {}
        self.item_similarities: Dict[Tuple[str, str], float] = {}
    
    def recommend(
        self,
        target_user_id: str,
        user_behaviors: List[UserBehavior],
        content_features: List[ContentFeature],
        request: RecommendationRequest
    ) -> List[RecommendationItem]:
        """协同过滤推荐"""
        # 构建用户-物品矩阵
        user_item_matrix = self._build_user_item_matrix(user_behaviors)
        
        if target_user_id not in user_item_matrix:
            return []
        
        # 计算用户相似度
        similar_users = self._find_similar_users(target_user_id, user_item_matrix)
        
        # 生成推荐
        recommendations = self._generate_collaborative_recommendations(
            target_user_id,
            similar_users,
            user_item_matrix,
            content_features,
            request
        )
        
        return recommendations[:request.count]
    
    def _build_user_item_matrix(self, behaviors: List[UserBehavior]) -> Dict[str, Dict[str, float]]:
        """构建用户-物品评分矩阵"""
        matrix = defaultdict(lambda: defaultdict(float))
        
        # 行为权重
        action_weights = {
            UserActionType.VIEW: 1.0,
            UserActionType.LIKE: 3.0,
            UserActionType.SHARE: 2.5,
            UserActionType.BOOKMARK: 2.0,
            UserActionType.COMMENT: 2.0,
            UserActionType.DOWNLOAD: 1.5,
            UserActionType.DISLIKE: -1.0
        }
        
        for behavior in behaviors:
            weight = action_weights.get(behavior.action_type, 1.0)
            
            # 考虑时间衰减
            days_ago = (datetime.now() - behavior.timestamp).days
            time_decay = math.exp(-days_ago / 30.0)  # 30天衰减
            
            score = weight * time_decay
            matrix[behavior.user_id][behavior.content_id] += score
        
        return dict(matrix)
    
    def _find_similar_users(
        self, 
        target_user_id: str, 
        user_item_matrix: Dict[str, Dict[str, float]]
    ) -> List[Tuple[str, float]]:
        """查找相似用户"""
        target_items = user_item_matrix[target_user_id]
        similarities = []
        
        for user_id, user_items in user_item_matrix.items():
            if user_id == target_user_id:
                continue
            
            # 计算余弦相似度
            similarity = self._cosine_similarity(target_items, user_items)
            if similarity > 0.1:  # 相似度阈值
                similarities.append((user_id, similarity))
        
        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:50]  # 取前50个相似用户
    
    def _cosine_similarity(self, items1: Dict[str, float], items2: Dict[str, float]) -> float:
        """计算余弦相似度"""
        common_items = set(items1.keys()) & set(items2.keys())
        
        if not common_items:
            return 0.0
        
        # 计算向量点积
        dot_product = sum(items1[item] * items2[item] for item in common_items)
        
        # 计算向量模长
        norm1 = math.sqrt(sum(score ** 2 for score in items1.values()))
        norm2 = math.sqrt(sum(score ** 2 for score in items2.values()))
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    def _generate_collaborative_recommendations(
        self,
        target_user_id: str,
        similar_users: List[Tuple[str, float]],
        user_item_matrix: Dict[str, Dict[str, float]],
        content_features: List[ContentFeature],
        request: RecommendationRequest
    ) -> List[RecommendationItem]:
        """生成协同过滤推荐"""
        target_items = set(user_item_matrix[target_user_id].keys())
        content_dict = {c.content_id: c for c in content_features}
        
        # 计算推荐分数
        item_scores = defaultdict(float)
        item_weights = defaultdict(float)
        
        for user_id, similarity in similar_users:
            user_items = user_item_matrix[user_id]
            
            for item_id, rating in user_items.items():
                if item_id not in target_items and item_id not in request.exclude_content_ids:
                    item_scores[item_id] += similarity * rating
                    item_weights[item_id] += similarity
        
        # 标准化分数
        recommendations = []
        for item_id, total_score in item_scores.items():
            if item_weights[item_id] > 0:
                normalized_score = total_score / item_weights[item_id]
                
                if item_id in content_dict:
                    content = content_dict[item_id]
                    item = RecommendationItem(
                        content_id=item_id,
                        content_feature=content,
                        score=normalized_score,
                        recommendation_type=RecommendationType.COLLABORATIVE,
                        reason="基于相似用户的偏好推荐",
                        reason_details={
                            "similar_users_count": len(similar_users),
                            "avg_similarity": sum(s for _, s in similar_users) / len(similar_users)
                        }
                    )
                    recommendations.append(item)
        
        # 按分数排序
        recommendations.sort(key=lambda x: x.score, reverse=True)
        return recommendations


class TrendingRecommender:
    """热门内容推荐算法"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def recommend(
        self,
        trending_contents: List[TrendingContent],
        request: RecommendationRequest
    ) -> List[RecommendationItem]:
        """热门内容推荐"""
        recommendations = []
        
        for trending in trending_contents:
            if trending.content_id in request.exclude_content_ids:
                continue
            
            # 过滤条件
            if request.categories and trending.content_feature.category not in request.categories:
                continue
            
            if request.min_quality and trending.content_feature.quality_score < request.min_quality:
                continue
            
            item = RecommendationItem(
                content_id=trending.content_id,
                content_feature=trending.content_feature,
                score=trending.trending_score,
                recommendation_type=RecommendationType.TRENDING,
                reason=f"当前热门内容，排名第{trending.rank}位",
                reason_details={
                    "trending_rank": trending.rank,
                    "trending_score": trending.trending_score,
                    "velocity": trending.velocity,
                    "recent_views": trending.recent_views
                }
            )
            recommendations.append(item)
        
        return recommendations[:request.count]


class HybridRecommender:
    """混合推荐算法"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.content_based = ContentBasedRecommender()
        self.collaborative = CollaborativeFilteringRecommender()
        self.trending = TrendingRecommender()
    
    def recommend(
        self,
        user_profile: UserProfile,
        user_behaviors: List[UserBehavior],
        content_features: List[ContentFeature],
        trending_contents: List[TrendingContent],
        request: RecommendationRequest
    ) -> List[RecommendationItem]:
        """混合推荐"""
        all_recommendations = []
        
        # 基于内容的推荐
        if RecommendationType.CONTENT_BASED in request.recommendation_types:
            content_recs = self.content_based.recommend(user_profile, content_features, request)
            all_recommendations.extend(content_recs)
        
        # 协同过滤推荐
        if RecommendationType.COLLABORATIVE in request.recommendation_types:
            collab_recs = self.collaborative.recommend(
                user_profile.user_id, user_behaviors, content_features, request
            )
            all_recommendations.extend(collab_recs)
        
        # 热门推荐
        if RecommendationType.TRENDING in request.recommendation_types:
            trending_recs = self.trending.recommend(trending_contents, request)
            all_recommendations.extend(trending_recs)
        
        # 去重和融合
        merged_recommendations = self._merge_recommendations(all_recommendations, request)
        
        # 多样性调整
        diversified_recommendations = self._apply_diversity(merged_recommendations, request)
        
        return diversified_recommendations[:request.count]
    
    def _merge_recommendations(
        self, 
        recommendations: List[RecommendationItem], 
        request: RecommendationRequest
    ) -> List[RecommendationItem]:
        """合并和去重推荐结果"""
        # 按内容ID分组
        content_groups = defaultdict(list)
        for rec in recommendations:
            content_groups[rec.content_id].append(rec)
        
        merged = []
        for content_id, recs in content_groups.items():
            if len(recs) == 1:
                merged.append(recs[0])
            else:
                # 多个算法推荐同一内容，融合分数
                merged_rec = self._merge_recommendation_items(recs)
                merged.append(merged_rec)
        
        # 按分数排序
        merged.sort(key=lambda x: x.score, reverse=True)
        return merged
    
    def _merge_recommendation_items(self, items: List[RecommendationItem]) -> RecommendationItem:
        """合并多个推荐项"""
        # 使用加权平均
        weights = {
            RecommendationType.CONTENT_BASED: 0.4,
            RecommendationType.COLLABORATIVE: 0.4,
            RecommendationType.TRENDING: 0.2
        }
        
        total_score = 0.0
        total_weight = 0.0
        algorithms = []
        
        for item in items:
            weight = weights.get(item.recommendation_type, 0.3)
            total_score += item.score * weight
            total_weight += weight
            algorithms.append(item.recommendation_type.value)
        
        final_score = total_score / total_weight if total_weight > 0 else 0.0
        
        # 使用第一个项目作为基础
        merged = items[0]
        merged.score = final_score
        merged.recommendation_type = RecommendationType.HYBRID
        merged.reason = f"多算法融合推荐 ({', '.join(algorithms)})"
        
        return merged
    
    def _apply_diversity(
        self, 
        recommendations: List[RecommendationItem], 
        request: RecommendationRequest
    ) -> List[RecommendationItem]:
        """应用多样性调整"""
        if request.diversity_factor <= 0:
            return recommendations
        
        diversified = []
        used_categories = set()
        used_authors = set()
        
        for rec in recommendations:
            # 计算多样性分数
            diversity_bonus = 0.0
            
            # 类别多样性
            if rec.content_feature.category not in used_categories:
                diversity_bonus += 0.1
                used_categories.add(rec.content_feature.category)
            
            # 作者多样性
            if rec.content_feature.author_id not in used_authors:
                diversity_bonus += 0.05
                used_authors.add(rec.content_feature.author_id)
            
            # 调整分数
            rec.score += diversity_bonus * request.diversity_factor
            diversified.append(rec)
        
        # 重新排序
        diversified.sort(key=lambda x: x.score, reverse=True)
        return diversified


class RecommendationEngine:
    """推荐引擎"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.hybrid_recommender = HybridRecommender()
    
    def get_recommendations(
        self,
        user_profile: UserProfile,
        user_behaviors: List[UserBehavior],
        content_features: List[ContentFeature],
        trending_contents: List[TrendingContent],
        request: RecommendationRequest
    ) -> List[RecommendationItem]:
        """获取推荐结果"""
        try:
            # 使用混合推荐算法
            recommendations = self.hybrid_recommender.recommend(
                user_profile,
                user_behaviors,
                content_features,
                trending_contents,
                request
            )
            
            # 设置排名
            for i, rec in enumerate(recommendations):
                rec.rank = i + 1
            
            self.logger.info(f"为用户 {user_profile.user_id} 生成了 {len(recommendations)} 个推荐")
            return recommendations
            
        except Exception as e:
            self.logger.error(f"推荐生成失败: {e}")
            return []
    
    def calculate_similarity(self, item1: ContentFeature, item2: ContentFeature) -> float:
        """计算内容相似度"""
        similarity = 0.0
        
        # 类别相似度
        if item1.category == item2.category:
            similarity += 0.3
        
        # 标签相似度
        common_tags = set(item1.tags) & set(item2.tags)
        if item1.tags and item2.tags:
            tag_similarity = len(common_tags) / len(set(item1.tags) | set(item2.tags))
            similarity += tag_similarity * 0.3
        
        # 质量相似度
        quality_diff = abs(item1.quality_score - item2.quality_score)
        quality_similarity = 1.0 - (quality_diff / 10.0)
        similarity += quality_similarity * 0.2
        
        # 复杂度相似度
        complexity_diff = abs(item1.complexity - item2.complexity)
        complexity_similarity = 1.0 - complexity_diff
        similarity += complexity_similarity * 0.2
        
        return min(similarity, 1.0)
