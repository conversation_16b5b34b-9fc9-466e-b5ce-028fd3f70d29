"""
用户行为分析器

分析用户行为模式，提取用户偏好和兴趣特征
"""

import math
from typing import Dict, List, Tuple, Optional
from collections import defaultdict, Counter
from datetime import datetime, timedelta

from ai_gen_hub.core.logging import get_logger
from .models import (
    UserBehavior,
    ContentFeature,
    UserPreference,
    UserProfile,
    UserActionType,
    ContentCategory,
    PreferenceType
)


class BehaviorAnalyzer:
    """用户行为分析器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # 行为权重配置
        self.action_weights = {
            UserActionType.VIEW: 1.0,
            UserActionType.LIKE: 5.0,
            UserActionType.DISLIKE: -3.0,
            UserActionType.SHARE: 4.0,
            UserActionType.COMMENT: 3.0,
            UserActionType.BOOKMARK: 6.0,
            UserActionType.DOWNLOAD: 3.5,
            UserActionType.COPY: 2.0,
            UserActionType.EDIT: 4.5,
            UserActionType.SEARCH: 1.5,
            UserActionType.GENERATE: 2.5,
            UserActionType.REGENERATE: 1.8
        }
        
        # 时间衰减参数
        self.time_decay_days = 30  # 30天衰减周期
    
    def analyze_user_behaviors(
        self, 
        user_id: str,
        behaviors: List[UserBehavior],
        content_features: Dict[str, ContentFeature]
    ) -> UserPreference:
        """分析用户行为，生成用户偏好"""
        
        # 过滤用户行为
        user_behaviors = [b for b in behaviors if b.user_id == user_id]
        
        if not user_behaviors:
            return self._create_default_preference(user_id)
        
        # 分析类别偏好
        category_preferences = self._analyze_category_preferences(user_behaviors, content_features)
        
        # 分析标签偏好
        tag_preferences = self._analyze_tag_preferences(user_behaviors, content_features)
        
        # 分析作者偏好
        author_preferences = self._analyze_author_preferences(user_behaviors, content_features)
        
        # 分析时间偏好
        time_preferences = self._analyze_time_preferences(user_behaviors)
        
        # 分析复杂度偏好
        complexity_preference = self._analyze_complexity_preference(user_behaviors, content_features)
        
        # 分析质量偏好
        quality_threshold = self._analyze_quality_threshold(user_behaviors, content_features)
        
        # 分析新颖性偏好
        novelty_preference = self._analyze_novelty_preference(user_behaviors, content_features)
        
        # 分析多样性偏好
        diversity_preference = self._analyze_diversity_preference(user_behaviors, content_features)
        
        # 计算置信度
        confidence = self._calculate_confidence(user_behaviors)
        
        preference = UserPreference(
            user_id=user_id,
            preference_type=PreferenceType.IMPLICIT,
            category_preferences=category_preferences,
            tag_preferences=tag_preferences,
            author_preferences=author_preferences,
            time_preferences=time_preferences,
            complexity_preference=complexity_preference,
            quality_threshold=quality_threshold,
            novelty_preference=novelty_preference,
            diversity_preference=diversity_preference,
            confidence=confidence
        )
        
        self.logger.info(f"分析用户 {user_id} 的行为偏好，置信度: {confidence:.2f}")
        return preference
    
    def _analyze_category_preferences(
        self, 
        behaviors: List[UserBehavior],
        content_features: Dict[str, ContentFeature]
    ) -> Dict[ContentCategory, float]:
        """分析类别偏好"""
        category_scores = defaultdict(float)
        category_counts = defaultdict(int)
        
        for behavior in behaviors:
            content = content_features.get(behavior.content_id)
            if not content:
                continue
            
            # 计算行为分数
            weight = self.action_weights.get(behavior.action_type, 1.0)
            time_decay = self._calculate_time_decay(behavior.timestamp)
            score = weight * time_decay
            
            # 考虑持续时间
            if behavior.duration:
                duration_factor = min(behavior.duration / 300.0, 2.0)  # 5分钟为基准
                score *= duration_factor
            
            category_scores[content.category] += score
            category_counts[content.category] += 1
        
        # 标准化分数
        preferences = {}
        max_score = max(category_scores.values()) if category_scores else 1.0
        
        for category, score in category_scores.items():
            normalized_score = score / max_score
            # 考虑交互频次
            frequency_bonus = min(category_counts[category] / 10.0, 0.5)
            preferences[category] = min(normalized_score + frequency_bonus, 1.0)
        
        return preferences
    
    def _analyze_tag_preferences(
        self,
        behaviors: List[UserBehavior],
        content_features: Dict[str, ContentFeature]
    ) -> Dict[str, float]:
        """分析标签偏好"""
        tag_scores = defaultdict(float)
        tag_counts = defaultdict(int)
        
        for behavior in behaviors:
            content = content_features.get(behavior.content_id)
            if not content or not content.tags:
                continue
            
            weight = self.action_weights.get(behavior.action_type, 1.0)
            time_decay = self._calculate_time_decay(behavior.timestamp)
            score = weight * time_decay
            
            # 分配到每个标签
            tag_score = score / len(content.tags)
            for tag in content.tags:
                tag_scores[tag] += tag_score
                tag_counts[tag] += 1
        
        # 标准化和过滤
        preferences = {}
        max_score = max(tag_scores.values()) if tag_scores else 1.0
        
        for tag, score in tag_scores.items():
            if tag_counts[tag] >= 2:  # 至少出现2次
                normalized_score = score / max_score
                preferences[tag] = normalized_score
        
        # 只保留前50个标签
        sorted_tags = sorted(preferences.items(), key=lambda x: x[1], reverse=True)
        return dict(sorted_tags[:50])
    
    def _analyze_author_preferences(
        self,
        behaviors: List[UserBehavior],
        content_features: Dict[str, ContentFeature]
    ) -> Dict[str, float]:
        """分析作者偏好"""
        author_scores = defaultdict(float)
        author_counts = defaultdict(int)
        
        for behavior in behaviors:
            content = content_features.get(behavior.content_id)
            if not content or not content.author_id:
                continue
            
            weight = self.action_weights.get(behavior.action_type, 1.0)
            time_decay = self._calculate_time_decay(behavior.timestamp)
            score = weight * time_decay
            
            author_scores[content.author_id] += score
            author_counts[content.author_id] += 1
        
        # 标准化
        preferences = {}
        max_score = max(author_scores.values()) if author_scores else 1.0
        
        for author_id, score in author_scores.items():
            if author_counts[author_id] >= 2:  # 至少交互2次
                normalized_score = score / max_score
                preferences[author_id] = normalized_score
        
        return preferences
    
    def _analyze_time_preferences(self, behaviors: List[UserBehavior]) -> Dict[str, float]:
        """分析时间偏好"""
        time_patterns = {
            "morning": defaultdict(float),    # 6-12
            "afternoon": defaultdict(float),  # 12-18
            "evening": defaultdict(float),    # 18-24
            "night": defaultdict(float),      # 0-6
            "weekday": defaultdict(float),
            "weekend": defaultdict(float)
        }
        
        for behavior in behaviors:
            hour = behavior.timestamp.hour
            weekday = behavior.timestamp.weekday()
            
            weight = self.action_weights.get(behavior.action_type, 1.0)
            
            # 时段偏好
            if 6 <= hour < 12:
                time_patterns["morning"]["count"] += weight
            elif 12 <= hour < 18:
                time_patterns["afternoon"]["count"] += weight
            elif 18 <= hour < 24:
                time_patterns["evening"]["count"] += weight
            else:
                time_patterns["night"]["count"] += weight
            
            # 工作日/周末偏好
            if weekday < 5:
                time_patterns["weekday"]["count"] += weight
            else:
                time_patterns["weekend"]["count"] += weight
        
        # 标准化
        preferences = {}
        total_activity = sum(sum(pattern.values()) for pattern in time_patterns.values())
        
        if total_activity > 0:
            for period, pattern in time_patterns.items():
                preferences[period] = pattern.get("count", 0) / total_activity
        
        return preferences
    
    def _analyze_complexity_preference(
        self,
        behaviors: List[UserBehavior],
        content_features: Dict[str, ContentFeature]
    ) -> float:
        """分析复杂度偏好"""
        complexity_scores = []
        
        for behavior in behaviors:
            content = content_features.get(behavior.content_id)
            if not content:
                continue
            
            weight = self.action_weights.get(behavior.action_type, 1.0)
            if weight > 0:  # 只考虑正向行为
                complexity_scores.append(content.complexity)
        
        if not complexity_scores:
            return 0.5  # 默认中等复杂度
        
        # 计算加权平均
        return sum(complexity_scores) / len(complexity_scores)
    
    def _analyze_quality_threshold(
        self,
        behaviors: List[UserBehavior],
        content_features: Dict[str, ContentFeature]
    ) -> float:
        """分析质量阈值"""
        quality_scores = []
        
        for behavior in behaviors:
            content = content_features.get(behavior.content_id)
            if not content:
                continue
            
            weight = self.action_weights.get(behavior.action_type, 1.0)
            if weight > 2.0:  # 只考虑强正向行为
                quality_scores.append(content.quality_score)
        
        if not quality_scores:
            return 5.0  # 默认中等质量
        
        # 计算25分位数作为阈值
        quality_scores.sort()
        index = max(0, int(len(quality_scores) * 0.25) - 1)
        return quality_scores[index]
    
    def _analyze_novelty_preference(
        self,
        behaviors: List[UserBehavior],
        content_features: Dict[str, ContentFeature]
    ) -> float:
        """分析新颖性偏好"""
        novelty_scores = []
        
        for behavior in behaviors:
            content = content_features.get(behavior.content_id)
            if not content:
                continue
            
            # 计算内容新颖性（基于创建时间）
            days_since_creation = (datetime.now() - content.created_at).days
            novelty = max(0, 1.0 - days_since_creation / 365.0)  # 一年内为新颖
            
            weight = self.action_weights.get(behavior.action_type, 1.0)
            if weight > 0:
                novelty_scores.append(novelty)
        
        if not novelty_scores:
            return 0.5
        
        return sum(novelty_scores) / len(novelty_scores)
    
    def _analyze_diversity_preference(
        self,
        behaviors: List[UserBehavior],
        content_features: Dict[str, ContentFeature]
    ) -> float:
        """分析多样性偏好"""
        categories = set()
        authors = set()
        tags = set()
        
        for behavior in behaviors:
            content = content_features.get(behavior.content_id)
            if not content:
                continue
            
            weight = self.action_weights.get(behavior.action_type, 1.0)
            if weight > 0:
                categories.add(content.category)
                if content.author_id:
                    authors.add(content.author_id)
                tags.update(content.tags)
        
        # 计算多样性分数
        category_diversity = len(categories) / len(ContentCategory) if categories else 0
        author_diversity = min(len(authors) / 10.0, 1.0) if authors else 0  # 最多10个作者
        tag_diversity = min(len(tags) / 20.0, 1.0) if tags else 0  # 最多20个标签
        
        return (category_diversity + author_diversity + tag_diversity) / 3.0
    
    def _calculate_time_decay(self, timestamp: datetime) -> float:
        """计算时间衰减因子"""
        days_ago = (datetime.now() - timestamp).days
        return math.exp(-days_ago / self.time_decay_days)
    
    def _calculate_confidence(self, behaviors: List[UserBehavior]) -> float:
        """计算偏好置信度"""
        if not behaviors:
            return 0.0
        
        # 基于行为数量
        behavior_count_score = min(len(behaviors) / 100.0, 1.0)  # 100个行为为满分
        
        # 基于行为多样性
        action_types = set(b.action_type for b in behaviors)
        diversity_score = len(action_types) / len(UserActionType)
        
        # 基于时间跨度
        if len(behaviors) > 1:
            time_span = (max(b.timestamp for b in behaviors) - 
                        min(b.timestamp for b in behaviors)).days
            time_score = min(time_span / 30.0, 1.0)  # 30天为满分
        else:
            time_score = 0.1
        
        # 综合置信度
        confidence = (behavior_count_score * 0.5 + 
                     diversity_score * 0.3 + 
                     time_score * 0.2)
        
        return min(confidence, 1.0)
    
    def _create_default_preference(self, user_id: str) -> UserPreference:
        """创建默认偏好"""
        return UserPreference(
            user_id=user_id,
            preference_type=PreferenceType.IMPLICIT,
            complexity_preference=0.5,
            quality_threshold=5.0,
            novelty_preference=0.5,
            diversity_preference=0.5,
            confidence=0.0
        )
    
    def update_user_profile(
        self,
        user_profile: UserProfile,
        behaviors: List[UserBehavior],
        content_features: Dict[str, ContentFeature]
    ) -> UserProfile:
        """更新用户画像"""
        user_behaviors = [b for b in behaviors if b.user_id == user_profile.user_id]
        
        if not user_behaviors:
            return user_profile
        
        # 更新行为统计
        user_profile.total_actions = len(user_behaviors)
        
        # 更新活跃时间
        active_hours = Counter()
        session_durations = []
        
        for behavior in user_behaviors:
            active_hours[behavior.timestamp.hour] += 1
            if behavior.duration:
                session_durations.append(behavior.duration)
        
        user_profile.active_hours = [hour for hour, _ in active_hours.most_common(8)]
        
        if session_durations:
            user_profile.avg_session_duration = sum(session_durations) / len(session_durations)
        
        # 更新使用频率
        if user_behaviors:
            days_span = (max(b.timestamp for b in user_behaviors) - 
                        min(b.timestamp for b in user_behaviors)).days
            if days_span > 0:
                actions_per_day = len(user_behaviors) / days_span
                if actions_per_day > 10:
                    user_profile.usage_frequency = "high"
                elif actions_per_day > 3:
                    user_profile.usage_frequency = "medium"
                else:
                    user_profile.usage_frequency = "low"
        
        # 更新偏好
        user_profile.preferences = self.analyze_user_behaviors(
            user_profile.user_id, behaviors, content_features
        )
        
        user_profile.updated_at = datetime.now()
        
        self.logger.info(f"更新用户画像: {user_profile.user_id}")
        return user_profile
