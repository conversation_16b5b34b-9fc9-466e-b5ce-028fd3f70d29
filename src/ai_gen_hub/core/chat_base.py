"""
AI Gen Hub 统一Chat接口基础实现

本模块提供统一Chat接口的基础实现类，包含通用功能和工具方法。
具体的供应商适配器可以继承这些基础类来减少重复代码。

主要功能：
- Chat供应商基础实现类
- 通用的参数验证和转换
- 统一的错误处理机制
- 消息格式转换工具
- 流式响应处理工具

设计原则：
- 代码复用：提供通用功能的默认实现
- 易于扩展：支持子类覆盖和扩展
- 错误处理：统一的异常处理机制
- 日志记录：详细的中文日志输出

作者：AI Gen Hub Team
创建时间：2025-08-24
"""

import asyncio
import json
import logging as std_logging
from typing import Any, AsyncIterator, Dict, List, Optional, Union
from datetime import datetime
from uuid import uuid4

import httpx
from pydantic import ValidationError

from .chat_interfaces import (
    ChatProvider,
    ChatMessage,
    ChatConfig,
    ChatResponse,
    ChatStreamChunk,
    ChatUsage,
    ChatMessageRole,
    ChatFinishReason,
    ChatStreamEventType,
    ChatError,
    ChatValidationError,
    ChatProviderError,
    ChatRateLimitError,
    ChatContentFilterError,
)

# 设置日志记录器
logger = std_logging.getLogger(__name__)


class BaseChatProvider(ChatProvider):
    """Chat供应商基础实现类
    
    提供通用功能的默认实现，具体供应商适配器可以继承此类。
    """
    
    def __init__(self, provider_name: str, config: Dict[str, Any]):
        """初始化基础Chat供应商
        
        Args:
            provider_name: 供应商名称
            config: 供应商配置
        """
        super().__init__(provider_name, config)
        
        # HTTP客户端配置
        self.base_url = config.get("base_url", "")
        self.api_key = config.get("api_key", "")
        self.timeout = config.get("timeout", 30.0)
        self.max_retries = config.get("max_retries", 3)
        
        # 创建HTTP客户端
        self._client: Optional[httpx.AsyncClient] = None
        
        # 支持的模型列表（子类应该覆盖）
        self._supported_models: List[str] = []
        
        # 参数映射配置（子类应该覆盖）
        self._parameter_mapping: Dict[str, str] = {}
        
        self.logger.info(f"初始化Chat供应商: {provider_name}")
    
    async def _get_client(self) -> httpx.AsyncClient:
        """获取HTTP客户端（懒加载）
        
        Returns:
            httpx.AsyncClient: HTTP客户端实例
        """
        if self._client is None:
            self._client = httpx.AsyncClient(
                timeout=httpx.Timeout(self.timeout),
                limits=httpx.Limits(max_connections=100, max_keepalive_connections=20)
            )
        return self._client
    
    async def _close_client(self):
        """关闭HTTP客户端"""
        if self._client:
            await self._client.aclose()
            self._client = None
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头（子类应该覆盖）
        
        Returns:
            Dict[str, str]: 请求头字典
        """
        return {
            "Content-Type": "application/json",
            "User-Agent": f"AI-Gen-Hub/{self.provider_name}"
        }
    
    def _validate_messages(self, messages: List[ChatMessage]) -> None:
        """验证消息列表
        
        Args:
            messages: 消息列表
            
        Raises:
            ChatValidationError: 消息验证失败
        """
        if not messages:
            raise ChatValidationError("消息列表不能为空", provider=self.provider_name)
        
        # 检查消息角色序列的合理性
        for i, message in enumerate(messages):
            if not message.content.strip():
                raise ChatValidationError(f"第{i+1}条消息内容不能为空", provider=self.provider_name)
            
            # 系统消息只能在开头
            if message.role == ChatMessageRole.SYSTEM and i > 0:
                # 检查前面是否还有非系统消息
                for prev_msg in messages[:i]:
                    if prev_msg.role != ChatMessageRole.SYSTEM:
                        raise ChatValidationError(
                            "系统消息必须在对话开头，不能穿插在用户和助手消息之间",
                            provider=self.provider_name
                        )
    
    def _validate_config(self, config: ChatConfig) -> None:
        """验证配置参数
        
        Args:
            config: Chat配置
            
        Raises:
            ChatValidationError: 配置验证失败
        """
        # 验证温度参数
        if not (0.0 <= config.temperature <= 2.0):
            raise ChatValidationError(
                f"温度参数必须在0.0-2.0之间，当前值: {config.temperature}",
                provider=self.provider_name
            )
        
        # 验证top_p参数
        if config.top_p is not None and not (0.0 <= config.top_p <= 1.0):
            raise ChatValidationError(
                f"top_p参数必须在0.0-1.0之间，当前值: {config.top_p}",
                provider=self.provider_name
            )
        
        # 验证max_tokens参数
        if config.max_tokens is not None and config.max_tokens <= 0:
            raise ChatValidationError(
                f"max_tokens必须大于0，当前值: {config.max_tokens}",
                provider=self.provider_name
            )
        
        # 验证停止序列
        if config.stop_sequences:
            if len(config.stop_sequences) > 10:
                raise ChatValidationError(
                    f"停止序列数量不能超过10个，当前数量: {len(config.stop_sequences)}",
                    provider=self.provider_name
                )
    
    def _map_parameters(self, config: ChatConfig) -> Dict[str, Any]:
        """映射配置参数到供应商特定格式（子类应该覆盖）
        
        Args:
            config: 统一配置参数
            
        Returns:
            Dict[str, Any]: 供应商特定的参数字典
        """
        # 默认实现：直接转换为字典
        params = config.dict(exclude_none=True)
        
        # 应用参数映射
        mapped_params = {}
        for key, value in params.items():
            mapped_key = self._parameter_mapping.get(key, key)
            mapped_params[mapped_key] = value
        
        return mapped_params
    
    def _map_messages(self, messages: List[ChatMessage]) -> Any:
        """映射消息格式到供应商特定格式（子类应该覆盖）
        
        Args:
            messages: 统一消息列表
            
        Returns:
            Any: 供应商特定的消息格式
        """
        # 默认实现：转换为字典列表
        return [msg.dict(exclude_none=True) for msg in messages]
    
    def _parse_response(self, response_data: Dict[str, Any], model: str) -> ChatResponse:
        """解析供应商响应为统一格式（子类应该覆盖）
        
        Args:
            response_data: 供应商原始响应数据
            model: 使用的模型名称
            
        Returns:
            ChatResponse: 统一格式的响应
        """
        # 默认实现：假设响应格式类似OpenAI
        choice = response_data.get("choices", [{}])[0]
        message_data = choice.get("message", {})
        
        # 构建响应消息
        response_message = ChatMessage(
            role=ChatMessageRole.ASSISTANT,
            content=message_data.get("content", ""),
            tool_calls=message_data.get("tool_calls")
        )
        
        # 构建使用量统计
        usage_data = response_data.get("usage", {})
        usage = ChatUsage(
            prompt_tokens=usage_data.get("prompt_tokens", 0),
            completion_tokens=usage_data.get("completion_tokens", 0),
            total_tokens=usage_data.get("total_tokens", 0)
        )
        
        # 映射完成原因
        finish_reason_map = {
            "stop": ChatFinishReason.STOP,
            "length": ChatFinishReason.LENGTH,
            "content_filter": ChatFinishReason.CONTENT_FILTER,
            "tool_calls": ChatFinishReason.TOOL_CALLS,
            "function_call": ChatFinishReason.TOOL_CALLS,
        }
        finish_reason = finish_reason_map.get(
            choice.get("finish_reason", "stop"),
            ChatFinishReason.STOP
        )
        
        return ChatResponse(
            id=response_data.get("id", str(uuid4())),
            model=model,
            provider=self.provider_name,
            message=response_message,
            finish_reason=finish_reason,
            usage=usage
        )
    
    def _parse_stream_chunk(self, chunk_data: Dict[str, Any], chunk_id: str) -> Optional[ChatStreamChunk]:
        """解析流式响应块（子类应该覆盖）
        
        Args:
            chunk_data: 流式响应块数据
            chunk_id: 块ID
            
        Returns:
            Optional[ChatStreamChunk]: 解析后的流式响应块，如果无效则返回None
        """
        # 默认实现：假设格式类似OpenAI
        choices = chunk_data.get("choices", [])
        if not choices:
            return None
        
        choice = choices[0]
        delta = choice.get("delta", {})
        
        # 确定事件类型
        if "content" in delta:
            event_type = ChatStreamEventType.CONTENT_DELTA
            delta_content = delta["content"]
        elif choice.get("finish_reason"):
            event_type = ChatStreamEventType.MESSAGE_STOP
            delta_content = None
        else:
            return None  # 跳过无效块
        
        # 构建流式响应块
        chunk = ChatStreamChunk(
            id=chunk_id,
            event_type=event_type,
            delta_content=delta_content
        )
        
        # 如果是结束块，添加完成信息
        if event_type == ChatStreamEventType.MESSAGE_STOP:
            finish_reason_map = {
                "stop": ChatFinishReason.STOP,
                "length": ChatFinishReason.LENGTH,
                "content_filter": ChatFinishReason.CONTENT_FILTER,
                "tool_calls": ChatFinishReason.TOOL_CALLS,
            }
            chunk.finish_reason = finish_reason_map.get(
                choice.get("finish_reason"),
                ChatFinishReason.STOP
            )
            
            # 添加使用量统计（如果有）
            usage_data = chunk_data.get("usage")
            if usage_data:
                chunk.usage = ChatUsage(
                    prompt_tokens=usage_data.get("prompt_tokens", 0),
                    completion_tokens=usage_data.get("completion_tokens", 0),
                    total_tokens=usage_data.get("total_tokens", 0)
                )
        
        return chunk
    
    async def _handle_http_error(self, response: httpx.Response) -> None:
        """处理HTTP错误响应
        
        Args:
            response: HTTP响应对象
            
        Raises:
            ChatError: 相应的Chat错误
        """
        try:
            error_data = response.json()
            error_message = error_data.get("error", {}).get("message", f"HTTP {response.status_code}")
        except:
            error_message = f"HTTP {response.status_code}: {response.text}"
        
        # 根据状态码抛出相应异常
        if response.status_code == 400:
            raise ChatValidationError(f"请求参数错误: {error_message}", provider=self.provider_name)
        elif response.status_code == 401:
            raise ChatProviderError(f"认证失败: {error_message}", provider=self.provider_name)
        elif response.status_code == 403:
            raise ChatContentFilterError(f"内容被过滤: {error_message}", provider=self.provider_name)
        elif response.status_code == 429:
            raise ChatRateLimitError(f"请求频率超限: {error_message}", provider=self.provider_name)
        elif response.status_code >= 500:
            raise ChatProviderError(f"服务器错误: {error_message}", provider=self.provider_name)
        else:
            raise ChatProviderError(f"未知错误: {error_message}", provider=self.provider_name)
    
    async def validate_config(self, config: ChatConfig) -> bool:
        """验证配置参数是否有效
        
        Args:
            config: Chat配置参数
            
        Returns:
            bool: 配置是否有效
        """
        try:
            self._validate_config(config)
            return True
        except ChatValidationError as e:
            self.logger.warning(f"配置验证失败: {e.message}")
            return False
    
    async def get_supported_models(self) -> List[str]:
        """获取支持的模型列表
        
        Returns:
            List[str]: 支持的模型名称列表
        """
        return self._supported_models.copy()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self._close_client()


class StreamAccumulator:
    """流式响应累积器
    
    用于累积流式响应块，构建完整的响应内容。
    """
    
    def __init__(self):
        """初始化累积器"""
        self.content_parts: List[str] = []
        self.tool_calls: List[Dict[str, Any]] = []
        self.finish_reason: Optional[ChatFinishReason] = None
        self.usage: Optional[ChatUsage] = None
        self.metadata: Dict[str, Any] = {}
    
    def add_chunk(self, chunk: ChatStreamChunk) -> None:
        """添加流式响应块
        
        Args:
            chunk: 流式响应块
        """
        if chunk.delta_content:
            self.content_parts.append(chunk.delta_content)
        
        if chunk.delta_tool_calls:
            self.tool_calls.extend(chunk.delta_tool_calls)
        
        if chunk.finish_reason:
            self.finish_reason = chunk.finish_reason
        
        if chunk.usage:
            self.usage = chunk.usage
        
        if chunk.metadata:
            self.metadata.update(chunk.metadata)
    
    def get_accumulated_content(self) -> str:
        """获取累积的完整内容
        
        Returns:
            str: 完整内容
        """
        return "".join(self.content_parts)
    
    def build_response(self, response_id: str, model: str, provider: str) -> ChatResponse:
        """构建完整的Chat响应
        
        Args:
            response_id: 响应ID
            model: 模型名称
            provider: 供应商名称
            
        Returns:
            ChatResponse: 完整的Chat响应
        """
        message = ChatMessage(
            role=ChatMessageRole.ASSISTANT,
            content=self.get_accumulated_content(),
            tool_calls=self.tool_calls if self.tool_calls else None
        )
        
        return ChatResponse(
            id=response_id,
            model=model,
            provider=provider,
            message=message,
            finish_reason=self.finish_reason or ChatFinishReason.STOP,
            usage=self.usage or ChatUsage(),
            metadata=self.metadata if self.metadata else None
        )
