"""
AI Gen Hub 响应模型定义

包含所有类型的响应模型：
- 传统格式的响应模型（保持向后兼容）
- 优化版本的响应模型（新的结构化设计）
- 流式响应模型
- 错误响应模型

重构说明：
- 整合了原有 interfaces.py 和 docs/api_optimization/ 中的响应模型
- 提供统一的响应格式和错误处理
- 支持流式输出和批量处理
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field

from .base import (
    BaseResponse,
    Message,
    Usage,
    FinishReason,
    ProviderType,
    generate_id,
    get_current_timestamp,
)

# 设置日志记录器
logger = logging.getLogger(__name__)


# =============================================================================
# 响应组件模型
# =============================================================================

class UsageStats(BaseModel):
    """增强的使用量统计模型"""
    prompt_tokens: int = Field(0, description="输入token数量")
    completion_tokens: int = Field(0, description="输出token数量")
    total_tokens: int = Field(0, description="总token数量")
    
    # 扩展统计信息
    thinking_tokens: Optional[int] = Field(None, description="思考过程token数量（某些模型支持）")
    cached_tokens: Optional[int] = Field(None, description="缓存命中的token数量")
    
    # 成本信息
    estimated_cost: Optional[float] = Field(None, description="预估成本（美元）")
    cost_breakdown: Optional[Dict[str, float]] = Field(None, description="成本明细")

    def __post_init__(self):
        """确保总token数量的一致性"""
        if self.total_tokens == 0:
            self.total_tokens = self.prompt_tokens + self.completion_tokens


class PerformanceMetrics(BaseModel):
    """性能指标模型"""
    processing_time: float = Field(..., description="总处理时间（秒）")
    queue_time: Optional[float] = Field(None, description="队列等待时间（秒）")
    inference_time: Optional[float] = Field(None, description="推理时间（秒）")
    
    # 流式输出相关指标
    first_token_time: Optional[float] = Field(None, description="首个token生成时间（秒）")
    tokens_per_second: Optional[float] = Field(None, description="生成速度（token/秒）")
    
    # 网络相关指标
    network_latency: Optional[float] = Field(None, description="网络延迟（秒）")
    retry_count: int = Field(0, description="重试次数")


class ProviderInfo(BaseModel):
    """供应商信息模型"""
    name: str = Field(..., description="供应商名称")
    model: str = Field(..., description="实际使用的模型名称")
    version: Optional[str] = Field(None, description="模型版本")
    region: Optional[str] = Field(None, description="服务区域")
    endpoint: Optional[str] = Field(None, description="API端点")


class ErrorInfo(BaseModel):
    """错误信息模型"""
    code: str = Field(..., description="错误代码")
    message: str = Field(..., description="错误消息")
    type: str = Field(..., description="错误类型")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    
    # 供应商特定错误信息
    provider_error: Optional[Dict[str, Any]] = Field(None, description="供应商原始错误信息")
    
    # 重试相关信息
    retryable: bool = Field(False, description="是否可重试")
    retry_after: Optional[int] = Field(None, description="建议重试间隔（秒）")


# =============================================================================
# 传统格式响应模型（保持向后兼容）
# =============================================================================

class TextGenerationChoice(BaseModel):
    """传统格式的文本生成选择模型"""
    index: int = Field(..., description="选择索引")
    message: Message = Field(..., description="生成的消息")
    finish_reason: Optional[str] = Field(None, description="结束原因")


class TextGenerationResponse(BaseResponse):
    """传统格式的文本生成响应模型"""
    object: str = Field("chat.completion", description="对象类型")
    model: str = Field(..., description="使用的模型")
    choices: List[TextGenerationChoice] = Field(..., description="生成选择列表")
    usage: Optional[Usage] = Field(None, description="使用量统计")
    provider: str = Field(..., description="供应商名称")


class StreamChoice(BaseModel):
    """流式选择项
    
    为流式输出提供 OpenAI 风格的增量数据结构：包含 delta 字段，便于上层通过
    choice.delta.content 的方式进行读取。
    """
    index: int = Field(..., description="选择索引")
    delta: Message = Field(..., description="增量消息（只包含新增内容）")
    finish_reason: Optional[str] = Field(None, description="结束原因")


class TextGenerationStreamChunk(BaseResponse):
    """传统格式的文本生成流式响应块模型"""
    object: str = Field("chat.completion.chunk", description="对象类型")
    model: str = Field(..., description="使用的模型")
    choices: List[StreamChoice] = Field(..., description="流式选择数据")
    provider: str = Field(..., description="供应商名称")


# =============================================================================
# 图像生成响应模型
# =============================================================================

class ImageData(BaseModel):
    """图像数据模型"""
    url: Optional[str] = Field(None, description="图像URL")
    b64_json: Optional[str] = Field(None, description="Base64编码的图像数据")
    revised_prompt: Optional[str] = Field(None, description="修订后的提示")


class ImageGenerationResponse(BaseResponse):
    """图像生成响应模型"""
    id: str = Field(..., description="响应ID")
    data: List[ImageData] = Field(..., description="生成的图像数据")
    provider: str = Field(..., description="供应商名称")


# =============================================================================
# 优化版本响应模型
# =============================================================================

class Choice(BaseModel):
    """优化版本的选择模型"""
    index: int = Field(..., description="选择索引")
    message: Message = Field(..., description="生成的消息")
    finish_reason: FinishReason = Field(..., description="完成原因")

    # 扩展信息
    logprobs: Optional[Dict[str, Any]] = Field(None, description="对数概率信息")
    safety_ratings: Optional[List[Dict[str, Any]]] = Field(None, description="安全评级")


class OptimizedTextGenerationResponse(BaseResponse):
    """优化版本的文本生成响应模型

    主要改进：
    1. 结构化的响应组织
    2. 丰富的元数据信息
    3. 标准化的错误处理
    4. 更好的性能监控支持
    """

    # === 基础响应信息 ===
    object: str = Field("chat.completion", description="对象类型")

    # === 核心响应数据 ===
    choices: List[Choice] = Field(..., description="生成选择列表")

    # === 使用量和性能信息 ===
    usage: Optional[UsageStats] = Field(None, description="使用量统计")
    performance: PerformanceMetrics = Field(..., description="性能指标")

    # === 供应商信息 ===
    provider: ProviderInfo = Field(..., description="供应商信息")

    # === 请求追踪信息 ===
    trace_id: Optional[str] = Field(None, description="链路追踪ID")

    # === 扩展元数据 ===
    metadata: Optional[Dict[str, Any]] = Field(None, description="响应元数据")

    def to_legacy_format(self) -> TextGenerationResponse:
        """转换为传统格式的响应

        确保优化版本响应可以与现有代码兼容。
        """
        # 转换选择列表
        legacy_choices = []
        for choice in self.choices:
            legacy_choice = TextGenerationChoice(
                index=choice.index,
                message=choice.message,
                finish_reason=choice.finish_reason.value
            )
            legacy_choices.append(legacy_choice)

        # 转换使用量统计
        legacy_usage = None
        if self.usage:
            legacy_usage = Usage(
                prompt_tokens=self.usage.prompt_tokens,
                completion_tokens=self.usage.completion_tokens,
                total_tokens=self.usage.total_tokens
            )

        return TextGenerationResponse(
            id=self.id,
            created=self.created,
            model=self.provider.model,
            choices=legacy_choices,
            usage=legacy_usage,
            provider=self.provider.name,
            request_id=self.request_id,
            processing_time=self.processing_time
        )


# =============================================================================
# 流式响应模型
# =============================================================================

class StreamChunk(BaseModel):
    """优化版本的流式响应块"""
    id: str = Field(..., description="响应ID")
    object: str = Field("chat.completion.chunk", description="对象类型")
    created: int = Field(..., description="创建时间戳")

    # 流式数据
    choices: List[Choice] = Field(..., description="流式选择数据")

    # 供应商信息
    provider: ProviderInfo = Field(..., description="供应商信息")

    # 请求追踪
    request_id: str = Field(..., description="请求ID")

    # 流式特有字段
    chunk_index: int = Field(..., description="块索引")
    is_final: bool = Field(False, description="是否为最后一块")

    # 使用量统计（通常只在最后一块中包含）
    usage: Optional[UsageStats] = Field(None, description="使用量统计")


class StreamEvent(BaseModel):
    """流式事件包装器"""
    event: str = Field(..., description="事件类型：chunk, error, done")
    data: Union[StreamChunk, ErrorInfo, str] = Field(..., description="事件数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="事件时间戳")


# =============================================================================
# 错误响应模型
# =============================================================================

class ErrorResponse(BaseModel):
    """标准化错误响应"""
    error: ErrorInfo = Field(..., description="错误信息")
    request_id: str = Field(..., description="请求ID")
    timestamp: int = Field(..., description="错误发生时间戳")

    # === 调试信息（仅开发环境）===
    debug_info: Optional[Dict[str, Any]] = Field(None, description="调试信息")


# =============================================================================
# 批量响应模型
# =============================================================================

class BatchTextGenerationResponse(BaseModel):
    """批量文本生成响应"""
    id: str = Field(..., description="批量请求ID")
    object: str = Field("batch.completion", description="对象类型")
    created: int = Field(..., description="创建时间戳")

    # === 批量结果 ===
    responses: List[Union[OptimizedTextGenerationResponse, ErrorResponse]] = Field(
        ..., description="批量响应结果"
    )

    # === 批量统计 ===
    total_requests: int = Field(..., description="总请求数")
    successful_requests: int = Field(..., description="成功请求数")
    failed_requests: int = Field(..., description="失败请求数")

    # === 批量性能 ===
    total_processing_time: float = Field(..., description="总处理时间")
    average_processing_time: float = Field(..., description="平均处理时间")

    # === 批量使用量统计 ===
    total_usage: Optional[UsageStats] = Field(None, description="总使用量统计")


# =============================================================================
# 响应工厂函数
# =============================================================================

def create_error_response(
    error_code: str,
    error_message: str,
    request_id: str,
    error_type: str = "api_error",
    details: Optional[Dict[str, Any]] = None
) -> ErrorResponse:
    """创建标准化错误响应

    Args:
        error_code: 错误代码
        error_message: 错误消息
        request_id: 请求ID
        error_type: 错误类型
        details: 错误详情

    Returns:
        ErrorResponse: 标准化错误响应
    """
    error_info = ErrorInfo(
        code=error_code,
        message=error_message,
        type=error_type,
        details=details
    )

    return ErrorResponse(
        error=error_info,
        request_id=request_id,
        timestamp=get_current_timestamp()
    )
