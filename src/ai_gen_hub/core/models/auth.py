"""
认证与授权相关数据模型

定义用户、角色、权限等认证授权相关的数据模型
"""

from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Set, Any
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, EmailStr, validator
from passlib.context import CryptContext


# =============================================================================
# 枚举定义
# =============================================================================

class UserRole(str, Enum):
    """用户角色枚举"""
    ADMIN = "admin"          # 管理员：完全访问权限
    DEVELOPER = "developer"  # 开发者：API访问和开发工具
    USER = "user"           # 普通用户：基础API访问
    GUEST = "guest"         # 访客：只读访问


class Permission(str, Enum):
    """权限枚举"""
    # 系统管理权限
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_CONFIG = "system:config"
    SYSTEM_MONITOR = "system:monitor"
    
    # 用户管理权限
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    USER_MANAGE_QUOTA = "user:manage_quota"
    
    # API访问权限
    API_TEXT_GENERATE = "api:text:generate"
    API_IMAGE_GENERATE = "api:image:generate"
    API_STREAM = "api:stream"
    API_BATCH = "api:batch"
    
    # 供应商管理权限
    PROVIDER_CONFIG = "provider:config"
    PROVIDER_MONITOR = "provider:monitor"
    
    # 调试和开发权限
    DEBUG_ACCESS = "debug:access"
    DEBUG_LOGS = "debug:logs"
    DEBUG_METRICS = "debug:metrics"


class UserStatus(str, Enum):
    """用户状态枚举"""
    ACTIVE = "active"        # 活跃
    INACTIVE = "inactive"    # 非活跃
    SUSPENDED = "suspended"  # 暂停
    BANNED = "banned"        # 封禁


class TokenType(str, Enum):
    """Token类型枚举"""
    ACCESS = "access"        # 访问令牌
    REFRESH = "refresh"      # 刷新令牌
    API_KEY = "api_key"      # API密钥


# =============================================================================
# 基础模型
# =============================================================================

class User(BaseModel):
    """用户模型"""
    id: UUID = Field(default_factory=uuid4, description="用户唯一标识")
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    
    # 认证信息
    password_hash: str = Field(..., description="密码哈希")
    role: UserRole = Field(UserRole.USER, description="用户角色")
    status: UserStatus = Field(UserStatus.ACTIVE, description="用户状态")
    
    # 权限和配额
    permissions: Set[Permission] = Field(default_factory=set, description="用户权限")
    api_quota: int = Field(1000, description="API调用配额（每月）")
    api_quota_used: int = Field(0, description="已使用的API配额")
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    last_login_at: Optional[datetime] = Field(None, description="最后登录时间")
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict, description="用户元数据")
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
            set: lambda v: list(v)
        }
    
    @validator('permissions', pre=True)
    def validate_permissions(cls, v):
        """验证权限列表"""
        if isinstance(v, list):
            return set(v)
        return v
    
    def has_permission(self, permission: Permission) -> bool:
        """检查用户是否有指定权限"""
        return permission in self.permissions
    
    def has_any_permission(self, permissions: List[Permission]) -> bool:
        """检查用户是否有任意一个指定权限"""
        return any(perm in self.permissions for perm in permissions)
    
    def has_all_permissions(self, permissions: List[Permission]) -> bool:
        """检查用户是否有所有指定权限"""
        return all(perm in self.permissions for perm in permissions)
    
    def is_admin(self) -> bool:
        """检查是否为管理员"""
        return self.role == UserRole.ADMIN
    
    def is_active(self) -> bool:
        """检查用户是否活跃"""
        return self.status == UserStatus.ACTIVE
    
    def can_use_api(self) -> bool:
        """检查是否可以使用API"""
        return (
            self.is_active() and 
            self.api_quota_used < self.api_quota and
            self.has_permission(Permission.API_TEXT_GENERATE)
        )


class UserCreate(BaseModel):
    """创建用户请求模型"""
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    password: str = Field(..., min_length=8, max_length=128)
    full_name: Optional[str] = Field(None, max_length=100)
    role: UserRole = Field(UserRole.USER)
    api_quota: int = Field(1000)
    permissions: List[Permission] = Field(default_factory=list)


class UserUpdate(BaseModel):
    """更新用户请求模型"""
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, max_length=100)
    role: Optional[UserRole] = None
    status: Optional[UserStatus] = None
    api_quota: Optional[int] = None
    permissions: Optional[List[Permission]] = None


class UserLogin(BaseModel):
    """用户登录请求模型"""
    username: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")
    remember_me: bool = Field(False, description="记住我")


class PasswordChange(BaseModel):
    """修改密码请求模型"""
    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., min_length=8, max_length=128, description="新密码")


class APIKey(BaseModel):
    """API密钥模型"""
    id: UUID = Field(default_factory=uuid4, description="密钥唯一标识")
    user_id: UUID = Field(..., description="用户ID")
    name: str = Field(..., max_length=100, description="密钥名称")
    key_hash: str = Field(..., description="密钥哈希")
    key_prefix: str = Field(..., description="密钥前缀（用于显示）")
    
    # 权限和限制
    permissions: Set[Permission] = Field(default_factory=set, description="密钥权限")
    rate_limit: int = Field(100, description="速率限制（每分钟）")
    
    # 状态和时间
    is_active: bool = Field(True, description="是否活跃")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    last_used_at: Optional[datetime] = Field(None, description="最后使用时间")
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
            set: lambda v: list(v)
        }


class APIKeyCreate(BaseModel):
    """创建API密钥请求模型"""
    name: str = Field(..., max_length=100, description="密钥名称")
    permissions: List[Permission] = Field(default_factory=list, description="密钥权限")
    rate_limit: int = Field(100, description="速率限制（每分钟）")
    expires_in_days: Optional[int] = Field(None, description="有效期（天）")


class Token(BaseModel):
    """JWT Token模型"""
    access_token: str = Field(..., description="访问令牌")
    refresh_token: Optional[str] = Field(None, description="刷新令牌")
    token_type: str = Field("bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")
    scope: str = Field("api_access", description="权限范围")


class TokenPayload(BaseModel):
    """JWT Token载荷模型"""
    sub: str = Field(..., description="用户ID")
    exp: int = Field(..., description="过期时间戳")
    iat: int = Field(..., description="签发时间戳")
    type: TokenType = Field(..., description="令牌类型")
    scope: str = Field(..., description="权限范围")
    permissions: List[Permission] = Field(default_factory=list, description="用户权限")


class UserUsage(BaseModel):
    """用户使用量统计模型"""
    user_id: UUID = Field(..., description="用户ID")
    service_type: str = Field(..., description="服务类型")
    model: str = Field(..., description="使用的模型")
    
    # 使用量统计
    tokens_used: int = Field(0, description="使用的token数量")
    requests_count: int = Field(1, description="请求次数")
    cost: float = Field(0.0, description="成本（美元）")
    
    # 时间信息
    date: datetime = Field(default_factory=datetime.utcnow, description="使用日期")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="记录创建时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


# =============================================================================
# 权限管理
# =============================================================================

class RolePermissions:
    """角色权限映射"""
    
    ROLE_PERMISSIONS = {
        UserRole.ADMIN: {
            # 系统管理
            Permission.SYSTEM_ADMIN,
            Permission.SYSTEM_CONFIG,
            Permission.SYSTEM_MONITOR,
            
            # 用户管理
            Permission.USER_CREATE,
            Permission.USER_READ,
            Permission.USER_UPDATE,
            Permission.USER_DELETE,
            Permission.USER_MANAGE_QUOTA,
            
            # API访问
            Permission.API_TEXT_GENERATE,
            Permission.API_IMAGE_GENERATE,
            Permission.API_STREAM,
            Permission.API_BATCH,
            
            # 供应商管理
            Permission.PROVIDER_CONFIG,
            Permission.PROVIDER_MONITOR,
            
            # 调试权限
            Permission.DEBUG_ACCESS,
            Permission.DEBUG_LOGS,
            Permission.DEBUG_METRICS,
        },
        
        UserRole.DEVELOPER: {
            # API访问
            Permission.API_TEXT_GENERATE,
            Permission.API_IMAGE_GENERATE,
            Permission.API_STREAM,
            Permission.API_BATCH,
            
            # 调试权限
            Permission.DEBUG_ACCESS,
            Permission.DEBUG_LOGS,
            Permission.DEBUG_METRICS,
            
            # 基础用户权限
            Permission.USER_READ,
        },
        
        UserRole.USER: {
            # 基础API访问
            Permission.API_TEXT_GENERATE,
            Permission.API_IMAGE_GENERATE,
            
            # 基础用户权限
            Permission.USER_READ,
        },
        
        UserRole.GUEST: {
            # 只读权限
            Permission.USER_READ,
        }
    }
    
    @classmethod
    def get_permissions_for_role(cls, role: UserRole) -> Set[Permission]:
        """获取角色对应的权限"""
        return cls.ROLE_PERMISSIONS.get(role, set())
    
    @classmethod
    def create_user_with_role_permissions(cls, user_data: UserCreate) -> User:
        """创建用户并分配角色权限"""
        # 获取角色对应的权限
        role_permissions = cls.get_permissions_for_role(user_data.role)
        
        # 合并用户指定的权限和角色权限
        all_permissions = role_permissions.union(set(user_data.permissions))
        
        # 创建用户
        user = User(
            username=user_data.username,
            email=user_data.email,
            full_name=user_data.full_name,
            password_hash="",  # 需要在外部设置
            role=user_data.role,
            api_quota=user_data.api_quota,
            permissions=all_permissions
        )
        
        return user


# =============================================================================
# 密码管理
# =============================================================================

class PasswordManager:
    """密码管理器"""
    
    def __init__(self):
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    def hash_password(self, password: str) -> str:
        """哈希密码"""
        return self.pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return self.pwd_context.verify(plain_password, hashed_password)
    
    def needs_update(self, hashed_password: str) -> bool:
        """检查密码是否需要更新"""
        return self.pwd_context.needs_update(hashed_password)
