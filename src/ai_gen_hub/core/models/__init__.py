"""
AI Gen Hub 核心数据模型模块

这个模块包含了所有的数据模型定义，包括：
- 请求模型（传统格式和优化版本）
- 响应模型（传统格式和优化版本）
- 基础模型类和枚举定义
- 模型验证逻辑

重构说明：
- 将原本分散在 docs/api_optimization/ 中的模型定义统一整合到这里
- 保持向后兼容性，支持传统格式和优化版本的双向转换
- 提供清晰的模块结构和导入接口
"""

from .base import (
    # 基础枚举
    MessageRole,
    ModelType,
    FinishReason,
    
    # 基础模型
    Message,
    Usage,
)

from .requests import (
    # 传统请求模型
    TextGenerationRequest,
    ImageGenerationRequest,
    
    # 优化版本请求模型
    OptimizedTextGenerationRequest,
    SimpleTextGenerationRequest,
    
    # 配置模型
    GenerationConfig,
    StreamConfig,
    SafetyConfig,
)

from .responses import (
    # 传统响应模型
    TextGenerationResponse,
    TextGenerationChoice,
    TextGenerationStreamChunk,
    StreamChoice,
    ImageGenerationResponse,
    ImageData,
    
    # 优化版本响应模型
    OptimizedTextGenerationResponse,
    StreamChunk,
    StreamEvent,
    ErrorResponse,
    BatchTextGenerationResponse,
    
    # 响应组件
    Choice,
    UsageStats,
    PerformanceMetrics,
    ProviderInfo,
    ErrorInfo,
)

__all__ = [
    # 基础枚举
    "MessageRole",
    "ModelType", 
    "FinishReason",
    
    # 基础模型
    "Message",
    "Usage",
    
    # 请求模型
    "TextGenerationRequest",
    "ImageGenerationRequest",
    "OptimizedTextGenerationRequest",
    "SimpleTextGenerationRequest",
    "GenerationConfig",
    "StreamConfig",
    "SafetyConfig",
    
    # 响应模型
    "TextGenerationResponse",
    "TextGenerationChoice",
    "TextGenerationStreamChunk",
    "StreamChoice",
    "ImageGenerationResponse",
    "ImageData",
    "OptimizedTextGenerationResponse",
    "StreamChunk",
    "StreamEvent",
    "ErrorResponse",
    "BatchTextGenerationResponse",
    "Choice",
    "UsageStats",
    "PerformanceMetrics",
    "ProviderInfo",
    "ErrorInfo",
]
