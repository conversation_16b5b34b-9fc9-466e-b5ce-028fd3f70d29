"""
AI Gen Hub 基础数据模型

包含所有模型的基础类、枚举定义和通用组件。
这些是其他模型文件的基础依赖。

重构说明：
- 从原有的 interfaces.py 和 docs/api_optimization/ 中提取基础定义
- 统一枚举定义，确保一致性
- 提供可复用的基础模型类
"""

import logging
import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field

# 设置日志记录器
logger = logging.getLogger(__name__)


# =============================================================================
# 枚举定义
# =============================================================================

class ModelType(str, Enum):
    """AI模型类型枚举"""
    TEXT_GENERATION = "text_generation"  # 文本生成模型
    IMAGE_GENERATION = "image_generation"  # 图像生成模型
    EMBEDDING = "embedding"  # 嵌入模型
    AUDIO_GENERATION = "audio_generation"  # 音频生成模型
    VIDEO_GENERATION = "video_generation"  # 视频生成模型


class MessageRole(str, Enum):
    """消息角色枚举"""
    SYSTEM = "system"  # 系统消息
    USER = "user"  # 用户消息
    ASSISTANT = "assistant"  # 助手消息
    FUNCTION = "function"  # 函数调用消息
    TOOL = "tool"  # 工具调用消息


class FinishReason(str, Enum):
    """完成原因枚举"""
    STOP = "stop"                    # 自然停止
    LENGTH = "length"                # 达到最大长度
    CONTENT_FILTER = "content_filter"  # 内容过滤
    FUNCTION_CALL = "function_call"   # 函数调用
    TOOL_CALLS = "tool_calls"        # 工具调用
    ERROR = "error"                  # 发生错误


class ProviderType(str, Enum):
    """供应商类型枚举"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    DASHSCOPE = "dashscope"
    CUSTOM = "custom"


# =============================================================================
# 基础数据模型
# =============================================================================

class Message(BaseModel):
    """聊天消息模型
    
    统一的消息格式，支持所有供应商的消息类型。
    """
    role: MessageRole = Field(..., description="消息角色")
    content: str = Field(..., min_length=1, description="消息内容，不能为空")
    name: Optional[str] = Field(None, description="消息发送者名称")
    function_call: Optional[Dict[str, Any]] = Field(None, description="函数调用信息")
    tool_calls: Optional[List[Dict[str, Any]]] = Field(None, description="工具调用信息")

    class Config:
        """Pydantic配置"""
        use_enum_values = True


class Usage(BaseModel):
    """基础使用量统计模型
    
    兼容 OpenAI 格式的使用量统计。
    """
    prompt_tokens: int = Field(0, description="输入token数量")
    completion_tokens: int = Field(0, description="输出token数量") 
    total_tokens: int = Field(0, description="总token数量")

    def __post_init__(self):
        """确保总token数量的一致性"""
        if self.total_tokens == 0:
            self.total_tokens = self.prompt_tokens + self.completion_tokens


class BaseRequest(BaseModel):
    """请求模型基类
    
    提供所有请求模型的通用字段和方法。
    """
    # 元数据字段
    request_id: Optional[str] = Field(None, description="请求ID，用于追踪")
    user: Optional[str] = Field(None, description="用户标识，用于追踪和分析")
    metadata: Optional[Dict[str, Any]] = Field(None, description="请求元数据")
    
    class Config:
        """Pydantic配置"""
        extra = "forbid"  # 禁止额外字段
        use_enum_values = True  # 使用枚举值
        validate_assignment = True  # 验证赋值


class BaseResponse(BaseModel):
    """响应模型基类
    
    提供所有响应模型的通用字段和方法。
    """
    # 基础响应信息
    id: str = Field(..., description="响应唯一标识")
    created: int = Field(..., description="创建时间戳")
    
    # 请求追踪信息
    request_id: str = Field(..., description="请求ID")
    
    # 性能信息
    processing_time: float = Field(..., description="处理时间（秒）")
    
    class Config:
        """Pydantic配置"""
        use_enum_values = True


# =============================================================================
# 配置模型基类
# =============================================================================

class BaseConfig(BaseModel):
    """配置模型基类
    
    为各种配置类提供通用的验证和序列化功能。
    """
    
    class Config:
        """Pydantic配置"""
        extra = "forbid"  # 禁止额外字段
        validate_assignment = True  # 验证赋值
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，过滤None值"""
        return {k: v for k, v in self.dict().items() if v is not None}
    
    def merge_with(self, other: "BaseConfig") -> "BaseConfig":
        """与另一个配置合并，other的值优先"""
        if not isinstance(other, self.__class__):
            raise ValueError(f"无法合并不同类型的配置: {type(self)} 和 {type(other)}")
        
        # 获取两个配置的字典表示
        self_dict = self.to_dict()
        other_dict = other.to_dict()
        
        # 合并配置
        merged_dict = {**self_dict, **other_dict}
        
        # 创建新的配置实例
        return self.__class__(**merged_dict)


# =============================================================================
# 工具函数
# =============================================================================

def generate_id(prefix: str = "") -> str:
    """生成唯一ID

    Args:
        prefix: ID前缀

    Returns:
        str: 唯一ID
    """
    unique_id = str(uuid.uuid4())
    return f"{prefix}{unique_id}" if prefix else unique_id


def get_current_timestamp() -> int:
    """获取当前时间戳
    
    Returns:
        int: Unix时间戳
    """
    return int(datetime.now().timestamp())


def validate_enum_value(value: Any, enum_class: type, field_name: str) -> Any:
    """验证枚举值
    
    Args:
        value: 要验证的值
        enum_class: 枚举类
        field_name: 字段名称（用于错误消息）
        
    Returns:
        Any: 验证后的枚举值
        
    Raises:
        ValueError: 如果值不是有效的枚举值
    """
    if isinstance(value, enum_class):
        return value
    
    if isinstance(value, str):
        try:
            return enum_class(value)
        except ValueError:
            valid_values = [e.value for e in enum_class]
            raise ValueError(f"{field_name} 必须是以下值之一: {valid_values}，得到: {value}")
    
    raise ValueError(f"{field_name} 必须是字符串或 {enum_class.__name__} 枚举，得到: {type(value)}")
