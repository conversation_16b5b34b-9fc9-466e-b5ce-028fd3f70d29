"""
企业SSO集成服务

支持多种SSO供应商的集成，包括SAML、OIDC、LDAP等
"""

import base64
import json
import hashlib
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from urllib.parse import urlencode, parse_qs

from ai_gen_hub.core.logging import get_logger
from ai_gen_hub.core.exceptions import AIGenHubException
from .models import SSOConfiguration, SSOProvider


class SSOIntegrationService:
    """SSO集成服务"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.sso_configs: Dict[str, SSOConfiguration] = {}
    
    def register_sso_config(self, config: SSOConfiguration) -> bool:
        """注册SSO配置"""
        try:
            # 验证配置
            self._validate_sso_config(config)
            
            # 存储配置
            self.sso_configs[config.config_id] = config
            
            self.logger.info(f"注册SSO配置: {config.name} ({config.provider.value})")
            return True
            
        except Exception as e:
            self.logger.error(f"注册SSO配置失败: {e}")
            return False
    
    def get_sso_config(self, organization_id: str) -> Optional[SSOConfiguration]:
        """获取组织的SSO配置"""
        for config in self.sso_configs.values():
            if config.organization_id == organization_id and config.enabled:
                return config
        return None
    
    def generate_sso_login_url(self, organization_id: str, redirect_url: str = None) -> str:
        """生成SSO登录URL"""
        config = self.get_sso_config(organization_id)
        if not config:
            raise AIGenHubException("未找到SSO配置")
        
        if config.provider == SSOProvider.SAML:
            return self._generate_saml_login_url(config, redirect_url)
        elif config.provider == SSOProvider.OIDC:
            return self._generate_oidc_login_url(config, redirect_url)
        elif config.provider == SSOProvider.AZURE_AD:
            return self._generate_azure_ad_login_url(config, redirect_url)
        elif config.provider == SSOProvider.GOOGLE_WORKSPACE:
            return self._generate_google_login_url(config, redirect_url)
        else:
            raise AIGenHubException(f"不支持的SSO供应商: {config.provider}")
    
    def process_sso_callback(self, organization_id: str, callback_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理SSO回调"""
        config = self.get_sso_config(organization_id)
        if not config:
            raise AIGenHubException("未找到SSO配置")
        
        if config.provider == SSOProvider.SAML:
            return self._process_saml_callback(config, callback_data)
        elif config.provider == SSOProvider.OIDC:
            return self._process_oidc_callback(config, callback_data)
        elif config.provider == SSOProvider.AZURE_AD:
            return self._process_azure_ad_callback(config, callback_data)
        elif config.provider == SSOProvider.GOOGLE_WORKSPACE:
            return self._process_google_callback(config, callback_data)
        else:
            raise AIGenHubException(f"不支持的SSO供应商: {config.provider}")
    
    def validate_sso_token(self, organization_id: str, token: str) -> Dict[str, Any]:
        """验证SSO令牌"""
        config = self.get_sso_config(organization_id)
        if not config:
            raise AIGenHubException("未找到SSO配置")
        
        try:
            # 解码JWT令牌（简化实现）
            parts = token.split('.')
            if len(parts) != 3:
                raise AIGenHubException("无效的JWT令牌格式")
            
            # 解码头部和载荷
            header = json.loads(base64.urlsafe_b64decode(parts[0] + '=='))
            payload = json.loads(base64.urlsafe_b64decode(parts[1] + '=='))
            
            # 检查令牌过期时间
            if 'exp' in payload:
                exp_time = datetime.fromtimestamp(payload['exp'])
                if datetime.now() > exp_time:
                    raise AIGenHubException("令牌已过期")
            
            # 映射用户属性
            user_info = self._map_user_attributes(config, payload)
            
            return user_info
            
        except Exception as e:
            self.logger.error(f"验证SSO令牌失败: {e}")
            raise AIGenHubException("令牌验证失败")
    
    def _validate_sso_config(self, config: SSOConfiguration):
        """验证SSO配置"""
        if not config.organization_id:
            raise AIGenHubException("组织ID不能为空")
        
        if not config.provider:
            raise AIGenHubException("SSO供应商不能为空")
        
        if config.provider == SSOProvider.SAML:
            if not config.saml_entity_id or not config.saml_sso_url:
                raise AIGenHubException("SAML配置不完整")
        
        elif config.provider == SSOProvider.OIDC:
            if not config.client_id or not config.oidc_discovery_url:
                raise AIGenHubException("OIDC配置不完整")
        
        elif config.provider == SSOProvider.LDAP:
            if not config.ldap_server or not config.ldap_base_dn:
                raise AIGenHubException("LDAP配置不完整")
    
    def _generate_saml_login_url(self, config: SSOConfiguration, redirect_url: str = None) -> str:
        """生成SAML登录URL"""
        # 构建SAML认证请求
        auth_request = self._build_saml_auth_request(config, redirect_url)
        
        # Base64编码
        encoded_request = base64.b64encode(auth_request.encode()).decode()
        
        # 构建登录URL
        params = {
            'SAMLRequest': encoded_request,
            'RelayState': redirect_url or ''
        }
        
        return f"{config.saml_sso_url}?{urlencode(params)}"
    
    def _generate_oidc_login_url(self, config: SSOConfiguration, redirect_url: str = None) -> str:
        """生成OIDC登录URL"""
        # 生成state参数
        state = hashlib.sha256(f"{config.organization_id}_{datetime.now().isoformat()}".encode()).hexdigest()[:16]
        
        # 构建授权URL
        params = {
            'response_type': 'code',
            'client_id': config.client_id,
            'redirect_uri': redirect_url or f"/sso/callback/{config.organization_id}",
            'scope': ' '.join(config.oidc_scope),
            'state': state
        }
        
        # 从discovery URL获取授权端点（简化实现）
        auth_endpoint = config.oidc_discovery_url.replace('/.well-known/openid_configuration', '/auth')
        
        return f"{auth_endpoint}?{urlencode(params)}"
    
    def _generate_azure_ad_login_url(self, config: SSOConfiguration, redirect_url: str = None) -> str:
        """生成Azure AD登录URL"""
        # Azure AD特定的参数
        params = {
            'response_type': 'code',
            'client_id': config.client_id,
            'redirect_uri': redirect_url or f"/sso/callback/{config.organization_id}",
            'scope': 'openid profile email',
            'response_mode': 'query',
            'state': hashlib.sha256(f"{config.organization_id}_{datetime.now().isoformat()}".encode()).hexdigest()[:16]
        }
        
        # Azure AD授权端点
        tenant_id = config.sso_config.get('tenant_id', 'common')
        auth_endpoint = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/authorize"
        
        return f"{auth_endpoint}?{urlencode(params)}"
    
    def _generate_google_login_url(self, config: SSOConfiguration, redirect_url: str = None) -> str:
        """生成Google登录URL"""
        params = {
            'response_type': 'code',
            'client_id': config.client_id,
            'redirect_uri': redirect_url or f"/sso/callback/{config.organization_id}",
            'scope': 'openid profile email',
            'access_type': 'offline',
            'state': hashlib.sha256(f"{config.organization_id}_{datetime.now().isoformat()}".encode()).hexdigest()[:16]
        }
        
        return f"https://accounts.google.com/o/oauth2/v2/auth?{urlencode(params)}"
    
    def _build_saml_auth_request(self, config: SSOConfiguration, redirect_url: str = None) -> str:
        """构建SAML认证请求"""
        request_id = hashlib.sha256(f"{config.organization_id}_{datetime.now().isoformat()}".encode()).hexdigest()
        issue_instant = datetime.now().isoformat() + 'Z'
        
        # 简化的SAML请求模板
        saml_request = f"""<?xml version="1.0" encoding="UTF-8"?>
<samlp:AuthnRequest xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol"
                    xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion"
                    ID="{request_id}"
                    Version="2.0"
                    IssueInstant="{issue_instant}"
                    Destination="{config.saml_sso_url}"
                    AssertionConsumerServiceURL="/sso/callback/{config.organization_id}">
    <saml:Issuer>{config.saml_entity_id}</saml:Issuer>
</samlp:AuthnRequest>"""
        
        return saml_request
    
    def _process_saml_callback(self, config: SSOConfiguration, callback_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理SAML回调"""
        saml_response = callback_data.get('SAMLResponse')
        if not saml_response:
            raise AIGenHubException("缺少SAML响应")
        
        # 解码SAML响应
        try:
            decoded_response = base64.b64decode(saml_response).decode()
            
            # 简化的SAML响应解析（实际应使用专业的SAML库）
            # 这里只是示例实现
            user_info = {
                'email': '<EMAIL>',
                'first_name': 'John',
                'last_name': 'Doe',
                'display_name': 'John Doe'
            }
            
            return self._map_user_attributes(config, user_info)
            
        except Exception as e:
            self.logger.error(f"处理SAML回调失败: {e}")
            raise AIGenHubException("SAML响应处理失败")
    
    def _process_oidc_callback(self, config: SSOConfiguration, callback_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理OIDC回调"""
        code = callback_data.get('code')
        state = callback_data.get('state')
        
        if not code:
            raise AIGenHubException("缺少授权码")
        
        # 交换访问令牌（简化实现）
        # 实际应发送HTTP请求到令牌端点
        user_info = {
            'sub': '12345',
            'email': '<EMAIL>',
            'given_name': 'John',
            'family_name': 'Doe',
            'name': 'John Doe'
        }
        
        return self._map_user_attributes(config, user_info)
    
    def _process_azure_ad_callback(self, config: SSOConfiguration, callback_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理Azure AD回调"""
        return self._process_oidc_callback(config, callback_data)
    
    def _process_google_callback(self, config: SSOConfiguration, callback_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理Google回调"""
        return self._process_oidc_callback(config, callback_data)
    
    def _map_user_attributes(self, config: SSOConfiguration, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """映射用户属性"""
        mapped_user = {}
        
        for local_attr, remote_attr in config.attribute_mapping.items():
            if remote_attr in user_data:
                mapped_user[local_attr] = user_data[remote_attr]
        
        # 设置默认值
        if 'role' not in mapped_user:
            mapped_user['role'] = config.default_role
        
        # 添加组织信息
        mapped_user['organization_id'] = config.organization_id
        mapped_user['sso_provider'] = config.provider.value
        
        return mapped_user


class LDAPIntegration:
    """LDAP集成"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def authenticate_user(self, config: SSOConfiguration, username: str, password: str) -> Dict[str, Any]:
        """LDAP用户认证"""
        try:
            # 简化的LDAP认证实现
            # 实际应使用ldap3库
            
            # 构建用户DN
            user_dn = f"uid={username},{config.ldap_base_dn}"
            
            # 模拟LDAP认证
            if self._validate_ldap_credentials(config, user_dn, password):
                # 获取用户属性
                user_attributes = self._get_ldap_user_attributes(config, user_dn)
                
                # 映射属性
                mapped_user = self._map_ldap_attributes(config, user_attributes)
                
                return mapped_user
            else:
                raise AIGenHubException("LDAP认证失败")
                
        except Exception as e:
            self.logger.error(f"LDAP认证失败: {e}")
            raise AIGenHubException("LDAP认证失败")
    
    def _validate_ldap_credentials(self, config: SSOConfiguration, user_dn: str, password: str) -> bool:
        """验证LDAP凭据"""
        # 简化实现，实际应连接LDAP服务器验证
        return len(password) >= 6  # 简单的密码长度检查
    
    def _get_ldap_user_attributes(self, config: SSOConfiguration, user_dn: str) -> Dict[str, Any]:
        """获取LDAP用户属性"""
        # 模拟LDAP属性
        return {
            'cn': 'John Doe',
            'mail': '<EMAIL>',
            'givenName': 'John',
            'sn': 'Doe',
            'uid': 'johndoe',
            'memberOf': ['cn=users,ou=groups,dc=example,dc=com']
        }
    
    def _map_ldap_attributes(self, config: SSOConfiguration, ldap_attributes: Dict[str, Any]) -> Dict[str, Any]:
        """映射LDAP属性"""
        mapped_user = {}
        
        # 标准属性映射
        attribute_map = {
            'email': 'mail',
            'first_name': 'givenName',
            'last_name': 'sn',
            'display_name': 'cn',
            'username': 'uid'
        }
        
        for local_attr, ldap_attr in attribute_map.items():
            if ldap_attr in ldap_attributes:
                mapped_user[local_attr] = ldap_attributes[ldap_attr]
        
        # 处理组成员关系
        if 'memberOf' in ldap_attributes:
            groups = ldap_attributes['memberOf']
            if isinstance(groups, list):
                mapped_user['groups'] = groups
            else:
                mapped_user['groups'] = [groups]
        
        # 设置默认角色
        mapped_user['role'] = config.default_role
        mapped_user['organization_id'] = config.organization_id
        mapped_user['sso_provider'] = 'ldap'
        
        return mapped_user


class SSOTokenManager:
    """SSO令牌管理器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.active_tokens: Dict[str, Dict[str, Any]] = {}
    
    def create_sso_token(self, user_info: Dict[str, Any], expires_in: int = 3600) -> str:
        """创建SSO令牌"""
        # 生成令牌ID
        token_id = hashlib.sha256(f"{user_info.get('email')}_{datetime.now().isoformat()}".encode()).hexdigest()
        
        # 创建令牌数据
        token_data = {
            'token_id': token_id,
            'user_info': user_info,
            'created_at': datetime.now(),
            'expires_at': datetime.now() + timedelta(seconds=expires_in),
            'organization_id': user_info.get('organization_id'),
            'sso_provider': user_info.get('sso_provider')
        }
        
        # 存储令牌
        self.active_tokens[token_id] = token_data
        
        self.logger.info(f"创建SSO令牌: {user_info.get('email')}")
        return token_id
    
    def validate_token(self, token_id: str) -> Optional[Dict[str, Any]]:
        """验证令牌"""
        token_data = self.active_tokens.get(token_id)
        if not token_data:
            return None
        
        # 检查过期时间
        if datetime.now() > token_data['expires_at']:
            del self.active_tokens[token_id]
            return None
        
        return token_data['user_info']
    
    def revoke_token(self, token_id: str) -> bool:
        """撤销令牌"""
        if token_id in self.active_tokens:
            del self.active_tokens[token_id]
            self.logger.info(f"撤销SSO令牌: {token_id}")
            return True
        return False
    
    def cleanup_expired_tokens(self):
        """清理过期令牌"""
        now = datetime.now()
        expired_tokens = [
            token_id for token_id, token_data in self.active_tokens.items()
            if now > token_data['expires_at']
        ]
        
        for token_id in expired_tokens:
            del self.active_tokens[token_id]
        
        if expired_tokens:
            self.logger.info(f"清理过期SSO令牌: {len(expired_tokens)} 个")
