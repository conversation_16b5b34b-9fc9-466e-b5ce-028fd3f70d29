"""
企业审计日志服务

提供全面的审计日志记录、查询、分析和报告功能
"""

import json
import hashlib
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict

from ai_gen_hub.core.logging import get_logger
from ai_gen_hub.core.exceptions import AIGenHubException
from .models import (
    AuditLog,
    AuditEventType,
    AuditLogQueryRequest,
    EnterpriseConfig,
    SecurityLevel
)


class AuditService:
    """审计服务"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.audit_logs: List[AuditLog] = []
        self.enterprise_configs: Dict[str, EnterpriseConfig] = {}
        
        # 风险评分配置
        self.risk_weights = {
            AuditEventType.USER_LOGIN: 1.0,
            AuditEventType.USER_LOGOUT: 0.5,
            AuditEventType.API_ACCESS: 2.0,
            AuditEventType.DATA_ACCESS: 3.0,
            AuditEventType.CONFIG_CHANGE: 5.0,
            AuditEventType.PERMISSION_CHANGE: 7.0,
            AuditEventType.SECURITY_EVENT: 8.0,
            AuditEventType.SYSTEM_EVENT: 4.0,
            AuditEventType.COMPLIANCE_EVENT: 6.0
        }
    
    def log_event(
        self,
        organization_id: str,
        event_type: AuditEventType,
        event_name: str,
        user_id: str = None,
        resource_type: str = None,
        resource_id: str = None,
        action: str = "",
        result: str = "success",
        **kwargs
    ) -> str:
        """记录审计事件"""
        try:
            # 检查是否启用审计
            config = self.enterprise_configs.get(organization_id)
            if config and not config.audit_enabled:
                return ""
            
            if config and event_type not in config.audit_events:
                return ""
            
            # 创建审计日志
            log_id = f"audit_{int(datetime.now().timestamp() * 1000000)}"
            
            audit_log = AuditLog(
                log_id=log_id,
                organization_id=organization_id,
                event_type=event_type,
                event_name=event_name,
                user_id=user_id,
                resource_type=resource_type,
                resource_id=resource_id,
                action=action,
                result=result,
                **kwargs
            )
            
            # 计算风险评分
            audit_log.risk_score = self._calculate_risk_score(audit_log)
            
            # 检测安全标志
            audit_log.security_flags = self._detect_security_flags(audit_log)
            
            # 存储日志
            self.audit_logs.append(audit_log)
            
            # 检查是否需要实时告警
            if audit_log.risk_score > 7.0 or audit_log.security_flags:
                self._trigger_security_alert(audit_log)
            
            self.logger.info(f"记录审计事件: {event_name} ({event_type.value})")
            return log_id
            
        except Exception as e:
            self.logger.error(f"记录审计事件失败: {e}")
            return ""
    
    def query_logs(self, request: AuditLogQueryRequest) -> Tuple[List[AuditLog], int]:
        """查询审计日志"""
        try:
            # 过滤日志
            filtered_logs = []
            
            for log in self.audit_logs:
                # 组织过滤
                if log.organization_id != request.organization_id:
                    continue
                
                # 事件类型过滤
                if request.event_types:
                    event_types = [AuditEventType(et) for et in request.event_types]
                    if log.event_type not in event_types:
                        continue
                
                # 用户过滤
                if request.user_id and log.user_id != request.user_id:
                    continue
                
                # 时间范围过滤
                if request.start_time and log.timestamp < request.start_time:
                    continue
                
                if request.end_time and log.timestamp > request.end_time:
                    continue
                
                filtered_logs.append(log)
            
            # 排序（按时间倒序）
            filtered_logs.sort(key=lambda x: x.timestamp, reverse=True)
            
            # 分页
            total_count = len(filtered_logs)
            start_idx = request.offset
            end_idx = start_idx + request.limit
            
            paginated_logs = filtered_logs[start_idx:end_idx]
            
            self.logger.info(f"查询审计日志: 返回 {len(paginated_logs)}/{total_count} 条")
            return paginated_logs, total_count
            
        except Exception as e:
            self.logger.error(f"查询审计日志失败: {e}")
            return [], 0
    
    def get_audit_statistics(self, organization_id: str, days: int = 30) -> Dict[str, Any]:
        """获取审计统计"""
        try:
            # 时间范围
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            
            # 过滤日志
            org_logs = [
                log for log in self.audit_logs
                if log.organization_id == organization_id and start_time <= log.timestamp <= end_time
            ]
            
            # 统计数据
            stats = {
                'total_events': len(org_logs),
                'event_types': defaultdict(int),
                'users': defaultdict(int),
                'results': defaultdict(int),
                'daily_counts': defaultdict(int),
                'risk_distribution': defaultdict(int),
                'security_events': 0,
                'failed_events': 0,
                'top_users': [],
                'top_resources': [],
                'time_range': {
                    'start': start_time.isoformat(),
                    'end': end_time.isoformat(),
                    'days': days
                }
            }
            
            # 分析日志
            resource_counts = defaultdict(int)
            
            for log in org_logs:
                # 事件类型统计
                stats['event_types'][log.event_type.value] += 1
                
                # 用户统计
                if log.user_id:
                    stats['users'][log.user_id] += 1
                
                # 结果统计
                stats['results'][log.result] += 1
                
                # 每日统计
                day_key = log.timestamp.strftime('%Y-%m-%d')
                stats['daily_counts'][day_key] += 1
                
                # 风险分布
                risk_level = self._get_risk_level(log.risk_score)
                stats['risk_distribution'][risk_level] += 1
                
                # 安全事件
                if log.event_type == AuditEventType.SECURITY_EVENT:
                    stats['security_events'] += 1
                
                # 失败事件
                if log.result in ['failure', 'error']:
                    stats['failed_events'] += 1
                
                # 资源统计
                if log.resource_type and log.resource_id:
                    resource_key = f"{log.resource_type}:{log.resource_id}"
                    resource_counts[resource_key] += 1
            
            # 排序统计
            stats['top_users'] = sorted(
                stats['users'].items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:10]
            
            stats['top_resources'] = sorted(
                resource_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:10]
            
            # 转换为普通字典
            stats['event_types'] = dict(stats['event_types'])
            stats['users'] = dict(stats['users'])
            stats['results'] = dict(stats['results'])
            stats['daily_counts'] = dict(stats['daily_counts'])
            stats['risk_distribution'] = dict(stats['risk_distribution'])
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取审计统计失败: {e}")
            return {}
    
    def export_audit_logs(
        self, 
        organization_id: str, 
        start_time: datetime, 
        end_time: datetime,
        format: str = "json"
    ) -> str:
        """导出审计日志"""
        try:
            # 查询日志
            request = AuditLogQueryRequest(
                organization_id=organization_id,
                start_time=start_time,
                end_time=end_time,
                limit=10000  # 大批量导出
            )
            
            logs, total_count = self.query_logs(request)
            
            if format.lower() == "json":
                return self._export_to_json(logs)
            elif format.lower() == "csv":
                return self._export_to_csv(logs)
            else:
                raise AIGenHubException(f"不支持的导出格式: {format}")
                
        except Exception as e:
            self.logger.error(f"导出审计日志失败: {e}")
            raise AIGenHubException("导出失败")
    
    def _calculate_risk_score(self, audit_log: AuditLog) -> float:
        """计算风险评分"""
        base_score = self.risk_weights.get(audit_log.event_type, 1.0)
        
        # 结果影响
        if audit_log.result == "failure":
            base_score *= 1.5
        elif audit_log.result == "error":
            base_score *= 2.0
        
        # 时间影响（非工作时间风险更高）
        hour = audit_log.timestamp.hour
        if hour < 6 or hour > 22:  # 非工作时间
            base_score *= 1.3
        
        # 用户影响
        if not audit_log.user_id:  # 匿名操作
            base_score *= 1.2
        
        # IP地址影响
        if audit_log.user_ip:
            # 简化的IP风险检查
            if self._is_suspicious_ip(audit_log.user_ip):
                base_score *= 1.5
        
        return min(base_score, 10.0)  # 最高10分
    
    def _detect_security_flags(self, audit_log: AuditLog) -> List[str]:
        """检测安全标志"""
        flags = []
        
        # 多次失败登录
        if audit_log.event_type == AuditEventType.USER_LOGIN and audit_log.result == "failure":
            recent_failures = self._count_recent_login_failures(audit_log.user_id, audit_log.user_ip)
            if recent_failures >= 3:
                flags.append("multiple_login_failures")
        
        # 权限提升
        if audit_log.event_type == AuditEventType.PERMISSION_CHANGE:
            if "admin" in audit_log.new_values.get("role", "").lower():
                flags.append("privilege_escalation")
        
        # 大量数据访问
        if audit_log.event_type == AuditEventType.DATA_ACCESS:
            if audit_log.metadata.get("record_count", 0) > 1000:
                flags.append("bulk_data_access")
        
        # 异常时间访问
        hour = audit_log.timestamp.hour
        if hour < 6 or hour > 22:
            flags.append("off_hours_access")
        
        # 配置更改
        if audit_log.event_type == AuditEventType.CONFIG_CHANGE:
            if "security" in audit_log.resource_type.lower():
                flags.append("security_config_change")
        
        return flags
    
    def _is_suspicious_ip(self, ip_address: str) -> bool:
        """检查可疑IP地址"""
        # 简化的IP检查逻辑
        suspicious_patterns = [
            "192.168.",  # 内网IP（在某些情况下可疑）
            "10.",       # 内网IP
            "172."       # 内网IP
        ]
        
        # 这里应该集成真实的威胁情报数据库
        return any(ip_address.startswith(pattern) for pattern in suspicious_patterns)
    
    def _count_recent_login_failures(self, user_id: str, ip_address: str, minutes: int = 15) -> int:
        """统计最近的登录失败次数"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        count = 0
        for log in self.audit_logs:
            if (log.event_type == AuditEventType.USER_LOGIN and
                log.result == "failure" and
                log.timestamp > cutoff_time and
                (log.user_id == user_id or log.user_ip == ip_address)):
                count += 1
        
        return count
    
    def _get_risk_level(self, risk_score: float) -> str:
        """获取风险级别"""
        if risk_score >= 8.0:
            return "critical"
        elif risk_score >= 6.0:
            return "high"
        elif risk_score >= 4.0:
            return "medium"
        else:
            return "low"
    
    def _trigger_security_alert(self, audit_log: AuditLog):
        """触发安全告警"""
        alert_data = {
            'log_id': audit_log.log_id,
            'organization_id': audit_log.organization_id,
            'event_type': audit_log.event_type.value,
            'risk_score': audit_log.risk_score,
            'security_flags': audit_log.security_flags,
            'timestamp': audit_log.timestamp.isoformat(),
            'user_id': audit_log.user_id,
            'user_ip': audit_log.user_ip,
            'action': audit_log.action,
            'resource': f"{audit_log.resource_type}:{audit_log.resource_id}" if audit_log.resource_type else None
        }
        
        self.logger.warning(f"安全告警: {json.dumps(alert_data)}")
        
        # 这里可以集成告警系统，如发送邮件、短信、Slack通知等
    
    def _export_to_json(self, logs: List[AuditLog]) -> str:
        """导出为JSON格式"""
        export_data = []
        
        for log in logs:
            log_dict = {
                'log_id': log.log_id,
                'organization_id': log.organization_id,
                'event_type': log.event_type.value,
                'event_name': log.event_name,
                'timestamp': log.timestamp.isoformat(),
                'user_id': log.user_id,
                'user_email': log.user_email,
                'user_ip': log.user_ip,
                'resource_type': log.resource_type,
                'resource_id': log.resource_id,
                'action': log.action,
                'result': log.result,
                'risk_score': log.risk_score,
                'security_flags': log.security_flags,
                'metadata': log.metadata
            }
            export_data.append(log_dict)
        
        return json.dumps(export_data, indent=2, ensure_ascii=False)
    
    def _export_to_csv(self, logs: List[AuditLog]) -> str:
        """导出为CSV格式"""
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入标题行
        headers = [
            'log_id', 'organization_id', 'event_type', 'event_name', 'timestamp',
            'user_id', 'user_email', 'user_ip', 'resource_type', 'resource_id',
            'action', 'result', 'risk_score', 'security_flags'
        ]
        writer.writerow(headers)
        
        # 写入数据行
        for log in logs:
            row = [
                log.log_id,
                log.organization_id,
                log.event_type.value,
                log.event_name,
                log.timestamp.isoformat(),
                log.user_id or '',
                log.user_email or '',
                log.user_ip or '',
                log.resource_type or '',
                log.resource_id or '',
                log.action,
                log.result,
                log.risk_score,
                ','.join(log.security_flags)
            ]
            writer.writerow(row)
        
        return output.getvalue()
    
    def register_enterprise_config(self, config: EnterpriseConfig):
        """注册企业配置"""
        self.enterprise_configs[config.organization_id] = config
        self.logger.info(f"注册企业配置: {config.organization_name}")
    
    def cleanup_old_logs(self, organization_id: str):
        """清理过期日志"""
        config = self.enterprise_configs.get(organization_id)
        if not config:
            return
        
        cutoff_date = datetime.now() - timedelta(days=config.audit_retention_days)
        
        original_count = len(self.audit_logs)
        self.audit_logs = [
            log for log in self.audit_logs
            if not (log.organization_id == organization_id and log.timestamp < cutoff_date)
        ]
        
        cleaned_count = original_count - len(self.audit_logs)
        if cleaned_count > 0:
            self.logger.info(f"清理过期审计日志: {cleaned_count} 条")
