"""
企业级部署和集成 - 核心模型

定义企业配置、SSO集成、审计日志、合规性等核心数据模型
"""

import time
from typing import Any, Dict, List, Optional, Union
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime

from ai_gen_hub.core.interfaces import BaseRequest, BaseResponse


class DeploymentType(Enum):
    """部署类型"""
    CLOUD = "cloud"              # 云部署
    ON_PREMISE = "on_premise"    # 私有化部署
    HYBRID = "hybrid"            # 混合部署
    SAAS = "saas"               # SaaS服务


class SSOProvider(Enum):
    """SSO供应商"""
    ACTIVE_DIRECTORY = "active_directory"
    AZURE_AD = "azure_ad"
    OKTA = "okta"
    GOOGLE_WORKSPACE = "google_workspace"
    SAML = "saml"
    OIDC = "oidc"
    LDAP = "ldap"


class AuditEventType(Enum):
    """审计事件类型"""
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    API_ACCESS = "api_access"
    DATA_ACCESS = "data_access"
    CONFIG_CHANGE = "config_change"
    PERMISSION_CHANGE = "permission_change"
    SECURITY_EVENT = "security_event"
    SYSTEM_EVENT = "system_event"
    COMPLIANCE_EVENT = "compliance_event"


class ComplianceStandard(Enum):
    """合规标准"""
    GDPR = "gdpr"               # 欧盟通用数据保护条例
    CCPA = "ccpa"               # 加州消费者隐私法
    HIPAA = "hipaa"             # 健康保险便携性和责任法案
    SOX = "sox"                 # 萨班斯-奥克斯利法案
    ISO27001 = "iso27001"       # ISO 27001信息安全管理
    SOC2 = "soc2"               # SOC 2审计标准


class SecurityLevel(Enum):
    """安全级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class EnterpriseConfig:
    """企业配置"""
    organization_id: str
    organization_name: str
    deployment_type: DeploymentType
    
    # 基本信息
    domain: str = ""
    contact_email: str = ""
    admin_email: str = ""
    
    # 安全配置
    security_level: SecurityLevel = SecurityLevel.MEDIUM
    enable_mfa: bool = True
    password_policy: Dict[str, Any] = field(default_factory=dict)
    session_timeout: int = 3600  # 秒
    
    # SSO配置
    sso_enabled: bool = False
    sso_provider: Optional[SSOProvider] = None
    sso_config: Dict[str, Any] = field(default_factory=dict)
    
    # 审计配置
    audit_enabled: bool = True
    audit_retention_days: int = 365
    audit_events: List[AuditEventType] = field(default_factory=list)
    
    # 合规配置
    compliance_standards: List[ComplianceStandard] = field(default_factory=list)
    data_residency: str = ""  # 数据驻留地区
    encryption_at_rest: bool = True
    encryption_in_transit: bool = True
    
    # 资源限制
    max_users: int = 1000
    max_api_calls_per_day: int = 100000
    max_storage_gb: int = 1000
    
    # 功能开关
    features_enabled: Dict[str, bool] = field(default_factory=dict)
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        if not self.password_policy:
            self.password_policy = {
                "min_length": 8,
                "require_uppercase": True,
                "require_lowercase": True,
                "require_numbers": True,
                "require_special_chars": True,
                "max_age_days": 90
            }
        
        if not self.audit_events:
            self.audit_events = [
                AuditEventType.USER_LOGIN,
                AuditEventType.USER_LOGOUT,
                AuditEventType.API_ACCESS,
                AuditEventType.CONFIG_CHANGE,
                AuditEventType.SECURITY_EVENT
            ]


@dataclass
class SSOConfiguration:
    """SSO配置"""
    config_id: str
    organization_id: str
    provider: SSOProvider
    
    # 基本配置
    enabled: bool = True
    name: str = ""
    description: str = ""
    
    # 连接配置
    endpoint_url: str = ""
    client_id: str = ""
    client_secret: str = ""
    certificate: str = ""
    
    # SAML配置
    saml_entity_id: Optional[str] = None
    saml_sso_url: Optional[str] = None
    saml_slo_url: Optional[str] = None
    saml_x509_cert: Optional[str] = None
    
    # OIDC配置
    oidc_discovery_url: Optional[str] = None
    oidc_scope: List[str] = field(default_factory=lambda: ["openid", "profile", "email"])
    
    # LDAP配置
    ldap_server: Optional[str] = None
    ldap_port: int = 389
    ldap_base_dn: Optional[str] = None
    ldap_bind_dn: Optional[str] = None
    ldap_bind_password: Optional[str] = None
    
    # 属性映射
    attribute_mapping: Dict[str, str] = field(default_factory=dict)
    
    # 用户同步
    auto_provision_users: bool = True
    default_role: str = "user"
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        if not self.config_id:
            self.config_id = f"sso_{int(time.time())}"
        
        if not self.attribute_mapping:
            self.attribute_mapping = {
                "email": "email",
                "first_name": "given_name",
                "last_name": "family_name",
                "display_name": "name"
            }


@dataclass
class AuditLog:
    """审计日志"""
    log_id: str
    organization_id: str
    event_type: AuditEventType
    
    # 事件信息
    event_name: str = ""
    event_description: str = ""
    event_category: str = ""
    
    # 用户信息
    user_id: Optional[str] = None
    user_email: Optional[str] = None
    user_ip: Optional[str] = None
    user_agent: Optional[str] = None
    
    # 资源信息
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    resource_name: Optional[str] = None
    
    # 操作信息
    action: str = ""
    result: str = "success"  # success, failure, error
    error_message: Optional[str] = None
    
    # 上下文信息
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    api_endpoint: Optional[str] = None
    http_method: Optional[str] = None
    
    # 数据变更
    old_values: Dict[str, Any] = field(default_factory=dict)
    new_values: Dict[str, Any] = field(default_factory=dict)
    
    # 安全信息
    risk_score: float = 0.0
    security_flags: List[str] = field(default_factory=list)
    
    # 时间信息
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.log_id:
            self.log_id = f"audit_{int(time.time())}"


@dataclass
class ComplianceReport:
    """合规报告"""
    report_id: str
    organization_id: str
    standard: ComplianceStandard
    
    # 报告信息
    title: str = ""
    description: str = ""
    report_period_start: datetime = field(default_factory=datetime.now)
    report_period_end: datetime = field(default_factory=datetime.now)
    
    # 合规状态
    compliance_score: float = 0.0  # 0-100
    total_controls: int = 0
    compliant_controls: int = 0
    non_compliant_controls: int = 0
    
    # 控制项详情
    control_results: List[Dict[str, Any]] = field(default_factory=list)
    
    # 发现的问题
    findings: List[Dict[str, Any]] = field(default_factory=list)
    
    # 建议措施
    recommendations: List[Dict[str, Any]] = field(default_factory=list)
    
    # 生成信息
    generated_by: str = ""
    generated_at: datetime = field(default_factory=datetime.now)
    
    # 审核信息
    reviewed_by: Optional[str] = None
    reviewed_at: Optional[datetime] = None
    approved_by: Optional[str] = None
    approved_at: Optional[datetime] = None
    
    # 文件信息
    report_file_path: Optional[str] = None
    report_file_size: int = 0
    
    def __post_init__(self):
        if not self.report_id:
            self.report_id = f"compliance_{int(time.time())}"


@dataclass
class DataRetentionPolicy:
    """数据保留策略"""
    policy_id: str
    organization_id: str
    
    # 策略信息
    name: str = ""
    description: str = ""
    enabled: bool = True
    
    # 数据类型
    data_types: List[str] = field(default_factory=list)
    
    # 保留规则
    retention_period_days: int = 365
    auto_delete: bool = False
    archive_before_delete: bool = True
    
    # 法律保留
    legal_hold: bool = False
    legal_hold_reason: str = ""
    legal_hold_until: Optional[datetime] = None
    
    # 地理位置
    applicable_regions: List[str] = field(default_factory=list)
    
    # 时间信息
    effective_date: datetime = field(default_factory=datetime.now)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        if not self.policy_id:
            self.policy_id = f"retention_{int(time.time())}"


@dataclass
class PrivacySettings:
    """隐私设置"""
    organization_id: str
    
    # 数据收集
    collect_analytics: bool = True
    collect_usage_stats: bool = True
    collect_error_logs: bool = True
    
    # 数据共享
    share_with_partners: bool = False
    share_for_research: bool = False
    share_anonymized_data: bool = False
    
    # 用户权利
    allow_data_export: bool = True
    allow_data_deletion: bool = True
    allow_data_portability: bool = True
    
    # Cookie设置
    essential_cookies: bool = True
    analytics_cookies: bool = True
    marketing_cookies: bool = False
    
    # 通知设置
    privacy_policy_url: str = ""
    cookie_policy_url: str = ""
    data_processing_notice: str = ""
    
    # 同意管理
    require_explicit_consent: bool = True
    consent_withdrawal_url: str = ""
    
    # 时间信息
    updated_at: datetime = field(default_factory=datetime.now)


# 请求和响应模型
@dataclass
class CreateEnterpriseConfigRequest(BaseRequest):
    """创建企业配置请求"""
    organization_name: str
    deployment_type: str
    domain: str
    contact_email: str
    security_level: str = "medium"


@dataclass
class UpdateEnterpriseConfigRequest(BaseRequest):
    """更新企业配置请求"""
    organization_id: str
    config_updates: Dict[str, Any]


@dataclass
class CreateSSOConfigRequest(BaseRequest):
    """创建SSO配置请求"""
    organization_id: str
    provider: str
    name: str
    endpoint_url: str
    client_id: str
    client_secret: str


@dataclass
class AuditLogQueryRequest(BaseRequest):
    """审计日志查询请求"""
    organization_id: str
    event_types: Optional[List[str]] = None
    user_id: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    limit: int = 100
    offset: int = 0


@dataclass
class ComplianceReportRequest(BaseRequest):
    """合规报告请求"""
    organization_id: str
    standard: str
    period_start: datetime
    period_end: datetime


@dataclass
class EnterpriseConfigResponse(BaseResponse):
    """企业配置响应"""
    config: EnterpriseConfig


@dataclass
class AuditLogResponse(BaseResponse):
    """审计日志响应"""
    logs: List[AuditLog]
    total_count: int
    has_more: bool


@dataclass
class ComplianceReportResponse(BaseResponse):
    """合规报告响应"""
    report: ComplianceReport


@dataclass
class DeploymentStatus:
    """部署状态"""
    organization_id: str
    deployment_type: DeploymentType
    
    # 状态信息
    status: str = "active"  # active, inactive, maintenance, error
    health_score: float = 100.0
    last_health_check: datetime = field(default_factory=datetime.now)
    
    # 版本信息
    current_version: str = ""
    available_version: str = ""
    update_available: bool = False
    
    # 资源使用
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    storage_usage: float = 0.0
    network_usage: float = 0.0
    
    # 服务状态
    services_status: Dict[str, str] = field(default_factory=dict)
    
    # 错误信息
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    # 时间信息
    uptime_seconds: int = 0
    last_restart: Optional[datetime] = None
