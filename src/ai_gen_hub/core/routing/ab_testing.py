"""
A/B测试管理器

负责A/B测试的创建、管理、统计分析等功能
"""

import asyncio
import math
import statistics
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict

from ai_gen_hub.core.logging import get_logger
from ai_gen_hub.core.exceptions import AIGenHubException
from .models import (
    ABTest,
    ABTestGroup,
    ABTestResult,
    ABTestStatus,
    MetricType,
    ModelConfig
)


class StatisticalAnalyzer:
    """统计分析器"""
    
    @staticmethod
    def calculate_confidence_interval(data: List[float], confidence_level: float = 0.95) -> Tuple[float, float]:
        """计算置信区间"""
        if not data:
            return 0.0, 0.0
        
        n = len(data)
        mean = statistics.mean(data)
        
        if n == 1:
            return mean, mean
        
        std_dev = statistics.stdev(data)
        std_error = std_dev / math.sqrt(n)
        
        # 简化的置信区间计算（使用正态分布近似）
        z_value = 1.96 if confidence_level == 0.95 else 2.58  # 95%或99%置信度
        margin_error = z_value * std_error
        
        return mean - margin_error, mean + margin_error
    
    @staticmethod
    def t_test(group1_data: List[float], group2_data: List[float]) -> Tuple[float, float]:
        """执行简化的t检验"""
        if not group1_data or not group2_data:
            return 0.0, 1.0
        
        mean1 = statistics.mean(group1_data)
        mean2 = statistics.mean(group2_data)
        
        if len(group1_data) == 1 and len(group2_data) == 1:
            return abs(mean1 - mean2), 0.5
        
        # 简化的t检验计算
        var1 = statistics.variance(group1_data) if len(group1_data) > 1 else 0
        var2 = statistics.variance(group2_data) if len(group2_data) > 1 else 0
        
        pooled_var = (var1 + var2) / 2
        if pooled_var == 0:
            return 0.0, 1.0
        
        t_stat = abs(mean1 - mean2) / math.sqrt(pooled_var * (1/len(group1_data) + 1/len(group2_data)))
        
        # 简化的p值计算
        p_value = max(0.001, 1 / (1 + t_stat))
        
        return t_stat, p_value


class ABTestManager:
    """A/B测试管理器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.tests: Dict[str, ABTest] = {}
        self.test_data: Dict[str, Dict[str, List[Dict[str, Any]]]] = defaultdict(lambda: defaultdict(list))
        self.analyzer = StatisticalAnalyzer()
    
    def create_test(self, test: ABTest) -> ABTest:
        """创建A/B测试"""
        # 验证测试配置
        self._validate_test(test)
        
        # 设置默认样本量
        if test.minimum_sample_size == 0:
            test.minimum_sample_size = 100
        
        self.tests[test.test_id] = test
        self.logger.info(f"创建A/B测试: {test.name} ({test.test_id})")
        
        return test
    
    def start_test(self, test_id: str) -> bool:
        """启动A/B测试"""
        test = self.tests.get(test_id)
        if not test:
            return False
        
        if test.status != ABTestStatus.DRAFT:
            raise AIGenHubException(f"测试状态不允许启动: {test.status}")
        
        test.status = ABTestStatus.RUNNING
        test.start_time = datetime.now()
        
        # 设置结束时间
        if test.duration_days and not test.end_time:
            test.end_time = test.start_time + timedelta(days=test.duration_days)
        
        test.updated_at = datetime.now()
        
        self.logger.info(f"启动A/B测试: {test.name}")
        return True
    
    def stop_test(self, test_id: str, reason: str = "") -> bool:
        """停止A/B测试"""
        test = self.tests.get(test_id)
        if not test:
            return False
        
        if test.status != ABTestStatus.RUNNING:
            return False
        
        test.status = ABTestStatus.COMPLETED
        test.end_time = datetime.now()
        test.updated_at = datetime.now()
        
        self.logger.info(f"停止A/B测试: {test.name}, 原因: {reason}")
        return True
    
    def record_result(self, test_id: str, group_id: str, metrics: Dict[MetricType, float], user_id: str = None):
        """记录测试结果"""
        if test_id not in self.tests:
            return
        
        test = self.tests[test_id]
        if not test.is_active():
            return
        
        # 记录数据点
        data_point = {
            'timestamp': datetime.now(),
            'user_id': user_id,
            'metrics': metrics
        }
        
        self.test_data[test_id][group_id].append(data_point)
        
        # 检查是否需要自动停止
        if test.auto_stop_on_significance:
            self._check_auto_stop(test_id)
    
    def get_test_results(self, test_id: str) -> Dict[str, ABTestResult]:
        """获取测试结果"""
        test = self.tests.get(test_id)
        if not test:
            return {}
        
        results = {}
        control_group = test.control_group
        
        # 计算对照组结果
        control_result = self._calculate_group_result(test_id, control_group.group_id, test)
        results[control_group.group_id] = control_result
        
        # 计算实验组结果
        for treatment_group in test.treatment_groups:
            treatment_result = self._calculate_group_result(test_id, treatment_group.group_id, test)
            
            # 与对照组比较
            self._compare_with_control(control_result, treatment_result, test)
            
            results[treatment_group.group_id] = treatment_result
        
        return results
    
    def _validate_test(self, test: ABTest):
        """验证测试配置"""
        if not test.control_group:
            raise AIGenHubException("必须设置对照组")
        
        if not test.treatment_groups:
            raise AIGenHubException("必须设置至少一个实验组")
        
        # 检查流量分配
        total_traffic = test.control_group.traffic_percentage
        for group in test.treatment_groups:
            total_traffic += group.traffic_percentage
        
        if total_traffic > 100:
            raise AIGenHubException("流量分配总和不能超过100%")
    
    def _calculate_group_result(self, test_id: str, group_id: str, test: ABTest) -> ABTestResult:
        """计算组结果"""
        data_points = self.test_data[test_id][group_id]
        
        result = ABTestResult(
            test_id=test_id,
            group_id=group_id,
            sample_size=len(data_points)
        )
        
        if not data_points:
            return result
        
        # 计算各项指标
        metrics_data = defaultdict(list)
        for point in data_points:
            for metric_type, value in point['metrics'].items():
                metrics_data[metric_type].append(value)
        
        # 计算平均值和置信区间
        for metric_type, values in metrics_data.items():
            if values:
                mean_value = statistics.mean(values)
                result.metrics[metric_type] = mean_value
                
                # 计算置信区间
                ci_lower, ci_upper = self.analyzer.calculate_confidence_interval(
                    values, test.confidence_level
                )
                result.confidence_interval[f"{metric_type.value}_lower"] = ci_lower
                result.confidence_interval[f"{metric_type.value}_upper"] = ci_upper
        
        # 设置时间范围
        if data_points:
            result.start_time = min(point['timestamp'] for point in data_points)
            result.end_time = max(point['timestamp'] for point in data_points)
        
        return result
    
    def _compare_with_control(self, control_result: ABTestResult, treatment_result: ABTestResult, test: ABTest):
        """与对照组比较"""
        control_data = self.test_data[test.test_id][control_result.group_id]
        treatment_data = self.test_data[test.test_id][treatment_result.group_id]
        
        if not control_data or not treatment_data:
            return
        
        # 对每个指标进行统计检验
        for metric_type in test.control_group.target_metrics:
            control_values = [point['metrics'].get(metric_type, 0) for point in control_data 
                            if metric_type in point['metrics']]
            treatment_values = [point['metrics'].get(metric_type, 0) for point in treatment_data 
                              if metric_type in point['metrics']]
            
            if not control_values or not treatment_values:
                continue
            
            # 执行t检验
            t_stat, p_value = self.analyzer.t_test(control_values, treatment_values)
            
            # 判断显著性
            alpha = 1 - test.confidence_level
            is_significant = p_value < alpha
            
            # 计算相对提升
            control_mean = statistics.mean(control_values)
            treatment_mean = statistics.mean(treatment_values)
            
            if control_mean != 0:
                relative_improvement = (treatment_mean - control_mean) / control_mean * 100
            else:
                relative_improvement = 0
            
            # 更新结果
            treatment_result.p_value = min(treatment_result.p_value, p_value)
            treatment_result.is_significant = treatment_result.is_significant or is_significant
            treatment_result.relative_improvement[metric_type] = relative_improvement
    
    def _check_auto_stop(self, test_id: str):
        """检查是否需要自动停止"""
        test = self.tests[test_id]
        results = self.get_test_results(test_id)
        
        # 检查样本量
        for group_id, result in results.items():
            if result.sample_size < test.minimum_sample_size:
                return  # 样本量不足
        
        # 检查显著性
        for group_id, result in results.items():
            if group_id == test.control_group.group_id:
                continue
            
            if result.is_significant:
                self.stop_test(test_id, "检测到统计显著性，自动停止")
                return
    
    def get_test_summary(self, test_id: str) -> Dict[str, Any]:
        """获取测试摘要"""
        test = self.tests.get(test_id)
        if not test:
            return {}
        
        results = self.get_test_results(test_id)
        
        summary = {
            'test_id': test_id,
            'name': test.name,
            'status': test.status.value,
            'start_time': test.start_time.isoformat() if test.start_time else None,
            'end_time': test.end_time.isoformat() if test.end_time else None,
            'total_participants': sum(result.sample_size for result in results.values()),
            'groups': []
        }
        
        # 添加组信息
        for group in test.get_all_groups():
            group_result = results.get(group.group_id)
            group_info = {
                'group_id': group.group_id,
                'name': group.name,
                'traffic_percentage': group.traffic_percentage,
                'sample_size': group_result.sample_size if group_result else 0,
                'is_control': group.group_id == test.control_group.group_id
            }
            
            if group_result:
                group_info['metrics'] = {k.value: v for k, v in group_result.metrics.items()}
                group_info['is_significant'] = group_result.is_significant
                group_info['relative_improvement'] = {k.value: v for k, v in group_result.relative_improvement.items()}
            
            summary['groups'].append(group_info)
        
        return summary
    
    def get_active_tests(self) -> List[ABTest]:
        """获取活跃的测试"""
        return [test for test in self.tests.values() if test.is_active()]
    
    def cleanup_expired_tests(self):
        """清理过期的测试"""
        now = datetime.now()
        expired_tests = []
        
        for test_id, test in self.tests.items():
            if test.end_time and now > test.end_time and test.status == ABTestStatus.RUNNING:
                expired_tests.append(test_id)
        
        for test_id in expired_tests:
            self.stop_test(test_id, "测试时间到期")
            self.logger.info(f"自动停止过期测试: {test_id}")
