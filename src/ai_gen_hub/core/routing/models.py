"""
智能模型路由和A/B测试 - 核心模型

定义路由策略、A/B测试、模型性能等核心数据模型
"""

import time
import uuid
from typing import Any, Dict, List, Optional, Union
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timedelta

from ai_gen_hub.core.interfaces import BaseRequest, BaseResponse


class RoutingStrategy(Enum):
    """路由策略"""
    ROUND_ROBIN = "round_robin"          # 轮询
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"  # 加权轮询
    LEAST_LATENCY = "least_latency"      # 最低延迟
    LEAST_COST = "least_cost"            # 最低成本
    HIGHEST_QUALITY = "highest_quality"   # 最高质量
    LOAD_BALANCED = "load_balanced"      # 负载均衡
    ADAPTIVE = "adaptive"                # 自适应
    AB_TEST = "ab_test"                  # A/B测试
    CANARY = "canary"                    # 金丝雀发布


class ModelStatus(Enum):
    """模型状态"""
    ACTIVE = "active"        # 激活
    INACTIVE = "inactive"    # 停用
    TESTING = "testing"      # 测试中
    DEPRECATED = "deprecated"  # 已弃用
    MAINTENANCE = "maintenance"  # 维护中


class ABTestStatus(Enum):
    """A/B测试状态"""
    DRAFT = "draft"          # 草稿
    RUNNING = "running"      # 运行中
    PAUSED = "paused"        # 暂停
    COMPLETED = "completed"  # 完成
    CANCELLED = "cancelled"  # 取消


class MetricType(Enum):
    """指标类型"""
    LATENCY = "latency"              # 延迟
    COST = "cost"                    # 成本
    QUALITY_SCORE = "quality_score"  # 质量分数
    SUCCESS_RATE = "success_rate"    # 成功率
    THROUGHPUT = "throughput"        # 吞吐量
    USER_SATISFACTION = "user_satisfaction"  # 用户满意度


@dataclass
class ModelConfig:
    """模型配置"""
    model_id: str
    provider: str
    model_name: str
    version: str = "latest"
    
    # 性能参数
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    top_p: Optional[float] = None
    
    # 成本信息
    cost_per_token: float = 0.0
    cost_per_request: float = 0.0
    
    # 质量评分
    quality_score: float = 0.0
    
    # 权重和优先级
    weight: float = 1.0
    priority: int = 0
    
    # 状态和限制
    status: ModelStatus = ModelStatus.ACTIVE
    rate_limit: Optional[int] = None  # 每分钟请求数
    daily_limit: Optional[int] = None  # 每日请求数
    
    # 标签和分类
    tags: List[str] = field(default_factory=list)
    category: str = "general"


@dataclass
class ModelMetrics:
    """模型性能指标"""
    model_id: str
    
    # 基础指标
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    
    # 性能指标
    avg_latency: float = 0.0
    min_latency: float = float('inf')
    max_latency: float = 0.0
    p95_latency: float = 0.0
    p99_latency: float = 0.0
    
    # 成本指标
    total_cost: float = 0.0
    avg_cost_per_request: float = 0.0
    
    # 质量指标
    avg_quality_score: float = 0.0
    user_satisfaction: float = 0.0
    
    # 时间窗口
    window_start: datetime = field(default_factory=datetime.now)
    window_end: datetime = field(default_factory=datetime.now)
    
    # 详细统计
    latency_histogram: Dict[str, int] = field(default_factory=dict)
    error_distribution: Dict[str, int] = field(default_factory=dict)
    
    def calculate_success_rate(self) -> float:
        """计算成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests
    
    def calculate_error_rate(self) -> float:
        """计算错误率"""
        if self.total_requests == 0:
            return 0.0
        return self.failed_requests / self.total_requests


@dataclass
class RoutingRule:
    """路由规则"""
    rule_id: str
    name: str
    description: str = ""
    
    # 条件匹配
    conditions: Dict[str, Any] = field(default_factory=dict)
    
    # 路由目标
    target_models: List[str] = field(default_factory=list)
    strategy: RoutingStrategy = RoutingStrategy.ROUND_ROBIN
    
    # 权重分配
    model_weights: Dict[str, float] = field(default_factory=dict)
    
    # 规则优先级
    priority: int = 0
    
    # 启用状态
    enabled: bool = True
    
    # 生效时间
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    
    def matches_request(self, request_context: Dict[str, Any]) -> bool:
        """检查请求是否匹配规则"""
        for key, expected_value in self.conditions.items():
            actual_value = request_context.get(key)
            
            if isinstance(expected_value, dict):
                # 支持范围匹配
                if "min" in expected_value and actual_value < expected_value["min"]:
                    return False
                if "max" in expected_value and actual_value > expected_value["max"]:
                    return False
                if "in" in expected_value and actual_value not in expected_value["in"]:
                    return False
            else:
                # 精确匹配
                if actual_value != expected_value:
                    return False
        
        return True


@dataclass
class ABTestGroup:
    """A/B测试组"""
    group_id: str
    name: str
    description: str = ""
    
    # 模型配置
    model_configs: List[ModelConfig] = field(default_factory=list)
    
    # 流量分配
    traffic_percentage: float = 50.0  # 百分比
    
    # 用户分组策略
    user_assignment_strategy: str = "random"  # random, hash, sticky
    
    # 测试指标
    target_metrics: List[MetricType] = field(default_factory=list)
    
    # 成功标准
    success_criteria: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ABTest:
    """A/B测试"""
    test_id: str
    name: str
    description: str = ""
    
    # 测试组
    control_group: ABTestGroup
    treatment_groups: List[ABTestGroup] = field(default_factory=list)
    
    # 测试状态
    status: ABTestStatus = ABTestStatus.DRAFT
    
    # 时间设置
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_days: Optional[int] = None
    
    # 流量设置
    total_traffic_percentage: float = 100.0
    
    # 统计设置
    minimum_sample_size: int = 1000
    confidence_level: float = 0.95
    statistical_power: float = 0.8
    
    # 自动决策
    auto_promote_winner: bool = False
    auto_stop_on_significance: bool = False
    
    # 创建信息
    created_by: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def get_all_groups(self) -> List[ABTestGroup]:
        """获取所有测试组"""
        return [self.control_group] + self.treatment_groups
    
    def is_active(self) -> bool:
        """检查测试是否激活"""
        if self.status != ABTestStatus.RUNNING:
            return False
        
        now = datetime.now()
        
        if self.start_time and now < self.start_time:
            return False
        
        if self.end_time and now > self.end_time:
            return False
        
        return True


@dataclass
class ABTestResult:
    """A/B测试结果"""
    test_id: str
    group_id: str
    
    # 基础统计
    sample_size: int = 0
    conversion_rate: float = 0.0
    
    # 性能指标
    metrics: Dict[MetricType, float] = field(default_factory=dict)
    
    # 统计显著性
    confidence_interval: Dict[str, float] = field(default_factory=dict)
    p_value: float = 1.0
    is_significant: bool = False
    
    # 相对提升
    relative_improvement: Dict[MetricType, float] = field(default_factory=dict)
    
    # 时间范围
    start_time: datetime = field(default_factory=datetime.now)
    end_time: datetime = field(default_factory=datetime.now)


@dataclass
class RoutingDecision:
    """路由决策"""
    request_id: str
    selected_model: ModelConfig
    strategy_used: RoutingStrategy
    
    # 决策原因
    decision_reason: str = ""
    decision_factors: Dict[str, Any] = field(default_factory=dict)
    
    # A/B测试信息
    ab_test_id: Optional[str] = None
    ab_test_group: Optional[str] = None
    
    # 时间戳
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 预期性能
    expected_latency: Optional[float] = None
    expected_cost: Optional[float] = None
    expected_quality: Optional[float] = None


@dataclass
class ModelPerformanceSnapshot:
    """模型性能快照"""
    model_id: str
    timestamp: datetime
    
    # 实时指标
    current_latency: float = 0.0
    current_load: float = 0.0
    current_error_rate: float = 0.0
    
    # 历史指标
    hourly_metrics: ModelMetrics = field(default_factory=lambda: ModelMetrics(""))
    daily_metrics: ModelMetrics = field(default_factory=lambda: ModelMetrics(""))
    
    # 健康状态
    health_score: float = 1.0
    is_healthy: bool = True
    
    # 预测指标
    predicted_latency: Optional[float] = None
    predicted_load: Optional[float] = None


# 请求和响应模型
@dataclass
class RoutingRequest(BaseRequest):
    """路由请求"""
    request_context: Dict[str, Any] = field(default_factory=dict)
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    preferred_models: List[str] = field(default_factory=list)
    exclude_models: List[str] = field(default_factory=list)
    max_latency: Optional[float] = None
    max_cost: Optional[float] = None
    min_quality: Optional[float] = None


@dataclass
class RoutingResponse(BaseResponse):
    """路由响应"""
    decision: RoutingDecision
    alternative_models: List[ModelConfig] = field(default_factory=list)


@dataclass
class ABTestCreateRequest(BaseRequest):
    """创建A/B测试请求"""
    name: str
    description: str = ""
    control_group: Dict[str, Any] = field(default_factory=dict)
    treatment_groups: List[Dict[str, Any]] = field(default_factory=list)
    duration_days: int = 7
    traffic_percentage: float = 10.0
    target_metrics: List[str] = field(default_factory=list)
    success_criteria: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ABTestUpdateRequest(BaseRequest):
    """更新A/B测试请求"""
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None
    traffic_percentage: Optional[float] = None
    end_time: Optional[datetime] = None


@dataclass
class ModelRegistrationRequest(BaseRequest):
    """模型注册请求"""
    model_id: str
    provider: str
    model_name: str
    version: str = "latest"
    config: Dict[str, Any] = field(default_factory=dict)
    cost_per_token: float = 0.0
    quality_score: float = 0.0
    tags: List[str] = field(default_factory=list)
