"""
智能模型路由引擎

负责模型选择、路由决策、A/B测试等核心功能
"""

import asyncio
import hashlib
import random
import time
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict

from ai_gen_hub.core.logging import get_logger
from ai_gen_hub.core.exceptions import AIGenHubException
from .models import (
    ModelConfig,
    ModelMetrics,
    RoutingRule,
    RoutingStrategy,
    RoutingDecision,
    RoutingRequest,
    ABTest,
    ABTestGroup,
    ABTestStatus,
    ModelStatus,
    MetricType,
    ModelPerformanceSnapshot
)


class ModelRegistry:
    """模型注册表"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.models: Dict[str, ModelConfig] = {}
        self.metrics: Dict[str, ModelMetrics] = {}
        self.performance_snapshots: Dict[str, ModelPerformanceSnapshot] = {}
    
    def register_model(self, model: ModelConfig):
        """注册模型"""
        self.models[model.model_id] = model
        self.metrics[model.model_id] = ModelMetrics(model.model_id)
        self.performance_snapshots[model.model_id] = ModelPerformanceSnapshot(
            model_id=model.model_id,
            timestamp=datetime.now()
        )
        self.logger.info(f"注册模型: {model.model_id} ({model.provider}/{model.model_name})")
    
    def get_model(self, model_id: str) -> Optional[ModelConfig]:
        """获取模型配置"""
        return self.models.get(model_id)
    
    def get_active_models(self) -> List[ModelConfig]:
        """获取激活的模型"""
        return [model for model in self.models.values() if model.status == ModelStatus.ACTIVE]
    
    def update_model_metrics(self, model_id: str, latency: float, cost: float, success: bool, quality_score: float = None):
        """更新模型指标"""
        if model_id not in self.metrics:
            return
        
        metrics = self.metrics[model_id]
        metrics.total_requests += 1
        
        if success:
            metrics.successful_requests += 1
            
            # 更新延迟统计
            metrics.avg_latency = (metrics.avg_latency * (metrics.successful_requests - 1) + latency) / metrics.successful_requests
            metrics.min_latency = min(metrics.min_latency, latency)
            metrics.max_latency = max(metrics.max_latency, latency)
            
            # 更新成本统计
            metrics.total_cost += cost
            metrics.avg_cost_per_request = metrics.total_cost / metrics.successful_requests
            
            # 更新质量分数
            if quality_score is not None:
                if metrics.avg_quality_score == 0:
                    metrics.avg_quality_score = quality_score
                else:
                    metrics.avg_quality_score = (metrics.avg_quality_score * (metrics.successful_requests - 1) + quality_score) / metrics.successful_requests
        else:
            metrics.failed_requests += 1
    
    def get_model_metrics(self, model_id: str) -> Optional[ModelMetrics]:
        """获取模型指标"""
        return self.metrics.get(model_id)


class RoutingEngine:
    """智能路由引擎"""
    
    def __init__(self, model_registry: ModelRegistry):
        self.logger = get_logger(__name__)
        self.model_registry = model_registry
        self.routing_rules: List[RoutingRule] = []
        self.ab_tests: Dict[str, ABTest] = {}
        
        # 路由统计
        self.routing_stats = defaultdict(int)
        self.last_selected_model = {}  # 用于轮询策略
    
    def add_routing_rule(self, rule: RoutingRule):
        """添加路由规则"""
        self.routing_rules.append(rule)
        # 按优先级排序
        self.routing_rules.sort(key=lambda r: r.priority, reverse=True)
        self.logger.info(f"添加路由规则: {rule.name}")
    
    async def route_request(self, request: RoutingRequest) -> RoutingDecision:
        """路由请求到最佳模型"""
        request_id = request.id or f"req_{int(time.time())}"
        
        # 获取候选模型
        candidate_models = await self._get_candidate_models(request)
        
        if not candidate_models:
            raise AIGenHubException("没有可用的模型")
        
        # 检查A/B测试
        ab_test_decision = await self._check_ab_tests(request, candidate_models)
        if ab_test_decision:
            return ab_test_decision
        
        # 应用路由规则
        rule_decision = await self._apply_routing_rules(request, candidate_models)
        if rule_decision:
            return rule_decision
        
        # 使用默认策略
        return await self._apply_default_strategy(request, candidate_models, RoutingStrategy.ADAPTIVE)
    
    async def _get_candidate_models(self, request: RoutingRequest) -> List[ModelConfig]:
        """获取候选模型"""
        models = self.model_registry.get_active_models()
        
        # 过滤首选模型
        if request.preferred_models:
            preferred = [m for m in models if m.model_id in request.preferred_models]
            if preferred:
                models = preferred
        
        # 排除指定模型
        if request.exclude_models:
            models = [m for m in models if m.model_id not in request.exclude_models]
        
        # 应用约束条件
        filtered_models = []
        for model in models:
            metrics = self.model_registry.get_model_metrics(model.model_id)
            if not metrics:
                continue
            
            # 延迟约束
            if request.max_latency and metrics.avg_latency > request.max_latency:
                continue
            
            # 成本约束
            if request.max_cost and metrics.avg_cost_per_request > request.max_cost:
                continue
            
            # 质量约束
            if request.min_quality and metrics.avg_quality_score < request.min_quality:
                continue
            
            filtered_models.append(model)
        
        return filtered_models
    
    async def _check_ab_tests(self, request: RoutingRequest, models: List[ModelConfig]) -> Optional[RoutingDecision]:
        """检查A/B测试"""
        for ab_test in self.ab_tests.values():
            if not ab_test.is_active():
                continue
            
            # 检查用户是否参与测试
            if not self._should_participate_in_test(request, ab_test):
                continue
            
            # 分配测试组
            group = self._assign_test_group(request, ab_test)
            if not group:
                continue
            
            # 从测试组选择模型
            group_models = [m for m in models if m.model_id in [mc.model_id for mc in group.model_configs]]
            if not group_models:
                continue
            
            selected_model = random.choice(group_models)
            
            return RoutingDecision(
                request_id=request.id or "",
                selected_model=selected_model,
                strategy_used=RoutingStrategy.AB_TEST,
                decision_reason=f"A/B测试: {ab_test.name}, 组: {group.name}",
                ab_test_id=ab_test.test_id,
                ab_test_group=group.group_id
            )
        
        return None
    
    def _should_participate_in_test(self, request: RoutingRequest, ab_test: ABTest) -> bool:
        """判断用户是否应该参与A/B测试"""
        # 基于用户ID或会话ID的哈希值决定
        user_key = request.user_id or request.session_id or "anonymous"
        hash_value = int(hashlib.md5(f"{ab_test.test_id}:{user_key}".encode()).hexdigest(), 16)
        
        # 计算百分比
        percentage = (hash_value % 100) + 1
        
        return percentage <= ab_test.total_traffic_percentage
    
    def _assign_test_group(self, request: RoutingRequest, ab_test: ABTest) -> Optional[ABTestGroup]:
        """分配A/B测试组"""
        user_key = request.user_id or request.session_id or "anonymous"
        hash_value = int(hashlib.md5(f"{ab_test.test_id}:group:{user_key}".encode()).hexdigest(), 16)
        
        # 计算累积权重
        all_groups = ab_test.get_all_groups()
        total_weight = sum(group.traffic_percentage for group in all_groups)
        
        if total_weight == 0:
            return None
        
        # 选择组
        target = (hash_value % int(total_weight * 100)) / 100
        cumulative = 0
        
        for group in all_groups:
            cumulative += group.traffic_percentage
            if target <= cumulative:
                return group
        
        return all_groups[0]  # 默认返回第一个组
    
    async def _apply_routing_rules(self, request: RoutingRequest, models: List[ModelConfig]) -> Optional[RoutingDecision]:
        """应用路由规则"""
        for rule in self.routing_rules:
            if not rule.enabled:
                continue
            
            # 检查时间范围
            now = datetime.now()
            if rule.start_time and now < rule.start_time:
                continue
            if rule.end_time and now > rule.end_time:
                continue
            
            # 检查条件匹配
            if not rule.matches_request(request.request_context):
                continue
            
            # 过滤目标模型
            target_models = [m for m in models if m.model_id in rule.target_models]
            if not target_models:
                continue
            
            # 应用策略选择模型
            selected_model = await self._apply_strategy(rule.strategy, target_models, rule.model_weights)
            
            return RoutingDecision(
                request_id=request.id or "",
                selected_model=selected_model,
                strategy_used=rule.strategy,
                decision_reason=f"路由规则: {rule.name}",
                decision_factors={"rule_id": rule.rule_id}
            )
        
        return None
    
    async def _apply_default_strategy(self, request: RoutingRequest, models: List[ModelConfig], strategy: RoutingStrategy) -> RoutingDecision:
        """应用默认策略"""
        selected_model = await self._apply_strategy(strategy, models)
        
        return RoutingDecision(
            request_id=request.id or "",
            selected_model=selected_model,
            strategy_used=strategy,
            decision_reason="默认路由策略"
        )
    
    async def _apply_strategy(self, strategy: RoutingStrategy, models: List[ModelConfig], weights: Dict[str, float] = None) -> ModelConfig:
        """应用路由策略"""
        if strategy == RoutingStrategy.ROUND_ROBIN:
            return self._round_robin_select(models)
        elif strategy == RoutingStrategy.WEIGHTED_ROUND_ROBIN:
            return self._weighted_round_robin_select(models, weights or {})
        elif strategy == RoutingStrategy.LEAST_LATENCY:
            return self._least_latency_select(models)
        elif strategy == RoutingStrategy.LEAST_COST:
            return self._least_cost_select(models)
        elif strategy == RoutingStrategy.HIGHEST_QUALITY:
            return self._highest_quality_select(models)
        elif strategy == RoutingStrategy.ADAPTIVE:
            return self._adaptive_select(models)
        else:
            return random.choice(models)
    
    def _round_robin_select(self, models: List[ModelConfig]) -> ModelConfig:
        """轮询选择"""
        if not models:
            raise AIGenHubException("没有可用模型")
        
        # 获取上次选择的模型索引
        last_index = self.last_selected_model.get("round_robin", -1)
        next_index = (last_index + 1) % len(models)
        
        self.last_selected_model["round_robin"] = next_index
        return models[next_index]
    
    def _weighted_round_robin_select(self, models: List[ModelConfig], weights: Dict[str, float]) -> ModelConfig:
        """加权轮询选择"""
        if not models:
            raise AIGenHubException("没有可用模型")
        
        # 计算权重
        model_weights = []
        for model in models:
            weight = weights.get(model.model_id, model.weight)
            model_weights.append(weight)
        
        # 加权随机选择
        total_weight = sum(model_weights)
        if total_weight == 0:
            return random.choice(models)
        
        rand = random.uniform(0, total_weight)
        cumulative = 0
        
        for i, weight in enumerate(model_weights):
            cumulative += weight
            if rand <= cumulative:
                return models[i]
        
        return models[-1]
    
    def _least_latency_select(self, models: List[ModelConfig]) -> ModelConfig:
        """最低延迟选择"""
        best_model = None
        best_latency = float('inf')
        
        for model in models:
            metrics = self.model_registry.get_model_metrics(model.model_id)
            if metrics and metrics.avg_latency < best_latency:
                best_latency = metrics.avg_latency
                best_model = model
        
        return best_model or models[0]
    
    def _least_cost_select(self, models: List[ModelConfig]) -> ModelConfig:
        """最低成本选择"""
        best_model = None
        best_cost = float('inf')
        
        for model in models:
            metrics = self.model_registry.get_model_metrics(model.model_id)
            if metrics and metrics.avg_cost_per_request < best_cost:
                best_cost = metrics.avg_cost_per_request
                best_model = model
        
        return best_model or models[0]
    
    def _highest_quality_select(self, models: List[ModelConfig]) -> ModelConfig:
        """最高质量选择"""
        best_model = None
        best_quality = -1
        
        for model in models:
            metrics = self.model_registry.get_model_metrics(model.model_id)
            if metrics and metrics.avg_quality_score > best_quality:
                best_quality = metrics.avg_quality_score
                best_model = model
        
        return best_model or models[0]
    
    def _adaptive_select(self, models: List[ModelConfig]) -> ModelConfig:
        """自适应选择"""
        # 综合考虑延迟、成本、质量等因素
        best_model = None
        best_score = -1
        
        for model in models:
            metrics = self.model_registry.get_model_metrics(model.model_id)
            if not metrics:
                continue
            
            # 计算综合分数（可以根据需要调整权重）
            latency_score = 1.0 / (1.0 + metrics.avg_latency) if metrics.avg_latency > 0 else 1.0
            cost_score = 1.0 / (1.0 + metrics.avg_cost_per_request) if metrics.avg_cost_per_request > 0 else 1.0
            quality_score = metrics.avg_quality_score / 10.0 if metrics.avg_quality_score > 0 else 0.5
            success_rate = metrics.calculate_success_rate()
            
            # 综合分数
            composite_score = (
                latency_score * 0.3 +
                cost_score * 0.2 +
                quality_score * 0.3 +
                success_rate * 0.2
            )
            
            if composite_score > best_score:
                best_score = composite_score
                best_model = model
        
        return best_model or models[0]
