"""
AI Gen Hub 核心工具模块

这个模块包含了核心功能的工具函数和辅助类：
- 参数验证工具
- 供应商能力检测
- 配置管理工具
- 通用工具函数

重构说明：
- 将原本分散的工具函数统一整合到这里
- 提供可复用的工具函数和辅助类
- 支持参数验证、供应商兼容性检查等功能
"""

from .parameter_validation import (
    ParameterValidator,
    ValidationResult,
    ValidationError,
    validate_generation_config,
    validate_stream_config,
    validate_safety_config,
)

from .provider_capabilities import (
    ProviderCapabilities,
    CapabilityChecker,
    get_provider_capabilities,
    validate_provider_compatibility,
)

from .config_utils import (
    ConfigManager,
    merge_configs,
    validate_config,
    normalize_config,
)

from .common import (
    generate_request_id,
    get_current_timestamp,
    format_error_message,
    safe_dict_get,
)

__all__ = [
    # 参数验证
    "ParameterValidator",
    "ValidationResult", 
    "ValidationError",
    "validate_generation_config",
    "validate_stream_config",
    "validate_safety_config",
    
    # 供应商能力
    "ProviderCapabilities",
    "CapabilityChecker",
    "get_provider_capabilities",
    "validate_provider_compatibility",
    
    # 配置工具
    "ConfigManager",
    "merge_configs",
    "validate_config",
    "normalize_config",
    
    # 通用工具
    "generate_request_id",
    "get_current_timestamp",
    "format_error_message",
    "safe_dict_get",
]
