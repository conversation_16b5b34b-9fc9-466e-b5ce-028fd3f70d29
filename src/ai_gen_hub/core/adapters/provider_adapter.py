"""
AI Gen Hub 供应商适配器

提供不同AI供应商API格式的适配功能：
- OpenAI API 格式适配
- Anthropic API 格式适配  
- Google AI API 格式适配
- DashScope API 格式适配

重构说明：
- 从 docs/api_optimization/ 移动供应商适配逻辑到这里
- 提供统一的供应商适配接口
- 支持供应商能力检测和参数验证
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union

from ..models.requests import OptimizedTextGenerationRequest
from ..models.base import MessageRole

logger = logging.getLogger(__name__)


# =============================================================================
# 供应商适配器基类
# =============================================================================

class ProviderAdapter(ABC):
    """供应商适配器抽象基类
    
    定义了所有供应商适配器必须实现的接口。
    """
    
    def __init__(self, provider_name: str):
        self.provider_name = provider_name
        self.logger = logging.getLogger(f"{__name__}.{provider_name}")
    
    @abstractmethod
    def adapt_request(self, request: OptimizedTextGenerationRequest) -> Dict[str, Any]:
        """将优化版本请求适配为供应商特定格式
        
        Args:
            request: 优化版本请求
            
        Returns:
            Dict[str, Any]: 供应商特定的API参数
        """
        pass
    
    @abstractmethod
    def get_capabilities(self) -> Dict[str, Any]:
        """获取供应商能力信息
        
        Returns:
            Dict[str, Any]: 供应商能力信息
        """
        pass
    
    def validate_request(self, request: OptimizedTextGenerationRequest) -> Dict[str, List[str]]:
        """验证请求是否与供应商兼容
        
        Args:
            request: 优化版本请求
            
        Returns:
            Dict[str, List[str]]: 验证结果
        """
        result = {
            "errors": [],
            "warnings": [],
            "info": []
        }
        
        capabilities = self.get_capabilities()
        
        # 检查token限制
        if request.generation.max_tokens is not None:
            max_limit = capabilities.get("max_tokens_limit", float('inf'))
            if request.generation.max_tokens > max_limit:
                result["warnings"].append(
                    f"max_tokens ({request.generation.max_tokens}) 超过供应商限制 ({max_limit})，将被调整"
                )
        
        # 检查不支持的功能
        if request.functions is not None and not capabilities.get("supports_functions", False):
            result["warnings"].append(f"{self.provider_name} 不支持函数调用功能，functions参数将被忽略")
        
        if request.tools is not None and not capabilities.get("supports_tools", False):
            result["warnings"].append(f"{self.provider_name} 不支持工具调用功能，tools参数将被忽略")
        
        return result


# =============================================================================
# OpenAI 适配器
# =============================================================================

class OpenAIAdapter(ProviderAdapter):
    """OpenAI API 适配器
    
    OpenAI API与我们的标准格式最为接近，几乎可以直接映射。
    """
    
    def __init__(self):
        super().__init__("openai")
    
    def adapt_request(self, request: OptimizedTextGenerationRequest) -> Dict[str, Any]:
        """适配为OpenAI API格式"""
        self.logger.info("适配请求为OpenAI格式")
        
        params = {
            "model": request.model,
            "messages": [
                {
                    "role": msg.role if isinstance(msg.role, str) else msg.role.value,
                    "content": msg.content,
                    **({"name": msg.name} if msg.name else {}),
                    **({"function_call": msg.function_call} if msg.function_call else {}),
                    **({"tool_calls": msg.tool_calls} if msg.tool_calls else {})
                }
                for msg in request.messages
            ],
            "stream": request.stream.enabled,
        }

        # 生成参数
        if request.generation.max_tokens is not None:
            params["max_tokens"] = request.generation.max_tokens
        params["temperature"] = request.generation.temperature
        if request.generation.top_p is not None:
            params["top_p"] = request.generation.top_p
        params["frequency_penalty"] = request.generation.frequency_penalty
        params["presence_penalty"] = request.generation.presence_penalty
        if request.generation.stop is not None:
            params["stop"] = request.generation.stop

        # 高级功能
        if request.functions is not None:
            params["functions"] = request.functions
        if request.function_call is not None:
            params["function_call"] = request.function_call
        if request.tools is not None:
            params["tools"] = request.tools
        if request.tool_choice is not None:
            params["tool_choice"] = request.tool_choice
        if request.response_format is not None:
            params["response_format"] = request.response_format
        if request.user is not None:
            params["user"] = request.user

        # 合并供应商特定参数
        params.update(request.provider_params)

        return params
    
    def get_capabilities(self) -> Dict[str, Any]:
        """获取OpenAI能力信息"""
        return {
            "supports_streaming": True,
            "supports_functions": True,
            "supports_tools": True,
            "supports_structured_output": True,
            "supports_safety_settings": False,
            "supports_thinking": False,
            "max_tokens_limit": 128000,
            "supported_parameters": [
                "temperature", "top_p", "frequency_penalty",
                "presence_penalty", "stop", "max_tokens"
            ],
            "required_parameters": ["model", "messages"],
            "message_roles": ["system", "user", "assistant", "function", "tool"]
        }


# =============================================================================
# Anthropic 适配器
# =============================================================================

class AnthropicAdapter(ProviderAdapter):
    """Anthropic API 适配器
    
    Anthropic API有一些特殊要求：
    1. system消息需要单独处理
    2. max_tokens是必需参数
    3. 不支持某些OpenAI特有的功能
    """
    
    def __init__(self):
        super().__init__("anthropic")
    
    def adapt_request(self, request: OptimizedTextGenerationRequest) -> Dict[str, Any]:
        """适配为Anthropic API格式"""
        self.logger.info("适配请求为Anthropic格式")
        
        # 分离system消息和对话消息
        system_messages = []
        conversation_messages = []

        for msg in request.messages:
            if msg.role == MessageRole.SYSTEM:
                system_messages.append(msg.content)
            elif msg.role in [MessageRole.USER, MessageRole.ASSISTANT]:
                conversation_messages.append({
                    "role": msg.role if isinstance(msg.role, str) else msg.role.value,
                    "content": msg.content
                })

        params = {
            "model": request.model,
            "messages": conversation_messages,
            "max_tokens": request.generation.max_tokens or 4096,  # Anthropic要求必须指定
            "stream": request.stream.enabled,
        }

        # 添加system消息（如果有）
        if system_messages:
            params["system"] = "\n".join(system_messages)

        # 生成参数（只添加Anthropic支持的）
        params["temperature"] = request.generation.temperature
        if request.generation.top_p is not None:
            params["top_p"] = request.generation.top_p
        if request.generation.top_k is not None:
            params["top_k"] = request.generation.top_k

        # 停止序列（Anthropic使用不同的字段名）
        if request.generation.stop is not None:
            stop_sequences = (
                [request.generation.stop] if isinstance(request.generation.stop, str)
                else request.generation.stop
            )
            params["stop_sequences"] = stop_sequences

        # 过滤不支持的功能并记录警告
        unsupported_features = []
        if request.functions is not None:
            unsupported_features.append("functions")
        if request.tools is not None:
            unsupported_features.append("tools")
        if request.response_format is not None:
            unsupported_features.append("response_format")

        if unsupported_features:
            self.logger.warning(f"Anthropic不支持以下功能，将被忽略: {unsupported_features}")

        # 合并供应商特定参数
        params.update(request.provider_params)

        return params
    
    def get_capabilities(self) -> Dict[str, Any]:
        """获取Anthropic能力信息"""
        return {
            "supports_streaming": True,
            "supports_functions": False,
            "supports_tools": False,
            "supports_structured_output": False,
            "supports_safety_settings": False,
            "supports_thinking": False,
            "max_tokens_limit": 200000,
            "supported_parameters": [
                "temperature", "top_p", "top_k", "stop_sequences", "max_tokens"
            ],
            "required_parameters": ["model", "messages", "max_tokens"],
            "message_roles": ["system", "user", "assistant"],
            "special_handling": {
                "system_message": "separate_field",
                "stop_parameter": "stop_sequences"
            }
        }


# =============================================================================
# Google AI 适配器
# =============================================================================

class GoogleAdapter(ProviderAdapter):
    """Google AI API 适配器

    Google AI API使用完全不同的格式：
    1. 消息格式为contents数组，每个消息包含parts
    2. system消息转换为systemInstruction
    3. 生成参数包装在generationConfig中
    4. 支持thinking功能和安全设置
    """

    def __init__(self):
        super().__init__("google")

    def adapt_request(self, request: OptimizedTextGenerationRequest) -> Dict[str, Any]:
        """适配为Google AI API格式"""
        self.logger.info("适配请求为Google AI格式")

        # 分离system消息和对话消息
        system_messages = []
        conversation_contents = []

        for msg in request.messages:
            if msg.role == MessageRole.SYSTEM:
                system_messages.append(msg.content)
            else:
                # 转换为Google格式
                role = "user" if msg.role == MessageRole.USER else "model"
                conversation_contents.append({
                    "role": role,
                    "parts": [{"text": msg.content}]
                })

        params = {
            "contents": conversation_contents,
            "generationConfig": {}
        }

        # 添加system指令（如果有）
        if system_messages:
            params["systemInstruction"] = {
                "parts": [{"text": "\n".join(system_messages)}]
            }

        # 生成配置
        generation_config = {}
        if request.generation.max_tokens is not None:
            generation_config["maxOutputTokens"] = request.generation.max_tokens
        generation_config["temperature"] = request.generation.temperature
        if request.generation.top_p is not None:
            generation_config["topP"] = request.generation.top_p
        if request.generation.top_k is not None:
            generation_config["topK"] = request.generation.top_k
        if request.generation.stop is not None:
            stop_sequences = (
                [request.generation.stop] if isinstance(request.generation.stop, str)
                else request.generation.stop
            )
            generation_config["stopSequences"] = stop_sequences

        params["generationConfig"] = generation_config

        # 安全设置
        if request.safety is not None:
            safety_settings = []
            # Google AI的安全类别
            safety_categories = [
                "HARM_CATEGORY_HARASSMENT",
                "HARM_CATEGORY_HATE_SPEECH",
                "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                "HARM_CATEGORY_DANGEROUS_CONTENT"
            ]

            # 映射安全级别
            threshold_mapping = {
                "low": "BLOCK_ONLY_HIGH",
                "medium": "BLOCK_MEDIUM_AND_ABOVE",
                "high": "BLOCK_LOW_AND_ABOVE",
                "strict": "BLOCK_LOW_AND_ABOVE"
            }

            threshold = threshold_mapping.get(request.safety.safety_level, "BLOCK_MEDIUM_AND_ABOVE")

            for category in safety_categories:
                safety_settings.append({
                    "category": category,
                    "threshold": threshold
                })

            params["safetySettings"] = safety_settings

        # Thinking配置（Google特有功能）
        if "thinking_budget" in request.provider_params:
            params["thinkingConfig"] = {
                "thinkingBudget": request.provider_params["thinking_budget"]
            }

        # 工具配置（如果有）
        if request.tools is not None:
            tools_config = []
            for tool in request.tools:
                if "function" in tool:
                    # 转换函数调用格式
                    tools_config.append({
                        "functionDeclarations": [tool["function"]]
                    })
            if tools_config:
                params["tools"] = tools_config

        # 合并其他供应商特定参数
        for key, value in request.provider_params.items():
            if key not in ["thinking_budget"]:  # 避免重复添加
                params[key] = value

        return params

    def get_capabilities(self) -> Dict[str, Any]:
        """获取Google AI能力信息"""
        return {
            "supports_streaming": True,
            "supports_functions": True,
            "supports_tools": True,
            "supports_structured_output": True,
            "supports_safety_settings": True,
            "supports_thinking": True,
            "max_tokens_limit": 8192,
            "supported_parameters": [
                "temperature", "top_p", "top_k", "stop_sequences", "max_output_tokens"
            ],
            "required_parameters": ["contents"],
            "message_roles": ["user", "model"],
            "special_handling": {
                "system_message": "system_instruction",
                "message_format": "contents_parts",
                "generation_config": "nested"
            }
        }


# =============================================================================
# DashScope 适配器
# =============================================================================

class DashScopeAdapter(ProviderAdapter):
    """DashScope API 适配器

    DashScope API使用嵌套的input结构：
    1. 消息放在input.messages中
    2. 生成参数放在parameters中
    3. 基本兼容OpenAI格式但有细微差异
    """

    def __init__(self):
        super().__init__("dashscope")

    def adapt_request(self, request: OptimizedTextGenerationRequest) -> Dict[str, Any]:
        """适配为DashScope API格式"""
        self.logger.info("适配请求为DashScope格式")

        # 转换消息格式
        messages = []
        for msg in request.messages:
            messages.append({
                "role": msg.role if isinstance(msg.role, str) else msg.role.value,
                "content": msg.content
            })

        params = {
            "model": request.model,
            "input": {
                "messages": messages
            },
            "parameters": {
                "result_format": "message"  # DashScope特有设置
            }
        }

        # 生成参数
        parameters = params["parameters"]
        if request.generation.max_tokens is not None:
            parameters["max_tokens"] = request.generation.max_tokens
        parameters["temperature"] = request.generation.temperature
        if request.generation.top_p is not None:
            parameters["top_p"] = request.generation.top_p
        if request.generation.top_k is not None:
            parameters["top_k"] = request.generation.top_k
        if request.generation.stop is not None:
            parameters["stop"] = request.generation.stop

        # 流式输出
        if request.stream.enabled:
            parameters["incremental_output"] = True

        # 工具调用支持
        if request.tools is not None:
            parameters["tools"] = request.tools
        if request.tool_choice is not None:
            parameters["tool_choice"] = request.tool_choice

        # 合并供应商特定参数
        if request.provider_params:
            # DashScope的特定参数通常放在parameters中
            parameters.update(request.provider_params)

        return params

    def get_capabilities(self) -> Dict[str, Any]:
        """获取DashScope能力信息"""
        return {
            "supports_streaming": True,
            "supports_functions": False,
            "supports_tools": True,
            "supports_structured_output": False,
            "supports_safety_settings": False,
            "supports_thinking": False,
            "max_tokens_limit": 32000,
            "supported_parameters": [
                "temperature", "top_p", "top_k", "stop", "max_tokens"
            ],
            "required_parameters": ["model", "input"],
            "message_roles": ["system", "user", "assistant"],
            "special_handling": {
                "message_format": "input_messages",
                "parameters": "nested"
            }
        }


# =============================================================================
# 供应商适配器工厂
# =============================================================================

class ProviderAdapterFactory:
    """供应商适配器工厂类

    负责创建和管理不同供应商的适配器实例。
    """

    _adapters = {
        "openai": OpenAIAdapter,
        "anthropic": AnthropicAdapter,
        "google": GoogleAdapter,
        "dashscope": DashScopeAdapter,
    }

    @classmethod
    def create_adapter(cls, provider_name: str) -> ProviderAdapter:
        """创建供应商适配器

        Args:
            provider_name: 供应商名称

        Returns:
            ProviderAdapter: 供应商适配器实例

        Raises:
            ValueError: 不支持的供应商
        """
        provider_name = provider_name.lower()

        if provider_name not in cls._adapters:
            raise ValueError(f"不支持的供应商: {provider_name}")

        adapter_class = cls._adapters[provider_name]
        return adapter_class()

    @classmethod
    def get_supported_providers(cls) -> List[str]:
        """获取支持的供应商列表

        Returns:
            List[str]: 支持的供应商名称列表
        """
        return list(cls._adapters.keys())

    @classmethod
    def register_adapter(cls, provider_name: str, adapter_class: type):
        """注册新的供应商适配器

        Args:
            provider_name: 供应商名称
            adapter_class: 适配器类
        """
        if not issubclass(adapter_class, ProviderAdapter):
            raise ValueError("适配器类必须继承自ProviderAdapter")

        cls._adapters[provider_name.lower()] = adapter_class
        logger.info(f"注册新的供应商适配器: {provider_name}")

    @classmethod
    def get_provider_capabilities(cls, provider_name: str) -> Dict[str, Any]:
        """获取供应商能力信息

        Args:
            provider_name: 供应商名称

        Returns:
            Dict[str, Any]: 供应商能力信息
        """
        adapter = cls.create_adapter(provider_name)
        return adapter.get_capabilities()
