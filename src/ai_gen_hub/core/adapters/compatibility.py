"""
AI Gen Hub 兼容性支持模块

提供向后兼容性和渐进式迁移支持：
- 兼容性检查工具
- 迁移辅助功能
- 向后兼容性混入类

重构说明：
- 提供平滑的迁移路径
- 确保现有代码继续工作
- 支持逐步升级到新的API设计
"""

import logging
from typing import Any, Dict, List, Optional, Union
import warnings

from ..models.requests import (
    TextGenerationRequest,
    OptimizedTextGenerationRequest,
    SimpleTextGenerationRequest,
)

logger = logging.getLogger(__name__)


# =============================================================================
# 兼容性检查器
# =============================================================================

class CompatibilityChecker:
    """兼容性检查器
    
    检查不同版本请求格式之间的兼容性，提供迁移建议。
    """
    
    @staticmethod
    def check_legacy_compatibility(request: Union[Dict[str, Any], TextGenerationRequest]) -> Dict[str, Any]:
        """检查传统格式请求的兼容性
        
        Args:
            request: 传统格式请求
            
        Returns:
            Dict[str, Any]: 兼容性检查结果
        """
        result = {
            "compatible": True,
            "issues": [],
            "recommendations": [],
            "migration_complexity": "low"
        }
        
        # 如果是对象，转换为字典
        if hasattr(request, 'dict'):
            request_dict = request.dict()
        else:
            request_dict = request
        
        # 检查必需字段
        required_fields = ["messages", "model"]
        for field in required_fields:
            if field not in request_dict:
                result["compatible"] = False
                result["issues"].append(f"缺少必需字段: {field}")
        
        # 检查已弃用的字段
        deprecated_fields = {
            "max_length": "使用 max_tokens 替代",
            "length_penalty": "此参数已不再支持",
        }
        
        for field, message in deprecated_fields.items():
            if field in request_dict:
                result["issues"].append(f"使用了已弃用的字段 {field}: {message}")
                result["migration_complexity"] = "medium"
        
        # 检查复杂配置
        complex_configs = ["functions", "tools", "response_format"]
        complex_count = sum(1 for config in complex_configs if request_dict.get(config))
        
        if complex_count > 2:
            result["migration_complexity"] = "high"
            result["recommendations"].append("建议分阶段迁移复杂功能")
        
        # 提供迁移建议
        if result["compatible"]:
            result["recommendations"].append("可以直接使用 OptimizedTextGenerationRequest.from_legacy_request() 进行转换")
        
        return result
    
    @staticmethod
    def check_provider_compatibility(request: OptimizedTextGenerationRequest, provider_name: str) -> Dict[str, Any]:
        """检查请求与特定供应商的兼容性
        
        Args:
            request: 优化版本请求
            provider_name: 供应商名称
            
        Returns:
            Dict[str, Any]: 兼容性检查结果
        """
        from .provider_adapter import ProviderAdapterFactory
        
        try:
            adapter = ProviderAdapterFactory.create_adapter(provider_name)
            return adapter.validate_request(request)
        except ValueError as e:
            return {
                "errors": [str(e)],
                "warnings": [],
                "info": []
            }


# =============================================================================
# 迁移辅助工具
# =============================================================================

class MigrationHelper:
    """迁移辅助工具
    
    提供从传统格式到优化版本的迁移支持。
    """
    
    @staticmethod
    def analyze_migration_impact(requests: List[Union[Dict[str, Any], TextGenerationRequest]]) -> Dict[str, Any]:
        """分析批量迁移的影响
        
        Args:
            requests: 传统格式请求列表
            
        Returns:
            Dict[str, Any]: 迁移影响分析结果
        """
        analysis = {
            "total_requests": len(requests),
            "compatible_requests": 0,
            "incompatible_requests": 0,
            "migration_complexity": {
                "low": 0,
                "medium": 0,
                "high": 0
            },
            "common_issues": {},
            "recommendations": []
        }
        
        for request in requests:
            compatibility = CompatibilityChecker.check_legacy_compatibility(request)
            
            if compatibility["compatible"]:
                analysis["compatible_requests"] += 1
            else:
                analysis["incompatible_requests"] += 1
            
            # 统计迁移复杂度
            complexity = compatibility["migration_complexity"]
            analysis["migration_complexity"][complexity] += 1
            
            # 统计常见问题
            for issue in compatibility["issues"]:
                if issue not in analysis["common_issues"]:
                    analysis["common_issues"][issue] = 0
                analysis["common_issues"][issue] += 1
        
        # 生成建议
        if analysis["incompatible_requests"] == 0:
            analysis["recommendations"].append("所有请求都兼容，可以安全迁移")
        else:
            analysis["recommendations"].append(f"需要修复 {analysis['incompatible_requests']} 个不兼容的请求")
        
        if analysis["migration_complexity"]["high"] > 0:
            analysis["recommendations"].append("建议分阶段迁移复杂功能")
        
        return analysis
    
    @staticmethod
    def create_migration_plan(requests: List[Union[Dict[str, Any], TextGenerationRequest]]) -> Dict[str, Any]:
        """创建迁移计划
        
        Args:
            requests: 传统格式请求列表
            
        Returns:
            Dict[str, Any]: 迁移计划
        """
        analysis = MigrationHelper.analyze_migration_impact(requests)
        
        plan = {
            "phases": [],
            "estimated_effort": "low",
            "timeline": "1-2 weeks",
            "risks": [],
            "prerequisites": []
        }
        
        # 第一阶段：简单迁移
        if analysis["migration_complexity"]["low"] > 0:
            plan["phases"].append({
                "phase": 1,
                "name": "简单请求迁移",
                "description": "迁移不需要复杂配置的请求",
                "requests_count": analysis["migration_complexity"]["low"],
                "effort": "low",
                "duration": "1-3 days"
            })
        
        # 第二阶段：中等复杂度迁移
        if analysis["migration_complexity"]["medium"] > 0:
            plan["phases"].append({
                "phase": 2,
                "name": "中等复杂度请求迁移",
                "description": "迁移包含已弃用字段的请求",
                "requests_count": analysis["migration_complexity"]["medium"],
                "effort": "medium",
                "duration": "3-7 days"
            })
        
        # 第三阶段：复杂迁移
        if analysis["migration_complexity"]["high"] > 0:
            plan["phases"].append({
                "phase": 3,
                "name": "复杂功能迁移",
                "description": "迁移包含复杂功能的请求",
                "requests_count": analysis["migration_complexity"]["high"],
                "effort": "high",
                "duration": "1-2 weeks"
            })
            plan["estimated_effort"] = "high"
            plan["timeline"] = "2-4 weeks"
        
        # 风险评估
        if analysis["incompatible_requests"] > 0:
            plan["risks"].append("存在不兼容的请求，需要手动修复")
        
        if analysis["migration_complexity"]["high"] > analysis["total_requests"] * 0.3:
            plan["risks"].append("超过30%的请求具有高复杂度，迁移风险较高")
        
        # 前置条件
        plan["prerequisites"].extend([
            "确保所有依赖的供应商适配器已更新",
            "准备回滚计划",
            "设置监控和日志记录"
        ])
        
        return plan


# =============================================================================
# 向后兼容性混入类
# =============================================================================

class BackwardCompatibilityMixin:
    """向后兼容性混入类
    
    为现有类提供向后兼容性支持。
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._compatibility_warnings = []
    
    def _emit_compatibility_warning(self, message: str, category: type = DeprecationWarning):
        """发出兼容性警告
        
        Args:
            message: 警告消息
            category: 警告类别
        """
        warning_msg = f"[兼容性警告] {message}"
        self._compatibility_warnings.append(warning_msg)
        warnings.warn(warning_msg, category, stacklevel=3)
        logger.warning(warning_msg)
    
    def get_compatibility_warnings(self) -> List[str]:
        """获取兼容性警告列表
        
        Returns:
            List[str]: 兼容性警告列表
        """
        return self._compatibility_warnings.copy()
    
    def clear_compatibility_warnings(self):
        """清除兼容性警告"""
        self._compatibility_warnings.clear()
    
    def handle_legacy_parameter(self, old_param: str, new_param: str, value: Any) -> Any:
        """处理传统参数
        
        Args:
            old_param: 旧参数名
            new_param: 新参数名
            value: 参数值
            
        Returns:
            Any: 处理后的值
        """
        self._emit_compatibility_warning(
            f"参数 '{old_param}' 已弃用，请使用 '{new_param}' 替代"
        )
        return value
    
    def validate_legacy_usage(self, **kwargs) -> Dict[str, Any]:
        """验证传统用法
        
        Args:
            **kwargs: 参数字典
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            "valid": True,
            "warnings": [],
            "suggestions": []
        }
        
        # 检查已弃用的参数
        deprecated_params = {
            "max_length": "max_tokens",
            "length_penalty": None,  # 已完全移除
        }
        
        for param, replacement in deprecated_params.items():
            if param in kwargs:
                if replacement:
                    result["warnings"].append(f"参数 '{param}' 已弃用，建议使用 '{replacement}'")
                    result["suggestions"].append(f"将 {param}={kwargs[param]} 改为 {replacement}={kwargs[param]}")
                else:
                    result["warnings"].append(f"参数 '{param}' 已不再支持")
                    result["suggestions"].append(f"移除参数 '{param}'")
        
        return result
