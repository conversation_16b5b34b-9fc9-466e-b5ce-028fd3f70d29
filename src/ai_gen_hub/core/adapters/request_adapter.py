"""
AI Gen Hub 请求适配器

提供请求格式之间的转换功能：
- 传统格式 ↔ 优化版本
- 简化版本 → 优化版本
- 向后兼容性支持

重构说明：
- 从 docs/api_optimization/ 移动核心适配器逻辑到这里
- 提供统一的请求适配接口
- 支持渐进式迁移和格式转换
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union

from ..models.requests import (
    TextGenerationRequest,
    OptimizedTextGenerationRequest,
    SimpleTextGenerationRequest,
)

logger = logging.getLogger(__name__)


# =============================================================================
# 请求适配器基类
# =============================================================================

class RequestAdapter(ABC):
    """请求适配器抽象基类
    
    用于在不同请求格式之间进行转换，支持渐进式迁移。
    """

    @staticmethod
    def adapt_request(request: Union[Dict[str, Any], TextGenerationRequest, OptimizedTextGenerationRequest, SimpleTextGenerationRequest]) -> OptimizedTextGenerationRequest:
        """适配请求到优化版本
        
        这是统一的请求适配入口，可以处理所有支持的请求格式。

        Args:
            request: 任意格式的请求

        Returns:
            OptimizedTextGenerationRequest: 优化版本请求
            
        Raises:
            ValueError: 不支持的请求类型
        """
        logger.info(f"开始适配 {type(request).__name__} 类型的请求")
        
        if isinstance(request, OptimizedTextGenerationRequest):
            logger.debug("请求已经是优化版本，直接返回")
            return request
        elif isinstance(request, SimpleTextGenerationRequest):
            logger.debug("转换简化版本请求为优化版本")
            return request.to_optimized()
        elif isinstance(request, (dict, TextGenerationRequest)):
            logger.debug("转换传统格式请求为优化版本")
            return OptimizedTextGenerationRequest.from_legacy_request(request)
        else:
            raise ValueError(f"不支持的请求类型: {type(request)}")

    @staticmethod
    def is_optimized_request(request: Any) -> bool:
        """检查是否为优化版本请求"""
        return isinstance(request, OptimizedTextGenerationRequest)
    
    @staticmethod
    def is_legacy_request(request: Any) -> bool:
        """检查是否为传统格式请求"""
        return isinstance(request, (dict, TextGenerationRequest))
    
    @staticmethod
    def is_simple_request(request: Any) -> bool:
        """检查是否为简化版本请求"""
        return isinstance(request, SimpleTextGenerationRequest)


# =============================================================================
# 具体适配器实现
# =============================================================================

class LegacyRequestAdapter(RequestAdapter):
    """传统格式请求适配器
    
    专门处理传统格式（字典或TextGenerationRequest）到优化版本的转换。
    """
    
    @classmethod
    def from_dict(cls, request_dict: Dict[str, Any]) -> OptimizedTextGenerationRequest:
        """从字典格式转换为优化版本
        
        Args:
            request_dict: 传统格式的请求字典
            
        Returns:
            OptimizedTextGenerationRequest: 优化版本请求
        """
        logger.info("从字典格式转换为优化版本请求")
        return OptimizedTextGenerationRequest.from_legacy_request(request_dict)
    
    @classmethod
    def from_legacy_object(cls, request: TextGenerationRequest) -> OptimizedTextGenerationRequest:
        """从传统请求对象转换为优化版本
        
        Args:
            request: 传统格式的请求对象
            
        Returns:
            OptimizedTextGenerationRequest: 优化版本请求
        """
        logger.info("从传统请求对象转换为优化版本请求")
        return OptimizedTextGenerationRequest.from_legacy_request(request.dict())
    
    @staticmethod
    def to_dict(request: OptimizedTextGenerationRequest) -> Dict[str, Any]:
        """将优化版本转换为传统字典格式
        
        Args:
            request: 优化版本请求
            
        Returns:
            Dict[str, Any]: 传统格式的请求字典
        """
        logger.info("将优化版本请求转换为传统字典格式")
        return request.to_legacy_format()


class OptimizedRequestAdapter(RequestAdapter):
    """优化版本请求适配器
    
    处理优化版本请求的各种操作和验证。
    """
    
    @staticmethod
    def validate_request(request: OptimizedTextGenerationRequest) -> Dict[str, List[str]]:
        """验证优化版本请求
        
        Args:
            request: 优化版本请求
            
        Returns:
            Dict[str, List[str]]: 验证结果，包含errors、warnings、info
        """
        result = {
            "errors": [],
            "warnings": [],
            "info": []
        }
        
        # 基础验证
        if not request.messages:
            result["errors"].append("消息列表不能为空")
        
        if not request.model:
            result["errors"].append("模型名称不能为空")
        
        # 配置验证
        if request.generation.temperature != 0.7 and request.generation.top_p is not None:
            result["warnings"].append("建议只使用temperature或top_p其中一个参数")
        
        if request.generation.max_tokens and request.generation.max_tokens > 32768:
            result["warnings"].append("max_tokens值较大，可能影响性能")
        
        # 流式配置验证
        if request.stream.enabled and request.stream.chunk_size and request.stream.chunk_size > 500:
            result["warnings"].append("流式输出块大小较大，可能影响响应速度")
        
        logger.info(f"请求验证完成: {len(result['errors'])} 错误, {len(result['warnings'])} 警告")
        return result
    
    @staticmethod
    def optimize_for_provider(request: OptimizedTextGenerationRequest, provider_name: str) -> OptimizedTextGenerationRequest:
        """为特定供应商优化请求
        
        Args:
            request: 原始优化版本请求
            provider_name: 供应商名称
            
        Returns:
            OptimizedTextGenerationRequest: 优化后的请求
        """
        logger.info(f"为供应商 {provider_name} 优化请求")
        
        # 创建请求副本
        optimized_request = request.copy(deep=True)
        
        # 根据供应商特性进行优化
        if provider_name.lower() == "anthropic":
            # Anthropic 要求必须设置 max_tokens
            if optimized_request.generation.max_tokens is None:
                optimized_request.generation.max_tokens = 4096
                logger.info("为Anthropic设置默认max_tokens: 4096")
        
        elif provider_name.lower() == "google":
            # Google AI 有较低的token限制
            if optimized_request.generation.max_tokens and optimized_request.generation.max_tokens > 8192:
                optimized_request.generation.max_tokens = 8192
                logger.warning("Google AI max_tokens限制为8192，已自动调整")
        
        return optimized_request


# =============================================================================
# 批量适配器
# =============================================================================

class BatchRequestAdapter:
    """批量请求适配器
    
    处理批量请求的适配和转换。
    """
    
    @staticmethod
    def adapt_batch_requests(requests: List[Union[Dict[str, Any], TextGenerationRequest, OptimizedTextGenerationRequest]]) -> List[OptimizedTextGenerationRequest]:
        """批量适配请求
        
        Args:
            requests: 批量请求列表
            
        Returns:
            List[OptimizedTextGenerationRequest]: 优化版本请求列表
        """
        logger.info(f"开始批量适配 {len(requests)} 个请求")
        
        adapted_requests = []
        for i, request in enumerate(requests):
            try:
                adapted_request = RequestAdapter.adapt_request(request)
                adapted_requests.append(adapted_request)
            except Exception as e:
                logger.error(f"适配第 {i+1} 个请求时发生错误: {e}")
                raise
        
        logger.info(f"批量适配完成，成功适配 {len(adapted_requests)} 个请求")
        return adapted_requests
    
    @staticmethod
    def validate_batch_requests(requests: List[OptimizedTextGenerationRequest]) -> Dict[str, Any]:
        """批量验证请求
        
        Args:
            requests: 优化版本请求列表
            
        Returns:
            Dict[str, Any]: 批量验证结果
        """
        logger.info(f"开始批量验证 {len(requests)} 个请求")
        
        batch_result = {
            "total_requests": len(requests),
            "valid_requests": 0,
            "invalid_requests": 0,
            "results": []
        }
        
        for i, request in enumerate(requests):
            validation_result = OptimizedRequestAdapter.validate_request(request)
            batch_result["results"].append({
                "index": i,
                "validation": validation_result
            })
            
            if not validation_result["errors"]:
                batch_result["valid_requests"] += 1
            else:
                batch_result["invalid_requests"] += 1
        
        logger.info(f"批量验证完成: {batch_result['valid_requests']} 有效, {batch_result['invalid_requests']} 无效")
        return batch_result
