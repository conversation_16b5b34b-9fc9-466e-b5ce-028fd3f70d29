"""
AI Gen Hub 适配器模块

这个模块包含了各种适配器的实现，用于：
- 请求格式转换（传统格式 ↔ 优化版本）
- 供应商API适配（OpenAI、Anthropic、Google、DashScope等）
- 向后兼容性支持
- 参数验证和转换

重构说明：
- 将原本在 docs/api_optimization/ 中的适配器实现移动到这里
- 提供统一的适配器接口和实现
- 支持渐进式迁移和供应商兼容性检查
"""

from .request_adapter import (
    RequestAdapter,
    LegacyRequestAdapter,
    OptimizedRequestAdapter,
)

from .provider_adapter import (
    ProviderAdapter,
    OpenAIAdapter,
    AnthropicAdapter,
    GoogleAdapter,
    DashScopeAdapter,
    ProviderAdapterFactory,
)

from .compatibility import (
    CompatibilityChecker,
    MigrationHelper,
    BackwardCompatibilityMixin,
)

__all__ = [
    # 请求适配器
    "RequestAdapter",
    "LegacyRequestAdapter", 
    "OptimizedRequestAdapter",
    
    # 供应商适配器
    "ProviderAdapter",
    "OpenAIAdapter",
    "AnthropicAdapter",
    "GoogleAdapter",
    "DashScopeAdapter",
    "ProviderAdapterFactory",
    
    # 兼容性支持
    "CompatibilityChecker",
    "MigrationHelper",
    "BackwardCompatibilityMixin",
]
