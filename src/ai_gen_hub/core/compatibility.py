"""
AI Gen Hub 供应商兼容性管理模块

提供v2接口与不同AI供应商之间的兼容性处理，包括：
- 参数映射和转换
- 供应商能力检查
- 消息格式转换
- 兼容性验证和建议

主要功能：
- ParameterAdapter：处理不同供应商的参数名称和格式映射
- ProviderCapabilityChecker：检查供应商支持的功能和限制
- MessageFormatConverter：转换不同的消息格式
- CompatibilityValidator：提供完整的兼容性验证

支持的供应商：
- OpenAI：完全兼容
- Google AI：需要参数映射
- Anthropic：需要消息格式转换
- Cohere：需要参数映射和消息格式转换
- Hugging Face：基本兼容（OpenAI格式）

更新日期：2025-08-14
"""

from typing import Any, Dict, List, Optional, Set, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import logging

from ai_gen_hub.core.interfaces import (
    OptimizedTextGenerationRequest,
    GenerationConfig,
    StreamConfig,
    SafetyConfig,
    Message,
    MessageRole
)


class ProviderType(Enum):
    """供应商类型枚举"""
    OPENAI = "openai"
    GOOGLE_AI = "google_ai"
    ANTHROPIC = "anthropic"
    COHERE = "cohere"
    HUGGINGFACE = "huggingface"
    DASHSCOPE = "dashscope"


@dataclass
class ParameterMapping:
    """参数映射配置"""
    source_param: str          # v2接口参数名
    target_param: str          # 供应商API参数名
    transform_func: Optional[callable] = None  # 参数值转换函数
    required: bool = True      # 是否必需参数
    default_value: Any = None  # 默认值


@dataclass
class ProviderCapability:
    """供应商能力定义"""
    supports_streaming: bool = True
    supports_functions: bool = False
    supports_tools: bool = False
    supports_vision: bool = False
    supports_top_k: bool = False
    supports_safety_config: bool = False
    supports_response_format: bool = False
    max_tokens_limit: Optional[int] = None
    supported_stop_sequences: int = 10  # 支持的停止序列数量


class ParameterAdapter:
    """参数适配器
    
    负责将v2接口的OptimizedTextGenerationRequest参数
    转换为不同供应商API所需的格式。
    
    功能：
    - 参数名称映射（如top_p → p）
    - 参数值转换（如格式转换）
    - 不支持参数的过滤
    - 默认值设置
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._parameter_mappings = self._init_parameter_mappings()
        self._provider_capabilities = self._init_provider_capabilities()
    
    def _init_parameter_mappings(self) -> Dict[ProviderType, List[ParameterMapping]]:
        """初始化参数映射配置"""
        return {
            ProviderType.COHERE: [
                ParameterMapping("temperature", "temperature"),
                ParameterMapping("max_tokens", "max_tokens"),
                ParameterMapping("top_p", "p"),  # Cohere使用p而不是top_p
                ParameterMapping("top_k", "k"),  # Cohere使用k而不是top_k
                ParameterMapping("stop", "stop_sequences", self._convert_stop_to_list),
            ],
            ProviderType.HUGGINGFACE: [
                # Hugging Face使用OpenAI兼容格式，大部分参数直接映射
                ParameterMapping("temperature", "temperature"),
                ParameterMapping("max_tokens", "max_tokens"),
                ParameterMapping("top_p", "top_p"),
                ParameterMapping("stop", "stop"),
                # 注意：Hugging Face不支持top_k
            ],
            ProviderType.OPENAI: [
                # OpenAI是标准格式，直接映射
                ParameterMapping("temperature", "temperature"),
                ParameterMapping("max_tokens", "max_tokens"),
                ParameterMapping("top_p", "top_p"),
                ParameterMapping("stop", "stop"),
            ],
            ProviderType.ANTHROPIC: [
                ParameterMapping("temperature", "temperature"),
                ParameterMapping("max_tokens", "max_tokens"),
                ParameterMapping("top_p", "top_p"),
                ParameterMapping("top_k", "top_k"),
                ParameterMapping("stop", "stop_sequences", self._convert_stop_to_list),
            ],
            ProviderType.GOOGLE_AI: [
                ParameterMapping("temperature", "temperature"),
                ParameterMapping("max_tokens", "maxOutputTokens"),  # Google AI使用不同的参数名
                ParameterMapping("top_p", "topP"),
                ParameterMapping("top_k", "topK"),
                ParameterMapping("stop", "stopSequences", self._convert_stop_to_list),
            ],
        }
    
    def _init_provider_capabilities(self) -> Dict[ProviderType, ProviderCapability]:
        """初始化供应商能力配置"""
        return {
            ProviderType.OPENAI: ProviderCapability(
                supports_streaming=True,
                supports_functions=True,
                supports_tools=True,
                supports_vision=True,
                supports_top_k=False,  # OpenAI不支持top_k
                supports_response_format=True,
                max_tokens_limit=4096
            ),
            ProviderType.COHERE: ProviderCapability(
                supports_streaming=True,
                supports_functions=True,
                supports_tools=True,
                supports_vision=False,
                supports_top_k=True,
                supports_safety_config=False,
                supports_response_format=False,
                max_tokens_limit=4000
            ),
            ProviderType.HUGGINGFACE: ProviderCapability(
                supports_streaming=True,
                supports_functions=True,  # 部分模型支持
                supports_tools=True,     # 部分模型支持
                supports_vision=True,    # 部分模型支持
                supports_top_k=False,    # OpenAI兼容API不支持
                supports_safety_config=False,
                supports_response_format=False,  # 部分模型支持
                max_tokens_limit=None    # 取决于具体模型
            ),
            ProviderType.ANTHROPIC: ProviderCapability(
                supports_streaming=True,
                supports_functions=True,
                supports_tools=True,
                supports_vision=True,
                supports_top_k=True,
                supports_safety_config=False,
                supports_response_format=False,
                max_tokens_limit=8192
            ),
            ProviderType.GOOGLE_AI: ProviderCapability(
                supports_streaming=True,
                supports_functions=True,
                supports_tools=True,
                supports_vision=True,
                supports_top_k=True,
                supports_safety_config=True,  # Google AI有安全设置
                supports_response_format=False,
                max_tokens_limit=2048
            ),
        }
    
    def adapt_parameters(
        self,
        request: OptimizedTextGenerationRequest,
        provider_type: ProviderType
    ) -> Dict[str, Any]:
        """适配参数到指定供应商格式
        
        Args:
            request: v2接口的优化请求
            provider_type: 目标供应商类型
            
        Returns:
            Dict[str, Any]: 适配后的参数字典
        """
        adapted_params = {}
        mappings = self._parameter_mappings.get(provider_type, [])
        capability = self._provider_capabilities.get(provider_type)
        
        # 处理生成配置参数
        if request.generation:
            for mapping in mappings:
                source_value = getattr(request.generation, mapping.source_param, None)
                
                if source_value is not None:
                    # 应用参数转换函数
                    if mapping.transform_func:
                        target_value = mapping.transform_func(source_value)
                    else:
                        target_value = source_value
                    
                    adapted_params[mapping.target_param] = target_value
                elif mapping.default_value is not None:
                    adapted_params[mapping.target_param] = mapping.default_value
        
        # 处理流式配置
        if request.stream and request.stream.enabled:
            adapted_params["stream"] = True
        
        # 过滤不支持的参数
        if capability:
            adapted_params = self._filter_unsupported_params(adapted_params, capability)
        
        return adapted_params
    
    def _filter_unsupported_params(
        self,
        params: Dict[str, Any],
        capability: ProviderCapability
    ) -> Dict[str, Any]:
        """过滤不支持的参数"""
        filtered_params = params.copy()
        
        # 检查top_k支持
        if not capability.supports_top_k and "k" in filtered_params:
            self.logger.warning("供应商不支持top_k参数，已忽略")
            del filtered_params["k"]
        
        if not capability.supports_top_k and "topK" in filtered_params:
            self.logger.warning("供应商不支持top_k参数，已忽略")
            del filtered_params["topK"]
        
        # 检查最大token限制
        if capability.max_tokens_limit and "max_tokens" in filtered_params:
            if filtered_params["max_tokens"] > capability.max_tokens_limit:
                self.logger.warning(
                    f"max_tokens ({filtered_params['max_tokens']}) 超过供应商限制 "
                    f"({capability.max_tokens_limit})，已调整"
                )
                filtered_params["max_tokens"] = capability.max_tokens_limit
        
        return filtered_params
    
    @staticmethod
    def _convert_stop_to_list(stop_value: Union[str, List[str]]) -> List[str]:
        """将停止序列转换为列表格式"""
        if isinstance(stop_value, str):
            return [stop_value]
        elif isinstance(stop_value, list):
            return stop_value
        else:
            return []
    
    def get_provider_capability(self, provider_type: ProviderType) -> Optional[ProviderCapability]:
        """获取供应商能力信息"""
        return self._provider_capabilities.get(provider_type)


class MessageFormatConverter:
    """消息格式转换器
    
    负责将标准的messages格式转换为不同供应商所需的格式。
    特别处理Cohere的message+chat_history+preamble格式。
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def convert_messages_for_cohere(
        self,
        messages: List[Message]
    ) -> Dict[str, Any]:
        """转换消息格式为Cohere API格式
        
        Cohere API需要将消息分为：
        - preamble: 系统消息
        - chat_history: 历史对话
        - message: 当前用户消息
        
        Args:
            messages: 标准消息列表
            
        Returns:
            Dict[str, Any]: Cohere格式的消息数据
        """
        result = {}
        
        # 分离系统消息和对话消息
        system_messages = []
        conversation_messages = []
        
        for msg in messages:
            if msg.role == MessageRole.SYSTEM:
                system_messages.append(msg)
            else:
                conversation_messages.append(msg)
        
        # 处理系统消息（preamble）
        if system_messages:
            preamble = "\n".join([msg.content for msg in system_messages])
            result["preamble"] = preamble
        
        # 处理对话消息
        if conversation_messages:
            if len(conversation_messages) == 1 and conversation_messages[0].role == MessageRole.USER:
                # 只有一个用户消息
                result["message"] = conversation_messages[0].content
            else:
                # 有对话历史
                chat_history = []
                current_message = None
                
                for i, msg in enumerate(conversation_messages):
                    if i == len(conversation_messages) - 1 and msg.role == MessageRole.USER:
                        # 最后一个用户消息作为当前消息
                        current_message = msg.content
                    else:
                        # 其他消息作为历史
                        role = "USER" if msg.role == MessageRole.USER else "CHATBOT"
                        chat_history.append({
                            "role": role,
                            "message": msg.content
                        })
                
                if chat_history:
                    result["chat_history"] = chat_history
                
                if current_message:
                    result["message"] = current_message
                else:
                    # 如果没有当前用户消息，使用默认消息
                    result["message"] = "继续对话"
        
        return result
    
    def convert_messages_for_anthropic(
        self,
        messages: List[Message]
    ) -> Dict[str, Any]:
        """转换消息格式为Anthropic API格式
        
        Anthropic API需要将系统消息单独处理。
        """
        result = {}
        
        # 分离系统消息和对话消息
        system_messages = []
        conversation_messages = []
        
        for msg in messages:
            if msg.role == MessageRole.SYSTEM:
                system_messages.append(msg)
            else:
                conversation_messages.append(msg)
        
        # 处理系统消息
        if system_messages:
            system_content = "\n".join([msg.content for msg in system_messages])
            result["system"] = system_content
        
        # 处理对话消息
        if conversation_messages:
            result["messages"] = [
                {
                    "role": msg.role.value,
                    "content": msg.content
                }
                for msg in conversation_messages
            ]
        
        return result


class CompatibilityValidator:
    """兼容性验证器

    提供完整的v2接口与供应商兼容性验证，包括：
    - 参数兼容性检查
    - 功能支持验证
    - 优化建议生成
    - 兼容性报告
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.parameter_adapter = ParameterAdapter()
        self.message_converter = MessageFormatConverter()

    def validate_compatibility(
        self,
        request: OptimizedTextGenerationRequest,
        provider_type: ProviderType
    ) -> Dict[str, Any]:
        """验证请求与供应商的兼容性

        Args:
            request: v2接口请求
            provider_type: 供应商类型

        Returns:
            Dict[str, Any]: 兼容性验证报告
        """
        report = {
            "provider": provider_type.value,
            "compatible": True,
            "warnings": [],
            "errors": [],
            "suggestions": [],
            "adapted_params": {},
            "unsupported_features": []
        }

        try:
            # 获取供应商能力
            capability = self.parameter_adapter.get_provider_capability(provider_type)
            if not capability:
                report["errors"].append(f"未知的供应商类型: {provider_type.value}")
                report["compatible"] = False
                return report

            # 验证基本参数
            self._validate_basic_parameters(request, capability, report)

            # 验证高级功能
            self._validate_advanced_features(request, capability, report)

            # 验证流式配置
            self._validate_streaming_config(request, capability, report)

            # 验证安全配置
            self._validate_safety_config(request, capability, report)

            # 生成适配后的参数
            report["adapted_params"] = self.parameter_adapter.adapt_parameters(
                request, provider_type
            )

            # 生成优化建议
            self._generate_suggestions(request, capability, report)

        except Exception as e:
            self.logger.error(f"兼容性验证失败: {e}")
            report["errors"].append(f"验证过程中发生错误: {str(e)}")
            report["compatible"] = False

        return report

    def _validate_basic_parameters(
        self,
        request: OptimizedTextGenerationRequest,
        capability: ProviderCapability,
        report: Dict[str, Any]
    ) -> None:
        """验证基本参数"""
        if not request.generation:
            return

        # 检查top_k支持
        if request.generation.top_k is not None and not capability.supports_top_k:
            report["warnings"].append("供应商不支持top_k参数，将被忽略")
            report["unsupported_features"].append("top_k")

        # 检查max_tokens限制
        if (request.generation.max_tokens and
            capability.max_tokens_limit and
            request.generation.max_tokens > capability.max_tokens_limit):
            report["warnings"].append(
                f"max_tokens ({request.generation.max_tokens}) 超过供应商限制 "
                f"({capability.max_tokens_limit})，将被调整"
            )

        # 检查温度参数范围
        if request.generation.temperature is not None:
            if request.generation.temperature < 0 or request.generation.temperature > 2:
                report["warnings"].append("temperature参数建议在0-2范围内")

    def _validate_advanced_features(
        self,
        request: OptimizedTextGenerationRequest,
        capability: ProviderCapability,
        report: Dict[str, Any]
    ) -> None:
        """验证高级功能"""
        # 检查函数调用支持
        if request.functions and not capability.supports_functions:
            report["warnings"].append("供应商不支持函数调用功能")
            report["unsupported_features"].append("functions")

        # 检查工具调用支持
        if request.tools and not capability.supports_tools:
            report["warnings"].append("供应商不支持工具调用功能")
            report["unsupported_features"].append("tools")

        # 检查结构化输出支持
        if request.response_format and not capability.supports_response_format:
            report["warnings"].append("供应商不支持结构化输出格式")
            report["unsupported_features"].append("response_format")

    def _validate_streaming_config(
        self,
        request: OptimizedTextGenerationRequest,
        capability: ProviderCapability,
        report: Dict[str, Any]
    ) -> None:
        """验证流式配置"""
        if not request.stream or not request.stream.enabled:
            return

        if not capability.supports_streaming:
            report["warnings"].append("供应商不支持流式输出")
            report["unsupported_features"].append("streaming")

        # 检查自定义块大小
        if request.stream.chunk_size is not None:
            report["warnings"].append("大部分供应商不支持自定义流式块大小")

    def _validate_safety_config(
        self,
        request: OptimizedTextGenerationRequest,
        capability: ProviderCapability,
        report: Dict[str, Any]
    ) -> None:
        """验证安全配置"""
        if not request.safety:
            return

        if not capability.supports_safety_config:
            report["warnings"].append("供应商不支持安全配置，将使用默认安全策略")
            report["unsupported_features"].append("safety_config")

    def _generate_suggestions(
        self,
        request: OptimizedTextGenerationRequest,
        capability: ProviderCapability,
        report: Dict[str, Any]
    ) -> None:
        """生成优化建议"""
        suggestions = []

        # 基于供应商能力的建议
        if capability.supports_vision and not any(
            hasattr(msg, 'images') and msg.images for msg in request.messages
        ):
            suggestions.append("该供应商支持视觉理解，可以在消息中添加图像内容")

        if capability.supports_tools and not request.tools:
            suggestions.append("该供应商支持工具调用，可以添加工具配置以增强功能")

        # 性能优化建议
        if request.generation and request.generation.max_tokens:
            if request.generation.max_tokens > 1000:
                suggestions.append("较大的max_tokens可能增加响应时间，考虑根据需要调整")

        if request.generation and request.generation.temperature:
            if request.generation.temperature > 1.5:
                suggestions.append("较高的temperature可能导致输出不稳定，建议使用0.7-1.0范围")

        report["suggestions"] = suggestions


class ProviderCompatibilityManager:
    """供应商兼容性管理器

    统一管理所有供应商的兼容性处理，提供高级接口。
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.validator = CompatibilityValidator()
        self.parameter_adapter = ParameterAdapter()
        self.message_converter = MessageFormatConverter()

        # 供应商类型映射
        self._provider_type_mapping = {
            "openai": ProviderType.OPENAI,
            "google_ai": ProviderType.GOOGLE_AI,
            "anthropic": ProviderType.ANTHROPIC,
            "cohere": ProviderType.COHERE,
            "huggingface": ProviderType.HUGGINGFACE,
            "dashscope": ProviderType.DASHSCOPE,
        }

    def get_provider_type(self, provider_name: str) -> Optional[ProviderType]:
        """根据供应商名称获取类型"""
        return self._provider_type_mapping.get(provider_name.lower())

    def validate_request_compatibility(
        self,
        request: OptimizedTextGenerationRequest,
        provider_name: str
    ) -> Dict[str, Any]:
        """验证请求兼容性"""
        provider_type = self.get_provider_type(provider_name)
        if not provider_type:
            return {
                "provider": provider_name,
                "compatible": False,
                "errors": [f"不支持的供应商: {provider_name}"],
                "warnings": [],
                "suggestions": []
            }

        return self.validator.validate_compatibility(request, provider_type)

    def adapt_request_for_provider(
        self,
        request: OptimizedTextGenerationRequest,
        provider_name: str
    ) -> Dict[str, Any]:
        """为指定供应商适配请求"""
        provider_type = self.get_provider_type(provider_name)
        if not provider_type:
            raise ValueError(f"不支持的供应商: {provider_name}")

        # 适配参数
        adapted_params = self.parameter_adapter.adapt_parameters(request, provider_type)

        # 适配消息格式
        if provider_type == ProviderType.COHERE:
            message_data = self.message_converter.convert_messages_for_cohere(request.messages)
            adapted_params.update(message_data)
        elif provider_type == ProviderType.ANTHROPIC:
            message_data = self.message_converter.convert_messages_for_anthropic(request.messages)
            adapted_params.update(message_data)
        else:
            # 其他供应商使用标准格式
            adapted_params["messages"] = [
                {
                    "role": msg.role.value,
                    "content": msg.content
                }
                for msg in request.messages
            ]

        # 添加模型信息
        adapted_params["model"] = request.model

        return adapted_params

    def get_all_provider_capabilities(self) -> Dict[str, Dict[str, Any]]:
        """获取所有供应商的能力信息"""
        capabilities = {}

        for provider_name, provider_type in self._provider_type_mapping.items():
            capability = self.parameter_adapter.get_provider_capability(provider_type)
            if capability:
                capabilities[provider_name] = {
                    "supports_streaming": capability.supports_streaming,
                    "supports_functions": capability.supports_functions,
                    "supports_tools": capability.supports_tools,
                    "supports_vision": capability.supports_vision,
                    "supports_top_k": capability.supports_top_k,
                    "supports_safety_config": capability.supports_safety_config,
                    "supports_response_format": capability.supports_response_format,
                    "max_tokens_limit": capability.max_tokens_limit,
                    "supported_stop_sequences": capability.supported_stop_sequences
                }

        return capabilities
