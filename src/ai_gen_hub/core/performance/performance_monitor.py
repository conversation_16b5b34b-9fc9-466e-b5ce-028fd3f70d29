"""
性能监控和优化系统

实时监控系统性能，自动优化配置，提供性能报告
"""

import asyncio
import time
import psutil
import statistics
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from collections import deque, defaultdict
from datetime import datetime, timedelta
import logging

from .load_balancer import SmartLoadBalancer
from .cache_manager import SmartCacheManager


@dataclass
class PerformanceSnapshot:
    """性能快照"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_io_read: int
    disk_io_write: int
    network_sent: int
    network_recv: int
    active_connections: int
    response_time_avg: float
    request_rate: float
    error_rate: float
    cache_hit_rate: float


@dataclass
class PerformanceAlert:
    """性能告警"""
    level: str  # INFO, WARNING, CRITICAL
    metric: str
    value: float
    threshold: float
    message: str
    timestamp: float
    resolved: bool = False


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, 
                 load_balancer: Optional[SmartLoadBalancer] = None,
                 cache_manager: Optional[SmartCacheManager] = None):
        self.load_balancer = load_balancer
        self.cache_manager = cache_manager
        self.logger = logging.getLogger(__name__)
        
        # 性能数据存储
        self.snapshots: deque = deque(maxlen=1440)  # 24小时数据（每分钟一个）
        self.request_times: deque = deque(maxlen=1000)  # 最近1000个请求
        self.error_counts: defaultdict = defaultdict(int)
        
        # 告警配置
        self.alert_thresholds = {
            "cpu_percent": {"warning": 70, "critical": 90},
            "memory_percent": {"warning": 80, "critical": 95},
            "response_time_avg": {"warning": 2.0, "critical": 5.0},
            "error_rate": {"warning": 0.05, "critical": 0.1},
            "cache_hit_rate": {"warning": 0.7, "critical": 0.5}  # 低于阈值告警
        }
        
        self.active_alerts: List[PerformanceAlert] = []
        self.alert_callbacks: List[Callable] = []
        
        # 监控任务
        self._monitor_task: Optional[asyncio.Task] = None
        self._optimization_task: Optional[asyncio.Task] = None
        
        # 性能基线
        self.baseline_metrics: Dict[str, float] = {}
        self.baseline_established = False
        
        # 自动优化配置
        self.auto_optimization_enabled = True
        self.optimization_interval = 300  # 5分钟
        
    async def start_monitoring(self, interval: int = 60):
        """开始性能监控"""
        if self._monitor_task is None:
            self._monitor_task = asyncio.create_task(
                self._monitoring_loop(interval)
            )
            self.logger.info("性能监控已启动")
        
        if self.auto_optimization_enabled and self._optimization_task is None:
            self._optimization_task = asyncio.create_task(
                self._optimization_loop()
            )
            self.logger.info("自动优化已启动")
    
    async def stop_monitoring(self):
        """停止性能监控"""
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
            self._monitor_task = None
        
        if self._optimization_task:
            self._optimization_task.cancel()
            try:
                await self._optimization_task
            except asyncio.CancelledError:
                pass
            self._optimization_task = None
        
        self.logger.info("性能监控已停止")
    
    async def _monitoring_loop(self, interval: int):
        """监控循环"""
        while True:
            try:
                snapshot = await self._collect_performance_data()
                self.snapshots.append(snapshot)
                
                # 检查告警
                await self._check_alerts(snapshot)
                
                # 建立性能基线
                if not self.baseline_established and len(self.snapshots) >= 10:
                    self._establish_baseline()
                
                await asyncio.sleep(interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"性能监控错误: {e}")
                await asyncio.sleep(interval)
    
    async def _optimization_loop(self):
        """优化循环"""
        while True:
            try:
                await asyncio.sleep(self.optimization_interval)
                
                if len(self.snapshots) >= 5:  # 至少5个数据点
                    await self._perform_optimization()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"自动优化错误: {e}")
    
    async def _collect_performance_data(self) -> PerformanceSnapshot:
        """收集性能数据"""
        # 系统资源
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk_io = psutil.disk_io_counters()
        network_io = psutil.net_io_counters()
        
        # 应用性能指标
        response_time_avg = self._calculate_avg_response_time()
        request_rate = self._calculate_request_rate()
        error_rate = self._calculate_error_rate()
        cache_hit_rate = await self._get_cache_hit_rate()
        
        return PerformanceSnapshot(
            timestamp=time.time(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_mb=memory.used / 1024 / 1024,
            disk_io_read=disk_io.read_bytes if disk_io else 0,
            disk_io_write=disk_io.write_bytes if disk_io else 0,
            network_sent=network_io.bytes_sent if network_io else 0,
            network_recv=network_io.bytes_recv if network_io else 0,
            active_connections=self._get_active_connections(),
            response_time_avg=response_time_avg,
            request_rate=request_rate,
            error_rate=error_rate,
            cache_hit_rate=cache_hit_rate
        )
    
    def record_request(self, response_time: float, success: bool = True):
        """记录请求"""
        self.request_times.append(time.time())
        if not success:
            self.error_counts[int(time.time() / 60)] += 1  # 按分钟统计错误
    
    def _calculate_avg_response_time(self) -> float:
        """计算平均响应时间"""
        if not self.request_times:
            return 0.0
        return statistics.mean(self.request_times)
    
    def _calculate_request_rate(self) -> float:
        """计算请求速率（每秒）"""
        if len(self.snapshots) < 2:
            return 0.0
        
        # 计算最近一分钟的请求数
        current_time = time.time()
        recent_requests = len([t for t in self.request_times 
                             if current_time - t < 60])
        return recent_requests / 60.0
    
    def _calculate_error_rate(self) -> float:
        """计算错误率"""
        total_requests = len(self.request_times)
        if total_requests == 0:
            return 0.0
        
        total_errors = sum(self.error_counts.values())
        return total_errors / total_requests
    
    async def _get_cache_hit_rate(self) -> float:
        """获取缓存命中率"""
        if self.cache_manager:
            stats = self.cache_manager.get_stats()
            return stats.get("hit_rate", 0.0)
        return 0.0
    
    def _get_active_connections(self) -> int:
        """获取活跃连接数"""
        if self.load_balancer:
            metrics_summary = self.load_balancer.get_metrics_summary()
            return sum(
                provider_metrics.get("current_connections", 0)
                for provider_metrics in metrics_summary.get("providers", {}).values()
            )
        return 0
    
    async def _check_alerts(self, snapshot: PerformanceSnapshot):
        """检查告警"""
        for metric, thresholds in self.alert_thresholds.items():
            value = getattr(snapshot, metric)
            
            # 特殊处理缓存命中率（低于阈值告警）
            if metric == "cache_hit_rate":
                if value < thresholds["critical"]:
                    await self._create_alert("CRITICAL", metric, value, thresholds["critical"])
                elif value < thresholds["warning"]:
                    await self._create_alert("WARNING", metric, value, thresholds["warning"])
            else:
                if value > thresholds["critical"]:
                    await self._create_alert("CRITICAL", metric, value, thresholds["critical"])
                elif value > thresholds["warning"]:
                    await self._create_alert("WARNING", metric, value, thresholds["warning"])
    
    async def _create_alert(self, level: str, metric: str, value: float, threshold: float):
        """创建告警"""
        message = f"{metric} {level.lower()}: {value:.2f} (阈值: {threshold})"
        
        alert = PerformanceAlert(
            level=level,
            metric=metric,
            value=value,
            threshold=threshold,
            message=message,
            timestamp=time.time()
        )
        
        self.active_alerts.append(alert)
        self.logger.warning(f"性能告警: {message}")
        
        # 调用告警回调
        for callback in self.alert_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(alert)
                else:
                    callback(alert)
            except Exception as e:
                self.logger.error(f"告警回调执行失败: {e}")
    
    def _establish_baseline(self):
        """建立性能基线"""
        if len(self.snapshots) < 10:
            return
        
        recent_snapshots = list(self.snapshots)[-10:]
        
        self.baseline_metrics = {
            "cpu_percent": statistics.mean(s.cpu_percent for s in recent_snapshots),
            "memory_percent": statistics.mean(s.memory_percent for s in recent_snapshots),
            "response_time_avg": statistics.mean(s.response_time_avg for s in recent_snapshots),
            "request_rate": statistics.mean(s.request_rate for s in recent_snapshots),
            "cache_hit_rate": statistics.mean(s.cache_hit_rate for s in recent_snapshots)
        }
        
        self.baseline_established = True
        self.logger.info(f"性能基线已建立: {self.baseline_metrics}")
    
    async def _perform_optimization(self):
        """执行性能优化"""
        if not self.baseline_established:
            return
        
        recent_snapshots = list(self.snapshots)[-5:]
        current_metrics = {
            "cpu_percent": statistics.mean(s.cpu_percent for s in recent_snapshots),
            "memory_percent": statistics.mean(s.memory_percent for s in recent_snapshots),
            "response_time_avg": statistics.mean(s.response_time_avg for s in recent_snapshots),
            "cache_hit_rate": statistics.mean(s.cache_hit_rate for s in recent_snapshots)
        }
        
        optimizations = []
        
        # CPU优化
        if current_metrics["cpu_percent"] > self.baseline_metrics["cpu_percent"] * 1.5:
            optimizations.append("高CPU使用率，建议增加负载均衡")
        
        # 内存优化
        if current_metrics["memory_percent"] > self.baseline_metrics["memory_percent"] * 1.3:
            optimizations.append("高内存使用率，建议清理缓存")
            if self.cache_manager:
                # 清理部分缓存
                await self.cache_manager.clear("temp:*")
        
        # 响应时间优化
        if current_metrics["response_time_avg"] > self.baseline_metrics["response_time_avg"] * 2:
            optimizations.append("响应时间过长，建议优化缓存策略")
        
        # 缓存优化
        if current_metrics["cache_hit_rate"] < self.baseline_metrics["cache_hit_rate"] * 0.8:
            optimizations.append("缓存命中率下降，建议预热缓存")
            if self.cache_manager:
                await self.cache_manager.preload_cache()
        
        if optimizations:
            self.logger.info(f"执行自动优化: {optimizations}")
    
    def get_performance_report(self, hours: int = 24) -> Dict[str, Any]:
        """获取性能报告"""
        if not self.snapshots:
            return {"error": "没有性能数据"}
        
        # 获取指定时间范围内的数据
        cutoff_time = time.time() - (hours * 3600)
        recent_snapshots = [s for s in self.snapshots if s.timestamp > cutoff_time]
        
        if not recent_snapshots:
            return {"error": "指定时间范围内没有数据"}
        
        report = {
            "时间范围": f"最近{hours}小时",
            "数据点数量": len(recent_snapshots),
            "系统资源": {
                "CPU使用率": {
                    "平均": f"{statistics.mean(s.cpu_percent for s in recent_snapshots):.2f}%",
                    "最大": f"{max(s.cpu_percent for s in recent_snapshots):.2f}%",
                    "最小": f"{min(s.cpu_percent for s in recent_snapshots):.2f}%"
                },
                "内存使用率": {
                    "平均": f"{statistics.mean(s.memory_percent for s in recent_snapshots):.2f}%",
                    "最大": f"{max(s.memory_percent for s in recent_snapshots):.2f}%",
                    "最小": f"{min(s.memory_percent for s in recent_snapshots):.2f}%"
                }
            },
            "应用性能": {
                "平均响应时间": f"{statistics.mean(s.response_time_avg for s in recent_snapshots):.3f}秒",
                "请求速率": f"{statistics.mean(s.request_rate for s in recent_snapshots):.2f}/秒",
                "错误率": f"{statistics.mean(s.error_rate for s in recent_snapshots):.2%}",
                "缓存命中率": f"{statistics.mean(s.cache_hit_rate for s in recent_snapshots):.2%}"
            },
            "活跃告警": len([a for a in self.active_alerts if not a.resolved]),
            "总告警数": len(self.active_alerts)
        }
        
        return report
