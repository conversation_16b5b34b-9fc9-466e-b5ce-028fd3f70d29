"""
性能优化器

集成负载均衡、缓存管理、性能监控等功能，提供统一的性能优化接口
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from .load_balancer import SmartLoadBalancer, LoadBalancingStrategy
from .cache_manager import SmartCacheManager, init_cache_manager
from .performance_monitor import PerformanceMonitor


@dataclass
class PerformanceConfig:
    """性能配置"""
    # 负载均衡配置
    load_balancing_strategy: LoadBalancingStrategy = LoadBalancingStrategy.ADAPTIVE
    health_check_interval: int = 30
    
    # 缓存配置
    memory_cache_size: int = 1000
    redis_url: Optional[str] = None
    cache_ttl: int = 3600
    
    # 监控配置
    monitoring_interval: int = 60
    auto_optimization: bool = True
    optimization_interval: int = 300
    
    # 告警阈值
    cpu_warning_threshold: float = 70.0
    cpu_critical_threshold: float = 90.0
    memory_warning_threshold: float = 80.0
    memory_critical_threshold: float = 95.0
    response_time_warning: float = 2.0
    response_time_critical: float = 5.0


class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self, config: PerformanceConfig = None):
        self.config = config or PerformanceConfig()
        self.logger = logging.getLogger(__name__)
        
        # 组件
        self.load_balancer: Optional[SmartLoadBalancer] = None
        self.cache_manager: Optional[SmartCacheManager] = None
        self.performance_monitor: Optional[PerformanceMonitor] = None
        
        # 状态
        self.is_initialized = False
        self.is_running = False
    
    async def initialize(self):
        """初始化性能优化器"""
        if self.is_initialized:
            return
        
        try:
            # 初始化负载均衡器
            self.load_balancer = SmartLoadBalancer(
                strategy=self.config.load_balancing_strategy
            )
            self.load_balancer.health_check_interval = self.config.health_check_interval
            
            # 初始化缓存管理器
            await init_cache_manager(
                redis_url=self.config.redis_url,
                memory_cache_size=self.config.memory_cache_size
            )
            from .cache_manager import get_cache_manager
            self.cache_manager = get_cache_manager()
            
            # 初始化性能监控器
            self.performance_monitor = PerformanceMonitor(
                load_balancer=self.load_balancer,
                cache_manager=self.cache_manager
            )
            
            # 配置告警阈值
            self.performance_monitor.alert_thresholds.update({
                "cpu_percent": {
                    "warning": self.config.cpu_warning_threshold,
                    "critical": self.config.cpu_critical_threshold
                },
                "memory_percent": {
                    "warning": self.config.memory_warning_threshold,
                    "critical": self.config.memory_critical_threshold
                },
                "response_time_avg": {
                    "warning": self.config.response_time_warning,
                    "critical": self.config.response_time_critical
                }
            })
            
            # 配置自动优化
            self.performance_monitor.auto_optimization_enabled = self.config.auto_optimization
            self.performance_monitor.optimization_interval = self.config.optimization_interval
            
            self.is_initialized = True
            self.logger.info("性能优化器初始化完成")
            
        except Exception as e:
            self.logger.error(f"性能优化器初始化失败: {e}")
            raise
    
    async def start(self):
        """启动性能优化器"""
        if not self.is_initialized:
            await self.initialize()
        
        if self.is_running:
            return
        
        try:
            # 启动负载均衡器健康检查
            if self.load_balancer:
                await self.load_balancer.start_health_check()
            
            # 启动性能监控
            if self.performance_monitor:
                await self.performance_monitor.start_monitoring(
                    interval=self.config.monitoring_interval
                )
            
            # 预热缓存
            if self.cache_manager:
                await self.cache_manager.preload_cache()
            
            self.is_running = True
            self.logger.info("性能优化器已启动")
            
        except Exception as e:
            self.logger.error(f"性能优化器启动失败: {e}")
            raise
    
    async def stop(self):
        """停止性能优化器"""
        if not self.is_running:
            return
        
        try:
            # 停止性能监控
            if self.performance_monitor:
                await self.performance_monitor.stop_monitoring()
            
            # 停止负载均衡器健康检查
            if self.load_balancer:
                await self.load_balancer.stop_health_check()
            
            # 关闭缓存管理器
            if self.cache_manager:
                await self.cache_manager.close()
            
            self.is_running = False
            self.logger.info("性能优化器已停止")
            
        except Exception as e:
            self.logger.error(f"性能优化器停止失败: {e}")
    
    def add_provider(self, provider: Any, weight: float = 1.0):
        """添加AI供应商"""
        if self.load_balancer:
            self.load_balancer.add_provider(provider, weight)
            self.logger.info(f"添加供应商: {provider.provider_id}, 权重: {weight}")
    
    def remove_provider(self, provider_id: str):
        """移除AI供应商"""
        if self.load_balancer:
            self.load_balancer.remove_provider(provider_id)
            self.logger.info(f"移除供应商: {provider_id}")
    
    async def select_provider(self, request_context: Dict[str, Any] = None) -> Optional[Any]:
        """选择最优供应商"""
        if self.load_balancer:
            return await self.load_balancer.select_provider(request_context)
        return None
    
    def record_request_start(self, provider_id: str):
        """记录请求开始"""
        if self.load_balancer:
            self.load_balancer.record_request_start(provider_id)
    
    def record_request_end(self, provider_id: str, response_time: float, success: bool):
        """记录请求结束"""
        if self.load_balancer:
            self.load_balancer.record_request_end(provider_id, response_time, success)
        
        if self.performance_monitor:
            self.performance_monitor.record_request(response_time, success)
    
    async def cache_get(self, key: str, default: Any = None) -> Any:
        """获取缓存"""
        if self.cache_manager:
            return await self.cache_manager.get(key, default)
        return default
    
    async def cache_set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存"""
        if self.cache_manager:
            return await self.cache_manager.set(key, value, ttl or self.config.cache_ttl)
        return False
    
    async def cache_delete(self, key: str) -> bool:
        """删除缓存"""
        if self.cache_manager:
            return await self.cache_manager.delete(key)
        return False
    
    def generate_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """生成缓存键"""
        if self.cache_manager:
            return self.cache_manager.generate_cache_key(prefix, *args, **kwargs)
        return f"{prefix}:{hash(str(args) + str(kwargs))}"
    
    async def get_or_set_cache(self, key: str, factory, ttl: Optional[int] = None) -> Any:
        """获取缓存，如果不存在则通过工厂函数创建"""
        if self.cache_manager:
            return await self.cache_manager.get_or_set(key, factory, ttl or self.config.cache_ttl)
        
        # 如果没有缓存管理器，直接调用工厂函数
        if asyncio.iscoroutinefunction(factory):
            return await factory()
        else:
            return factory()
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = {
            "optimizer_status": {
                "initialized": self.is_initialized,
                "running": self.is_running
            }
        }
        
        # 负载均衡器统计
        if self.load_balancer:
            stats["load_balancer"] = self.load_balancer.get_metrics_summary()
        
        # 缓存统计
        if self.cache_manager:
            stats["cache"] = self.cache_manager.get_stats()
        
        # 性能监控统计
        if self.performance_monitor:
            stats["performance"] = self.performance_monitor.get_performance_report(hours=1)
        
        return stats
    
    def add_alert_callback(self, callback):
        """添加告警回调"""
        if self.performance_monitor:
            self.performance_monitor.alert_callbacks.append(callback)
    
    async def optimize_performance(self):
        """手动触发性能优化"""
        if self.performance_monitor:
            await self.performance_monitor._perform_optimization()
            self.logger.info("手动性能优化完成")


# 全局性能优化器实例
_global_optimizer: Optional[PerformanceOptimizer] = None


def get_performance_optimizer() -> Optional[PerformanceOptimizer]:
    """获取全局性能优化器"""
    return _global_optimizer


async def init_performance_optimizer(config: PerformanceConfig = None) -> PerformanceOptimizer:
    """初始化全局性能优化器"""
    global _global_optimizer
    
    if _global_optimizer is None:
        _global_optimizer = PerformanceOptimizer(config)
        await _global_optimizer.initialize()
    
    return _global_optimizer


async def start_performance_optimizer():
    """启动全局性能优化器"""
    if _global_optimizer:
        await _global_optimizer.start()


async def stop_performance_optimizer():
    """停止全局性能优化器"""
    if _global_optimizer:
        await _global_optimizer.stop()


def performance_optimized(cache_prefix: str = None, ttl: int = 3600):
    """性能优化装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            optimizer = get_performance_optimizer()
            if not optimizer:
                # 如果没有优化器，直接执行函数
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
            
            # 生成缓存键
            if cache_prefix:
                cache_key = optimizer.generate_cache_key(cache_prefix, *args, **kwargs)
                
                # 尝试从缓存获取
                result = await optimizer.cache_get(cache_key)
                if result is not None:
                    return result
            
            # 执行函数
            start_time = asyncio.get_event_loop().time()
            try:
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # 缓存结果
                if cache_prefix:
                    await optimizer.cache_set(cache_key, result, ttl)
                
                return result
                
            except Exception as e:
                # 记录错误
                response_time = asyncio.get_event_loop().time() - start_time
                if optimizer.performance_monitor:
                    optimizer.performance_monitor.record_request(response_time, False)
                raise
        
        return wrapper
    return decorator
