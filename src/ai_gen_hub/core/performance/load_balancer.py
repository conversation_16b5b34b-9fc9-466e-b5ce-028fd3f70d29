"""
智能负载均衡器

实现AI供应商之间的智能负载均衡，包括健康检查、故障转移、性能监控等功能
"""

import asyncio
import time
import random
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import logging

# 移除不存在的导入，使用基础类型


class LoadBalancingStrategy(Enum):
    """负载均衡策略"""
    ROUND_ROBIN = "round_robin"          # 轮询
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"  # 加权轮询
    LEAST_CONNECTIONS = "least_connections"        # 最少连接
    RESPONSE_TIME = "response_time"                # 响应时间优先
    HEALTH_SCORE = "health_score"                  # 健康分数优先
    ADAPTIVE = "adaptive"                          # 自适应


@dataclass
class ProviderMetrics:
    """供应商性能指标"""
    provider_id: str
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_response_time: float = 0.0
    current_connections: int = 0
    last_request_time: float = 0.0
    health_score: float = 1.0
    weight: float = 1.0
    
    # 响应时间历史（最近100次请求）
    response_times: deque = field(default_factory=lambda: deque(maxlen=100))
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests
    
    @property
    def average_response_time(self) -> float:
        """平均响应时间"""
        if not self.response_times:
            return 0.0
        return sum(self.response_times) / len(self.response_times)
    
    @property
    def p95_response_time(self) -> float:
        """95%分位数响应时间"""
        if len(self.response_times) < 20:
            return self.average_response_time
        
        sorted_times = sorted(self.response_times)
        index = int(len(sorted_times) * 0.95)
        return sorted_times[index]
    
    def update_request(self, response_time: float, success: bool):
        """更新请求指标"""
        self.total_requests += 1
        self.total_response_time += response_time
        self.response_times.append(response_time)
        self.last_request_time = time.time()
        
        if success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1
        
        # 更新健康分数
        self._update_health_score()
    
    def _update_health_score(self):
        """更新健康分数"""
        # 基于成功率、响应时间和连接数计算健康分数
        success_score = self.success_rate
        
        # 响应时间分数（越快越好）
        avg_time = self.average_response_time
        time_score = max(0, 1 - (avg_time / 10.0))  # 假设10秒为最差响应时间
        
        # 连接数分数（连接数越少越好）
        connection_score = max(0, 1 - (self.current_connections / 100.0))  # 假设100为最大连接数
        
        # 综合分数
        self.health_score = (success_score * 0.5 + time_score * 0.3 + connection_score * 0.2)


class SmartLoadBalancer:
    """智能负载均衡器"""
    
    def __init__(self, strategy: LoadBalancingStrategy = LoadBalancingStrategy.ADAPTIVE):
        self.strategy = strategy
        self.providers: Dict[str, BaseProvider] = {}
        self.metrics: Dict[str, ProviderMetrics] = {}
        self.round_robin_index = 0
        self.logger = logging.getLogger(__name__)
        
        # 健康检查配置
        self.health_check_interval = 30  # 秒
        self.health_check_timeout = 5    # 秒
        self.unhealthy_threshold = 3     # 连续失败次数
        self.recovery_threshold = 2      # 连续成功次数
        
        # 自适应配置
        self.adaptive_window = 300       # 自适应窗口（秒）
        self.last_strategy_change = time.time()
        
        # 启动健康检查任务
        self._health_check_task = None
    
    def add_provider(self, provider: Any, weight: float = 1.0):
        """添加供应商"""
        provider_id = provider.provider_id
        self.providers[provider_id] = provider
        self.metrics[provider_id] = ProviderMetrics(
            provider_id=provider_id,
            weight=weight
        )
        
        self.logger.info(f"添加供应商: {provider_id}, 权重: {weight}")
    
    def remove_provider(self, provider_id: str):
        """移除供应商"""
        if provider_id in self.providers:
            del self.providers[provider_id]
            del self.metrics[provider_id]
            self.logger.info(f"移除供应商: {provider_id}")
    
    async def select_provider(self, request_context: Dict[str, Any] = None) -> Optional[Any]:
        """选择最优供应商"""
        available_providers = await self._get_healthy_providers()
        
        if not available_providers:
            self.logger.warning("没有可用的健康供应商")
            return None
        
        if len(available_providers) == 1:
            return available_providers[0]
        
        # 根据策略选择供应商
        if self.strategy == LoadBalancingStrategy.ROUND_ROBIN:
            return self._round_robin_select(available_providers)
        elif self.strategy == LoadBalancingStrategy.WEIGHTED_ROUND_ROBIN:
            return self._weighted_round_robin_select(available_providers)
        elif self.strategy == LoadBalancingStrategy.LEAST_CONNECTIONS:
            return self._least_connections_select(available_providers)
        elif self.strategy == LoadBalancingStrategy.RESPONSE_TIME:
            return self._response_time_select(available_providers)
        elif self.strategy == LoadBalancingStrategy.HEALTH_SCORE:
            return self._health_score_select(available_providers)
        elif self.strategy == LoadBalancingStrategy.ADAPTIVE:
            return await self._adaptive_select(available_providers, request_context)
        else:
            return random.choice(available_providers)
    
    async def _get_healthy_providers(self) -> List[Any]:
        """获取健康的供应商列表"""
        healthy_providers = []
        
        for provider_id, provider in self.providers.items():
            metrics = self.metrics[provider_id]
            
            # 检查健康分数
            if metrics.health_score > 0.3:  # 健康分数阈值
                healthy_providers.append(provider)
        
        return healthy_providers
    
    def _round_robin_select(self, providers: List[Any]) -> Any:
        """轮询选择"""
        provider = providers[self.round_robin_index % len(providers)]
        self.round_robin_index += 1
        return provider
    
    def _weighted_round_robin_select(self, providers: List[Any]) -> Any:
        """加权轮询选择"""
        weights = []
        for provider in providers:
            metrics = self.metrics[provider.provider_id]
            weights.append(metrics.weight)

        # 基于权重随机选择
        total_weight = sum(weights)
        if total_weight == 0:
            return random.choice(providers)

        rand = random.uniform(0, total_weight)
        current_weight = 0

        for i, weight in enumerate(weights):
            current_weight += weight
            if rand <= current_weight:
                return providers[i]

        return providers[-1]

    def _least_connections_select(self, providers: List[Any]) -> Any:
        """最少连接选择"""
        min_connections = float('inf')
        selected_provider = None

        for provider in providers:
            metrics = self.metrics[provider.provider_id]
            if metrics.current_connections < min_connections:
                min_connections = metrics.current_connections
                selected_provider = provider

        return selected_provider or providers[0]

    def _response_time_select(self, providers: List[Any]) -> Any:
        """响应时间优先选择"""
        best_time = float('inf')
        selected_provider = None

        for provider in providers:
            metrics = self.metrics[provider.provider_id]
            avg_time = metrics.average_response_time

            if avg_time < best_time:
                best_time = avg_time
                selected_provider = provider

        return selected_provider or providers[0]

    def _health_score_select(self, providers: List[Any]) -> Any:
        """健康分数优先选择"""
        best_score = -1
        selected_provider = None

        for provider in providers:
            metrics = self.metrics[provider.provider_id]
            if metrics.health_score > best_score:
                best_score = metrics.health_score
                selected_provider = provider

        return selected_provider or providers[0]
    
    async def _adaptive_select(self, providers: List[Any],
                             request_context: Dict[str, Any] = None) -> Any:
        """自适应选择"""
        # 分析当前性能状况，动态调整策略
        current_time = time.time()
        
        # 如果距离上次策略调整超过窗口时间，重新评估策略
        if current_time - self.last_strategy_change > self.adaptive_window:
            await self._evaluate_and_adjust_strategy()
            self.last_strategy_change = current_time
        
        # 综合考虑多个因素
        scores = {}
        for provider in providers:
            metrics = self.metrics[provider.provider_id]
            
            # 综合分数 = 健康分数 * 0.4 + 响应时间分数 * 0.3 + 连接数分数 * 0.3
            health_score = metrics.health_score
            
            # 响应时间分数（归一化）
            max_time = max(m.average_response_time for m in self.metrics.values()) or 1
            time_score = 1 - (metrics.average_response_time / max_time)
            
            # 连接数分数（归一化）
            max_connections = max(m.current_connections for m in self.metrics.values()) or 1
            connection_score = 1 - (metrics.current_connections / max_connections) if max_connections > 0 else 1
            
            total_score = health_score * 0.4 + time_score * 0.3 + connection_score * 0.3
            scores[provider.provider_id] = total_score
        
        # 选择分数最高的供应商
        best_provider_id = max(scores, key=scores.get)
        return next(p for p in providers if p.provider_id == best_provider_id)
    
    async def _evaluate_and_adjust_strategy(self):
        """评估并调整策略"""
        # 分析各供应商的性能表现
        total_requests = sum(m.total_requests for m in self.metrics.values())
        if total_requests < 100:  # 数据不足，保持当前策略
            return
        
        # 计算整体性能指标
        overall_success_rate = sum(m.successful_requests for m in self.metrics.values()) / total_requests
        overall_avg_time = sum(m.total_response_time for m in self.metrics.values()) / total_requests
        
        # 根据性能指标调整策略
        if overall_success_rate < 0.9:
            # 成功率低，优先考虑健康分数
            self.strategy = LoadBalancingStrategy.HEALTH_SCORE
        elif overall_avg_time > 2.0:
            # 响应时间慢，优先考虑响应时间
            self.strategy = LoadBalancingStrategy.RESPONSE_TIME
        else:
            # 性能良好，使用加权轮询
            self.strategy = LoadBalancingStrategy.WEIGHTED_ROUND_ROBIN
        
        self.logger.info(f"自适应调整策略为: {self.strategy.value}")
    
    def record_request_start(self, provider_id: str):
        """记录请求开始"""
        if provider_id in self.metrics:
            self.metrics[provider_id].current_connections += 1
    
    def record_request_end(self, provider_id: str, response_time: float, success: bool):
        """记录请求结束"""
        if provider_id in self.metrics:
            metrics = self.metrics[provider_id]
            metrics.current_connections = max(0, metrics.current_connections - 1)
            metrics.update_request(response_time, success)
    
    async def start_health_check(self):
        """启动健康检查"""
        if self._health_check_task is None:
            self._health_check_task = asyncio.create_task(self._health_check_loop())
    
    async def stop_health_check(self):
        """停止健康检查"""
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
            self._health_check_task = None
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"健康检查错误: {e}")
                await asyncio.sleep(self.health_check_interval)
    
    async def _perform_health_checks(self):
        """执行健康检查"""
        for provider_id, provider in self.providers.items():
            try:
                # 简单的健康检查：检查供应商状态
                start_time = time.time()
                is_healthy = await self._check_provider_health(provider)
                response_time = time.time() - start_time
                
                # 更新健康状态
                metrics = self.metrics[provider_id]
                if is_healthy:
                    metrics.health_score = min(1.0, metrics.health_score + 0.1)
                else:
                    metrics.health_score = max(0.0, metrics.health_score - 0.2)
                
                self.logger.debug(f"供应商 {provider_id} 健康检查: {'健康' if is_healthy else '不健康'}, "
                                f"分数: {metrics.health_score:.2f}")
                
            except Exception as e:
                self.logger.error(f"供应商 {provider_id} 健康检查失败: {e}")
                self.metrics[provider_id].health_score = max(0.0, 
                    self.metrics[provider_id].health_score - 0.3)
    
    async def _check_provider_health(self, provider: Any) -> bool:
        """检查单个供应商健康状态"""
        try:
            # 这里可以实现具体的健康检查逻辑
            # 例如发送简单的测试请求
            return hasattr(provider, 'status') and provider.status == 'active'
        except:
            return False
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        summary = {
            "strategy": self.strategy.value,
            "total_providers": len(self.providers),
            "healthy_providers": len([m for m in self.metrics.values() if m.health_score > 0.3]),
            "providers": {}
        }
        
        for provider_id, metrics in self.metrics.items():
            summary["providers"][provider_id] = {
                "total_requests": metrics.total_requests,
                "success_rate": f"{metrics.success_rate:.2%}",
                "avg_response_time": f"{metrics.average_response_time:.3f}s",
                "current_connections": metrics.current_connections,
                "health_score": f"{metrics.health_score:.2f}",
                "weight": metrics.weight
            }
        
        return summary
