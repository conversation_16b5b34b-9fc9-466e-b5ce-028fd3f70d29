"""
高性能缓存管理器

实现多级缓存、智能预热、压缩存储等功能
"""

import asyncio
import json
import time
import hashlib
import zlib
from typing import Any, Dict, List, Optional, Union, Callable
from dataclasses import dataclass
from enum import Enum
import logging
from collections import OrderedDict, defaultdict

import redis.asyncio as redis


class CacheLevel(Enum):
    """缓存级别"""
    MEMORY = "memory"      # 内存缓存
    REDIS = "redis"        # Redis缓存
    PERSISTENT = "persistent"  # 持久化缓存


class CacheStrategy(Enum):
    """缓存策略"""
    LRU = "lru"           # 最近最少使用
    LFU = "lfu"           # 最少使用频率
    TTL = "ttl"           # 基于过期时间
    ADAPTIVE = "adaptive"  # 自适应


@dataclass
class CacheItem:
    """缓存项"""
    key: str
    value: Any
    created_at: float
    last_accessed: float
    access_count: int = 0
    ttl: Optional[float] = None
    compressed: bool = False
    size: int = 0
    
    @property
    def is_expired(self) -> bool:
        """是否已过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    @property
    def age(self) -> float:
        """缓存项年龄（秒）"""
        return time.time() - self.created_at


class MemoryCache:
    """内存缓存"""
    
    def __init__(self, max_size: int = 1000, strategy: CacheStrategy = CacheStrategy.LRU):
        self.max_size = max_size
        self.strategy = strategy
        self.cache: OrderedDict[str, CacheItem] = OrderedDict()
        self.access_counts: Dict[str, int] = defaultdict(int)
        self.total_size = 0
        
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        if key not in self.cache:
            return None
        
        item = self.cache[key]
        
        # 检查是否过期
        if item.is_expired:
            self.delete(key)
            return None
        
        # 更新访问信息
        item.last_accessed = time.time()
        item.access_count += 1
        self.access_counts[key] += 1
        
        # LRU策略：移动到末尾
        if self.strategy == CacheStrategy.LRU:
            self.cache.move_to_end(key)
        
        return item.value
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None, compress: bool = False) -> bool:
        """设置缓存项"""
        # 计算大小
        size = self._calculate_size(value)
        
        # 压缩处理
        if compress and size > 1024:  # 大于1KB才压缩
            try:
                if isinstance(value, str):
                    compressed_value = zlib.compress(value.encode('utf-8'))
                else:
                    compressed_value = zlib.compress(json.dumps(value).encode('utf-8'))
                
                if len(compressed_value) < size:
                    value = compressed_value
                    compress = True
                    size = len(compressed_value)
                else:
                    compress = False
            except:
                compress = False
        
        # 创建缓存项
        item = CacheItem(
            key=key,
            value=value,
            created_at=time.time(),
            last_accessed=time.time(),
            ttl=ttl,
            compressed=compress,
            size=size
        )
        
        # 检查是否需要清理空间
        while len(self.cache) >= self.max_size or (self.total_size + size > self.max_size * 1024):
            if not self._evict_item():
                break
        
        # 添加到缓存
        if key in self.cache:
            self.total_size -= self.cache[key].size
        
        self.cache[key] = item
        self.total_size += size
        
        return True
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        if key in self.cache:
            item = self.cache.pop(key)
            self.total_size -= item.size
            if key in self.access_counts:
                del self.access_counts[key]
            return True
        return False
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_counts.clear()
        self.total_size = 0
    
    def _evict_item(self) -> bool:
        """驱逐缓存项"""
        if not self.cache:
            return False
        
        if self.strategy == CacheStrategy.LRU:
            # 删除最久未使用的项
            key = next(iter(self.cache))
        elif self.strategy == CacheStrategy.LFU:
            # 删除使用频率最低的项
            key = min(self.access_counts, key=self.access_counts.get)
        elif self.strategy == CacheStrategy.TTL:
            # 删除最先过期的项
            expired_items = [(k, v) for k, v in self.cache.items() if v.is_expired]
            if expired_items:
                key = expired_items[0][0]
            else:
                key = next(iter(self.cache))
        else:
            # 自适应策略
            key = self._adaptive_evict()
        
        return self.delete(key)
    
    def _adaptive_evict(self) -> str:
        """自适应驱逐策略"""
        # 综合考虑访问频率、最后访问时间和缓存项大小
        scores = {}
        current_time = time.time()
        
        for key, item in self.cache.items():
            # 计算综合分数（越低越容易被驱逐）
            age_score = (current_time - item.last_accessed) / 3600  # 小时
            frequency_score = 1.0 / (item.access_count + 1)
            size_score = item.size / 1024  # KB
            
            scores[key] = age_score + frequency_score + size_score * 0.1
        
        return max(scores, key=scores.get)
    
    def _calculate_size(self, value: Any) -> int:
        """计算值的大小"""
        try:
            if isinstance(value, str):
                return len(value.encode('utf-8'))
            elif isinstance(value, (int, float)):
                return 8
            elif isinstance(value, bool):
                return 1
            else:
                return len(json.dumps(value).encode('utf-8'))
        except:
            return 1024  # 默认大小
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_accesses = sum(self.access_counts.values())
        return {
            "items": len(self.cache),
            "max_size": self.max_size,
            "total_size_kb": self.total_size / 1024,
            "total_accesses": total_accesses,
            "strategy": self.strategy.value,
            "hit_rate": 0.0  # 需要在上层计算
        }


class SmartCacheManager:
    """智能缓存管理器"""
    
    def __init__(self, redis_url: str = None, memory_cache_size: int = 1000):
        self.memory_cache = MemoryCache(max_size=memory_cache_size)
        self.redis_client: Optional[redis.Redis] = None
        self.logger = logging.getLogger(__name__)
        
        # 统计信息
        self.stats = {
            "memory_hits": 0,
            "redis_hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0
        }
        
        # 预热配置
        self.preload_patterns: List[str] = []
        self.preload_functions: Dict[str, Callable] = {}
        
        # 初始化Redis连接
        if redis_url:
            self._init_redis(redis_url)
    
    def _init_redis(self, redis_url: str):
        """初始化Redis连接"""
        try:
            self.redis_client = redis.from_url(redis_url)
            self.logger.info("Redis缓存已连接")
        except Exception as e:
            self.logger.error(f"Redis连接失败: {e}")
    
    async def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        # 1. 尝试内存缓存
        value = self.memory_cache.get(key)
        if value is not None:
            self.stats["memory_hits"] += 1
            return self._decompress_if_needed(value)
        
        # 2. 尝试Redis缓存
        if self.redis_client:
            try:
                redis_value = await self.redis_client.get(key)
                if redis_value:
                    self.stats["redis_hits"] += 1
                    # 反序列化
                    value = json.loads(redis_value)
                    # 回写到内存缓存
                    self.memory_cache.set(key, value, ttl=3600)  # 1小时TTL
                    return self._decompress_if_needed(value)
            except Exception as e:
                self.logger.error(f"Redis获取失败: {e}")
        
        # 3. 缓存未命中
        self.stats["misses"] += 1
        return default
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None, 
                 levels: List[CacheLevel] = None) -> bool:
        """设置缓存值"""
        if levels is None:
            levels = [CacheLevel.MEMORY, CacheLevel.REDIS]
        
        self.stats["sets"] += 1
        success = True
        
        # 内存缓存
        if CacheLevel.MEMORY in levels:
            compress = len(str(value)) > 1024  # 大于1KB压缩
            self.memory_cache.set(key, value, ttl=ttl, compress=compress)
        
        # Redis缓存
        if CacheLevel.REDIS in levels and self.redis_client:
            try:
                serialized_value = json.dumps(value, ensure_ascii=False)
                if ttl:
                    await self.redis_client.setex(key, ttl, serialized_value)
                else:
                    await self.redis_client.set(key, serialized_value)
            except Exception as e:
                self.logger.error(f"Redis设置失败: {e}")
                success = False
        
        return success
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        self.stats["deletes"] += 1
        success = True
        
        # 删除内存缓存
        self.memory_cache.delete(key)
        
        # 删除Redis缓存
        if self.redis_client:
            try:
                await self.redis_client.delete(key)
            except Exception as e:
                self.logger.error(f"Redis删除失败: {e}")
                success = False
        
        return success
    
    async def clear(self, pattern: str = None):
        """清空缓存"""
        # 清空内存缓存
        self.memory_cache.clear()
        
        # 清空Redis缓存
        if self.redis_client:
            try:
                if pattern:
                    keys = await self.redis_client.keys(pattern)
                    if keys:
                        await self.redis_client.delete(*keys)
                else:
                    await self.redis_client.flushdb()
            except Exception as e:
                self.logger.error(f"Redis清空失败: {e}")
    
    def _decompress_if_needed(self, value: Any) -> Any:
        """如果需要则解压缩"""
        if isinstance(value, bytes):
            try:
                # 尝试解压缩
                decompressed = zlib.decompress(value)
                return json.loads(decompressed.decode('utf-8'))
            except:
                # 如果解压缩失败，返回原值
                return value
        return value
    
    def generate_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """生成缓存键"""
        # 创建一个包含所有参数的字符串
        key_parts = [prefix]
        
        # 添加位置参数
        for arg in args:
            if isinstance(arg, (dict, list)):
                key_parts.append(json.dumps(arg, sort_keys=True))
            else:
                key_parts.append(str(arg))
        
        # 添加关键字参数
        if kwargs:
            sorted_kwargs = sorted(kwargs.items())
            key_parts.append(json.dumps(sorted_kwargs))
        
        # 生成哈希
        key_string = "|".join(key_parts)
        return hashlib.md5(key_string.encode('utf-8')).hexdigest()
    
    async def get_or_set(self, key: str, factory: Callable, ttl: Optional[int] = None) -> Any:
        """获取缓存，如果不存在则通过工厂函数创建"""
        value = await self.get(key)
        if value is not None:
            return value
        
        # 缓存未命中，通过工厂函数创建
        try:
            if asyncio.iscoroutinefunction(factory):
                value = await factory()
            else:
                value = factory()
            
            await self.set(key, value, ttl=ttl)
            return value
        except Exception as e:
            self.logger.error(f"工厂函数执行失败: {e}")
            return None
    
    def add_preload_pattern(self, pattern: str, factory: Callable):
        """添加预加载模式"""
        self.preload_patterns.append(pattern)
        self.preload_functions[pattern] = factory
    
    async def preload_cache(self):
        """预加载缓存"""
        self.logger.info("开始预加载缓存")
        
        for pattern in self.preload_patterns:
            try:
                factory = self.preload_functions[pattern]
                if asyncio.iscoroutinefunction(factory):
                    await factory()
                else:
                    factory()
                self.logger.info(f"预加载模式完成: {pattern}")
            except Exception as e:
                self.logger.error(f"预加载模式失败 {pattern}: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self.stats["memory_hits"] + self.stats["redis_hits"] + self.stats["misses"]
        
        stats = {
            "total_requests": total_requests,
            "memory_hits": self.stats["memory_hits"],
            "redis_hits": self.stats["redis_hits"],
            "misses": self.stats["misses"],
            "sets": self.stats["sets"],
            "deletes": self.stats["deletes"],
            "hit_rate": 0.0,
            "memory_hit_rate": 0.0,
            "redis_hit_rate": 0.0,
            "memory_cache": self.memory_cache.get_stats()
        }
        
        if total_requests > 0:
            stats["hit_rate"] = (self.stats["memory_hits"] + self.stats["redis_hits"]) / total_requests
            stats["memory_hit_rate"] = self.stats["memory_hits"] / total_requests
            stats["redis_hit_rate"] = self.stats["redis_hits"] / total_requests
        
        return stats
    
    async def close(self):
        """关闭缓存管理器"""
        if self.redis_client:
            await self.redis_client.close()


# 全局缓存管理器实例
cache_manager: Optional[SmartCacheManager] = None


def get_cache_manager() -> SmartCacheManager:
    """获取全局缓存管理器"""
    global cache_manager
    if cache_manager is None:
        cache_manager = SmartCacheManager()
    return cache_manager


async def init_cache_manager(redis_url: str = None, memory_cache_size: int = 1000):
    """初始化全局缓存管理器"""
    global cache_manager
    cache_manager = SmartCacheManager(redis_url, memory_cache_size)
    await cache_manager.preload_cache()


def cache_result(key_prefix: str, ttl: int = 3600):
    """缓存装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            cache = get_cache_manager()
            cache_key = cache.generate_cache_key(key_prefix, *args, **kwargs)
            
            # 尝试从缓存获取
            result = await cache.get(cache_key)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            await cache.set(cache_key, result, ttl=ttl)
            return result
        
        return wrapper
    return decorator
