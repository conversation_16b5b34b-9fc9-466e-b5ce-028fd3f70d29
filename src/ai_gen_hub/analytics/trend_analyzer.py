"""
趋势分析器

提供数据趋势分析、预测和模式识别功能
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
import logging

from ai_gen_hub.core.logging import LoggerMixin


@dataclass
class TrendResult:
    """趋势分析结果"""
    metric_name: str
    trend_direction: str  # 'increasing', 'decreasing', 'stable'
    trend_strength: float  # 0-1, 趋势强度
    predicted_values: List[float]
    confidence_interval: Tuple[float, float]
    seasonal_pattern: Optional[Dict[str, float]] = None
    anomalies: List[Dict[str, Any]] = None


class TrendAnalyzer(LoggerMixin):
    """趋势分析器"""
    
    def __init__(self):
        """初始化趋势分析器"""
        self.scaler = StandardScaler()
        self.models = {}  # 存储不同指标的模型
        
    async def analyze_metric_trend(
        self,
        metric_name: str,
        data_points: List[Dict[str, Any]],
        prediction_days: int = 7
    ) -> TrendResult:
        """分析指标趋势
        
        Args:
            metric_name: 指标名称
            data_points: 数据点列表，包含timestamp和value
            prediction_days: 预测天数
            
        Returns:
            趋势分析结果
        """
        try:
            if len(data_points) < 3:
                self.logger.warning(f"数据点不足，无法分析趋势: {metric_name}")
                return TrendResult(
                    metric_name=metric_name,
                    trend_direction='unknown',
                    trend_strength=0.0,
                    predicted_values=[],
                    confidence_interval=(0.0, 0.0)
                )
            
            # 准备数据
            df = pd.DataFrame(data_points)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.sort_values('timestamp')
            
            # 特征工程
            X, y = self._prepare_features(df)
            
            # 训练模型
            model = LinearRegression()
            model.fit(X, y)
            
            # 计算趋势方向和强度
            trend_direction, trend_strength = self._calculate_trend(model, X, y)
            
            # 预测未来值
            predicted_values = self._predict_future(model, df, prediction_days)
            
            # 计算置信区间
            confidence_interval = self._calculate_confidence_interval(model, X, y)
            
            # 检测季节性模式
            seasonal_pattern = self._detect_seasonal_pattern(df)
            
            # 检测异常值
            anomalies = self._detect_anomalies(df)
            
            return TrendResult(
                metric_name=metric_name,
                trend_direction=trend_direction,
                trend_strength=trend_strength,
                predicted_values=predicted_values,
                confidence_interval=confidence_interval,
                seasonal_pattern=seasonal_pattern,
                anomalies=anomalies
            )
            
        except Exception as e:
            self.logger.error(f"趋势分析失败: {metric_name}, 错误: {e}")
            return TrendResult(
                metric_name=metric_name,
                trend_direction='error',
                trend_strength=0.0,
                predicted_values=[],
                confidence_interval=(0.0, 0.0)
            )
    
    def _prepare_features(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """准备特征数据"""
        # 时间特征
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        df['day_of_month'] = df['timestamp'].dt.day
        df['month'] = df['timestamp'].dt.month
        
        # 时间序列索引
        df['time_index'] = range(len(df))
        
        # 滑动窗口特征
        df['value_ma_3'] = df['value'].rolling(window=3, min_periods=1).mean()
        df['value_ma_7'] = df['value'].rolling(window=7, min_periods=1).mean()
        
        # 特征矩阵
        feature_columns = ['time_index', 'hour', 'day_of_week', 'day_of_month', 'month']
        X = df[feature_columns].values
        y = df['value'].values
        
        return X, y
    
    def _calculate_trend(self, model: LinearRegression, X: np.ndarray, y: np.ndarray) -> Tuple[str, float]:
        """计算趋势方向和强度"""
        # 趋势方向基于时间系数
        time_coef = model.coef_[0]  # 时间索引的系数
        
        if time_coef > 0.01:
            direction = 'increasing'
        elif time_coef < -0.01:
            direction = 'decreasing'
        else:
            direction = 'stable'
        
        # 趋势强度基于R²分数
        strength = model.score(X, y)
        strength = max(0.0, min(1.0, strength))  # 限制在0-1范围
        
        return direction, strength
    
    def _predict_future(self, model: LinearRegression, df: pd.DataFrame, days: int) -> List[float]:
        """预测未来值"""
        predictions = []
        last_timestamp = df['timestamp'].iloc[-1]
        
        for i in range(1, days + 1):
            future_timestamp = last_timestamp + timedelta(days=i)
            
            # 构造特征
            features = [
                len(df) + i - 1,  # time_index
                future_timestamp.hour,
                future_timestamp.weekday(),
                future_timestamp.day,
                future_timestamp.month
            ]
            
            prediction = model.predict([features])[0]
            predictions.append(float(prediction))
        
        return predictions
    
    def _calculate_confidence_interval(self, model: LinearRegression, X: np.ndarray, y: np.ndarray) -> Tuple[float, float]:
        """计算置信区间"""
        predictions = model.predict(X)
        residuals = y - predictions
        std_error = np.std(residuals)
        
        # 95%置信区间
        confidence_interval = (
            float(-1.96 * std_error),
            float(1.96 * std_error)
        )
        
        return confidence_interval
    
    def _detect_seasonal_pattern(self, df: pd.DataFrame) -> Dict[str, float]:
        """检测季节性模式"""
        try:
            # 按小时统计平均值
            hourly_avg = df.groupby(df['timestamp'].dt.hour)['value'].mean()
            
            # 按星期统计平均值
            weekly_avg = df.groupby(df['timestamp'].dt.dayofweek)['value'].mean()
            
            return {
                'hourly_pattern': hourly_avg.to_dict(),
                'weekly_pattern': weekly_avg.to_dict()
            }
        except Exception:
            return {}
    
    def _detect_anomalies(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """检测异常值"""
        try:
            # 使用IQR方法检测异常值
            Q1 = df['value'].quantile(0.25)
            Q3 = df['value'].quantile(0.75)
            IQR = Q3 - Q1
            
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            anomalies = []
            for idx, row in df.iterrows():
                if row['value'] < lower_bound or row['value'] > upper_bound:
                    anomalies.append({
                        'timestamp': row['timestamp'].isoformat(),
                        'value': row['value'],
                        'type': 'outlier',
                        'severity': 'high' if abs(row['value'] - df['value'].median()) > 2 * df['value'].std() else 'medium'
                    })
            
            return anomalies
        except Exception:
            return []
    
    async def get_trend_summary(self, metrics: List[str], days: int = 30) -> Dict[str, Any]:
        """获取多个指标的趋势摘要
        
        Args:
            metrics: 指标名称列表
            days: 分析天数
            
        Returns:
            趋势摘要
        """
        summary = {
            'analysis_period': days,
            'timestamp': datetime.now().isoformat(),
            'metrics_summary': {},
            'overall_health': 'good'
        }
        
        # 这里需要从数据库或监控系统获取实际数据
        # 暂时返回示例结构
        for metric in metrics:
            summary['metrics_summary'][metric] = {
                'trend': 'stable',
                'strength': 0.7,
                'prediction_accuracy': 0.85,
                'anomaly_count': 0
            }
        
        return summary
