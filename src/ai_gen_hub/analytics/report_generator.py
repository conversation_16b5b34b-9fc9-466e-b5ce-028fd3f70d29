"""
报告生成器

提供自定义报告生成、定时报告、数据导出等功能
"""

import json
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import pandas as pd
from jinja2 import Template
import matplotlib.pyplot as plt
import seaborn as sns
from io import BytesIO
import base64

from ai_gen_hub.core.logging import LoggerMixin
from ai_gen_hub.monitoring.metrics import metrics_collector


@dataclass
class ReportConfig:
    """报告配置"""
    name: str
    description: str
    metrics: List[str]
    time_range: str  # 'daily', 'weekly', 'monthly'
    format: str  # 'html', 'pdf', 'json', 'excel'
    recipients: List[str]
    schedule: Optional[str] = None  # cron表达式
    template: Optional[str] = None
    filters: Optional[Dict[str, Any]] = None


@dataclass
class ReportData:
    """报告数据"""
    title: str
    generated_at: datetime
    time_range: Dict[str, datetime]
    summary: Dict[str, Any]
    metrics: Dict[str, Any]
    charts: Dict[str, str]  # base64编码的图表
    recommendations: List[str]


class ReportGenerator(LoggerMixin):
    """报告生成器"""
    
    def __init__(self):
        """初始化报告生成器"""
        self.report_configs = {}
        self.templates_dir = Path("src/ai_gen_hub/templates/reports")
        self.output_dir = Path("reports")
        self.output_dir.mkdir(exist_ok=True)
        
        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
    
    def register_report(self, config: ReportConfig) -> None:
        """注册报告配置
        
        Args:
            config: 报告配置
        """
        self.report_configs[config.name] = config
        self.logger.info(f"注册报告配置: {config.name}")
    
    async def generate_report(
        self,
        report_name: str,
        custom_time_range: Optional[Dict[str, datetime]] = None
    ) -> ReportData:
        """生成报告
        
        Args:
            report_name: 报告名称
            custom_time_range: 自定义时间范围
            
        Returns:
            报告数据
        """
        try:
            config = self.report_configs.get(report_name)
            if not config:
                raise ValueError(f"未找到报告配置: {report_name}")
            
            # 确定时间范围
            time_range = custom_time_range or self._get_time_range(config.time_range)
            
            # 收集数据
            metrics_data = await self._collect_metrics_data(config.metrics, time_range)
            
            # 生成摘要
            summary = self._generate_summary(metrics_data)
            
            # 生成图表
            charts = await self._generate_charts(metrics_data)
            
            # 生成建议
            recommendations = self._generate_recommendations(metrics_data, summary)
            
            report_data = ReportData(
                title=config.description,
                generated_at=datetime.now(),
                time_range=time_range,
                summary=summary,
                metrics=metrics_data,
                charts=charts,
                recommendations=recommendations
            )
            
            self.logger.info(f"报告生成完成: {report_name}")
            return report_data
            
        except Exception as e:
            self.logger.error(f"报告生成失败: {report_name}, 错误: {e}")
            raise
    
    async def export_report(
        self,
        report_data: ReportData,
        format: str = 'html',
        output_path: Optional[str] = None
    ) -> str:
        """导出报告
        
        Args:
            report_data: 报告数据
            format: 导出格式
            output_path: 输出路径
            
        Returns:
            导出文件路径
        """
        try:
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = self.output_dir / f"report_{timestamp}.{format}"
            
            if format == 'html':
                content = await self._generate_html_report(report_data)
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            elif format == 'json':
                content = self._serialize_report_data(report_data)
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(content, f, ensure_ascii=False, indent=2)
            
            elif format == 'excel':
                await self._export_excel_report(report_data, output_path)
            
            else:
                raise ValueError(f"不支持的导出格式: {format}")
            
            self.logger.info(f"报告导出完成: {output_path}")
            return str(output_path)
            
        except Exception as e:
            self.logger.error(f"报告导出失败: {e}")
            raise
    
    def _get_time_range(self, range_type: str) -> Dict[str, datetime]:
        """获取时间范围"""
        now = datetime.now()
        
        if range_type == 'daily':
            start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end = start + timedelta(days=1)
        elif range_type == 'weekly':
            start = now - timedelta(days=now.weekday())
            start = start.replace(hour=0, minute=0, second=0, microsecond=0)
            end = start + timedelta(days=7)
        elif range_type == 'monthly':
            start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            if now.month == 12:
                end = start.replace(year=now.year + 1, month=1)
            else:
                end = start.replace(month=now.month + 1)
        else:
            # 默认为过去24小时
            start = now - timedelta(hours=24)
            end = now
        
        return {'start': start, 'end': end}
    
    async def _collect_metrics_data(
        self,
        metrics: List[str],
        time_range: Dict[str, datetime]
    ) -> Dict[str, Any]:
        """收集指标数据"""
        data = {}
        
        # 这里需要从实际的监控系统获取数据
        # 暂时返回示例数据
        for metric in metrics:
            data[metric] = {
                'current_value': 85.5,
                'previous_value': 82.3,
                'change_percent': 3.9,
                'trend': 'increasing',
                'data_points': [
                    {'timestamp': time_range['start'] + timedelta(hours=i), 'value': 80 + i * 0.5}
                    for i in range(24)
                ]
            }
        
        return data
    
    def _generate_summary(self, metrics_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成摘要"""
        summary = {
            'total_metrics': len(metrics_data),
            'improving_metrics': 0,
            'declining_metrics': 0,
            'stable_metrics': 0,
            'key_insights': []
        }
        
        for metric_name, metric_data in metrics_data.items():
            trend = metric_data.get('trend', 'stable')
            if trend == 'increasing':
                summary['improving_metrics'] += 1
            elif trend == 'decreasing':
                summary['declining_metrics'] += 1
            else:
                summary['stable_metrics'] += 1
        
        # 生成关键洞察
        if summary['improving_metrics'] > summary['declining_metrics']:
            summary['key_insights'].append("整体性能呈上升趋势")
        elif summary['declining_metrics'] > summary['improving_metrics']:
            summary['key_insights'].append("部分指标需要关注")
        else:
            summary['key_insights'].append("系统运行稳定")
        
        return summary
    
    async def _generate_charts(self, metrics_data: Dict[str, Any]) -> Dict[str, str]:
        """生成图表"""
        charts = {}
        
        try:
            # 生成趋势图
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('系统指标趋势图', fontsize=16)
            
            metric_names = list(metrics_data.keys())[:4]  # 最多显示4个指标
            
            for i, metric_name in enumerate(metric_names):
                ax = axes[i // 2, i % 2]
                data_points = metrics_data[metric_name]['data_points']
                
                timestamps = [point['timestamp'] for point in data_points]
                values = [point['value'] for point in data_points]
                
                ax.plot(timestamps, values, marker='o', linewidth=2)
                ax.set_title(f'{metric_name}')
                ax.set_xlabel('时间')
                ax.set_ylabel('值')
                ax.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 转换为base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
            buffer.seek(0)
            chart_base64 = base64.b64encode(buffer.getvalue()).decode()
            charts['trends'] = chart_base64
            
            plt.close()
            
        except Exception as e:
            self.logger.error(f"图表生成失败: {e}")
        
        return charts
    
    def _generate_recommendations(
        self,
        metrics_data: Dict[str, Any],
        summary: Dict[str, Any]
    ) -> List[str]:
        """生成建议"""
        recommendations = []
        
        # 基于指标数据生成建议
        for metric_name, metric_data in metrics_data.items():
            change_percent = metric_data.get('change_percent', 0)
            
            if change_percent > 10:
                recommendations.append(f"{metric_name} 增长显著，建议继续保持")
            elif change_percent < -10:
                recommendations.append(f"{metric_name} 下降明显，需要关注和优化")
        
        # 基于摘要生成建议
        if summary['declining_metrics'] > summary['total_metrics'] * 0.3:
            recommendations.append("建议进行系统性能优化")
        
        if not recommendations:
            recommendations.append("系统运行正常，继续保持当前状态")
        
        return recommendations
    
    async def _generate_html_report(self, report_data: ReportData) -> str:
        """生成HTML报告"""
        template_str = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>{{ title }}</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .header { border-bottom: 2px solid #333; padding-bottom: 20px; }
                .summary { background: #f5f5f5; padding: 20px; margin: 20px 0; }
                .metric { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
                .chart { text-align: center; margin: 20px 0; }
                .recommendations { background: #e8f4fd; padding: 20px; margin: 20px 0; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{{ title }}</h1>
                <p>生成时间: {{ generated_at }}</p>
                <p>时间范围: {{ time_range.start }} - {{ time_range.end }}</p>
            </div>
            
            <div class="summary">
                <h2>摘要</h2>
                <p>总指标数: {{ summary.total_metrics }}</p>
                <p>改善指标: {{ summary.improving_metrics }}</p>
                <p>下降指标: {{ summary.declining_metrics }}</p>
            </div>
            
            {% if charts.trends %}
            <div class="chart">
                <h2>趋势图</h2>
                <img src="data:image/png;base64,{{ charts.trends }}" alt="趋势图">
            </div>
            {% endif %}
            
            <div class="recommendations">
                <h2>建议</h2>
                <ul>
                {% for rec in recommendations %}
                    <li>{{ rec }}</li>
                {% endfor %}
                </ul>
            </div>
        </body>
        </html>
        """
        
        template = Template(template_str)
        return template.render(**asdict(report_data))
    
    def _serialize_report_data(self, report_data: ReportData) -> Dict[str, Any]:
        """序列化报告数据"""
        data = asdict(report_data)
        
        # 转换datetime对象
        data['generated_at'] = report_data.generated_at.isoformat()
        data['time_range']['start'] = report_data.time_range['start'].isoformat()
        data['time_range']['end'] = report_data.time_range['end'].isoformat()
        
        return data
    
    async def _export_excel_report(self, report_data: ReportData, output_path: str) -> None:
        """导出Excel报告"""
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # 摘要页
            summary_df = pd.DataFrame([report_data.summary])
            summary_df.to_excel(writer, sheet_name='摘要', index=False)
            
            # 指标数据页
            for metric_name, metric_data in report_data.metrics.items():
                if 'data_points' in metric_data:
                    df = pd.DataFrame(metric_data['data_points'])
                    df.to_excel(writer, sheet_name=metric_name[:31], index=False)  # Excel工作表名称限制
    
    async def schedule_reports(self) -> None:
        """定时报告任务"""
        # 这里可以实现定时报告功能
        # 使用APScheduler或类似的调度器
        pass
