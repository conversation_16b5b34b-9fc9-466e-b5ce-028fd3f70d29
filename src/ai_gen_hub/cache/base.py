"""
AI Gen Hub 缓存基础接口

定义缓存系统的抽象接口和基础实现，包括：
- 缓存接口定义
- 缓存键生成策略
- 缓存统计和监控
- 缓存失效策略
"""

import hashlib
import json
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from ai_gen_hub.core.logging import LoggerMixin


class CacheBackend(str, Enum):
    """缓存后端类型"""
    MEMORY = "memory"  # 内存缓存
    REDIS = "redis"  # Redis缓存
    MULTI_LEVEL = "multi_level"  # 多级缓存


@dataclass
class CacheStats:
    """缓存统计信息"""
    hits: int = 0  # 命中次数
    misses: int = 0  # 未命中次数
    sets: int = 0  # 设置次数
    deletes: int = 0  # 删除次数
    evictions: int = 0  # 驱逐次数
    total_size: int = 0  # 总大小（字节）
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    @property
    def miss_rate(self) -> float:
        """未命中率"""
        return 1.0 - self.hit_rate


class CacheInterface(ABC):
    """缓存接口抽象基类"""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值，如果不存在则返回None
        """
        pass
    
    @abstractmethod
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒），None表示不过期
            
        Returns:
            是否设置成功
        """
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """删除缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """检查缓存键是否存在
        
        Args:
            key: 缓存键
            
        Returns:
            是否存在
        """
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """清空所有缓存
        
        Returns:
            是否清空成功
        """
        pass
    
    @abstractmethod
    async def get_stats(self) -> CacheStats:
        """获取缓存统计信息
        
        Returns:
            缓存统计信息
        """
        pass
    
    @abstractmethod
    async def get_size(self) -> int:
        """获取缓存大小
        
        Returns:
            缓存大小（字节）
        """
        pass


class CacheKeyGenerator(LoggerMixin):
    """缓存键生成器
    
    负责根据请求参数生成唯一的缓存键
    """
    
    def __init__(self, prefix: str = "ai_gen_hub"):
        """初始化缓存键生成器
        
        Args:
            prefix: 缓存键前缀
        """
        self.prefix = prefix
    
    def generate_key(
        self,
        request_type: str,
        model: str,
        parameters: Dict[str, Any],
        user_id: Optional[str] = None
    ) -> str:
        """生成缓存键
        
        Args:
            request_type: 请求类型（text_generation, image_generation等）
            model: 模型名称
            parameters: 请求参数
            user_id: 用户ID（可选）
            
        Returns:
            生成的缓存键
        """
        # 构建键的组成部分
        key_parts = [
            self.prefix,
            request_type,
            model
        ]
        
        # 添加用户ID（如果提供）
        if user_id:
            key_parts.append(f"user:{user_id}")
        
        # 处理参数
        param_hash = self._hash_parameters(parameters)
        key_parts.append(param_hash)
        
        # 组合成最终的键
        cache_key = ":".join(key_parts)
        
        self.logger.debug(
            "生成缓存键",
            request_type=request_type,
            model=model,
            cache_key=cache_key
        )
        
        return cache_key
    
    def _hash_parameters(self, parameters: Dict[str, Any]) -> str:
        """对参数进行哈希
        
        Args:
            parameters: 请求参数
            
        Returns:
            参数的哈希值
        """
        # 过滤掉不影响结果的参数
        filtered_params = self._filter_cache_relevant_params(parameters)
        
        # 标准化参数
        normalized_params = self._normalize_parameters(filtered_params)
        
        # 生成哈希
        param_str = json.dumps(normalized_params, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(param_str.encode('utf-8')).hexdigest()
    
    def _filter_cache_relevant_params(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """过滤与缓存相关的参数
        
        Args:
            parameters: 原始参数
            
        Returns:
            过滤后的参数
        """
        # 排除不影响结果的参数
        excluded_params = {
            'stream',  # 流式输出不影响最终结果
            'user',  # 用户ID不影响结果
            'request_id',  # 请求ID不影响结果
            'provider_params',  # 供应商特定参数可能不影响结果
        }
        
        return {
            key: value for key, value in parameters.items()
            if key not in excluded_params and value is not None
        }
    
    def _normalize_parameters(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """标准化参数
        
        Args:
            parameters: 原始参数
            
        Returns:
            标准化后的参数
        """
        normalized = {}
        
        for key, value in parameters.items():
            if isinstance(value, float):
                # 浮点数保留6位小数
                normalized[key] = round(value, 6)
            elif isinstance(value, list):
                # 列表转换为元组（可哈希）
                normalized[key] = tuple(value)
            elif isinstance(value, dict):
                # 递归处理嵌套字典
                normalized[key] = self._normalize_parameters(value)
            else:
                normalized[key] = value
        
        return normalized
    
    def generate_pattern_key(
        self,
        request_type: str,
        model: str,
        pattern: str = "*"
    ) -> str:
        """生成模式匹配键
        
        Args:
            request_type: 请求类型
            model: 模型名称
            pattern: 匹配模式
            
        Returns:
            模式匹配键
        """
        return f"{self.prefix}:{request_type}:{model}:{pattern}"


class CacheSerializer:
    """缓存序列化器
    
    负责缓存值的序列化和反序列化
    """
    
    @staticmethod
    def serialize(value: Any) -> bytes:
        """序列化值

        Args:
            value: 要序列化的值

        Returns:
            序列化后的字节数据
        """
        try:
            # 添加类型信息和时间戳
            data = {
                'value': value,
                'type': type(value).__name__,
                'timestamp': time.time()
            }

            json_str = json.dumps(data, ensure_ascii=False, default=CacheSerializer._json_serializer)
            return json_str.encode('utf-8')

        except Exception as e:
            raise ValueError(f"序列化失败: {e}")

    @staticmethod
    def _json_serializer(obj: Any) -> Any:
        """自定义JSON序列化器

        Args:
            obj: 要序列化的对象

        Returns:
            可序列化的对象
        """
        # 处理Pydantic模型（优先使用v2的model_dump方法）
        if hasattr(obj, 'model_dump'):
            return obj.model_dump()
        elif hasattr(obj, 'dict'):
            return obj.dict()

        # 处理枚举
        if hasattr(obj, 'value'):
            return obj.value

        # 处理datetime对象
        if hasattr(obj, 'isoformat'):
            return obj.isoformat()

        # 处理其他对象，转换为字符串
        return str(obj)
    
    @staticmethod
    def deserialize(data: bytes) -> Any:
        """反序列化值
        
        Args:
            data: 序列化的字节数据
            
        Returns:
            反序列化后的值
        """
        try:
            json_str = data.decode('utf-8')
            data_dict = json.loads(json_str)
            
            return data_dict['value']
            
        except Exception as e:
            raise ValueError(f"反序列化失败: {e}")
    
    @staticmethod
    def get_size(value: Any) -> int:
        """获取值的大小
        
        Args:
            value: 要计算大小的值
            
        Returns:
            值的大小（字节）
        """
        try:
            serialized = CacheSerializer.serialize(value)
            return len(serialized)
        except Exception:
            return 0


class BaseCacheManager(CacheInterface, LoggerMixin):
    """缓存管理器基类"""
    
    def __init__(
        self,
        backend: CacheBackend,
        default_ttl: int = 3600,
        key_prefix: str = "ai_gen_hub"
    ):
        """初始化缓存管理器
        
        Args:
            backend: 缓存后端类型
            default_ttl: 默认TTL（秒）
            key_prefix: 键前缀
        """
        self.backend = backend
        self.default_ttl = default_ttl
        self.key_generator = CacheKeyGenerator(key_prefix)
        self.serializer = CacheSerializer()
        self.stats = CacheStats()
    
    async def get_or_set(
        self,
        key: str,
        factory_func,
        ttl: Optional[int] = None,
        *args,
        **kwargs
    ) -> Any:
        """获取缓存值，如果不存在则调用工厂函数生成并缓存
        
        Args:
            key: 缓存键
            factory_func: 工厂函数
            ttl: 过期时间
            *args: 工厂函数参数
            **kwargs: 工厂函数关键字参数
            
        Returns:
            缓存值或新生成的值
        """
        # 尝试从缓存获取
        cached_value = await self.get(key)
        if cached_value is not None:
            return cached_value
        
        # 缓存未命中，调用工厂函数
        try:
            if asyncio.iscoroutinefunction(factory_func):
                value = await factory_func(*args, **kwargs)
            else:
                value = factory_func(*args, **kwargs)
            
            # 缓存新值
            await self.set(key, value, ttl or self.default_ttl)
            return value
            
        except Exception as e:
            self.logger.error(
                "工厂函数执行失败",
                key=key,
                error=str(e)
            )
            raise
    
    async def delete_pattern(self, pattern: str) -> int:
        """删除匹配模式的缓存键
        
        Args:
            pattern: 匹配模式
            
        Returns:
            删除的键数量
        """
        # 基类实现为空，子类可以重写
        return 0
    
    async def get_keys(self, pattern: str = "*") -> List[str]:
        """获取匹配模式的缓存键列表
        
        Args:
            pattern: 匹配模式
            
        Returns:
            匹配的键列表
        """
        # 基类实现为空，子类可以重写
        return []
