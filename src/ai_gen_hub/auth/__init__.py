"""
AI Gen Hub 身份认证和授权模块

提供完整的身份认证和授权解决方案，包括：
- OAuth2/OIDC 身份提供商集成
- API Token 管理
- 基于角色的访问控制 (RBAC)
- 安全特性和审计日志

架构设计：
1. 控制台登录：通过外部IdP进行OAuth2/OIDC授权登录
2. API接口认证：使用API Token进行身份验证
3. 权限控制：基于RBAC的细粒度权限管理
4. 安全保护：JWT签名验证、频率限制、审计日志等
"""

from ai_gen_hub.auth.idp import (
    IdentityProvider,
    OAuth2Provider,
    OIDCProvider,
    Auth0Provider,
    KeycloakProvider,
    AzureADProvider,
    GoogleOAuthProvider,
    create_idp_provider,
)

from ai_gen_hub.auth.tokens import (
    TokenManager,
    APITokenManager,
    JWTTokenManager,
    TokenType,
    TokenScope,
)

from ai_gen_hub.auth.rbac import (
    RBACManager,
    Role,
    Permission,
    Resource,
    AccessPolicy,
)

from ai_gen_hub.auth.security import (
    SecurityManager,
    AuditLogger,
    RateLimiter,
    SecurityHeaders,
)

from ai_gen_hub.auth.middleware import (
    AuthenticationMiddleware,
    AuthorizationMiddleware,
    SecurityMiddleware,
)

from ai_gen_hub.auth.models import (
    AuthUser,
    AuthSession,
    APIToken,
    AuthConfig,
    IdPConfig,
)

from ai_gen_hub.auth.exceptions import (
    AuthenticationError,
    AuthorizationError,
    TokenError,
    IdPError,
    SecurityError,
)

__all__ = [
    # IdP 集成
    "IdentityProvider",
    "OAuth2Provider", 
    "OIDCProvider",
    "Auth0Provider",
    "KeycloakProvider",
    "AzureADProvider",
    "GoogleOAuthProvider",
    "create_idp_provider",
    
    # Token 管理
    "TokenManager",
    "APITokenManager",
    "JWTTokenManager",
    "TokenType",
    "TokenScope",
    
    # RBAC 权限控制
    "RBACManager",
    "Role",
    "Permission",
    "Resource",
    "AccessPolicy",
    
    # 安全特性
    "SecurityManager",
    "AuditLogger",
    "RateLimiter",
    "SecurityHeaders",
    
    # 中间件
    "AuthenticationMiddleware",
    "AuthorizationMiddleware", 
    "SecurityMiddleware",
    
    # 数据模型
    "AuthUser",
    "AuthSession",
    "APIToken",
    "AuthConfig",
    "IdPConfig",
    
    # 异常类
    "AuthenticationError",
    "AuthorizationError",
    "TokenError",
    "IdPError",
    "SecurityError",
]
