"""
令牌管理系统

提供完整的令牌管理功能，包括：
- API Token生成、验证、撤销
- JWT Token管理
- 令牌权限和范围控制
- 令牌生命周期管理
- 自动刷新机制
"""

import hashlib
import secrets
import string
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Any, Tuple
from uuid import UUID, uuid4

import jwt
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

from ai_gen_hub.auth.exceptions import (
    TokenError,
    TokenExpiredError,
    TokenInvalidError,
    TokenRevokedError,
    AuthenticationError,
)
from ai_gen_hub.auth.models import (
    APIToken,
    TokenType,
    TokenScope,
    Permission,
    AuthUser,
)
from ai_gen_hub.core.logging import LoggerMixin


class TokenManager(LoggerMixin):
    """令牌管理器基类
    
    提供令牌管理的通用功能和接口定义
    """
    
    def __init__(self, secret_key: str):
        """初始化令牌管理器
        
        Args:
            secret_key: 用于令牌签名的密钥
        """
        super().__init__()
        self.secret_key = secret_key
        self._revoked_tokens: Set[str] = set()  # 已撤销的令牌集合
    
    def _generate_token_id(self) -> str:
        """生成令牌ID"""
        return str(uuid4())
    
    def _hash_token(self, token: str) -> str:
        """对令牌进行哈希处理"""
        return hashlib.sha256(token.encode()).hexdigest()
    
    def revoke_token(self, token_id: str) -> None:
        """撤销令牌
        
        Args:
            token_id: 令牌ID
        """
        self._revoked_tokens.add(token_id)
        self.logger.info("令牌已撤销", token_id=token_id)
    
    def is_token_revoked(self, token_id: str) -> bool:
        """检查令牌是否已撤销
        
        Args:
            token_id: 令牌ID
            
        Returns:
            是否已撤销
        """
        return token_id in self._revoked_tokens
    
    def cleanup_expired_tokens(self) -> int:
        """清理过期令牌
        
        Returns:
            清理的令牌数量
        """
        # 子类实现具体的清理逻辑
        return 0


class APITokenManager(TokenManager):
    """API令牌管理器
    
    专门管理API访问令牌，支持：
    - 长期有效的API密钥
    - 权限范围控制
    - 使用统计和监控
    """
    
    def __init__(self, secret_key: str):
        super().__init__(secret_key)
        self._tokens: Dict[str, APIToken] = {}  # token_hash -> APIToken
        self._user_tokens: Dict[UUID, List[str]] = {}  # user_id -> [token_hash]
    
    def generate_api_token(
        self,
        user_id: UUID,
        name: str,
        scopes: Optional[Set[TokenScope]] = None,
        permissions: Optional[Set[Permission]] = None,
        expires_in_days: Optional[int] = None,
        rate_limit: int = 100,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Tuple[APIToken, str]:
        """生成API令牌
        
        Args:
            user_id: 用户ID
            name: 令牌名称
            scopes: 权限范围
            permissions: 具体权限
            expires_in_days: 过期天数（None表示永不过期）
            rate_limit: 速率限制
            metadata: 元数据
            
        Returns:
            (APIToken对象, 原始令牌字符串) 元组
        """
        # 生成令牌
        token = self._generate_api_key()
        token_hash = self._hash_token(token)
        token_prefix = token[:8] + "..."
        
        # 计算过期时间
        expires_at = None
        if expires_in_days:
            expires_at = datetime.utcnow() + timedelta(days=expires_in_days)
        
        # 创建令牌对象
        api_token = APIToken(
            user_id=user_id,
            name=name,
            token_hash=token_hash,
            token_prefix=token_prefix,
            token_type=TokenType.API_KEY,
            scopes=scopes or {TokenScope.API_ACCESS},
            permissions=permissions or set(),
            rate_limit=rate_limit,
            expires_at=expires_at,
            metadata=metadata or {}
        )
        
        # 存储令牌
        self._tokens[token_hash] = api_token
        
        # 更新用户令牌索引
        if user_id not in self._user_tokens:
            self._user_tokens[user_id] = []
        self._user_tokens[user_id].append(token_hash)
        
        self.logger.info(
            "API令牌生成成功",
            user_id=str(user_id),
            token_name=name,
            token_prefix=token_prefix,
            scopes=list(scopes) if scopes else [],
            expires_at=expires_at.isoformat() if expires_at else None
        )
        
        return api_token, token
    
    def _generate_api_key(self) -> str:
        """生成API密钥"""
        # 生成32字符的随机密钥
        alphabet = string.ascii_letters + string.digits
        return "ak_" + "".join(secrets.choice(alphabet) for _ in range(32))
    
    def verify_api_token(self, token: str) -> Optional[APIToken]:
        """验证API令牌
        
        Args:
            token: 原始令牌字符串
            
        Returns:
            APIToken对象或None
        """
        token_hash = self._hash_token(token)
        api_token = self._tokens.get(token_hash)
        
        if not api_token:
            self.logger.warning("API令牌不存在", token_prefix=token[:8] + "...")
            return None
        
        # 检查令牌状态
        if not api_token.is_active:
            self.logger.warning("API令牌已禁用", token_id=str(api_token.id))
            return None
        
        # 检查是否已撤销
        if self.is_token_revoked(str(api_token.id)):
            self.logger.warning("API令牌已撤销", token_id=str(api_token.id))
            return None
        
        # 检查是否过期
        if api_token.is_expired():
            self.logger.warning("API令牌已过期", token_id=str(api_token.id))
            return None
        
        # 更新最后使用时间
        api_token.last_used_at = datetime.utcnow()
        
        self.logger.debug(
            "API令牌验证成功",
            token_id=str(api_token.id),
            user_id=str(api_token.user_id),
            token_name=api_token.name
        )
        
        return api_token
    
    def get_user_tokens(self, user_id: UUID) -> List[APIToken]:
        """获取用户的所有令牌
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户的令牌列表
        """
        token_hashes = self._user_tokens.get(user_id, [])
        tokens = []
        
        for token_hash in token_hashes:
            token = self._tokens.get(token_hash)
            if token:
                tokens.append(token)
        
        return tokens
    
    def revoke_api_token(self, token_id: UUID) -> bool:
        """撤销API令牌
        
        Args:
            token_id: 令牌ID
            
        Returns:
            是否成功撤销
        """
        # 查找令牌
        for token_hash, api_token in self._tokens.items():
            if api_token.id == token_id:
                # 撤销令牌
                self.revoke_token(str(token_id))
                api_token.is_active = False
                
                self.logger.info(
                    "API令牌撤销成功",
                    token_id=str(token_id),
                    user_id=str(api_token.user_id),
                    token_name=api_token.name
                )
                return True
        
        self.logger.warning("API令牌不存在，无法撤销", token_id=str(token_id))
        return False
    
    def update_token_permissions(
        self,
        token_id: UUID,
        scopes: Optional[Set[TokenScope]] = None,
        permissions: Optional[Set[Permission]] = None
    ) -> bool:
        """更新令牌权限
        
        Args:
            token_id: 令牌ID
            scopes: 新的权限范围
            permissions: 新的具体权限
            
        Returns:
            是否成功更新
        """
        # 查找令牌
        for api_token in self._tokens.values():
            if api_token.id == token_id:
                if scopes is not None:
                    api_token.scopes = scopes
                
                if permissions is not None:
                    api_token.permissions = permissions
                
                self.logger.info(
                    "API令牌权限更新成功",
                    token_id=str(token_id),
                    scopes=list(scopes) if scopes else None,
                    permissions=list(permissions) if permissions else None
                )
                return True
        
        return False
    
    def cleanup_expired_tokens(self) -> int:
        """清理过期的API令牌"""
        expired_count = 0
        current_time = datetime.utcnow()
        
        # 收集过期令牌
        expired_tokens = []
        for token_hash, api_token in self._tokens.items():
            if api_token.expires_at and current_time > api_token.expires_at:
                expired_tokens.append(token_hash)
                expired_count += 1
        
        # 删除过期令牌
        for token_hash in expired_tokens:
            api_token = self._tokens[token_hash]
            del self._tokens[token_hash]
            
            # 从用户令牌索引中删除
            user_tokens = self._user_tokens.get(api_token.user_id, [])
            if token_hash in user_tokens:
                user_tokens.remove(token_hash)
        
        if expired_count > 0:
            self.logger.info(f"清理了 {expired_count} 个过期的API令牌")
        
        return expired_count


class JWTTokenManager(TokenManager):
    """JWT令牌管理器
    
    专门管理JWT访问令牌，支持：
    - 短期有效的访问令牌
    - 长期有效的刷新令牌
    - 令牌刷新机制
    - 用户会话管理
    """
    
    def __init__(
        self,
        secret_key: str,
        algorithm: str = "HS256",
        access_token_expire_minutes: int = 30,
        refresh_token_expire_days: int = 7
    ):
        super().__init__(secret_key)
        self.algorithm = algorithm
        self.access_token_expire_minutes = access_token_expire_minutes
        self.refresh_token_expire_days = refresh_token_expire_days
        self._refresh_tokens: Dict[str, Dict[str, Any]] = {}  # jti -> token_info
    
    def create_access_token(
        self,
        user: AuthUser,
        scopes: Optional[Set[TokenScope]] = None,
        expires_delta: Optional[timedelta] = None,
        additional_claims: Optional[Dict[str, Any]] = None
    ) -> str:
        """创建访问令牌
        
        Args:
            user: 用户对象
            scopes: 权限范围
            expires_delta: 自定义过期时间
            additional_claims: 额外的声明
            
        Returns:
            JWT访问令牌
        """
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        # 构建JWT载荷
        payload = {
            "sub": str(user.id),
            "username": user.username,
            "email": user.email,
            "role": user.role.value,
            "permissions": [perm.value for perm in user.permissions],
            "scopes": [scope.value for scope in (scopes or {TokenScope.API_ACCESS})],
            "exp": int(expire.timestamp()),
            "iat": int(datetime.utcnow().timestamp()),
            "jti": self._generate_token_id(),
            "type": TokenType.ACCESS.value,
        }
        
        # 添加额外声明
        if additional_claims:
            payload.update(additional_claims)
        
        # 生成JWT
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        self.logger.debug(
            "访问令牌创建成功",
            user_id=str(user.id),
            username=user.username,
            expires_at=expire.isoformat(),
            scopes=list(scopes) if scopes else []
        )
        
        return token
    
    def create_refresh_token(
        self,
        user: AuthUser,
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """创建刷新令牌
        
        Args:
            user: 用户对象
            expires_delta: 自定义过期时间
            
        Returns:
            JWT刷新令牌
        """
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        
        jti = self._generate_token_id()
        
        # 构建JWT载荷
        payload = {
            "sub": str(user.id),
            "exp": int(expire.timestamp()),
            "iat": int(datetime.utcnow().timestamp()),
            "jti": jti,
            "type": TokenType.REFRESH.value,
        }
        
        # 生成JWT
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        # 存储刷新令牌信息
        self._refresh_tokens[jti] = {
            "user_id": user.id,
            "created_at": datetime.utcnow(),
            "expires_at": expire,
            "is_active": True
        }
        
        self.logger.debug(
            "刷新令牌创建成功",
            user_id=str(user.id),
            jti=jti,
            expires_at=expire.isoformat()
        )
        
        return token
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """验证JWT令牌
        
        Args:
            token: JWT令牌字符串
            
        Returns:
            令牌载荷
            
        Raises:
            TokenError: 令牌验证失败
        """
        try:
            # 解码JWT
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm]
            )
            
            # 检查令牌类型
            token_type = payload.get("type")
            if not token_type:
                raise TokenInvalidError("令牌缺少类型信息")
            
            # 检查是否已撤销
            jti = payload.get("jti")
            if jti and self.is_token_revoked(jti):
                raise TokenRevokedError("令牌已被撤销")
            
            # 对于刷新令牌，检查额外状态
            if token_type == TokenType.REFRESH.value:
                refresh_info = self._refresh_tokens.get(jti)
                if not refresh_info or not refresh_info.get("is_active"):
                    raise TokenRevokedError("刷新令牌已失效")
            
            self.logger.debug(
                "JWT令牌验证成功",
                user_id=payload.get("sub"),
                token_type=token_type,
                jti=jti
            )
            
            return payload
            
        except jwt.ExpiredSignatureError:
            raise TokenExpiredError("令牌已过期")
        except jwt.InvalidTokenError as e:
            raise TokenInvalidError(f"令牌无效: {str(e)}")
    
    def refresh_access_token(self, refresh_token: str, user: AuthUser) -> str:
        """使用刷新令牌获取新的访问令牌
        
        Args:
            refresh_token: 刷新令牌
            user: 用户对象
            
        Returns:
            新的访问令牌
            
        Raises:
            TokenError: 刷新失败
        """
        # 验证刷新令牌
        payload = self.verify_token(refresh_token)
        
        if payload.get("type") != TokenType.REFRESH.value:
            raise TokenInvalidError("不是有效的刷新令牌")
        
        if payload.get("sub") != str(user.id):
            raise TokenInvalidError("刷新令牌用户不匹配")
        
        # 创建新的访问令牌
        new_access_token = self.create_access_token(user)
        
        self.logger.info(
            "访问令牌刷新成功",
            user_id=str(user.id),
            refresh_jti=payload.get("jti")
        )
        
        return new_access_token
    
    def revoke_refresh_token(self, refresh_token: str) -> bool:
        """撤销刷新令牌
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            是否成功撤销
        """
        try:
            payload = self.verify_token(refresh_token)
            jti = payload.get("jti")
            
            if jti:
                # 撤销令牌
                self.revoke_token(jti)
                
                # 更新刷新令牌状态
                if jti in self._refresh_tokens:
                    self._refresh_tokens[jti]["is_active"] = False
                
                self.logger.info("刷新令牌撤销成功", jti=jti)
                return True
                
        except TokenError:
            pass
        
        return False
    
    def cleanup_expired_tokens(self) -> int:
        """清理过期的刷新令牌"""
        expired_count = 0
        current_time = datetime.utcnow()
        
        # 收集过期令牌
        expired_jtis = []
        for jti, token_info in self._refresh_tokens.items():
            if current_time > token_info["expires_at"]:
                expired_jtis.append(jti)
                expired_count += 1
        
        # 删除过期令牌
        for jti in expired_jtis:
            del self._refresh_tokens[jti]
            self._revoked_tokens.discard(jti)
        
        if expired_count > 0:
            self.logger.info(f"清理了 {expired_count} 个过期的刷新令牌")
        
        return expired_count
