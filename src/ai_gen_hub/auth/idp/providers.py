"""
具体的身份提供商实现

实现了主流身份提供商的具体集成，包括：
- Auth0
- Keycloak
- Azure AD
- Google OAuth
- GitHub
- GitLab
"""

from typing import Dict, Any, Optional, Tuple

from ai_gen_hub.auth.idp.base import OAuth2Provider, OIDCProvider
from ai_gen_hub.auth.exceptions import IdPConfigurationError, IdPError
from ai_gen_hub.auth.models import IdPConfig, IdPType


class Auth0Provider(OIDCProvider):
    """Auth0身份提供商
    
    支持Auth0的OIDC集成，包括：
    - 标准OIDC流程
    - 用户信息获取
    - ID令牌验证
    """
    
    def __init__(self, config: IdPConfig):
        super().__init__(config)
        
        # Auth0特定配置调整
        if not config.issuer and hasattr(config, 'domain'):
            config.issuer = f"https://{config.metadata.get('domain')}/"
        
        if not config.jwks_url and config.issuer:
            config.jwks_url = f"{config.issuer}.well-known/jwks.json"
    
    async def _validate_configuration(self) -> None:
        """验证Auth0配置"""
        await super()._validate_configuration()
        
        # Auth0需要domain配置
        domain = self.config.metadata.get('domain')
        if not domain:
            raise IdPConfigurationError("Auth0需要配置domain")
        
        # 自动设置URL
        if not self.config.authorization_url:
            self.config.authorization_url = f"https://{domain}/authorize"
        
        if not self.config.token_url:
            self.config.token_url = f"https://{domain}/oauth/token"
        
        if not self.config.userinfo_url:
            self.config.userinfo_url = f"https://{domain}/userinfo"
    
    def generate_authorization_url(
        self, 
        state: Optional[str] = None,
        nonce: Optional[str] = None,
        **kwargs
    ) -> Tuple[str, str, str]:
        """生成Auth0授权URL"""
        # Auth0特定参数
        auth0_params = {
            "audience": self.config.audience or f"https://{self.config.metadata.get('domain')}/api/v2/",
        }
        auth0_params.update(kwargs)
        
        return super().generate_authorization_url(state, nonce, **auth0_params)


class KeycloakProvider(OIDCProvider):
    """Keycloak身份提供商
    
    支持Keycloak的OIDC集成，包括：
    - 标准OIDC流程
    - 角色和权限映射
    - 多租户支持
    """
    
    def __init__(self, config: IdPConfig):
        super().__init__(config)
        
        # Keycloak特定配置
        realm = config.metadata.get('realm', 'master')
        base_url = config.metadata.get('base_url')
        
        if base_url and realm:
            realm_url = f"{base_url}/realms/{realm}"
            
            if not config.issuer:
                config.issuer = realm_url
            
            if not config.authorization_url:
                config.authorization_url = f"{realm_url}/protocol/openid-connect/auth"
            
            if not config.token_url:
                config.token_url = f"{realm_url}/protocol/openid-connect/token"
            
            if not config.userinfo_url:
                config.userinfo_url = f"{realm_url}/protocol/openid-connect/userinfo"
            
            if not config.jwks_url:
                config.jwks_url = f"{realm_url}/protocol/openid-connect/certs"
    
    async def _validate_configuration(self) -> None:
        """验证Keycloak配置"""
        await super()._validate_configuration()
        
        # Keycloak需要base_url和realm
        base_url = self.config.metadata.get('base_url')
        realm = self.config.metadata.get('realm')
        
        if not base_url:
            raise IdPConfigurationError("Keycloak需要配置base_url")
        
        if not realm:
            raise IdPConfigurationError("Keycloak需要配置realm")
    
    def map_user_info(self, user_info: Dict[str, Any]) -> 'AuthUser':
        """映射Keycloak用户信息"""
        # Keycloak特定的用户信息映射
        auth_user = super().map_user_info(user_info)
        
        # 提取Keycloak角色信息
        realm_access = user_info.get('realm_access', {})
        resource_access = user_info.get('resource_access', {})
        
        auth_user.metadata.update({
            'keycloak_roles': realm_access.get('roles', []),
            'keycloak_resource_access': resource_access,
            'keycloak_groups': user_info.get('groups', [])
        })
        
        return auth_user


class AzureADProvider(OIDCProvider):
    """Azure AD身份提供商
    
    支持Azure AD的OIDC集成，包括：
    - Microsoft Graph API集成
    - 多租户支持
    - 条件访问策略
    """
    
    def __init__(self, config: IdPConfig):
        super().__init__(config)
        
        # Azure AD特定配置
        tenant_id = config.metadata.get('tenant_id', 'common')
        
        if not config.issuer:
            config.issuer = f"https://login.microsoftonline.com/{tenant_id}/v2.0"
        
        if not config.authorization_url:
            config.authorization_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/authorize"
        
        if not config.token_url:
            config.token_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"
        
        if not config.userinfo_url:
            config.userinfo_url = "https://graph.microsoft.com/v1.0/me"
        
        if not config.jwks_url:
            config.jwks_url = f"https://login.microsoftonline.com/{tenant_id}/discovery/v2.0/keys"
    
    async def _validate_configuration(self) -> None:
        """验证Azure AD配置"""
        await super()._validate_configuration()
        
        # Azure AD需要tenant_id
        tenant_id = self.config.metadata.get('tenant_id')
        if not tenant_id:
            raise IdPConfigurationError("Azure AD需要配置tenant_id")
    
    def generate_authorization_url(
        self, 
        state: Optional[str] = None,
        nonce: Optional[str] = None,
        **kwargs
    ) -> Tuple[str, str, str]:
        """生成Azure AD授权URL"""
        # Azure AD特定参数
        azure_params = {
            "response_mode": "query",
        }
        azure_params.update(kwargs)
        
        return super().generate_authorization_url(state, nonce, **azure_params)
    
    def map_user_info(self, user_info: Dict[str, Any]) -> 'AuthUser':
        """映射Azure AD用户信息"""
        # Azure AD特定的用户信息映射
        auth_user = super().map_user_info(user_info)
        
        # Azure AD特定字段
        auth_user.metadata.update({
            'azure_object_id': user_info.get('id'),
            'azure_tenant_id': self.config.metadata.get('tenant_id'),
            'azure_upn': user_info.get('userPrincipalName'),
            'azure_job_title': user_info.get('jobTitle'),
            'azure_department': user_info.get('department')
        })
        
        return auth_user


class GoogleOAuthProvider(OIDCProvider):
    """Google OAuth身份提供商
    
    支持Google OAuth的OIDC集成，包括：
    - Google账户登录
    - Google Workspace集成
    - 权限范围管理
    """
    
    def __init__(self, config: IdPConfig):
        super().__init__(config)
        
        # Google OAuth特定配置
        if not config.issuer:
            config.issuer = "https://accounts.google.com"
        
        if not config.authorization_url:
            config.authorization_url = "https://accounts.google.com/o/oauth2/v2/auth"
        
        if not config.token_url:
            config.token_url = "https://oauth2.googleapis.com/token"
        
        if not config.userinfo_url:
            config.userinfo_url = "https://openidconnect.googleapis.com/v1/userinfo"
        
        if not config.jwks_url:
            config.jwks_url = "https://www.googleapis.com/oauth2/v3/certs"
    
    def generate_authorization_url(
        self, 
        state: Optional[str] = None,
        nonce: Optional[str] = None,
        **kwargs
    ) -> Tuple[str, str, str]:
        """生成Google OAuth授权URL"""
        # Google特定参数
        google_params = {
            "access_type": "offline",  # 获取refresh_token
            "prompt": "consent",       # 强制显示同意页面
        }
        google_params.update(kwargs)
        
        return super().generate_authorization_url(state, nonce, **google_params)
    
    def map_user_info(self, user_info: Dict[str, Any]) -> 'AuthUser':
        """映射Google用户信息"""
        # Google特定的用户信息映射
        auth_user = super().map_user_info(user_info)
        
        # Google特定字段
        auth_user.metadata.update({
            'google_id': user_info.get('sub'),
            'google_picture': user_info.get('picture'),
            'google_locale': user_info.get('locale'),
            'google_email_verified': user_info.get('email_verified', False)
        })
        
        return auth_user


class GitHubProvider(OAuth2Provider):
    """GitHub身份提供商
    
    支持GitHub OAuth集成，包括：
    - GitHub账户登录
    - 组织和团队信息
    - 仓库权限
    """
    
    def __init__(self, config: IdPConfig):
        super().__init__(config)
        
        # GitHub特定配置
        if not config.authorization_url:
            config.authorization_url = "https://github.com/login/oauth/authorize"
        
        if not config.token_url:
            config.token_url = "https://github.com/login/oauth/access_token"
        
        if not config.userinfo_url:
            config.userinfo_url = "https://api.github.com/user"
        
        # GitHub默认scopes
        if not config.scopes:
            config.scopes = ["user:email"]
    
    async def get_user_info(self, access_token: str) -> Dict[str, Any]:
        """获取GitHub用户信息"""
        if not self.client:
            raise IdPError("IdP客户端未初始化")
        
        try:
            # 获取基本用户信息
            user_response = await self.client.get(
                "https://api.github.com/user",
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Accept": "application/vnd.github.v3+json"
                }
            )
            
            if user_response.status_code != 200:
                raise IdPError(f"获取GitHub用户信息失败: {user_response.status_code}")
            
            user_info = user_response.json()
            
            # 获取邮箱信息（如果用户邮箱不公开）
            if not user_info.get('email'):
                emails_response = await self.client.get(
                    "https://api.github.com/user/emails",
                    headers={
                        "Authorization": f"Bearer {access_token}",
                        "Accept": "application/vnd.github.v3+json"
                    }
                )
                
                if emails_response.status_code == 200:
                    emails = emails_response.json()
                    primary_email = next(
                        (email for email in emails if email.get('primary')), 
                        emails[0] if emails else None
                    )
                    if primary_email:
                        user_info['email'] = primary_email['email']
            
            return user_info
            
        except Exception as e:
            raise IdPError(f"获取GitHub用户信息失败: {str(e)}")
    
    def map_user_info(self, user_info: Dict[str, Any]) -> 'AuthUser':
        """映射GitHub用户信息"""
        from ai_gen_hub.auth.models import AuthUser
        
        # GitHub特定的用户信息映射
        external_id = str(user_info.get('id'))
        username = user_info.get('login')
        email = user_info.get('email')
        full_name = user_info.get('name')
        
        if not external_id:
            raise IdPError("GitHub用户信息中缺少用户ID")
        
        if not email:
            raise IdPError("GitHub用户信息中缺少邮箱地址")
        
        auth_user = AuthUser(
            username=username,
            email=email,
            full_name=full_name,
            external_id=external_id,
            idp_type=IdPType.GITHUB,
            metadata={
                'idp_name': self.config.name,
                'github_id': external_id,
                'github_login': username,
                'github_avatar_url': user_info.get('avatar_url'),
                'github_html_url': user_info.get('html_url'),
                'github_company': user_info.get('company'),
                'github_location': user_info.get('location'),
                'github_bio': user_info.get('bio'),
                'github_public_repos': user_info.get('public_repos'),
                'github_followers': user_info.get('followers'),
                'github_following': user_info.get('following'),
                'original_user_info': user_info
            }
        )
        
        return auth_user


class GitLabProvider(OIDCProvider):
    """GitLab身份提供商
    
    支持GitLab OIDC集成，包括：
    - GitLab.com和自托管GitLab
    - 项目和组权限
    - API访问
    """
    
    def __init__(self, config: IdPConfig):
        super().__init__(config)
        
        # GitLab特定配置
        base_url = config.metadata.get('base_url', 'https://gitlab.com')
        
        if not config.issuer:
            config.issuer = base_url
        
        if not config.authorization_url:
            config.authorization_url = f"{base_url}/oauth/authorize"
        
        if not config.token_url:
            config.token_url = f"{base_url}/oauth/token"
        
        if not config.userinfo_url:
            config.userinfo_url = f"{base_url}/api/v4/user"
        
        if not config.jwks_url:
            config.jwks_url = f"{base_url}/.well-known/jwks.json"
    
    def map_user_info(self, user_info: Dict[str, Any]) -> 'AuthUser':
        """映射GitLab用户信息"""
        # GitLab特定的用户信息映射
        auth_user = super().map_user_info(user_info)
        
        # GitLab特定字段
        auth_user.metadata.update({
            'gitlab_id': user_info.get('id'),
            'gitlab_username': user_info.get('username'),
            'gitlab_avatar_url': user_info.get('avatar_url'),
            'gitlab_web_url': user_info.get('web_url'),
            'gitlab_state': user_info.get('state'),
            'gitlab_is_admin': user_info.get('is_admin', False)
        })
        
        return auth_user


# IdP提供商工厂函数
def create_idp_provider(config: IdPConfig) -> IdentityProvider:
    """创建IdP提供商实例
    
    Args:
        config: IdP配置
        
    Returns:
        IdP提供商实例
    """
    provider_map = {
        IdPType.AUTH0: Auth0Provider,
        IdPType.KEYCLOAK: KeycloakProvider,
        IdPType.AZURE_AD: AzureADProvider,
        IdPType.GOOGLE_OAUTH: GoogleOAuthProvider,
        IdPType.GITHUB: GitHubProvider,
        IdPType.GITLAB: GitLabProvider,
    }
    
    provider_class = provider_map.get(config.type)
    if not provider_class:
        raise IdPConfigurationError(f"不支持的IdP类型: {config.type}")
    
    return provider_class(config)
