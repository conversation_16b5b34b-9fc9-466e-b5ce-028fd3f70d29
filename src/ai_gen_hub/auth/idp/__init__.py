"""
身份提供商集成模块

提供OAuth2/OIDC身份提供商的完整集成支持，包括：
- 抽象基类和通用功能
- 主流IdP的具体实现
- 配置管理和验证
- 用户信息映射

支持的身份提供商：
- Auth0: 企业级身份管理平台
- Keycloak: 开源身份和访问管理
- Azure AD: 微软云身份服务
- Google OAuth: Google账户集成
- GitHub: 开发者平台集成
- GitLab: DevOps平台集成
"""

from ai_gen_hub.auth.idp.base import (
    IdentityProvider,
    OAuth2Provider,
    OIDCProvider,
)

from ai_gen_hub.auth.idp.providers import (
    Auth0Provider,
    KeycloakProvider,
    AzureADProvider,
    GoogleOAuthProvider,
    GitHubProvider,
    GitLabProvider,
    create_idp_provider,
)

__all__ = [
    # 基础类
    "IdentityProvider",
    "OAuth2Provider",
    "OIDCProvider",
    
    # 具体提供商
    "Auth0Provider",
    "KeycloakProvider", 
    "AzureADProvider",
    "GoogleOAuthProvider",
    "GitHubProvider",
    "GitLabProvider",
    
    # 工厂函数
    "create_idp_provider",
]
