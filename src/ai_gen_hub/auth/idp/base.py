"""
身份提供商基础类

定义了身份提供商的抽象基类和通用功能，
支持OAuth2和OIDC协议的标准实现。
"""

import asyncio
import secrets
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import urlencode, parse_qs, urlparse

import httpx
import jwt
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa

from ai_gen_hub.auth.exceptions import (
    IdPError,
    IdPConnectionError,
    IdPConfigurationError,
    OAuth2Error,
    TokenError,
)
from ai_gen_hub.auth.models import IdPConfig, AuthUser, IdPType
from ai_gen_hub.core.logging import LoggerMixin


class IdentityProvider(ABC, LoggerMixin):
    """身份提供商抽象基类
    
    定义了所有身份提供商必须实现的核心接口，
    包括OAuth2/OIDC流程的标准方法。
    """
    
    def __init__(self, config: IdPConfig):
        """初始化身份提供商
        
        Args:
            config: IdP配置
        """
        super().__init__()
        self.config = config
        self.client: Optional[httpx.AsyncClient] = None
        self._jwks_cache: Dict[str, Any] = {}
        self._jwks_cache_time: float = 0
        self._jwks_cache_ttl: int = 3600  # 1小时缓存
    
    async def initialize(self) -> None:
        """初始化IdP连接"""
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0),
            limits=httpx.Limits(max_connections=10)
        )
        
        # 验证配置
        await self._validate_configuration()
        
        self.logger.info(f"身份提供商 {self.config.name} 初始化完成")
    
    async def cleanup(self) -> None:
        """清理资源"""
        if self.client:
            await self.client.aclose()
            self.client = None
    
    @abstractmethod
    async def _validate_configuration(self) -> None:
        """验证IdP配置
        
        子类必须实现此方法来验证特定IdP的配置要求
        """
        pass
    
    def generate_authorization_url(
        self, 
        state: Optional[str] = None,
        nonce: Optional[str] = None,
        **kwargs
    ) -> Tuple[str, str, str]:
        """生成授权URL
        
        Args:
            state: 状态参数（可选，如果不提供会自动生成）
            nonce: 随机数（可选，如果不提供会自动生成）
            **kwargs: 其他参数
            
        Returns:
            (authorization_url, state, nonce) 元组
        """
        if not state:
            state = secrets.token_urlsafe(32)
        
        if not nonce:
            nonce = secrets.token_urlsafe(32)
        
        params = {
            "response_type": "code",
            "client_id": self.config.client_id,
            "redirect_uri": self.config.redirect_uri,
            "scope": " ".join(self.config.scopes),
            "state": state,
            "nonce": nonce,
        }
        
        # 添加额外参数
        params.update(kwargs)
        
        authorization_url = f"{self.config.authorization_url}?{urlencode(params)}"
        
        self.logger.debug(
            "生成授权URL",
            idp=self.config.name,
            state=state[:8] + "...",
            scopes=self.config.scopes
        )
        
        return authorization_url, state, nonce
    
    async def exchange_code_for_tokens(
        self, 
        code: str,
        state: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """交换授权码获取令牌
        
        Args:
            code: 授权码
            state: 状态参数
            **kwargs: 其他参数
            
        Returns:
            令牌响应字典
        """
        if not self.client:
            raise IdPConnectionError("IdP客户端未初始化")
        
        token_data = {
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": self.config.redirect_uri,
            "client_id": self.config.client_id,
            "client_secret": self.config.client_secret,
        }
        
        # 添加额外参数
        token_data.update(kwargs)
        
        try:
            response = await self.client.post(
                self.config.token_url,
                data=token_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code != 200:
                error_data = response.json() if response.headers.get("content-type", "").startswith("application/json") else {}
                raise OAuth2Error(
                    f"令牌交换失败: {response.status_code}",
                    oauth_error=error_data.get("error"),
                    oauth_description=error_data.get("error_description")
                )
            
            tokens = response.json()
            
            self.logger.info(
                "令牌交换成功",
                idp=self.config.name,
                token_type=tokens.get("token_type"),
                expires_in=tokens.get("expires_in")
            )
            
            return tokens
            
        except httpx.RequestError as e:
            raise IdPConnectionError(f"令牌交换请求失败: {str(e)}")
    
    async def get_user_info(self, access_token: str) -> Dict[str, Any]:
        """获取用户信息
        
        Args:
            access_token: 访问令牌
            
        Returns:
            用户信息字典
        """
        if not self.client:
            raise IdPConnectionError("IdP客户端未初始化")
        
        if not self.config.userinfo_url:
            raise IdPConfigurationError("未配置用户信息URL")
        
        try:
            response = await self.client.get(
                self.config.userinfo_url,
                headers={"Authorization": f"Bearer {access_token}"}
            )
            
            if response.status_code != 200:
                raise IdPError(f"获取用户信息失败: {response.status_code}")
            
            user_info = response.json()
            
            self.logger.debug(
                "获取用户信息成功",
                idp=self.config.name,
                user_id=user_info.get(self.config.user_mapping.get("id", "sub"))
            )
            
            return user_info
            
        except httpx.RequestError as e:
            raise IdPConnectionError(f"获取用户信息请求失败: {str(e)}")
    
    def map_user_info(self, user_info: Dict[str, Any]) -> AuthUser:
        """映射用户信息到内部用户模型
        
        Args:
            user_info: IdP返回的用户信息
            
        Returns:
            内部用户模型
        """
        mapping = self.config.user_mapping
        
        # 提取基础字段
        external_id = user_info.get(mapping.get("id", "sub"))
        username = user_info.get(mapping.get("username", "preferred_username")) or external_id
        email = user_info.get(mapping.get("email", "email"))
        full_name = user_info.get(mapping.get("full_name", "name"))
        
        if not external_id:
            raise IdPError("用户信息中缺少用户ID")
        
        if not email:
            raise IdPError("用户信息中缺少邮箱地址")
        
        # 创建用户对象
        auth_user = AuthUser(
            username=username,
            email=email,
            full_name=full_name,
            external_id=external_id,
            idp_type=self.config.type,
            metadata={
                "idp_name": self.config.name,
                "original_user_info": user_info
            }
        )
        
        self.logger.debug(
            "用户信息映射完成",
            idp=self.config.name,
            external_id=external_id,
            username=username,
            email=email
        )
        
        return auth_user
    
    async def verify_id_token(self, id_token: str, nonce: Optional[str] = None) -> Dict[str, Any]:
        """验证ID令牌（OIDC）
        
        Args:
            id_token: ID令牌
            nonce: 随机数
            
        Returns:
            ID令牌载荷
        """
        try:
            # 获取JWKS
            jwks = await self._get_jwks()
            
            # 解码令牌头部获取kid
            unverified_header = jwt.get_unverified_header(id_token)
            kid = unverified_header.get("kid")
            
            if not kid:
                raise TokenError("ID令牌缺少kid字段")
            
            # 查找对应的公钥
            public_key = None
            for key in jwks.get("keys", []):
                if key.get("kid") == kid:
                    public_key = self._jwk_to_public_key(key)
                    break
            
            if not public_key:
                raise TokenError(f"未找到kid为{kid}的公钥")
            
            # 验证令牌
            payload = jwt.decode(
                id_token,
                public_key,
                algorithms=["RS256", "ES256"],
                audience=self.config.client_id,
                issuer=self.config.issuer
            )
            
            # 验证nonce
            if nonce and payload.get("nonce") != nonce:
                raise TokenError("ID令牌nonce验证失败")
            
            self.logger.debug(
                "ID令牌验证成功",
                idp=self.config.name,
                sub=payload.get("sub"),
                exp=payload.get("exp")
            )
            
            return payload
            
        except jwt.InvalidTokenError as e:
            raise TokenError(f"ID令牌验证失败: {str(e)}")
    
    async def _get_jwks(self) -> Dict[str, Any]:
        """获取JWKS（JSON Web Key Set）
        
        Returns:
            JWKS字典
        """
        if not self.config.jwks_url:
            raise IdPConfigurationError("未配置JWKS URL")
        
        # 检查缓存
        current_time = time.time()
        if (self._jwks_cache and 
            current_time - self._jwks_cache_time < self._jwks_cache_ttl):
            return self._jwks_cache
        
        if not self.client:
            raise IdPConnectionError("IdP客户端未初始化")
        
        try:
            response = await self.client.get(self.config.jwks_url)
            
            if response.status_code != 200:
                raise IdPConnectionError(f"获取JWKS失败: {response.status_code}")
            
            jwks = response.json()
            
            # 更新缓存
            self._jwks_cache = jwks
            self._jwks_cache_time = current_time
            
            self.logger.debug(
                "JWKS获取成功",
                idp=self.config.name,
                keys_count=len(jwks.get("keys", []))
            )
            
            return jwks
            
        except httpx.RequestError as e:
            raise IdPConnectionError(f"获取JWKS请求失败: {str(e)}")
    
    def _jwk_to_public_key(self, jwk: Dict[str, Any]) -> Any:
        """将JWK转换为公钥对象
        
        Args:
            jwk: JSON Web Key
            
        Returns:
            公钥对象
        """
        # 这里简化实现，实际应该根据不同的key type处理
        # 对于RSA密钥
        if jwk.get("kty") == "RSA":
            from cryptography.hazmat.primitives.asymmetric.rsa import RSAPublicNumbers
            from cryptography.hazmat.primitives.serialization import Encoding, PublicFormat
            import base64
            
            n = int.from_bytes(
                base64.urlsafe_b64decode(jwk["n"] + "=="), 
                byteorder="big"
            )
            e = int.from_bytes(
                base64.urlsafe_b64decode(jwk["e"] + "=="), 
                byteorder="big"
            )
            
            public_numbers = RSAPublicNumbers(e, n)
            return public_numbers.public_key()
        
        raise TokenError(f"不支持的密钥类型: {jwk.get('kty')}")
    
    async def refresh_access_token(self, refresh_token: str) -> Dict[str, Any]:
        """刷新访问令牌
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            新的令牌响应
        """
        if not self.client:
            raise IdPConnectionError("IdP客户端未初始化")
        
        token_data = {
            "grant_type": "refresh_token",
            "refresh_token": refresh_token,
            "client_id": self.config.client_id,
            "client_secret": self.config.client_secret,
        }
        
        try:
            response = await self.client.post(
                self.config.token_url,
                data=token_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code != 200:
                error_data = response.json() if response.headers.get("content-type", "").startswith("application/json") else {}
                raise OAuth2Error(
                    f"令牌刷新失败: {response.status_code}",
                    oauth_error=error_data.get("error"),
                    oauth_description=error_data.get("error_description")
                )
            
            tokens = response.json()
            
            self.logger.info(
                "令牌刷新成功",
                idp=self.config.name,
                token_type=tokens.get("token_type")
            )
            
            return tokens
            
        except httpx.RequestError as e:
            raise IdPConnectionError(f"令牌刷新请求失败: {str(e)}")


class OAuth2Provider(IdentityProvider):
    """OAuth2身份提供商基类
    
    实现标准OAuth2协议的通用功能
    """
    
    async def _validate_configuration(self) -> None:
        """验证OAuth2配置"""
        required_fields = [
            "client_id", "client_secret", 
            "authorization_url", "token_url", "redirect_uri"
        ]
        
        for field in required_fields:
            if not getattr(self.config, field):
                raise IdPConfigurationError(f"缺少必要配置: {field}")
        
        # 验证URL格式
        for url_field in ["authorization_url", "token_url", "userinfo_url"]:
            url = getattr(self.config, url_field)
            if url and not url.startswith(("http://", "https://")):
                raise IdPConfigurationError(f"无效的URL格式: {url_field}")


class OIDCProvider(OAuth2Provider):
    """OIDC身份提供商基类
    
    在OAuth2基础上增加OIDC特性支持
    """
    
    async def _validate_configuration(self) -> None:
        """验证OIDC配置"""
        await super()._validate_configuration()
        
        # OIDC需要额外的配置
        if not self.config.issuer:
            raise IdPConfigurationError("OIDC需要配置issuer")
        
        if not self.config.jwks_url:
            raise IdPConfigurationError("OIDC需要配置jwks_url")
        
        # 确保scopes包含openid
        if "openid" not in self.config.scopes:
            self.config.scopes.insert(0, "openid")
