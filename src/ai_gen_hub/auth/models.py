"""
认证和授权数据模型

定义了身份认证和授权系统中使用的所有数据模型，
包括用户、会话、令牌、配置等核心实体。
"""

from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Set, Any, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, validator, EmailStr


class TokenType(str, Enum):
    """令牌类型枚举"""
    ACCESS = "access"           # 访问令牌
    REFRESH = "refresh"         # 刷新令牌
    API_KEY = "api_key"        # API密钥
    SESSION = "session"         # 会话令牌


class TokenScope(str, Enum):
    """令牌权限范围枚举"""
    READ = "read"               # 只读权限
    WRITE = "write"             # 读写权限
    ADMIN = "admin"             # 管理员权限
    API_ACCESS = "api_access"   # API访问权限
    CONSOLE_ACCESS = "console_access"  # 控制台访问权限


class UserRole(str, Enum):
    """用户角色枚举"""
    SUPER_ADMIN = "super_admin"     # 超级管理员
    ADMIN = "admin"                 # 管理员
    DEVELOPER = "developer"         # 开发者
    USER = "user"                   # 普通用户
    READONLY = "readonly"           # 只读用户


class UserStatus(str, Enum):
    """用户状态枚举"""
    ACTIVE = "active"           # 活跃
    INACTIVE = "inactive"       # 非活跃
    SUSPENDED = "suspended"     # 暂停
    PENDING = "pending"         # 待激活


class Permission(str, Enum):
    """权限枚举"""
    # 系统权限
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_CONFIG = "system:config"
    SYSTEM_MONITOR = "system:monitor"
    
    # 用户管理权限
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    USER_MANAGE = "user:manage"
    
    # API权限
    API_TEXT_GENERATE = "api:text:generate"
    API_IMAGE_GENERATE = "api:image:generate"
    API_PROVIDER_MANAGE = "api:provider:manage"
    API_TOKEN_MANAGE = "api:token:manage"
    
    # 控制台权限
    CONSOLE_ACCESS = "console:access"
    CONSOLE_ANALYTICS = "console:analytics"
    CONSOLE_SETTINGS = "console:settings"


class IdPType(str, Enum):
    """身份提供商类型枚举"""
    AUTH0 = "auth0"
    KEYCLOAK = "keycloak"
    AZURE_AD = "azure_ad"
    GOOGLE_OAUTH = "google_oauth"
    GITHUB = "github"
    GITLAB = "gitlab"
    OKTA = "okta"
    CUSTOM_OIDC = "custom_oidc"


class AuthUser(BaseModel):
    """认证用户模型"""
    id: UUID = Field(default_factory=uuid4, description="用户唯一标识")
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    
    # 认证信息
    password_hash: Optional[str] = Field(None, description="密码哈希（本地认证）")
    external_id: Optional[str] = Field(None, description="外部IdP用户ID")
    idp_type: Optional[IdPType] = Field(None, description="身份提供商类型")
    
    # 角色和权限
    role: UserRole = Field(UserRole.USER, description="用户角色")
    permissions: Set[Permission] = Field(default_factory=set, description="用户权限")
    status: UserStatus = Field(UserStatus.ACTIVE, description="用户状态")
    
    # 配额和限制
    api_quota: int = Field(1000, description="API调用配额（每月）")
    api_quota_used: int = Field(0, description="已使用的API配额")
    rate_limit: int = Field(100, description="速率限制（每分钟）")
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    last_login_at: Optional[datetime] = Field(None, description="最后登录时间")
    last_activity_at: Optional[datetime] = Field(None, description="最后活动时间")
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict, description="用户元数据")
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
            set: lambda v: list(v)
        }
    
    @validator('permissions', pre=True)
    def validate_permissions(cls, v):
        """验证权限列表"""
        if isinstance(v, list):
            return set(v)
        return v
    
    def has_permission(self, permission: Permission) -> bool:
        """检查用户是否有指定权限"""
        return permission in self.permissions
    
    def has_any_permission(self, permissions: List[Permission]) -> bool:
        """检查用户是否有任一指定权限"""
        return any(perm in self.permissions for perm in permissions)
    
    def has_all_permissions(self, permissions: List[Permission]) -> bool:
        """检查用户是否有所有指定权限"""
        return all(perm in self.permissions for perm in permissions)
    
    def is_active(self) -> bool:
        """检查用户是否活跃"""
        return self.status == UserStatus.ACTIVE
    
    def is_admin(self) -> bool:
        """检查用户是否是管理员"""
        return self.role in [UserRole.ADMIN, UserRole.SUPER_ADMIN]


class AuthSession(BaseModel):
    """认证会话模型"""
    id: UUID = Field(default_factory=uuid4, description="会话唯一标识")
    user_id: UUID = Field(..., description="用户ID")
    session_token: str = Field(..., description="会话令牌")
    
    # 会话信息
    ip_address: Optional[str] = Field(None, description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理")
    device_info: Optional[Dict[str, Any]] = Field(None, description="设备信息")
    
    # 时间管理
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    last_accessed_at: datetime = Field(default_factory=datetime.utcnow, description="最后访问时间")
    expires_at: datetime = Field(..., description="过期时间")
    
    # 状态
    is_active: bool = Field(True, description="是否活跃")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }
    
    def is_expired(self) -> bool:
        """检查会话是否过期"""
        return datetime.utcnow() > self.expires_at
    
    def is_valid(self) -> bool:
        """检查会话是否有效"""
        return self.is_active and not self.is_expired()


class APIToken(BaseModel):
    """API令牌模型"""
    id: UUID = Field(default_factory=uuid4, description="令牌唯一标识")
    user_id: UUID = Field(..., description="用户ID")
    name: str = Field(..., max_length=100, description="令牌名称")
    
    # 令牌信息
    token_hash: str = Field(..., description="令牌哈希")
    token_prefix: str = Field(..., description="令牌前缀（用于显示）")
    token_type: TokenType = Field(TokenType.API_KEY, description="令牌类型")
    
    # 权限和限制
    scopes: Set[TokenScope] = Field(default_factory=set, description="令牌权限范围")
    permissions: Set[Permission] = Field(default_factory=set, description="令牌权限")
    rate_limit: int = Field(100, description="速率限制（每分钟）")
    
    # 状态和时间
    is_active: bool = Field(True, description="是否活跃")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    last_used_at: Optional[datetime] = Field(None, description="最后使用时间")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict, description="令牌元数据")
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
            set: lambda v: list(v)
        }
    
    @validator('scopes', pre=True)
    def validate_scopes(cls, v):
        """验证权限范围列表"""
        if isinstance(v, list):
            return set(v)
        return v
    
    @validator('permissions', pre=True)
    def validate_permissions(cls, v):
        """验证权限列表"""
        if isinstance(v, list):
            return set(v)
        return v
    
    def is_expired(self) -> bool:
        """检查令牌是否过期"""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at
    
    def is_valid(self) -> bool:
        """检查令牌是否有效"""
        return self.is_active and not self.is_expired()
    
    def has_scope(self, scope: TokenScope) -> bool:
        """检查令牌是否有指定权限范围"""
        return scope in self.scopes
    
    def has_permission(self, permission: Permission) -> bool:
        """检查令牌是否有指定权限"""
        return permission in self.permissions


class IdPConfig(BaseModel):
    """身份提供商配置模型"""
    name: str = Field(..., description="IdP名称")
    type: IdPType = Field(..., description="IdP类型")
    enabled: bool = Field(True, description="是否启用")
    
    # OAuth2/OIDC配置
    client_id: str = Field(..., description="客户端ID")
    client_secret: str = Field(..., description="客户端密钥")
    authorization_url: str = Field(..., description="授权URL")
    token_url: str = Field(..., description="令牌URL")
    userinfo_url: Optional[str] = Field(None, description="用户信息URL")
    jwks_url: Optional[str] = Field(None, description="JWKS URL")
    
    # 配置选项
    scopes: List[str] = Field(default_factory=lambda: ["openid", "profile", "email"], description="权限范围")
    redirect_uri: str = Field(..., description="重定向URI")
    
    # 高级配置
    issuer: Optional[str] = Field(None, description="签发者")
    audience: Optional[str] = Field(None, description="受众")
    
    # 用户映射配置
    user_mapping: Dict[str, str] = Field(
        default_factory=lambda: {
            "id": "sub",
            "username": "preferred_username",
            "email": "email",
            "full_name": "name"
        },
        description="用户字段映射"
    )
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict, description="IdP元数据")
    
    class Config:
        use_enum_values = True


class AuthConfig(BaseModel):
    """认证系统配置模型"""
    # JWT配置
    jwt_secret_key: str = Field(..., description="JWT密钥")
    jwt_algorithm: str = Field("HS256", description="JWT算法")
    jwt_access_token_expire_minutes: int = Field(30, description="访问令牌过期时间（分钟）")
    jwt_refresh_token_expire_days: int = Field(7, description="刷新令牌过期时间（天）")
    
    # 会话配置
    session_expire_hours: int = Field(24, description="会话过期时间（小时）")
    session_cookie_name: str = Field("ai_gen_hub_session", description="会话Cookie名称")
    session_cookie_secure: bool = Field(True, description="会话Cookie安全标志")
    session_cookie_httponly: bool = Field(True, description="会话Cookie HttpOnly标志")
    
    # 安全配置
    password_min_length: int = Field(8, description="密码最小长度")
    password_require_uppercase: bool = Field(True, description="密码是否需要大写字母")
    password_require_lowercase: bool = Field(True, description="密码是否需要小写字母")
    password_require_numbers: bool = Field(True, description="密码是否需要数字")
    password_require_symbols: bool = Field(False, description="密码是否需要符号")
    
    # 频率限制配置
    rate_limit_enabled: bool = Field(True, description="是否启用频率限制")
    rate_limit_requests_per_minute: int = Field(100, description="每分钟请求限制")
    rate_limit_burst_size: int = Field(10, description="突发请求大小")
    
    # 审计日志配置
    audit_log_enabled: bool = Field(True, description="是否启用审计日志")
    audit_log_retention_days: int = Field(90, description="审计日志保留天数")
    
    # IdP配置
    idp_configs: Dict[str, IdPConfig] = Field(default_factory=dict, description="身份提供商配置")
    default_idp: Optional[str] = Field(None, description="默认身份提供商")
    
    # 其他配置
    allow_registration: bool = Field(False, description="是否允许用户注册")
    require_email_verification: bool = Field(True, description="是否需要邮箱验证")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
