"""
安全特性模块

提供完整的安全保护功能，包括：
- JWT签名验证
- 请求频率限制
- 审计日志记录
- 安全头部设置
- 异常活动检测
"""

import asyncio
import hashlib
import time
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Any, Tuple
from uuid import UUID

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from ai_gen_hub.auth.exceptions import (
    RateLimitError,
    SecurityPolicyViolationError,
    SecurityError,
)
from ai_gen_hub.auth.models import AuthUser, Permission
from ai_gen_hub.core.logging import LoggerMixin


class RateLimiter(LoggerMixin):
    """频率限制器
    
    实现基于令牌桶算法的频率限制，支持：
    - 全局频率限制
    - 用户级频率限制
    - IP级频率限制
    - 动态限制调整
    """
    
    def __init__(
        self,
        default_requests_per_minute: int = 100,
        default_burst_size: int = 10,
        cleanup_interval: int = 300  # 5分钟清理一次
    ):
        super().__init__()
        self.default_requests_per_minute = default_requests_per_minute
        self.default_burst_size = default_burst_size
        self.cleanup_interval = cleanup_interval
        
        # 存储不同类型的限制器
        self._user_limiters: Dict[UUID, Dict[str, Any]] = {}
        self._ip_limiters: Dict[str, Dict[str, Any]] = {}
        self._global_limiter: Dict[str, Any] = self._create_limiter_state(
            default_requests_per_minute, default_burst_size
        )
        
        # 最后清理时间
        self._last_cleanup = time.time()
    
    def _create_limiter_state(
        self, 
        requests_per_minute: int, 
        burst_size: int
    ) -> Dict[str, Any]:
        """创建限制器状态"""
        return {
            "requests_per_minute": requests_per_minute,
            "burst_size": burst_size,
            "tokens": burst_size,
            "last_refill": time.time(),
            "request_count": 0,
            "last_request": time.time()
        }
    
    def _refill_tokens(self, limiter_state: Dict[str, Any]) -> None:
        """补充令牌"""
        now = time.time()
        time_passed = now - limiter_state["last_refill"]
        
        # 计算应该补充的令牌数
        tokens_to_add = time_passed * (limiter_state["requests_per_minute"] / 60.0)
        
        # 更新令牌数，不超过桶容量
        limiter_state["tokens"] = min(
            limiter_state["burst_size"],
            limiter_state["tokens"] + tokens_to_add
        )
        limiter_state["last_refill"] = now
    
    def check_rate_limit(
        self,
        identifier: str,
        identifier_type: str = "global",
        custom_limit: Optional[int] = None,
        custom_burst: Optional[int] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """检查频率限制
        
        Args:
            identifier: 标识符（用户ID、IP地址等）
            identifier_type: 标识符类型（user、ip、global）
            custom_limit: 自定义限制（每分钟请求数）
            custom_burst: 自定义突发大小
            
        Returns:
            (是否允许, 限制信息) 元组
        """
        # 选择对应的限制器存储
        if identifier_type == "user":
            limiters = self._user_limiters
        elif identifier_type == "ip":
            limiters = self._ip_limiters
        else:
            # 全局限制器
            limiter_state = self._global_limiter
            self._refill_tokens(limiter_state)
            
            if limiter_state["tokens"] >= 1:
                limiter_state["tokens"] -= 1
                limiter_state["request_count"] += 1
                limiter_state["last_request"] = time.time()
                return True, self._get_limit_info(limiter_state)
            else:
                return False, self._get_limit_info(limiter_state)
        
        # 获取或创建限制器状态
        if identifier not in limiters:
            limiters[identifier] = self._create_limiter_state(
                custom_limit or self.default_requests_per_minute,
                custom_burst or self.default_burst_size
            )
        
        limiter_state = limiters[identifier]
        
        # 如果提供了自定义限制，更新限制器
        if custom_limit:
            limiter_state["requests_per_minute"] = custom_limit
        if custom_burst:
            limiter_state["burst_size"] = custom_burst
        
        # 补充令牌
        self._refill_tokens(limiter_state)
        
        # 检查是否有可用令牌
        if limiter_state["tokens"] >= 1:
            limiter_state["tokens"] -= 1
            limiter_state["request_count"] += 1
            limiter_state["last_request"] = time.time()
            
            self.logger.debug(
                "频率限制检查通过",
                identifier=identifier,
                identifier_type=identifier_type,
                remaining_tokens=limiter_state["tokens"]
            )
            
            return True, self._get_limit_info(limiter_state)
        else:
            self.logger.warning(
                "频率限制超出",
                identifier=identifier,
                identifier_type=identifier_type,
                requests_per_minute=limiter_state["requests_per_minute"]
            )
            
            return False, self._get_limit_info(limiter_state)
    
    def _get_limit_info(self, limiter_state: Dict[str, Any]) -> Dict[str, Any]:
        """获取限制信息"""
        return {
            "requests_per_minute": limiter_state["requests_per_minute"],
            "burst_size": limiter_state["burst_size"],
            "remaining_tokens": int(limiter_state["tokens"]),
            "request_count": limiter_state["request_count"],
            "reset_time": limiter_state["last_refill"] + 60  # 下次重置时间
        }
    
    def get_user_limit_info(self, user_id: UUID) -> Optional[Dict[str, Any]]:
        """获取用户限制信息"""
        user_key = str(user_id)
        if user_key in self._user_limiters:
            return self._get_limit_info(self._user_limiters[user_key])
        return None
    
    def set_user_limit(
        self,
        user_id: UUID,
        requests_per_minute: int,
        burst_size: Optional[int] = None
    ) -> None:
        """设置用户自定义限制"""
        user_key = str(user_id)
        if user_key not in self._user_limiters:
            self._user_limiters[user_key] = self._create_limiter_state(
                requests_per_minute,
                burst_size or self.default_burst_size
            )
        else:
            limiter_state = self._user_limiters[user_key]
            limiter_state["requests_per_minute"] = requests_per_minute
            if burst_size:
                limiter_state["burst_size"] = burst_size
        
        self.logger.info(
            "用户频率限制设置成功",
            user_id=str(user_id),
            requests_per_minute=requests_per_minute,
            burst_size=burst_size
        )
    
    def cleanup_old_limiters(self) -> None:
        """清理旧的限制器状态"""
        now = time.time()
        
        # 只在间隔时间后执行清理
        if now - self._last_cleanup < self.cleanup_interval:
            return
        
        cutoff_time = now - 3600  # 1小时前
        
        # 清理用户限制器
        expired_users = [
            user_id for user_id, state in self._user_limiters.items()
            if state["last_request"] < cutoff_time
        ]
        for user_id in expired_users:
            del self._user_limiters[user_id]
        
        # 清理IP限制器
        expired_ips = [
            ip for ip, state in self._ip_limiters.items()
            if state["last_request"] < cutoff_time
        ]
        for ip in expired_ips:
            del self._ip_limiters[ip]
        
        self._last_cleanup = now
        
        if expired_users or expired_ips:
            self.logger.info(
                "清理过期限制器",
                expired_users=len(expired_users),
                expired_ips=len(expired_ips)
            )


class AuditLogger(LoggerMixin):
    """审计日志记录器
    
    记录系统中的重要安全事件，包括：
    - 用户认证事件
    - 权限检查事件
    - 敏感操作事件
    - 异常活动事件
    """
    
    def __init__(self, retention_days: int = 90):
        super().__init__()
        self.retention_days = retention_days
        self._audit_logs: List[Dict[str, Any]] = []
        self._max_logs_in_memory = 10000
    
    def log_authentication_event(
        self,
        user_id: Optional[UUID],
        event_type: str,
        success: bool,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """记录认证事件
        
        Args:
            user_id: 用户ID
            event_type: 事件类型（login、logout、token_refresh等）
            success: 是否成功
            ip_address: IP地址
            user_agent: 用户代理
            details: 额外详情
        """
        self._log_audit_event(
            category="authentication",
            event_type=event_type,
            user_id=user_id,
            success=success,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details
        )
    
    def log_authorization_event(
        self,
        user_id: UUID,
        permission: str,
        resource: Optional[str],
        success: bool,
        ip_address: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """记录授权事件
        
        Args:
            user_id: 用户ID
            permission: 权限名称
            resource: 资源标识
            success: 是否成功
            ip_address: IP地址
            details: 额外详情
        """
        self._log_audit_event(
            category="authorization",
            event_type="permission_check",
            user_id=user_id,
            success=success,
            ip_address=ip_address,
            details={
                "permission": permission,
                "resource": resource,
                **(details or {})
            }
        )
    
    def log_security_event(
        self,
        event_type: str,
        severity: str,
        user_id: Optional[UUID] = None,
        ip_address: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """记录安全事件
        
        Args:
            event_type: 事件类型
            severity: 严重程度（low、medium、high、critical）
            user_id: 用户ID
            ip_address: IP地址
            details: 额外详情
        """
        self._log_audit_event(
            category="security",
            event_type=event_type,
            user_id=user_id,
            ip_address=ip_address,
            details={
                "severity": severity,
                **(details or {})
            }
        )
    
    def log_api_event(
        self,
        user_id: Optional[UUID],
        endpoint: str,
        method: str,
        status_code: int,
        response_time: float,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """记录API访问事件
        
        Args:
            user_id: 用户ID
            endpoint: API端点
            method: HTTP方法
            status_code: 响应状态码
            response_time: 响应时间
            ip_address: IP地址
            user_agent: 用户代理
            details: 额外详情
        """
        self._log_audit_event(
            category="api_access",
            event_type="api_call",
            user_id=user_id,
            success=200 <= status_code < 400,
            ip_address=ip_address,
            user_agent=user_agent,
            details={
                "endpoint": endpoint,
                "method": method,
                "status_code": status_code,
                "response_time": response_time,
                **(details or {})
            }
        )
    
    def _log_audit_event(
        self,
        category: str,
        event_type: str,
        user_id: Optional[UUID] = None,
        success: Optional[bool] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """记录审计事件"""
        audit_log = {
            "timestamp": datetime.utcnow().isoformat(),
            "category": category,
            "event_type": event_type,
            "user_id": str(user_id) if user_id else None,
            "success": success,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "details": details or {}
        }
        
        # 添加到内存日志
        self._audit_logs.append(audit_log)
        
        # 限制内存中的日志数量
        if len(self._audit_logs) > self._max_logs_in_memory:
            self._audit_logs = self._audit_logs[-self._max_logs_in_memory:]
        
        # 记录到系统日志
        self.logger.info(
            "审计事件",
            category=category,
            event_type=event_type,
            user_id=str(user_id) if user_id else None,
            success=success,
            ip_address=ip_address,
            **audit_log["details"]
        )
    
    def get_audit_logs(
        self,
        category: Optional[str] = None,
        user_id: Optional[UUID] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """获取审计日志
        
        Args:
            category: 事件类别
            user_id: 用户ID
            start_time: 开始时间
            end_time: 结束时间
            limit: 返回数量限制
            
        Returns:
            审计日志列表
        """
        filtered_logs = self._audit_logs
        
        # 按条件过滤
        if category:
            filtered_logs = [log for log in filtered_logs if log["category"] == category]
        
        if user_id:
            user_id_str = str(user_id)
            filtered_logs = [log for log in filtered_logs if log["user_id"] == user_id_str]
        
        if start_time:
            start_time_str = start_time.isoformat()
            filtered_logs = [log for log in filtered_logs if log["timestamp"] >= start_time_str]
        
        if end_time:
            end_time_str = end_time.isoformat()
            filtered_logs = [log for log in filtered_logs if log["timestamp"] <= end_time_str]
        
        # 按时间倒序排序并限制数量
        filtered_logs.sort(key=lambda x: x["timestamp"], reverse=True)
        return filtered_logs[:limit]


class SecurityHeaders:
    """安全头部管理器
    
    设置HTTP安全头部，包括：
    - Content Security Policy (CSP)
    - X-Frame-Options
    - X-Content-Type-Options
    - X-XSS-Protection
    - Strict-Transport-Security
    """
    
    @staticmethod
    def get_security_headers() -> Dict[str, str]:
        """获取安全头部"""
        return {
            # 内容安全策略
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self' https:; "
                "connect-src 'self' https:; "
                "frame-ancestors 'none';"
            ),
            
            # 防止点击劫持
            "X-Frame-Options": "DENY",
            
            # 防止MIME类型嗅探
            "X-Content-Type-Options": "nosniff",
            
            # XSS保护
            "X-XSS-Protection": "1; mode=block",
            
            # 强制HTTPS（生产环境）
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            
            # 引用者策略
            "Referrer-Policy": "strict-origin-when-cross-origin",
            
            # 权限策略
            "Permissions-Policy": (
                "geolocation=(), "
                "microphone=(), "
                "camera=(), "
                "payment=(), "
                "usb=(), "
                "magnetometer=(), "
                "gyroscope=(), "
                "speaker=()"
            ),
        }
    
    @staticmethod
    def apply_security_headers(response: Response) -> None:
        """应用安全头部到响应"""
        headers = SecurityHeaders.get_security_headers()
        for name, value in headers.items():
            response.headers[name] = value


class SecurityManager(LoggerMixin):
    """安全管理器
    
    统一管理所有安全特性，包括：
    - 频率限制
    - 审计日志
    - 异常检测
    - 安全策略
    """
    
    def __init__(
        self,
        rate_limiter: Optional[RateLimiter] = None,
        audit_logger: Optional[AuditLogger] = None
    ):
        super().__init__()
        self.rate_limiter = rate_limiter or RateLimiter()
        self.audit_logger = audit_logger or AuditLogger()
        
        # 异常活动检测
        self._failed_attempts: Dict[str, List[float]] = defaultdict(list)
        self._suspicious_ips: Set[str] = set()
        
        # 安全策略配置
        self.max_failed_attempts = 5
        self.failed_attempts_window = 300  # 5分钟
        self.lockout_duration = 900  # 15分钟
    
    def check_request_security(
        self,
        request: Request,
        user: Optional[AuthUser] = None
    ) -> Tuple[bool, Optional[str]]:
        """检查请求安全性
        
        Args:
            request: HTTP请求
            user: 用户对象（如果已认证）
            
        Returns:
            (是否允许, 拒绝原因) 元组
        """
        client_ip = self._get_client_ip(request)
        
        # 检查IP是否被标记为可疑
        if client_ip in self._suspicious_ips:
            self.audit_logger.log_security_event(
                "blocked_suspicious_ip",
                "high",
                user_id=user.id if user else None,
                ip_address=client_ip
            )
            return False, "IP地址被标记为可疑"
        
        # 检查频率限制
        if user:
            # 用户级限制
            allowed, limit_info = self.rate_limiter.check_rate_limit(
                str(user.id), "user", user.rate_limit
            )
            if not allowed:
                self.audit_logger.log_security_event(
                    "rate_limit_exceeded",
                    "medium",
                    user_id=user.id,
                    ip_address=client_ip,
                    details=limit_info
                )
                return False, "用户请求频率超出限制"
        
        # IP级限制
        allowed, limit_info = self.rate_limiter.check_rate_limit(
            client_ip, "ip"
        )
        if not allowed:
            self.audit_logger.log_security_event(
                "ip_rate_limit_exceeded",
                "medium",
                user_id=user.id if user else None,
                ip_address=client_ip,
                details=limit_info
            )
            return False, "IP请求频率超出限制"
        
        return True, None
    
    def record_failed_authentication(
        self,
        identifier: str,
        ip_address: str,
        user_agent: Optional[str] = None
    ) -> None:
        """记录认证失败事件"""
        current_time = time.time()
        
        # 记录失败尝试
        self._failed_attempts[identifier].append(current_time)
        
        # 清理过期的失败记录
        cutoff_time = current_time - self.failed_attempts_window
        self._failed_attempts[identifier] = [
            attempt_time for attempt_time in self._failed_attempts[identifier]
            if attempt_time > cutoff_time
        ]
        
        # 检查是否超过失败阈值
        if len(self._failed_attempts[identifier]) >= self.max_failed_attempts:
            self._suspicious_ips.add(ip_address)
            
            self.audit_logger.log_security_event(
                "multiple_failed_attempts",
                "high",
                ip_address=ip_address,
                details={
                    "identifier": identifier,
                    "attempt_count": len(self._failed_attempts[identifier]),
                    "user_agent": user_agent
                }
            )
            
            self.logger.warning(
                "检测到多次认证失败",
                identifier=identifier,
                ip_address=ip_address,
                attempt_count=len(self._failed_attempts[identifier])
            )
    
    def is_account_locked(self, identifier: str) -> bool:
        """检查账户是否被锁定"""
        if identifier not in self._failed_attempts:
            return False
        
        current_time = time.time()
        cutoff_time = current_time - self.failed_attempts_window
        
        # 清理过期的失败记录
        self._failed_attempts[identifier] = [
            attempt_time for attempt_time in self._failed_attempts[identifier]
            if attempt_time > cutoff_time
        ]
        
        return len(self._failed_attempts[identifier]) >= self.max_failed_attempts
    
    def clear_failed_attempts(self, identifier: str) -> None:
        """清除失败尝试记录（成功认证后调用）"""
        if identifier in self._failed_attempts:
            del self._failed_attempts[identifier]
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头部
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 使用客户端IP
        return request.client.host if request.client else "unknown"


class JWTSecurityValidator(LoggerMixin):
    """JWT安全验证器

    提供增强的JWT安全验证功能：
    - 签名验证
    - 时间戳验证
    - 黑名单检查
    - 令牌绑定验证
    """

    def __init__(self, secret_key: str):
        super().__init__()
        self.secret_key = secret_key
        self.blacklisted_tokens: Set[str] = set()
        self.token_fingerprints: Dict[str, str] = {}

    def validate_token_security(
        self,
        token: str,
        user_agent: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> bool:
        """验证令牌安全性

        Args:
            token: JWT令牌
            user_agent: 用户代理字符串
            ip_address: IP地址

        Returns:
            bool: 是否通过安全验证
        """
        try:
            # 检查黑名单
            if self._is_token_blacklisted(token):
                self.logger.warning("令牌在黑名单中", token_prefix=token[:10])
                return False

            # 验证令牌绑定
            if not self._validate_token_binding(token, user_agent, ip_address):
                self.logger.warning("令牌绑定验证失败", token_prefix=token[:10])
                return False

            # 验证令牌完整性
            if not self._validate_token_integrity(token):
                self.logger.warning("令牌完整性验证失败", token_prefix=token[:10])
                return False

            return True

        except Exception as e:
            self.logger.error(f"令牌安全验证失败: {e}")
            return False

    def blacklist_token(self, token: str, reason: str = "security_violation"):
        """将令牌加入黑名单

        Args:
            token: 要加入黑名单的令牌
            reason: 加入黑名单的原因
        """
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        self.blacklisted_tokens.add(token_hash)

        self.logger.info(
            "令牌已加入黑名单",
            token_hash=token_hash[:16],
            reason=reason
        )

    def _is_token_blacklisted(self, token: str) -> bool:
        """检查令牌是否在黑名单中"""
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        return token_hash in self.blacklisted_tokens

    def _validate_token_binding(
        self,
        token: str,
        user_agent: Optional[str],
        ip_address: Optional[str]
    ) -> bool:
        """验证令牌绑定"""
        try:
            import jwt

            # 解码令牌获取绑定信息
            payload = jwt.decode(token, self.secret_key, algorithms=["HS256"])

            # 检查IP绑定
            bound_ip = payload.get("bound_ip")
            if bound_ip and bound_ip != ip_address:
                return False

            # 检查用户代理绑定
            bound_ua_hash = payload.get("bound_ua_hash")
            if bound_ua_hash and user_agent:
                current_ua_hash = hashlib.sha256(user_agent.encode()).hexdigest()[:16]
                if bound_ua_hash != current_ua_hash:
                    return False

            return True

        except Exception:
            return False

    def _validate_token_integrity(self, token: str) -> bool:
        """验证令牌完整性"""
        try:
            import jwt

            # 验证签名
            jwt.decode(token, self.secret_key, algorithms=["HS256"])
            return True

        except jwt.InvalidTokenError:
            return False
        except Exception:
            return False


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头部中间件

    自动添加安全相关的HTTP头部：
    - Content Security Policy (CSP)
    - HTTP Strict Transport Security (HSTS)
    - X-Frame-Options
    - X-Content-Type-Options
    - X-XSS-Protection
    - Referrer-Policy
    """

    def __init__(
        self,
        app,
        csp_policy: Optional[str] = None,
        hsts_max_age: int = 31536000,  # 1年
        enable_hsts_subdomains: bool = True,
        enable_hsts_preload: bool = False
    ):
        super().__init__(app)
        self.csp_policy = csp_policy or self._get_default_csp()
        self.hsts_max_age = hsts_max_age
        self.enable_hsts_subdomains = enable_hsts_subdomains
        self.enable_hsts_preload = enable_hsts_preload

    async def dispatch(self, request: Request, call_next):
        """处理请求并添加安全头部"""
        response = await call_next(request)

        # 添加安全头部
        self._add_security_headers(response)

        return response

    def _add_security_headers(self, response: Response):
        """添加安全头部"""
        # Content Security Policy
        if self.csp_policy:
            response.headers["Content-Security-Policy"] = self.csp_policy

        # HTTP Strict Transport Security
        hsts_value = f"max-age={self.hsts_max_age}"
        if self.enable_hsts_subdomains:
            hsts_value += "; includeSubDomains"
        if self.enable_hsts_preload:
            hsts_value += "; preload"
        response.headers["Strict-Transport-Security"] = hsts_value

        # X-Frame-Options
        response.headers["X-Frame-Options"] = "DENY"

        # X-Content-Type-Options
        response.headers["X-Content-Type-Options"] = "nosniff"

        # X-XSS-Protection
        response.headers["X-XSS-Protection"] = "1; mode=block"

        # Referrer-Policy
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"

        # X-Permitted-Cross-Domain-Policies
        response.headers["X-Permitted-Cross-Domain-Policies"] = "none"

        # Cache-Control for sensitive endpoints
        if self._is_sensitive_endpoint(response):
            response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate, private"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"

    def _get_default_csp(self) -> str:
        """获取默认的CSP策略"""
        return (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' data:; "
            "connect-src 'self'; "
            "frame-ancestors 'none'; "
            "base-uri 'self'; "
            "form-action 'self'"
        )

    def _is_sensitive_endpoint(self, response: Response) -> bool:
        """判断是否为敏感端点"""
        # 可以根据响应内容或路径判断
        # 这里简单判断是否包含认证相关内容
        content_type = response.headers.get("content-type", "")
        return "application/json" in content_type


class SecurityPolicyEnforcer(LoggerMixin):
    """安全策略执行器

    执行各种安全策略：
    - 密码策略
    - 会话策略
    - 访问策略
    - 数据保护策略
    """

    def __init__(self):
        super().__init__()
        self.password_policy = self._get_default_password_policy()
        self.session_policy = self._get_default_session_policy()

    def validate_password_policy(self, password: str) -> Tuple[bool, List[str]]:
        """验证密码策略

        Args:
            password: 要验证的密码

        Returns:
            Tuple[bool, List[str]]: (是否通过验证, 错误信息列表)
        """
        errors = []

        # 长度检查
        if len(password) < self.password_policy["min_length"]:
            errors.append(f"密码长度至少需要{self.password_policy['min_length']}个字符")

        if len(password) > self.password_policy.get("max_length", 128):
            errors.append(f"密码长度不能超过{self.password_policy.get('max_length', 128)}个字符")

        # 复杂度检查
        if self.password_policy.get("require_uppercase", False):
            if not any(c.isupper() for c in password):
                errors.append("密码必须包含至少一个大写字母")

        if self.password_policy.get("require_lowercase", False):
            if not any(c.islower() for c in password):
                errors.append("密码必须包含至少一个小写字母")

        if self.password_policy.get("require_numbers", False):
            if not any(c.isdigit() for c in password):
                errors.append("密码必须包含至少一个数字")

        if self.password_policy.get("require_symbols", False):
            symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?"
            if not any(c in symbols for c in password):
                errors.append("密码必须包含至少一个特殊字符")

        # 禁用密码检查
        forbidden_passwords = self.password_policy.get("forbidden_passwords", [])
        if password.lower() in [p.lower() for p in forbidden_passwords]:
            errors.append("不能使用常见的弱密码")

        return len(errors) == 0, errors

    def validate_session_policy(
        self,
        user: AuthUser,
        ip_address: str,
        user_agent: str
    ) -> Tuple[bool, Optional[str]]:
        """验证会话策略

        Args:
            user: 用户对象
            ip_address: IP地址
            user_agent: 用户代理

        Returns:
            Tuple[bool, Optional[str]]: (是否通过验证, 错误信息)
        """
        # 检查并发会话限制
        max_sessions = self.session_policy.get("max_concurrent_sessions", 5)
        # 这里需要实际的会话计数逻辑

        # 检查地理位置异常
        if self._is_suspicious_location(user, ip_address):
            return False, "检测到异常登录位置"

        # 检查设备指纹
        if self._is_suspicious_device(user, user_agent):
            return False, "检测到异常设备"

        return True, None

    def _get_default_password_policy(self) -> Dict[str, Any]:
        """获取默认密码策略"""
        return {
            "min_length": 8,
            "max_length": 128,
            "require_uppercase": True,
            "require_lowercase": True,
            "require_numbers": True,
            "require_symbols": False,
            "forbidden_passwords": [
                "password", "123456", "123456789", "qwerty",
                "abc123", "password123", "admin", "root",
                "user", "test", "guest", "demo"
            ]
        }

    def _get_default_session_policy(self) -> Dict[str, Any]:
        """获取默认会话策略"""
        return {
            "max_concurrent_sessions": 5,
            "idle_timeout_minutes": 30,
            "absolute_timeout_hours": 8,
            "require_device_verification": False,
            "enable_location_tracking": True
        }

    def _is_suspicious_location(self, user: AuthUser, ip_address: str) -> bool:
        """检查是否为可疑位置"""
        # 这里可以集成地理位置服务
        # 简单实现：检查是否为内网IP
        private_ranges = [
            "10.", "172.16.", "172.17.", "172.18.", "172.19.",
            "172.20.", "172.21.", "172.22.", "172.23.", "172.24.",
            "172.25.", "172.26.", "172.27.", "172.28.", "172.29.",
            "172.30.", "172.31.", "192.168.", "127."
        ]

        # 内网IP认为是安全的
        if any(ip_address.startswith(prefix) for prefix in private_ranges):
            return False

        # 这里可以添加更复杂的地理位置检查逻辑
        return False

    def _is_suspicious_device(self, user: AuthUser, user_agent: str) -> bool:
        """检查是否为可疑设备"""
        # 这里可以实现设备指纹识别
        # 简单实现：检查用户代理是否异常
        suspicious_patterns = [
            "bot", "crawler", "spider", "scraper",
            "curl", "wget", "python-requests"
        ]

        user_agent_lower = user_agent.lower()
        return any(pattern in user_agent_lower for pattern in suspicious_patterns)


class ThreatDetector(LoggerMixin):
    """威胁检测器

    检测各种安全威胁：
    - 暴力破解攻击
    - 异常访问模式
    - 恶意请求
    - 数据泄露风险
    """

    def __init__(self):
        super().__init__()
        self.failed_attempts: Dict[str, List[datetime]] = defaultdict(list)
        self.suspicious_ips: Set[str] = set()
        self.attack_patterns: List[str] = [
            "union select", "drop table", "insert into",
            "<script", "javascript:", "eval(",
            "../", "..\\", "etc/passwd"
        ]

    def detect_brute_force(
        self,
        identifier: str,
        ip_address: str,
        success: bool
    ) -> bool:
        """检测暴力破解攻击

        Args:
            identifier: 用户标识符（用户名或邮箱）
            ip_address: IP地址
            success: 是否成功

        Returns:
            bool: 是否检测到暴力破解攻击
        """
        now = datetime.utcnow()

        if not success:
            # 记录失败尝试
            self.failed_attempts[identifier].append(now)
            self.failed_attempts[ip_address].append(now)

            # 清理过期记录（保留最近1小时的记录）
            cutoff = now - timedelta(hours=1)
            self.failed_attempts[identifier] = [
                attempt for attempt in self.failed_attempts[identifier]
                if attempt > cutoff
            ]
            self.failed_attempts[ip_address] = [
                attempt for attempt in self.failed_attempts[ip_address]
                if attempt > cutoff
            ]

            # 检查是否超过阈值
            if len(self.failed_attempts[identifier]) >= 5:
                self.logger.warning(
                    "检测到针对用户的暴力破解攻击",
                    identifier=identifier,
                    attempts=len(self.failed_attempts[identifier])
                )
                return True

            if len(self.failed_attempts[ip_address]) >= 10:
                self.logger.warning(
                    "检测到来自IP的暴力破解攻击",
                    ip_address=ip_address,
                    attempts=len(self.failed_attempts[ip_address])
                )
                self.suspicious_ips.add(ip_address)
                return True

        return False

    def detect_malicious_request(self, request_data: str) -> bool:
        """检测恶意请求

        Args:
            request_data: 请求数据

        Returns:
            bool: 是否为恶意请求
        """
        request_lower = request_data.lower()

        for pattern in self.attack_patterns:
            if pattern in request_lower:
                self.logger.warning(
                    "检测到恶意请求模式",
                    pattern=pattern,
                    request_preview=request_data[:100]
                )
                return True

        return False

    def is_suspicious_ip(self, ip_address: str) -> bool:
        """检查IP是否可疑

        Args:
            ip_address: IP地址

        Returns:
            bool: 是否为可疑IP
        """
        return ip_address in self.suspicious_ips
