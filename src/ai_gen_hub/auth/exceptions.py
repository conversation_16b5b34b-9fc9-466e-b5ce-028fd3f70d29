"""
认证和授权相关异常类

定义了身份认证和授权系统中使用的所有异常类型，
提供清晰的错误分类和详细的错误信息。
"""

from typing import Optional, Dict, Any


class AuthError(Exception):
    """认证授权基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}


class AuthenticationError(AuthError):
    """身份认证失败异常
    
    当用户身份验证失败时抛出，包括：
    - 无效的凭证
    - 过期的令牌
    - 未找到用户
    """
    pass


class AuthorizationError(AuthError):
    """授权失败异常
    
    当用户权限不足时抛出，包括：
    - 缺少必要权限
    - 角色权限不足
    - 资源访问被拒绝
    """
    pass


class TokenError(AuthError):
    """令牌相关异常
    
    Token处理过程中的错误，包括：
    - 令牌格式错误
    - 令牌签名无效
    - 令牌已过期
    - 令牌已被撤销
    """
    pass


class TokenExpiredError(TokenError):
    """令牌过期异常"""
    
    def __init__(self, message: str = "令牌已过期", **kwargs):
        super().__init__(message, **kwargs)


class TokenInvalidError(TokenError):
    """令牌无效异常"""
    
    def __init__(self, message: str = "令牌无效", **kwargs):
        super().__init__(message, **kwargs)


class TokenRevokedError(TokenError):
    """令牌已撤销异常"""
    
    def __init__(self, message: str = "令牌已被撤销", **kwargs):
        super().__init__(message, **kwargs)


class IdPError(AuthError):
    """身份提供商相关异常
    
    与外部IdP交互时的错误，包括：
    - IdP连接失败
    - OAuth2/OIDC流程错误
    - IdP配置错误
    """
    pass


class IdPConnectionError(IdPError):
    """IdP连接错误"""
    
    def __init__(self, message: str = "身份提供商连接失败", **kwargs):
        super().__init__(message, **kwargs)


class IdPConfigurationError(IdPError):
    """IdP配置错误"""
    
    def __init__(self, message: str = "身份提供商配置错误", **kwargs):
        super().__init__(message, **kwargs)


class OAuth2Error(IdPError):
    """OAuth2流程错误"""
    
    def __init__(
        self, 
        message: str, 
        oauth_error: Optional[str] = None,
        oauth_description: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.oauth_error = oauth_error
        self.oauth_description = oauth_description


class SecurityError(AuthError):
    """安全相关异常
    
    安全策略违反或安全检查失败，包括：
    - 频率限制超出
    - 安全策略违反
    - 可疑活动检测
    """
    pass


class RateLimitError(SecurityError):
    """频率限制异常"""
    
    def __init__(
        self, 
        message: str = "请求频率超出限制", 
        retry_after: Optional[int] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.retry_after = retry_after


class SecurityPolicyViolationError(SecurityError):
    """安全策略违反异常"""
    
    def __init__(self, message: str = "违反安全策略", **kwargs):
        super().__init__(message, **kwargs)


class RBACError(AuthError):
    """RBAC权限控制异常
    
    基于角色的访问控制相关错误，包括：
    - 角色不存在
    - 权限不存在
    - 权限分配错误
    """
    pass


class RoleNotFoundError(RBACError):
    """角色不存在异常"""
    
    def __init__(self, role_name: str, **kwargs):
        message = f"角色不存在: {role_name}"
        super().__init__(message, **kwargs)
        self.role_name = role_name


class PermissionNotFoundError(RBACError):
    """权限不存在异常"""
    
    def __init__(self, permission_name: str, **kwargs):
        message = f"权限不存在: {permission_name}"
        super().__init__(message, **kwargs)
        self.permission_name = permission_name


class InsufficientPermissionError(RBACError):
    """权限不足异常"""
    
    def __init__(
        self, 
        required_permission: str,
        user_permissions: Optional[list] = None,
        **kwargs
    ):
        message = f"权限不足，需要权限: {required_permission}"
        super().__init__(message, **kwargs)
        self.required_permission = required_permission
        self.user_permissions = user_permissions or []


class SessionError(AuthError):
    """会话相关异常
    
    用户会话管理相关错误，包括：
    - 会话不存在
    - 会话已过期
    - 会话无效
    """
    pass


class SessionNotFoundError(SessionError):
    """会话不存在异常"""
    
    def __init__(self, session_id: str, **kwargs):
        message = f"会话不存在: {session_id}"
        super().__init__(message, **kwargs)
        self.session_id = session_id


class SessionExpiredError(SessionError):
    """会话过期异常"""
    
    def __init__(self, message: str = "会话已过期", **kwargs):
        super().__init__(message, **kwargs)


class ConfigurationError(AuthError):
    """配置错误异常
    
    认证系统配置相关错误，包括：
    - 配置缺失
    - 配置格式错误
    - 配置验证失败
    """
    pass


class MissingConfigurationError(ConfigurationError):
    """配置缺失异常"""
    
    def __init__(self, config_key: str, **kwargs):
        message = f"缺少必要配置: {config_key}"
        super().__init__(message, **kwargs)
        self.config_key = config_key


class InvalidConfigurationError(ConfigurationError):
    """配置无效异常"""
    
    def __init__(self, config_key: str, reason: str, **kwargs):
        message = f"配置无效 {config_key}: {reason}"
        super().__init__(message, **kwargs)
        self.config_key = config_key
        self.reason = reason


# 异常映射字典，用于HTTP状态码映射
EXCEPTION_STATUS_MAP = {
    AuthenticationError: 401,
    TokenExpiredError: 401,
    TokenInvalidError: 401,
    TokenRevokedError: 401,
    AuthorizationError: 403,
    InsufficientPermissionError: 403,
    RateLimitError: 429,
    SecurityPolicyViolationError: 403,
    IdPConnectionError: 502,
    IdPConfigurationError: 500,
    OAuth2Error: 400,
    ConfigurationError: 500,
    MissingConfigurationError: 500,
    InvalidConfigurationError: 500,
}


def get_http_status_code(exception: AuthError) -> int:
    """获取异常对应的HTTP状态码
    
    Args:
        exception: 认证授权异常
        
    Returns:
        HTTP状态码
    """
    return EXCEPTION_STATUS_MAP.get(type(exception), 500)
