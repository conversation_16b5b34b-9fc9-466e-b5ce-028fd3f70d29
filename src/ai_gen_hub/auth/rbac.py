"""
基于角色的访问控制 (RBAC) 系统

提供完整的RBAC功能，包括：
- 角色定义和管理
- 权限分配和继承
- 资源级访问控制
- 动态权限检查
- 权限策略管理
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Set, Optional, Any, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field

from ai_gen_hub.auth.exceptions import (
    RBACError,
    RoleNotFoundError,
    PermissionNotFoundError,
    InsufficientPermissionError,
)
from ai_gen_hub.auth.models import Permission, UserRole, AuthUser
from ai_gen_hub.core.logging import LoggerMixin


class ResourceType(str, Enum):
    """资源类型枚举"""
    SYSTEM = "system"               # 系统资源
    USER = "user"                   # 用户资源
    API = "api"                     # API资源
    PROVIDER = "provider"           # 供应商资源
    TOKEN = "token"                 # 令牌资源
    CONSOLE = "console"             # 控制台资源
    ANALYTICS = "analytics"         # 分析资源


class ActionType(str, Enum):
    """操作类型枚举"""
    CREATE = "create"               # 创建
    READ = "read"                   # 读取
    UPDATE = "update"               # 更新
    DELETE = "delete"               # 删除
    EXECUTE = "execute"             # 执行
    MANAGE = "manage"               # 管理


class Resource(BaseModel):
    """资源模型"""
    id: UUID = Field(default_factory=uuid4, description="资源ID")
    type: ResourceType = Field(..., description="资源类型")
    name: str = Field(..., description="资源名称")
    path: str = Field(..., description="资源路径")
    parent_id: Optional[UUID] = Field(None, description="父资源ID")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="资源元数据")
    
    class Config:
        use_enum_values = True


class Role(BaseModel):
    """角色模型"""
    id: UUID = Field(default_factory=uuid4, description="角色ID")
    name: str = Field(..., description="角色名称")
    display_name: str = Field(..., description="角色显示名称")
    description: Optional[str] = Field(None, description="角色描述")
    
    # 权限
    permissions: Set[Permission] = Field(default_factory=set, description="角色权限")
    parent_roles: Set[UUID] = Field(default_factory=set, description="父角色ID列表")
    
    # 状态
    is_active: bool = Field(True, description="是否活跃")
    is_system: bool = Field(False, description="是否系统角色")
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict, description="角色元数据")
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
            set: lambda v: list(v)
        }


class AccessPolicy(BaseModel):
    """访问策略模型"""
    id: UUID = Field(default_factory=uuid4, description="策略ID")
    name: str = Field(..., description="策略名称")
    description: Optional[str] = Field(None, description="策略描述")
    
    # 策略规则
    resource_type: ResourceType = Field(..., description="资源类型")
    resource_path: Optional[str] = Field(None, description="资源路径模式")
    actions: Set[ActionType] = Field(..., description="允许的操作")
    
    # 条件
    conditions: Dict[str, Any] = Field(default_factory=dict, description="访问条件")
    
    # 状态
    is_active: bool = Field(True, description="是否活跃")
    priority: int = Field(0, description="策略优先级")
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
            set: lambda v: list(v)
        }


class RBACManager(LoggerMixin):
    """RBAC管理器
    
    提供完整的基于角色的访问控制功能
    """
    
    def __init__(self):
        super().__init__()
        self._roles: Dict[UUID, Role] = {}
        self._role_name_index: Dict[str, UUID] = {}
        self._resources: Dict[UUID, Resource] = {}
        self._policies: Dict[UUID, AccessPolicy] = {}
        self._user_roles: Dict[UUID, Set[UUID]] = {}  # user_id -> role_ids
        
        # 初始化系统角色
        self._initialize_system_roles()
    
    def _initialize_system_roles(self) -> None:
        """初始化系统预定义角色"""
        system_roles = [
            {
                "name": "super_admin",
                "display_name": "超级管理员",
                "description": "拥有所有权限的超级管理员",
                "permissions": set(Permission),
                "is_system": True
            },
            {
                "name": "admin",
                "display_name": "管理员",
                "description": "系统管理员，拥有大部分管理权限",
                "permissions": {
                    Permission.SYSTEM_CONFIG,
                    Permission.SYSTEM_MONITOR,
                    Permission.USER_CREATE,
                    Permission.USER_READ,
                    Permission.USER_UPDATE,
                    Permission.USER_DELETE,
                    Permission.API_PROVIDER_MANAGE,
                    Permission.API_TOKEN_MANAGE,
                    Permission.CONSOLE_ACCESS,
                    Permission.CONSOLE_ANALYTICS,
                    Permission.CONSOLE_SETTINGS,
                },
                "is_system": True
            },
            {
                "name": "developer",
                "display_name": "开发者",
                "description": "开发者角色，拥有API访问和基本管理权限",
                "permissions": {
                    Permission.USER_READ,
                    Permission.API_TEXT_GENERATE,
                    Permission.API_IMAGE_GENERATE,
                    Permission.API_TOKEN_MANAGE,
                    Permission.CONSOLE_ACCESS,
                    Permission.CONSOLE_ANALYTICS,
                },
                "is_system": True
            },
            {
                "name": "user",
                "display_name": "普通用户",
                "description": "普通用户角色，拥有基本API访问权限",
                "permissions": {
                    Permission.USER_READ,
                    Permission.API_TEXT_GENERATE,
                    Permission.API_IMAGE_GENERATE,
                    Permission.CONSOLE_ACCESS,
                },
                "is_system": True
            },
            {
                "name": "readonly",
                "display_name": "只读用户",
                "description": "只读用户角色，只能查看信息",
                "permissions": {
                    Permission.USER_READ,
                    Permission.CONSOLE_ACCESS,
                },
                "is_system": True
            }
        ]
        
        for role_data in system_roles:
            role = Role(**role_data)
            self._roles[role.id] = role
            self._role_name_index[role.name] = role.id
        
        self.logger.info(f"初始化了 {len(system_roles)} 个系统角色")
    
    # =============================================================================
    # 角色管理
    # =============================================================================
    
    def create_role(
        self,
        name: str,
        display_name: str,
        description: Optional[str] = None,
        permissions: Optional[Set[Permission]] = None,
        parent_roles: Optional[Set[UUID]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Role:
        """创建角色
        
        Args:
            name: 角色名称
            display_name: 角色显示名称
            description: 角色描述
            permissions: 角色权限
            parent_roles: 父角色ID列表
            metadata: 角色元数据
            
        Returns:
            创建的角色对象
            
        Raises:
            RBACError: 角色名称已存在
        """
        if name in self._role_name_index:
            raise RBACError(f"角色名称已存在: {name}")
        
        role = Role(
            name=name,
            display_name=display_name,
            description=description,
            permissions=permissions or set(),
            parent_roles=parent_roles or set(),
            metadata=metadata or {}
        )
        
        self._roles[role.id] = role
        self._role_name_index[name] = role.id
        
        self.logger.info(
            "角色创建成功",
            role_id=str(role.id),
            role_name=name,
            permissions_count=len(role.permissions)
        )
        
        return role
    
    def get_role(self, role_id: UUID) -> Optional[Role]:
        """根据ID获取角色"""
        return self._roles.get(role_id)
    
    def get_role_by_name(self, name: str) -> Optional[Role]:
        """根据名称获取角色"""
        role_id = self._role_name_index.get(name)
        return self._roles.get(role_id) if role_id else None
    
    def update_role(
        self,
        role_id: UUID,
        display_name: Optional[str] = None,
        description: Optional[str] = None,
        permissions: Optional[Set[Permission]] = None,
        parent_roles: Optional[Set[UUID]] = None,
        is_active: Optional[bool] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """更新角色
        
        Args:
            role_id: 角色ID
            display_name: 新的显示名称
            description: 新的描述
            permissions: 新的权限集合
            parent_roles: 新的父角色集合
            is_active: 新的活跃状态
            metadata: 新的元数据
            
        Returns:
            是否更新成功
        """
        role = self._roles.get(role_id)
        if not role:
            return False
        
        # 系统角色不允许修改权限
        if role.is_system and permissions is not None:
            raise RBACError("不能修改系统角色的权限")
        
        # 更新字段
        if display_name is not None:
            role.display_name = display_name
        
        if description is not None:
            role.description = description
        
        if permissions is not None:
            role.permissions = permissions
        
        if parent_roles is not None:
            role.parent_roles = parent_roles
        
        if is_active is not None:
            role.is_active = is_active
        
        if metadata is not None:
            role.metadata.update(metadata)
        
        role.updated_at = datetime.utcnow()
        
        self.logger.info(
            "角色更新成功",
            role_id=str(role_id),
            role_name=role.name
        )
        
        return True
    
    def delete_role(self, role_id: UUID) -> bool:
        """删除角色
        
        Args:
            role_id: 角色ID
            
        Returns:
            是否删除成功
        """
        role = self._roles.get(role_id)
        if not role:
            return False
        
        # 系统角色不允许删除
        if role.is_system:
            raise RBACError("不能删除系统角色")
        
        # 检查是否有用户使用此角色
        users_with_role = [
            user_id for user_id, role_ids in self._user_roles.items()
            if role_id in role_ids
        ]
        
        if users_with_role:
            raise RBACError(f"角色正在被 {len(users_with_role)} 个用户使用，无法删除")
        
        # 删除角色
        del self._roles[role_id]
        del self._role_name_index[role.name]
        
        self.logger.info(
            "角色删除成功",
            role_id=str(role_id),
            role_name=role.name
        )
        
        return True
    
    def list_roles(self, include_inactive: bool = False) -> List[Role]:
        """列出所有角色
        
        Args:
            include_inactive: 是否包含非活跃角色
            
        Returns:
            角色列表
        """
        roles = list(self._roles.values())
        
        if not include_inactive:
            roles = [role for role in roles if role.is_active]
        
        return sorted(roles, key=lambda r: r.name)
    
    # =============================================================================
    # 用户角色管理
    # =============================================================================
    
    def assign_role_to_user(self, user_id: UUID, role_id: UUID) -> bool:
        """为用户分配角色
        
        Args:
            user_id: 用户ID
            role_id: 角色ID
            
        Returns:
            是否分配成功
        """
        role = self._roles.get(role_id)
        if not role:
            raise RoleNotFoundError(str(role_id))
        
        if not role.is_active:
            raise RBACError("不能分配非活跃角色")
        
        if user_id not in self._user_roles:
            self._user_roles[user_id] = set()
        
        self._user_roles[user_id].add(role_id)
        
        self.logger.info(
            "用户角色分配成功",
            user_id=str(user_id),
            role_id=str(role_id),
            role_name=role.name
        )
        
        return True
    
    def remove_role_from_user(self, user_id: UUID, role_id: UUID) -> bool:
        """从用户移除角色
        
        Args:
            user_id: 用户ID
            role_id: 角色ID
            
        Returns:
            是否移除成功
        """
        user_roles = self._user_roles.get(user_id, set())
        
        if role_id in user_roles:
            user_roles.remove(role_id)
            
            role = self._roles.get(role_id)
            role_name = role.name if role else str(role_id)
            
            self.logger.info(
                "用户角色移除成功",
                user_id=str(user_id),
                role_id=str(role_id),
                role_name=role_name
            )
            
            return True
        
        return False
    
    def get_user_roles(self, user_id: UUID) -> List[Role]:
        """获取用户的所有角色
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户角色列表
        """
        role_ids = self._user_roles.get(user_id, set())
        roles = []
        
        for role_id in role_ids:
            role = self._roles.get(role_id)
            if role and role.is_active:
                roles.append(role)
        
        return roles
    
    def get_user_permissions(self, user_id: UUID) -> Set[Permission]:
        """获取用户的所有权限（包括继承的权限）
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户权限集合
        """
        permissions = set()
        user_roles = self.get_user_roles(user_id)
        
        for role in user_roles:
            # 添加角色直接权限
            permissions.update(role.permissions)
            
            # 添加父角色权限（递归）
            parent_permissions = self._get_inherited_permissions(role.parent_roles)
            permissions.update(parent_permissions)
        
        return permissions
    
    def _get_inherited_permissions(self, parent_role_ids: Set[UUID]) -> Set[Permission]:
        """递归获取继承的权限
        
        Args:
            parent_role_ids: 父角色ID集合
            
        Returns:
            继承的权限集合
        """
        permissions = set()
        
        for parent_role_id in parent_role_ids:
            parent_role = self._roles.get(parent_role_id)
            if parent_role and parent_role.is_active:
                # 添加父角色权限
                permissions.update(parent_role.permissions)
                
                # 递归添加祖先角色权限
                if parent_role.parent_roles:
                    inherited = self._get_inherited_permissions(parent_role.parent_roles)
                    permissions.update(inherited)
        
        return permissions
    
    # =============================================================================
    # 权限检查
    # =============================================================================
    
    def check_permission(
        self,
        user_id: UUID,
        permission: Permission,
        resource: Optional[Resource] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """检查用户是否有指定权限
        
        Args:
            user_id: 用户ID
            permission: 需要检查的权限
            resource: 相关资源（可选）
            context: 上下文信息（可选）
            
        Returns:
            是否有权限
        """
        user_permissions = self.get_user_permissions(user_id)
        
        # 检查直接权限
        if permission in user_permissions:
            return True
        
        # 检查通配符权限
        if Permission.SYSTEM_ADMIN in user_permissions:
            return True
        
        # 检查资源级权限（如果提供了资源）
        if resource:
            return self._check_resource_permission(
                user_id, permission, resource, context
            )
        
        return False
    
    def _check_resource_permission(
        self,
        user_id: UUID,
        permission: Permission,
        resource: Resource,
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """检查资源级权限
        
        Args:
            user_id: 用户ID
            permission: 权限
            resource: 资源
            context: 上下文
            
        Returns:
            是否有权限
        """
        # 这里可以实现更复杂的资源级权限检查逻辑
        # 例如：检查用户是否是资源的所有者、检查资源的访问策略等
        
        # 简化实现：检查用户是否有对应资源类型的管理权限
        user_permissions = self.get_user_permissions(user_id)
        
        # 根据资源类型检查相应的管理权限
        resource_permission_map = {
            ResourceType.USER: Permission.USER_MANAGE,
            ResourceType.API: Permission.API_PROVIDER_MANAGE,
            ResourceType.TOKEN: Permission.API_TOKEN_MANAGE,
            ResourceType.CONSOLE: Permission.CONSOLE_SETTINGS,
        }
        
        manage_permission = resource_permission_map.get(resource.type)
        if manage_permission and manage_permission in user_permissions:
            return True
        
        return False
    
    def require_permission(
        self,
        user_id: UUID,
        permission: Permission,
        resource: Optional[Resource] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """要求用户具有指定权限，否则抛出异常
        
        Args:
            user_id: 用户ID
            permission: 需要的权限
            resource: 相关资源（可选）
            context: 上下文信息（可选）
            
        Raises:
            InsufficientPermissionError: 权限不足
        """
        if not self.check_permission(user_id, permission, resource, context):
            user_permissions = self.get_user_permissions(user_id)
            raise InsufficientPermissionError(
                permission.value,
                [perm.value for perm in user_permissions]
            )
    
    def check_any_permission(
        self,
        user_id: UUID,
        permissions: List[Permission],
        resource: Optional[Resource] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """检查用户是否有任一指定权限
        
        Args:
            user_id: 用户ID
            permissions: 权限列表
            resource: 相关资源（可选）
            context: 上下文信息（可选）
            
        Returns:
            是否有任一权限
        """
        return any(
            self.check_permission(user_id, perm, resource, context)
            for perm in permissions
        )
    
    def check_all_permissions(
        self,
        user_id: UUID,
        permissions: List[Permission],
        resource: Optional[Resource] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """检查用户是否有所有指定权限
        
        Args:
            user_id: 用户ID
            permissions: 权限列表
            resource: 相关资源（可选）
            context: 上下文信息（可选）
            
        Returns:
            是否有所有权限
        """
        return all(
            self.check_permission(user_id, perm, resource, context)
            for perm in permissions
        )
