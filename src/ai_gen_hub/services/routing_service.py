"""
智能模型路由服务

提供模型注册、路由决策、A/B测试等功能
"""

import asyncio
import time
from typing import Any, Dict, List, Optional
from uuid import uuid4
from datetime import datetime

from ai_gen_hub.core.logging import get_logger
from ai_gen_hub.core.exceptions import AIGenHubException
from ai_gen_hub.core.routing.models import (
    ModelConfig,
    RoutingRule,
    RoutingRequest,
    RoutingResponse,
    RoutingDecision,
    ABTest,
    ABTestGroup,
    ABTestCreateRequest,
    ABTestUpdateRequest,
    ModelRegistrationRequest,
    RoutingStrategy,
    ModelStatus,
    ABTestStatus,
    MetricType
)
from ai_gen_hub.core.routing.engine import ModelRegistry, RoutingEngine
from ai_gen_hub.core.routing.ab_testing import ABTestManager


class RoutingService:
    """智能路由服务"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.model_registry = ModelRegistry()
        self.routing_engine = RoutingEngine(self.model_registry)
        self.ab_test_manager = ABTestManager()
        
        # 将A/B测试管理器连接到路由引擎
        self.routing_engine.ab_tests = self.ab_test_manager.tests
        
        # 启动后台任务
        self._start_background_tasks()
    
    def _start_background_tasks(self):
        """启动后台任务"""
        # 这里可以启动定期清理、指标收集等任务
        pass
    
    # 模型管理
    async def register_model(self, request: ModelRegistrationRequest, user_id: str = None) -> ModelConfig:
        """注册模型"""
        model = ModelConfig(
            model_id=request.model_id,
            provider=request.provider,
            model_name=request.model_name,
            version=request.version,
            cost_per_token=request.cost_per_token,
            quality_score=request.quality_score,
            tags=request.tags,
            **request.config
        )
        
        self.model_registry.register_model(model)
        
        self.logger.info(f"注册模型: {model.model_id}")
        return model
    
    async def unregister_model(self, model_id: str, user_id: str = None) -> bool:
        """注销模型"""
        model = self.model_registry.get_model(model_id)
        if not model:
            return False
        
        self.model_registry.unregister_model(model_id)
        
        self.logger.info(f"注销模型: {model_id}")
        return True
    
    async def update_model_status(self, model_id: str, status: ModelStatus, user_id: str = None) -> bool:
        """更新模型状态"""
        model = self.model_registry.get_model(model_id)
        if not model:
            return False
        
        model.status = status
        
        self.logger.info(f"更新模型状态: {model_id} -> {status.value}")
        return True
    
    async def get_model(self, model_id: str) -> Optional[ModelConfig]:
        """获取模型配置"""
        return self.model_registry.get_model(model_id)
    
    async def list_models(self, provider: str = None, category: str = None, status: ModelStatus = None) -> List[ModelConfig]:
        """列出模型"""
        models = list(self.model_registry.models.values())
        
        # 过滤条件
        if provider:
            models = [m for m in models if m.provider == provider]
        
        if category:
            models = [m for m in models if m.category == category]
        
        if status:
            models = [m for m in models if m.status == status]
        
        return models
    
    # 路由管理
    async def add_routing_rule(self, rule: RoutingRule, user_id: str = None) -> RoutingRule:
        """添加路由规则"""
        self.routing_engine.add_routing_rule(rule)
        
        self.logger.info(f"添加路由规则: {rule.name}")
        return rule
    
    async def remove_routing_rule(self, rule_id: str, user_id: str = None) -> bool:
        """移除路由规则"""
        self.routing_engine.remove_routing_rule(rule_id)
        
        self.logger.info(f"移除路由规则: {rule_id}")
        return True
    
    async def route_request(self, request: RoutingRequest, user_id: str = None) -> RoutingResponse:
        """路由请求"""
        # 设置用户ID
        if user_id:
            request.user_id = user_id
        
        # 执行路由决策
        decision = await self.routing_engine.route_request(request)
        
        # 记录路由决策
        self.logger.info(
            "路由决策",
            model_id=decision.selected_model.model_id,
            strategy=decision.strategy_used.value,
            reason=decision.decision_reason
        )
        
        # 获取备选模型
        alternative_models = await self._get_alternative_models(request, decision.selected_model)
        
        return RoutingResponse(
            id=request.id or str(uuid4()),
            decision=decision,
            alternative_models=alternative_models
        )
    
    async def record_request_result(
        self,
        request_id: str,
        model_id: str,
        latency: float,
        cost: float,
        success: bool,
        quality_score: float = None,
        user_id: str = None,
        ab_test_id: str = None,
        ab_test_group: str = None
    ):
        """记录请求结果"""
        # 更新模型指标
        self.model_registry.update_model_metrics(model_id, latency, cost, success, quality_score)
        
        # 记录A/B测试结果
        if ab_test_id and ab_test_group:
            metrics = {}
            if latency is not None:
                metrics[MetricType.LATENCY] = latency
            if cost is not None:
                metrics[MetricType.COST] = cost
            if quality_score is not None:
                metrics[MetricType.QUALITY_SCORE] = quality_score
            if success is not None:
                metrics[MetricType.SUCCESS_RATE] = 1.0 if success else 0.0
            
            self.ab_test_manager.record_result(ab_test_id, ab_test_group, metrics, user_id)
        
        self.logger.info(
            "记录请求结果",
            request_id=request_id,
            model_id=model_id,
            latency=latency,
            cost=cost,
            success=success
        )
    
    # A/B测试管理
    async def create_ab_test(self, request: ABTestCreateRequest, user_id: str = None) -> ABTest:
        """创建A/B测试"""
        test_id = str(uuid4())
        
        # 创建对照组
        control_group = ABTestGroup(
            group_id=f"{test_id}_control",
            name="对照组",
            description=request.control_group.get("description", ""),
            model_configs=[],  # 这里需要根据实际情况填充
            traffic_percentage=request.control_group.get("traffic_percentage", 50.0),
            target_metrics=[MetricType(m) for m in request.target_metrics]
        )
        
        # 创建实验组
        treatment_groups = []
        for i, group_data in enumerate(request.treatment_groups):
            treatment_group = ABTestGroup(
                group_id=f"{test_id}_treatment_{i}",
                name=group_data.get("name", f"实验组{i+1}"),
                description=group_data.get("description", ""),
                model_configs=[],  # 这里需要根据实际情况填充
                traffic_percentage=group_data.get("traffic_percentage", 50.0),
                target_metrics=[MetricType(m) for m in request.target_metrics]
            )
            treatment_groups.append(treatment_group)
        
        # 创建A/B测试
        ab_test = ABTest(
            test_id=test_id,
            name=request.name,
            description=request.description,
            control_group=control_group,
            treatment_groups=treatment_groups,
            duration_days=request.duration_days,
            total_traffic_percentage=request.traffic_percentage,
            created_by=user_id or "anonymous"
        )
        
        # 创建测试
        created_test = self.ab_test_manager.create_test(ab_test)
        
        self.logger.info(f"创建A/B测试: {created_test.name} ({created_test.test_id})")
        return created_test
    
    async def start_ab_test(self, test_id: str, user_id: str = None) -> bool:
        """启动A/B测试"""
        success = self.ab_test_manager.start_test(test_id)
        
        if success:
            self.logger.info(f"启动A/B测试: {test_id}")
        
        return success
    
    async def stop_ab_test(self, test_id: str, reason: str = "", user_id: str = None) -> bool:
        """停止A/B测试"""
        success = self.ab_test_manager.stop_test(test_id, reason)
        
        if success:
            self.logger.info(f"停止A/B测试: {test_id}")
        
        return success
    
    async def get_ab_test(self, test_id: str) -> Optional[ABTest]:
        """获取A/B测试"""
        return self.ab_test_manager.tests.get(test_id)
    
    async def list_ab_tests(self, status: ABTestStatus = None) -> List[ABTest]:
        """列出A/B测试"""
        tests = list(self.ab_test_manager.tests.values())
        
        if status:
            tests = [t for t in tests if t.status == status]
        
        return tests
    
    async def get_ab_test_results(self, test_id: str) -> Dict[str, Any]:
        """获取A/B测试结果"""
        return self.ab_test_manager.get_test_summary(test_id)
    
    # 统计和监控
    async def get_model_metrics(self, model_id: str) -> Optional[Dict[str, Any]]:
        """获取模型指标"""
        metrics = self.model_registry.get_model_metrics(model_id)
        if not metrics:
            return None
        
        return {
            'model_id': metrics.model_id,
            'total_requests': metrics.total_requests,
            'successful_requests': metrics.successful_requests,
            'failed_requests': metrics.failed_requests,
            'success_rate': metrics.calculate_success_rate(),
            'error_rate': metrics.calculate_error_rate(),
            'avg_latency': metrics.avg_latency,
            'min_latency': metrics.min_latency,
            'max_latency': metrics.max_latency,
            'total_cost': metrics.total_cost,
            'avg_cost_per_request': metrics.avg_cost_per_request,
            'avg_quality_score': metrics.avg_quality_score,
            'window_start': metrics.window_start.isoformat(),
            'window_end': metrics.window_end.isoformat()
        }
    
    async def get_routing_statistics(self) -> Dict[str, Any]:
        """获取路由统计"""
        return {
            'total_models': len(self.model_registry.models),
            'active_models': len(self.model_registry.get_active_models()),
            'total_routing_rules': len(self.routing_engine.routing_rules),
            'active_ab_tests': len(self.ab_test_manager.get_active_tests()),
            'routing_stats': dict(self.routing_engine.routing_stats)
        }
    
    async def _get_alternative_models(self, request: RoutingRequest, selected_model: ModelConfig) -> List[ModelConfig]:
        """获取备选模型"""
        candidate_models = await self.routing_engine._get_candidate_models(request)
        
        # 排除已选择的模型
        alternatives = [m for m in candidate_models if m.model_id != selected_model.model_id]
        
        # 按质量分数排序，返回前3个
        alternatives.sort(key=lambda m: m.quality_score, reverse=True)
        
        return alternatives[:3]
    
    async def cleanup_expired_tests(self):
        """清理过期的测试"""
        self.ab_test_manager.cleanup_expired_tests()
