"""
AI工作流服务

提供工作流的创建、管理、执行等功能
"""

import asyncio
import json
from typing import Any, Dict, List, Optional
from uuid import uuid4
from datetime import datetime

from ai_gen_hub.core.logging import get_logger
from ai_gen_hub.core.exceptions import AIGenHubException
from ai_gen_hub.core.workflow.models import (
    Workflow,
    WorkflowNode,
    WorkflowExecution,
    WorkflowTemplate,
    WorkflowMetadata,
    NodeConfig,
    NodePosition,
    NodeConnection,
    NodeType,
    WorkflowStatus,
    WorkflowCreateRequest,
    WorkflowUpdateRequest,
    WorkflowExecuteRequest
)
from ai_gen_hub.core.workflow.engine import WorkflowEngine


class WorkflowService:
    """工作流服务"""
    
    def __init__(self, storage_backend=None):
        self.logger = get_logger(__name__)
        self.storage_backend = storage_backend
        self.engine = WorkflowEngine()
        
        # 内存存储（生产环境应使用数据库）
        self.workflows: Dict[str, Workflow] = {}
        self.executions: Dict[str, WorkflowExecution] = {}
        self.templates: Dict[str, WorkflowTemplate] = {}
        
        # 注册事件回调
        self._register_event_callbacks()
    
    def _register_event_callbacks(self):
        """注册工作流引擎事件回调"""
        self.engine.add_event_callback('workflow_started', self._on_workflow_started)
        self.engine.add_event_callback('workflow_completed', self._on_workflow_completed)
        self.engine.add_event_callback('workflow_failed', self._on_workflow_failed)
    
    async def _on_workflow_started(self, execution: WorkflowExecution, workflow: Workflow, node: WorkflowNode = None):
        """工作流开始事件处理"""
        self.executions[execution.id] = execution
        self.logger.info(f"工作流开始执行: {workflow.metadata.name} ({execution.id})")
    
    async def _on_workflow_completed(self, execution: WorkflowExecution, workflow: Workflow, node: WorkflowNode = None):
        """工作流完成事件处理"""
        workflow.execution_count += 1
        workflow.success_count += 1
        self.logger.info(f"工作流执行完成: {workflow.metadata.name} ({execution.id})")
    
    async def _on_workflow_failed(self, execution: WorkflowExecution, workflow: Workflow, node: WorkflowNode = None):
        """工作流失败事件处理"""
        workflow.execution_count += 1
        workflow.failure_count += 1
        self.logger.error(f"工作流执行失败: {workflow.metadata.name} ({execution.id})")
    
    async def create_workflow(self, request: WorkflowCreateRequest, user_id: str = None) -> Workflow:
        """创建工作流"""
        workflow_id = str(uuid4())
        
        # 创建节点
        nodes = []
        for node_data in request.nodes:
            node = WorkflowNode(
                id=node_data.get('id', str(uuid4())),
                type=NodeType(node_data['type']),
                config=NodeConfig(**node_data['config']),
                position=NodePosition(**node_data['position'])
            )
            nodes.append(node)
        
        # 创建连接
        connections = []
        for conn_data in request.connections:
            connection = NodeConnection(**conn_data)
            connections.append(connection)
        
        # 创建工作流
        workflow = Workflow(
            id=workflow_id,
            metadata=request.metadata,
            nodes=nodes,
            connections=connections,
            status=WorkflowStatus.DRAFT
        )
        
        # 验证工作流
        validation_errors = workflow.validate()
        if validation_errors:
            raise AIGenHubException(f"工作流验证失败: {', '.join(validation_errors)}")
        
        # 保存工作流
        self.workflows[workflow_id] = workflow
        
        self.logger.info(f"创建工作流: {workflow.metadata.name} ({workflow_id})")
        
        return workflow
    
    async def get_workflow(self, workflow_id: str, user_id: str = None) -> Optional[Workflow]:
        """获取工作流"""
        workflow = self.workflows.get(workflow_id)
        if not workflow:
            return None
        
        # 检查权限
        if not self._check_workflow_permission(workflow, user_id, 'read'):
            raise AIGenHubException("没有权限访问此工作流")
        
        return workflow
    
    async def list_workflows(self, user_id: str = None, category: str = None, tags: List[str] = None) -> List[Workflow]:
        """列出工作流"""
        workflows = []
        
        for workflow in self.workflows.values():
            # 检查权限
            if not self._check_workflow_permission(workflow, user_id, 'read'):
                continue
            
            # 过滤条件
            if category and workflow.metadata.category != category:
                continue
            
            if tags:
                workflow_tags = set(workflow.metadata.tags)
                if not workflow_tags.intersection(set(tags)):
                    continue
            
            workflows.append(workflow)
        
        return workflows
    
    async def execute_workflow(self, request: WorkflowExecuteRequest, user_id: str = None) -> WorkflowExecution:
        """执行工作流"""
        workflow = self.workflows.get(request.workflow_id)
        if not workflow:
            raise AIGenHubException(f"工作流不存在: {request.workflow_id}")
        
        # 检查权限
        if not self._check_workflow_permission(workflow, user_id, 'execute'):
            raise AIGenHubException("没有权限执行此工作流")
        
        # 检查工作流状态
        if workflow.status != WorkflowStatus.ACTIVE:
            # 如果是草稿状态，自动激活
            if workflow.status == WorkflowStatus.DRAFT:
                workflow.status = WorkflowStatus.ACTIVE
            else:
                raise AIGenHubException(f"工作流状态不允许执行: {workflow.status}")
        
        # 执行工作流
        if request.async_execution:
            # 异步执行
            task = asyncio.create_task(
                self.engine.execute_workflow(
                    workflow=workflow,
                    input_data=request.input_data,
                    context=request.context,
                    executor_id=user_id
                )
            )
            
            # 创建执行实例占位符
            execution = WorkflowExecution(
                id=f"async_{int(asyncio.get_event_loop().time())}_{workflow.id[:8]}",
                workflow_id=workflow.id,
                status=WorkflowStatus.RUNNING,
                start_time=datetime.now(),
                input_data=request.input_data,
                context=request.context,
                executor_id=user_id
            )
            
            self.executions[execution.id] = execution
            
            return execution
        else:
            # 同步执行
            execution = await self.engine.execute_workflow(
                workflow=workflow,
                input_data=request.input_data,
                context=request.context,
                executor_id=user_id
            )
            
            return execution
    
    async def get_execution(self, execution_id: str, user_id: str = None) -> Optional[WorkflowExecution]:
        """获取执行实例"""
        execution = self.executions.get(execution_id)
        if not execution:
            # 检查引擎中的运行实例
            execution = self.engine.get_execution_status(execution_id)
        
        if not execution:
            return None
        
        # 检查权限
        workflow = self.workflows.get(execution.workflow_id)
        if workflow and not self._check_workflow_permission(workflow, user_id, 'read'):
            raise AIGenHubException("没有权限访问此执行实例")
        
        return execution
    
    async def cancel_execution(self, execution_id: str, user_id: str = None) -> bool:
        """取消执行"""
        execution = await self.get_execution(execution_id, user_id)
        if not execution:
            return False
        
        # 检查权限
        workflow = self.workflows.get(execution.workflow_id)
        if workflow and not self._check_workflow_permission(workflow, user_id, 'execute'):
            raise AIGenHubException("没有权限取消此执行")
        
        return await self.engine.cancel_execution(execution_id)
    
    async def create_template(self, workflow_id: str, template_name: str, template_description: str, user_id: str = None) -> WorkflowTemplate:
        """创建工作流模板"""
        workflow = await self.get_workflow(workflow_id, user_id)
        if not workflow:
            raise AIGenHubException(f"工作流不存在: {workflow_id}")
        
        template_id = str(uuid4())
        
        # 创建模板
        template = WorkflowTemplate(
            id=template_id,
            name=template_name,
            description=template_description,
            category=workflow.metadata.category,
            workflow=workflow,
            author=user_id or "anonymous"
        )
        
        self.templates[template_id] = template
        
        self.logger.info(f"创建工作流模板: {template_name} ({template_id})")
        
        return template
    
    async def get_templates(self, category: str = None, tags: List[str] = None) -> List[WorkflowTemplate]:
        """获取工作流模板"""
        templates = []
        
        for template in self.templates.values():
            # 过滤条件
            if category and template.category != category:
                continue
            
            if tags:
                template_tags = set(template.tags)
                if not template_tags.intersection(set(tags)):
                    continue
            
            templates.append(template)
        
        return templates
    
    async def create_workflow_from_template(self, template_id: str, workflow_name: str, user_id: str = None) -> Workflow:
        """从模板创建工作流"""
        template = self.templates.get(template_id)
        if not template:
            raise AIGenHubException(f"模板不存在: {template_id}")
        
        # 复制工作流
        workflow_id = str(uuid4())
        
        # 创建新的元数据
        metadata = WorkflowMetadata(
            name=workflow_name,
            description=template.description,
            version="1.0.0",
            author=user_id or "anonymous",
            category=template.category,
            tags=template.tags.copy()
        )
        
        # 复制节点和连接
        nodes = []
        for node in template.workflow.nodes:
            new_node = WorkflowNode(
                id=str(uuid4()),
                type=node.type,
                config=node.config,
                position=node.position
            )
            nodes.append(new_node)
        
        connections = []
        for conn in template.workflow.connections:
            new_connection = NodeConnection(
                source_node_id=conn.source_node_id,
                target_node_id=conn.target_node_id,
                source_port=conn.source_port,
                target_port=conn.target_port,
                condition=conn.condition
            )
            connections.append(new_connection)
        
        # 创建工作流
        workflow = Workflow(
            id=workflow_id,
            metadata=metadata,
            nodes=nodes,
            connections=connections,
            status=WorkflowStatus.DRAFT
        )
        
        self.workflows[workflow_id] = workflow
        
        # 更新模板使用统计
        template.usage_count += 1
        
        self.logger.info(f"从模板创建工作流: {workflow_name} ({workflow_id})")
        
        return workflow
    
    def _check_workflow_permission(self, workflow: Workflow, user_id: str, action: str) -> bool:
        """检查工作流权限"""
        # 简单的权限检查逻辑
        if workflow.metadata.is_public:
            return True
        
        if user_id in workflow.metadata.allowed_users:
            return True
        
        # 这里可以添加更复杂的权限逻辑
        return True  # 暂时允许所有操作
    
    async def get_workflow_statistics(self) -> Dict[str, Any]:
        """获取工作流统计信息"""
        total_workflows = len(self.workflows)
        total_executions = len(self.executions)
        running_executions = len(self.engine.get_running_executions())
        
        # 按状态统计工作流
        status_counts = {}
        for workflow in self.workflows.values():
            status = workflow.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        # 按类别统计工作流
        category_counts = {}
        for workflow in self.workflows.values():
            category = workflow.metadata.category
            category_counts[category] = category_counts.get(category, 0) + 1
        
        return {
            'total_workflows': total_workflows,
            'total_executions': total_executions,
            'running_executions': running_executions,
            'status_distribution': status_counts,
            'category_distribution': category_counts,
            'total_templates': len(self.templates)
        }
