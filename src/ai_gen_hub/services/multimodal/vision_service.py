"""
图像理解服务

提供图像分析和理解功能：
- 图像描述生成
- 图像问答
- OCR文字识别
- 物体检测和分类
- 场景理解
- 图像内容审核
"""

import asyncio
import base64
import io
import time
from typing import Any, Dict, List, Optional, Union, Tuple
from uuid import uuid4
from dataclasses import dataclass
from enum import Enum
from PIL import Image
import json

from ai_gen_hub.core.interfaces import BaseRequest, BaseResponse
from ai_gen_hub.core.exceptions import (
    InvalidRequestError,
    ModelNotSupportedError,
    AIGenHubException
)
from ai_gen_hub.core.logging import get_logger


class VisionTask(Enum):
    """视觉任务类型"""
    DESCRIBE = "describe"  # 图像描述
    QA = "qa"  # 图像问答
    OCR = "ocr"  # 文字识别
    DETECT = "detect"  # 物体检测
    CLASSIFY = "classify"  # 图像分类
    SEGMENT = "segment"  # 图像分割
    MODERATE = "moderate"  # 内容审核


class DetailLevel(Enum):
    """描述详细程度"""
    LOW = "low"  # 简单描述
    MEDIUM = "medium"  # 中等详细
    HIGH = "high"  # 详细描述


@dataclass
class BoundingBox:
    """边界框"""
    x: float  # 左上角x坐标（相对位置 0-1）
    y: float  # 左上角y坐标（相对位置 0-1）
    width: float  # 宽度（相对大小 0-1）
    height: float  # 高度（相对大小 0-1）
    confidence: float = 0.0  # 置信度


@dataclass
class DetectedObject:
    """检测到的物体"""
    label: str  # 物体标签
    confidence: float  # 置信度
    bbox: BoundingBox  # 边界框
    attributes: Optional[Dict[str, Any]] = None  # 额外属性


@dataclass
class OCRResult:
    """OCR识别结果"""
    text: str  # 识别的文字
    confidence: float  # 置信度
    bbox: BoundingBox  # 文字区域
    language: Optional[str] = None  # 检测到的语言


@dataclass
class VisionRequest(BaseRequest):
    """图像理解请求"""
    image: str  # Base64编码的图像
    task: VisionTask  # 任务类型
    prompt: Optional[str] = None  # 任务提示（用于描述和问答）
    detail_level: DetailLevel = DetailLevel.MEDIUM  # 详细程度
    language: str = "zh-CN"  # 输出语言
    max_objects: int = 10  # 最大检测物体数量
    confidence_threshold: float = 0.5  # 置信度阈值
    include_attributes: bool = False  # 是否包含物体属性
    ocr_languages: Optional[List[str]] = None  # OCR语言列表


@dataclass
class VisionResponse(BaseResponse):
    """图像理解响应"""
    task: VisionTask  # 任务类型
    description: Optional[str] = None  # 图像描述
    answer: Optional[str] = None  # 问答回答
    objects: Optional[List[DetectedObject]] = None  # 检测到的物体
    ocr_results: Optional[List[OCRResult]] = None  # OCR结果
    categories: Optional[List[Dict[str, float]]] = None  # 分类结果
    moderation: Optional[Dict[str, Any]] = None  # 内容审核结果
    confidence: float = 0.0  # 整体置信度
    processing_time: float = 0.0  # 处理时间


class VisionService:
    """图像理解服务"""
    
    def __init__(self, provider_manager=None, cache_manager=None):
        self.provider_manager = provider_manager
        self.cache_manager = cache_manager
        self.logger = get_logger(__name__)
        
        # 支持的图像格式
        self.supported_formats = ["JPEG", "PNG", "WEBP", "BMP"]
        
        # 图像处理配置
        self.max_image_size = 4096  # 最大图像尺寸
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        
        # 任务配置
        self.task_timeouts = {
            VisionTask.DESCRIBE: 10,
            VisionTask.QA: 15,
            VisionTask.OCR: 20,
            VisionTask.DETECT: 25,
            VisionTask.CLASSIFY: 10,
            VisionTask.SEGMENT: 30,
            VisionTask.MODERATE: 5
        }
    
    async def analyze_image(
        self,
        request: VisionRequest,
        user_id: Optional[str] = None,
        request_id: Optional[str] = None
    ) -> VisionResponse:
        """分析图像"""
        if request_id is None:
            request_id = str(uuid4())
        
        self.logger.info(
            "开始图像分析",
            task=request.task.value,
            detail_level=request.detail_level.value,
            language=request.language,
            request_id=request_id
        )
        
        # 验证请求
        await self._validate_request(request)
        
        # 预处理图像
        image = await self._preprocess_image(request.image)
        
        # 检查缓存
        cache_key = self._generate_cache_key(request)
        if self.cache_manager:
            cached_response = await self.cache_manager.get(cache_key)
            if cached_response:
                self.logger.info("视觉分析缓存命中", request_id=request_id)
                return VisionResponse(**cached_response)
        
        # 选择供应商
        provider = await self._select_provider(request)
        if not provider:
            raise ModelNotSupportedError(f"没有可用的{request.task.value}供应商")
        
        # 执行分析
        start_time = time.time()
        try:
            response = await self._perform_analysis(provider, request, image, request_id)
            response.processing_time = time.time() - start_time
            
            # 缓存响应
            if self.cache_manager:
                await self.cache_manager.set(cache_key, response.__dict__, ttl=1800)
            
            self.logger.info(
                "图像分析完成",
                task=request.task.value,
                processing_time=response.processing_time,
                confidence=response.confidence,
                request_id=request_id
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"图像分析失败: {e}", request_id=request_id)
            raise
    
    async def describe_image(
        self,
        image: str,
        detail_level: DetailLevel = DetailLevel.MEDIUM,
        language: str = "zh-CN",
        user_id: Optional[str] = None
    ) -> str:
        """生成图像描述"""
        request = VisionRequest(
            image=image,
            task=VisionTask.DESCRIBE,
            detail_level=detail_level,
            language=language
        )
        
        response = await self.analyze_image(request, user_id)
        return response.description or "无法生成图像描述"
    
    async def answer_image_question(
        self,
        image: str,
        question: str,
        language: str = "zh-CN",
        user_id: Optional[str] = None
    ) -> str:
        """回答图像相关问题"""
        request = VisionRequest(
            image=image,
            task=VisionTask.QA,
            prompt=question,
            language=language
        )
        
        response = await self.analyze_image(request, user_id)
        return response.answer or "无法回答该问题"
    
    async def extract_text(
        self,
        image: str,
        languages: Optional[List[str]] = None,
        user_id: Optional[str] = None
    ) -> List[OCRResult]:
        """提取图像中的文字"""
        request = VisionRequest(
            image=image,
            task=VisionTask.OCR,
            ocr_languages=languages or ["zh-CN", "en"]
        )
        
        response = await self.analyze_image(request, user_id)
        return response.ocr_results or []
    
    async def detect_objects(
        self,
        image: str,
        max_objects: int = 10,
        confidence_threshold: float = 0.5,
        include_attributes: bool = False,
        user_id: Optional[str] = None
    ) -> List[DetectedObject]:
        """检测图像中的物体"""
        request = VisionRequest(
            image=image,
            task=VisionTask.DETECT,
            max_objects=max_objects,
            confidence_threshold=confidence_threshold,
            include_attributes=include_attributes
        )
        
        response = await self.analyze_image(request, user_id)
        return response.objects or []
    
    async def classify_image(
        self,
        image: str,
        user_id: Optional[str] = None
    ) -> List[Dict[str, float]]:
        """分类图像"""
        request = VisionRequest(
            image=image,
            task=VisionTask.CLASSIFY
        )
        
        response = await self.analyze_image(request, user_id)
        return response.categories or []
    
    async def moderate_content(
        self,
        image: str,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """内容审核"""
        request = VisionRequest(
            image=image,
            task=VisionTask.MODERATE
        )
        
        response = await self.analyze_image(request, user_id)
        return response.moderation or {"safe": True, "categories": []}
    
    async def _validate_request(self, request: VisionRequest):
        """验证请求"""
        if not request.image:
            raise InvalidRequestError("缺少图像数据")
        
        # 验证图像大小
        try:
            image_data = base64.b64decode(request.image)
            if len(image_data) > self.max_file_size:
                raise InvalidRequestError(f"图像文件过大，最大支持 {self.max_file_size // 1024 // 1024}MB")
        except Exception as e:
            raise InvalidRequestError(f"无效的图像数据: {e}")
        
        # 验证任务特定参数
        if request.task == VisionTask.QA and not request.prompt:
            raise InvalidRequestError("问答任务需要提供问题")
        
        if request.confidence_threshold < 0.0 or request.confidence_threshold > 1.0:
            raise InvalidRequestError("置信度阈值必须在0.0-1.0之间")
        
        if request.max_objects < 1 or request.max_objects > 100:
            raise InvalidRequestError("最大物体数量必须在1-100之间")
    
    async def _preprocess_image(self, image_data: str) -> Image.Image:
        """预处理图像"""
        try:
            # 解码Base64图像
            image_bytes = base64.b64decode(image_data)
            image = Image.open(io.BytesIO(image_bytes))
            
            # 转换为RGB格式
            if image.mode != "RGB":
                image = image.convert("RGB")
            
            # 检查图像尺寸
            width, height = image.size
            if width > self.max_image_size or height > self.max_image_size:
                # 等比例缩放
                ratio = min(self.max_image_size / width, self.max_image_size / height)
                new_size = (int(width * ratio), int(height * ratio))
                image = image.resize(new_size, Image.Resampling.LANCZOS)
                
                self.logger.info(f"图像已缩放: {width}x{height} -> {new_size[0]}x{new_size[1]}")
            
            return image
            
        except Exception as e:
            raise InvalidRequestError(f"图像预处理失败: {e}")
    
    def _generate_cache_key(self, request: VisionRequest) -> str:
        """生成缓存键"""
        import hashlib
        
        key_data = f"{request.task.value}_{request.image[:100]}_{request.prompt}_{request.detail_level.value}_{request.language}"
        return f"vision:{hashlib.md5(key_data.encode()).hexdigest()}"
    
    async def _select_provider(self, request: VisionRequest):
        """选择供应商"""
        # 根据任务类型选择最合适的供应商
        task_providers = {
            VisionTask.DESCRIBE: "gpt-4-vision",
            VisionTask.QA: "gpt-4-vision",
            VisionTask.OCR: "azure_computer_vision",
            VisionTask.DETECT: "azure_computer_vision",
            VisionTask.CLASSIFY: "google_vision",
            VisionTask.SEGMENT: "azure_computer_vision",
            VisionTask.MODERATE: "azure_content_moderator"
        }
        
        return task_providers.get(request.task, "gpt-4-vision")
    
    async def _perform_analysis(
        self,
        provider: str,
        request: VisionRequest,
        image: Image.Image,
        request_id: str
    ) -> VisionResponse:
        """执行图像分析"""
        # 根据任务类型调用不同的分析方法
        if request.task == VisionTask.DESCRIBE:
            return await self._describe_image(provider, request, image, request_id)
        elif request.task == VisionTask.QA:
            return await self._answer_question(provider, request, image, request_id)
        elif request.task == VisionTask.OCR:
            return await self._extract_text(provider, request, image, request_id)
        elif request.task == VisionTask.DETECT:
            return await self._detect_objects(provider, request, image, request_id)
        elif request.task == VisionTask.CLASSIFY:
            return await self._classify_image(provider, request, image, request_id)
        elif request.task == VisionTask.MODERATE:
            return await self._moderate_content(provider, request, image, request_id)
        else:
            raise InvalidRequestError(f"不支持的任务类型: {request.task}")
    
    async def _describe_image(self, provider: str, request: VisionRequest, image: Image.Image, request_id: str) -> VisionResponse:
        """生成图像描述"""
        await asyncio.sleep(2)  # 模拟处理时间
        
        descriptions = {
            DetailLevel.LOW: "这是一张图片。",
            DetailLevel.MEDIUM: "这是一张包含多个元素的彩色图片，展示了丰富的视觉内容。",
            DetailLevel.HIGH: "这是一张高质量的彩色图片，包含了复杂的场景构成，具有良好的光线和色彩平衡，展现了丰富的细节和层次感。"
        }
        
        return VisionResponse(
            id=request_id,
            task=request.task,
            description=descriptions[request.detail_level],
            confidence=0.92
        )
    
    async def _answer_question(self, provider: str, request: VisionRequest, image: Image.Image, request_id: str) -> VisionResponse:
        """回答图像问题"""
        await asyncio.sleep(3)  # 模拟处理时间
        
        return VisionResponse(
            id=request_id,
            task=request.task,
            answer=f"根据图像内容，关于'{request.prompt}'的回答是：这是一个基于图像分析的智能回答。",
            confidence=0.88
        )
    
    async def _extract_text(self, provider: str, request: VisionRequest, image: Image.Image, request_id: str) -> VisionResponse:
        """提取文字"""
        await asyncio.sleep(4)  # 模拟处理时间
        
        # 模拟OCR结果
        ocr_results = [
            OCRResult(
                text="示例文字内容",
                confidence=0.95,
                bbox=BoundingBox(x=0.1, y=0.2, width=0.3, height=0.05),
                language="zh-CN"
            ),
            OCRResult(
                text="Example Text",
                confidence=0.92,
                bbox=BoundingBox(x=0.5, y=0.6, width=0.25, height=0.04),
                language="en"
            )
        ]
        
        return VisionResponse(
            id=request_id,
            task=request.task,
            ocr_results=ocr_results,
            confidence=0.94
        )
    
    async def _detect_objects(self, provider: str, request: VisionRequest, image: Image.Image, request_id: str) -> VisionResponse:
        """检测物体"""
        await asyncio.sleep(5)  # 模拟处理时间
        
        # 模拟检测结果
        objects = [
            DetectedObject(
                label="person",
                confidence=0.95,
                bbox=BoundingBox(x=0.2, y=0.1, width=0.3, height=0.8),
                attributes={"age": "adult", "gender": "unknown"} if request.include_attributes else None
            ),
            DetectedObject(
                label="car",
                confidence=0.88,
                bbox=BoundingBox(x=0.6, y=0.4, width=0.35, height=0.25),
                attributes={"color": "blue", "type": "sedan"} if request.include_attributes else None
            )
        ]
        
        # 过滤低置信度结果
        filtered_objects = [obj for obj in objects if obj.confidence >= request.confidence_threshold]
        
        return VisionResponse(
            id=request_id,
            task=request.task,
            objects=filtered_objects[:request.max_objects],
            confidence=0.91
        )
    
    async def _classify_image(self, provider: str, request: VisionRequest, image: Image.Image, request_id: str) -> VisionResponse:
        """分类图像"""
        await asyncio.sleep(2)  # 模拟处理时间
        
        categories = [
            {"outdoor": 0.85},
            {"nature": 0.72},
            {"landscape": 0.68},
            {"urban": 0.45}
        ]
        
        return VisionResponse(
            id=request_id,
            task=request.task,
            categories=categories,
            confidence=0.85
        )
    
    async def _moderate_content(self, provider: str, request: VisionRequest, image: Image.Image, request_id: str) -> VisionResponse:
        """内容审核"""
        await asyncio.sleep(1)  # 模拟处理时间
        
        moderation = {
            "safe": True,
            "categories": {
                "adult": 0.02,
                "violence": 0.01,
                "racy": 0.03
            },
            "overall_score": 0.02
        }
        
        return VisionResponse(
            id=request_id,
            task=request.task,
            moderation=moderation,
            confidence=0.98
        )
