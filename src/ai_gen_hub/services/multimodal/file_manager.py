"""
多模态文件管理系统

提供多媒体文件的存储、处理和管理功能：
- 文件上传和下载
- 格式转换
- 缩略图生成
- 文件压缩和优化
- 元数据提取
- 文件安全检查
"""

import asyncio
import os
import hashlib
import mimetypes
import time
from typing import Any, Dict, List, Optional, Union, BinaryIO, Tuple
from uuid import uuid4
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import aiofiles
import aiohttp
from PIL import Image, ImageOps

from ai_gen_hub.core.interfaces import BaseRequest, BaseResponse
from ai_gen_hub.core.exceptions import (
    InvalidRequestError,
    AIGenHubException
)
from ai_gen_hub.core.logging import get_logger


class FileType(Enum):
    """文件类型"""
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    DOCUMENT = "document"
    UNKNOWN = "unknown"


class StorageProvider(Enum):
    """存储供应商"""
    LOCAL = "local"
    S3 = "s3"
    AZURE = "azure"
    GCS = "gcs"


@dataclass
class FileMetadata:
    """文件元数据"""
    file_id: str
    filename: str
    file_type: FileType
    mime_type: str
    size: int  # 文件大小（字节）
    checksum: str  # 文件校验和
    created_at: float
    updated_at: float
    
    # 媒体特定元数据
    width: Optional[int] = None
    height: Optional[int] = None
    duration: Optional[float] = None  # 音频/视频时长（秒）
    bitrate: Optional[int] = None
    sample_rate: Optional[int] = None
    channels: Optional[int] = None
    
    # 存储信息
    storage_provider: StorageProvider = StorageProvider.LOCAL
    storage_path: Optional[str] = None
    public_url: Optional[str] = None
    
    # 处理状态
    processed: bool = False
    thumbnail_url: Optional[str] = None
    compressed_url: Optional[str] = None


@dataclass
class FileUploadRequest(BaseRequest):
    """文件上传请求"""
    filename: str
    content_type: str
    file_data: bytes
    generate_thumbnail: bool = True
    compress: bool = True
    extract_metadata: bool = True
    public: bool = False


@dataclass
class FileUploadResponse(BaseResponse):
    """文件上传响应"""
    file_id: str
    filename: str
    file_type: FileType
    size: int
    url: str
    thumbnail_url: Optional[str] = None
    metadata: Optional[FileMetadata] = None


class MultimodalFileManager:
    """多模态文件管理器"""
    
    def __init__(self, storage_config: Dict[str, Any] = None):
        self.logger = get_logger(__name__)
        self.storage_config = storage_config or {}
        
        # 存储配置
        self.storage_provider = StorageProvider(
            self.storage_config.get("provider", "local")
        )
        self.base_path = Path(self.storage_config.get("base_path", "./uploads"))
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        # 文件限制
        self.max_file_size = self.storage_config.get("max_file_size", 100 * 1024 * 1024)  # 100MB
        self.allowed_types = self.storage_config.get("allowed_types", [
            "image/jpeg", "image/png", "image/webp", "image/gif",
            "audio/mpeg", "audio/wav", "audio/ogg", "audio/flac",
            "video/mp4", "video/webm", "video/avi",
            "application/pdf", "text/plain"
        ])
        
        # 处理配置
        self.thumbnail_sizes = [(150, 150), (300, 300), (600, 600)]
        self.compression_quality = 85
        
        # 文件元数据存储
        self.metadata_store: Dict[str, FileMetadata] = {}
    
    async def upload_file(
        self,
        request: FileUploadRequest,
        user_id: Optional[str] = None
    ) -> FileUploadResponse:
        """上传文件"""
        file_id = str(uuid4())
        
        self.logger.info(
            "开始文件上传",
            filename=request.filename,
            size=len(request.file_data),
            content_type=request.content_type,
            file_id=file_id
        )
        
        # 验证文件
        await self._validate_file(request)
        
        # 确定文件类型
        file_type = self._determine_file_type(request.content_type)
        
        # 计算文件校验和
        checksum = hashlib.sha256(request.file_data).hexdigest()
        
        # 生成存储路径
        storage_path = await self._generate_storage_path(file_id, request.filename)
        
        # 保存文件
        await self._save_file(storage_path, request.file_data)
        
        # 创建元数据
        metadata = FileMetadata(
            file_id=file_id,
            filename=request.filename,
            file_type=file_type,
            mime_type=request.content_type,
            size=len(request.file_data),
            checksum=checksum,
            created_at=time.time(),
            updated_at=time.time(),
            storage_provider=self.storage_provider,
            storage_path=str(storage_path)
        )
        
        # 提取媒体元数据
        if request.extract_metadata:
            await self._extract_media_metadata(metadata, storage_path)
        
        # 生成缩略图
        thumbnail_url = None
        if request.generate_thumbnail and file_type == FileType.IMAGE:
            thumbnail_url = await self._generate_thumbnail(metadata, storage_path)
            metadata.thumbnail_url = thumbnail_url
        
        # 压缩文件
        if request.compress:
            await self._compress_file(metadata, storage_path)
        
        # 生成公开URL
        public_url = await self._generate_public_url(file_id, request.public)
        metadata.public_url = public_url
        
        # 保存元数据
        self.metadata_store[file_id] = metadata
        
        self.logger.info(
            "文件上传完成",
            file_id=file_id,
            storage_path=str(storage_path),
            thumbnail_url=thumbnail_url
        )
        
        return FileUploadResponse(
            id=file_id,
            file_id=file_id,
            filename=request.filename,
            file_type=file_type,
            size=len(request.file_data),
            url=public_url,
            thumbnail_url=thumbnail_url,
            metadata=metadata
        )
    
    async def get_file(self, file_id: str) -> Optional[FileMetadata]:
        """获取文件元数据"""
        return self.metadata_store.get(file_id)
    
    async def download_file(self, file_id: str) -> Optional[bytes]:
        """下载文件"""
        metadata = await self.get_file(file_id)
        if not metadata or not metadata.storage_path:
            return None
        
        try:
            async with aiofiles.open(metadata.storage_path, 'rb') as f:
                return await f.read()
        except Exception as e:
            self.logger.error(f"文件下载失败: {e}")
            return None
    
    async def delete_file(self, file_id: str) -> bool:
        """删除文件"""
        metadata = await self.get_file(file_id)
        if not metadata:
            return False
        
        try:
            # 删除主文件
            if metadata.storage_path and os.path.exists(metadata.storage_path):
                os.remove(metadata.storage_path)
            
            # 删除缩略图
            if metadata.thumbnail_url:
                thumbnail_path = self._url_to_path(metadata.thumbnail_url)
                if os.path.exists(thumbnail_path):
                    os.remove(thumbnail_path)
            
            # 删除元数据
            del self.metadata_store[file_id]
            
            self.logger.info(f"文件删除成功: {file_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"文件删除失败: {e}")
            return False
    
    async def _validate_file(self, request: FileUploadRequest):
        """验证文件"""
        # 检查文件大小
        if len(request.file_data) > self.max_file_size:
            raise InvalidRequestError(f"文件过大，最大支持 {self.max_file_size // 1024 // 1024}MB")
        
        # 检查文件类型
        if request.content_type not in self.allowed_types:
            raise InvalidRequestError(f"不支持的文件类型: {request.content_type}")
        
        # 检查文件名
        if not request.filename or len(request.filename) > 255:
            raise InvalidRequestError("文件名无效")
        
        # 安全检查：检查文件头
        await self._security_check(request.file_data, request.content_type)
    
    def _determine_file_type(self, mime_type: str) -> FileType:
        """确定文件类型"""
        if mime_type.startswith("image/"):
            return FileType.IMAGE
        elif mime_type.startswith("audio/"):
            return FileType.AUDIO
        elif mime_type.startswith("video/"):
            return FileType.VIDEO
        elif mime_type in ["application/pdf", "text/plain"]:
            return FileType.DOCUMENT
        else:
            return FileType.UNKNOWN
    
    async def _generate_storage_path(self, file_id: str, filename: str) -> Path:
        """生成存储路径"""
        # 按日期分目录
        date_path = time.strftime("%Y/%m/%d")
        dir_path = self.base_path / date_path
        dir_path.mkdir(parents=True, exist_ok=True)
        
        # 保留原始扩展名
        ext = Path(filename).suffix
        return dir_path / f"{file_id}{ext}"
    
    async def _save_file(self, path: Path, data: bytes):
        """保存文件"""
        async with aiofiles.open(path, 'wb') as f:
            await f.write(data)
    
    async def _extract_media_metadata(self, metadata: FileMetadata, file_path: Path):
        """提取媒体元数据"""
        try:
            if metadata.file_type == FileType.IMAGE:
                with Image.open(file_path) as img:
                    metadata.width, metadata.height = img.size
            
            # 对于音频和视频，这里可以使用ffprobe等工具
            # 目前使用模拟数据
            elif metadata.file_type == FileType.AUDIO:
                metadata.duration = 180.0  # 模拟3分钟
                metadata.sample_rate = 44100
                metadata.channels = 2
                metadata.bitrate = 320
            
            elif metadata.file_type == FileType.VIDEO:
                metadata.width = 1920
                metadata.height = 1080
                metadata.duration = 300.0  # 模拟5分钟
                metadata.bitrate = 5000
                
        except Exception as e:
            self.logger.error(f"元数据提取失败: {e}")
    
    async def _generate_thumbnail(self, metadata: FileMetadata, file_path: Path) -> Optional[str]:
        """生成缩略图"""
        try:
            if metadata.file_type != FileType.IMAGE:
                return None
            
            with Image.open(file_path) as img:
                # 生成中等尺寸缩略图
                thumbnail_size = self.thumbnail_sizes[1]  # 300x300
                
                # 创建缩略图
                img.thumbnail(thumbnail_size, Image.Resampling.LANCZOS)
                
                # 保存缩略图
                thumbnail_path = file_path.parent / f"thumb_{file_path.name}"
                img.save(thumbnail_path, optimize=True, quality=self.compression_quality)
                
                # 生成URL
                return await self._path_to_url(thumbnail_path)
                
        except Exception as e:
            self.logger.error(f"缩略图生成失败: {e}")
            return None
    
    async def _compress_file(self, metadata: FileMetadata, file_path: Path):
        """压缩文件"""
        try:
            if metadata.file_type == FileType.IMAGE:
                with Image.open(file_path) as img:
                    # 压缩图像
                    compressed_path = file_path.parent / f"compressed_{file_path.name}"
                    img.save(compressed_path, optimize=True, quality=self.compression_quality)
                    
                    # 如果压缩后文件更小，替换原文件
                    if compressed_path.stat().st_size < file_path.stat().st_size:
                        compressed_path.replace(file_path)
                        metadata.size = file_path.stat().st_size
                    else:
                        compressed_path.unlink()
                        
        except Exception as e:
            self.logger.error(f"文件压缩失败: {e}")
    
    async def _generate_public_url(self, file_id: str, public: bool) -> str:
        """生成公开URL"""
        if self.storage_provider == StorageProvider.LOCAL:
            return f"/files/{file_id}"
        else:
            # 对于云存储，这里应该生成实际的URL
            return f"https://storage.example.com/files/{file_id}"
    
    async def _path_to_url(self, path: Path) -> str:
        """路径转URL"""
        relative_path = path.relative_to(self.base_path)
        return f"/files/{relative_path}"
    
    def _url_to_path(self, url: str) -> str:
        """URL转路径"""
        # 简单实现，实际应该更复杂
        filename = url.split("/")[-1]
        return str(self.base_path / filename)
    
    async def _security_check(self, file_data: bytes, mime_type: str):
        """安全检查"""
        # 检查文件头是否与MIME类型匹配
        file_signatures = {
            "image/jpeg": [b"\xff\xd8\xff"],
            "image/png": [b"\x89PNG\r\n\x1a\n"],
            "image/gif": [b"GIF87a", b"GIF89a"],
            "application/pdf": [b"%PDF"],
        }
        
        if mime_type in file_signatures:
            signatures = file_signatures[mime_type]
            if not any(file_data.startswith(sig) for sig in signatures):
                raise InvalidRequestError("文件内容与类型不匹配")
        
        # 检查恶意内容（简单实现）
        malicious_patterns = [b"<script", b"javascript:", b"vbscript:"]
        for pattern in malicious_patterns:
            if pattern in file_data.lower():
                raise InvalidRequestError("检测到潜在恶意内容")
