"""
多模态AI服务模块

提供图像生成、语音处理、图像理解、文件管理等多模态AI功能
"""

from .image_enhanced import (
    EnhancedImageGenerationService,
    ImageEditRequest,
    ImageStyleTransferRequest,
    ImageUpscaleRequest
)
from .speech_service import (
    SpeechService,
    TTSRequest,
    STTRequest,
    VoiceCloneRequest,
    AudioFormat,
    Language,
    VoiceType
)
from .vision_service import (
    VisionService,
    VisionRequest,
    VisionTask,
    DetailLevel,
    BoundingBox,
    DetectedObject,
    OCRResult
)
from .file_manager import (
    MultimodalFileManager,
    FileUploadRequest,
    FileUploadResponse,
    FileMetadata,
    FileType,
    StorageProvider
)

__all__ = [
    # 增强图像生成
    "EnhancedImageGenerationService",
    "ImageEditRequest",
    "ImageStyleTransferRequest", 
    "ImageUpscaleRequest",
    
    # 语音处理
    "SpeechService",
    "TTSRequest",
    "STTRequest",
    "VoiceCloneRequest",
    "AudioFormat",
    "Language",
    "VoiceType",
    
    # 图像理解
    "VisionService",
    "VisionRequest",
    "VisionTask",
    "DetailLevel",
    "BoundingBox",
    "DetectedObject",
    "OCRResult",
    
    # 文件管理
    "MultimodalFileManager",
    "FileUploadRequest",
    "FileUploadResponse",
    "FileMetadata",
    "FileType",
    "StorageProvider"
]
