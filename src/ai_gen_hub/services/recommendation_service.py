"""
AI内容个性化推荐服务

提供用户行为分析、内容推荐、偏好学习等功能
"""

import asyncio
from typing import Any, Dict, List, Optional
from uuid import uuid4
from datetime import datetime, timedelta
from collections import defaultdict

from ai_gen_hub.core.logging import get_logger
from ai_gen_hub.core.exceptions import AIGenHubException
from ai_gen_hub.core.recommendation.models import (
    UserBehavior,
    ContentFeature,
    UserPreference,
    UserProfile,
    RecommendationItem,
    RecommendationRequest,
    RecommendationResponse,
    RecommendationFeedback,
    TrendingContent,
    UserActionType,
    ContentCategory,
    RecommendationType,
    PreferenceType,
    GetRecommendationsRequest,
    RecordBehaviorRequest,
    UpdatePreferencesRequest,
    GetTrendingRequest,
    RecommendationStatsResponse
)
from ai_gen_hub.core.recommendation.algorithms import RecommendationEngine
from ai_gen_hub.core.recommendation.behavior_analyzer import BehaviorAnalyzer


class RecommendationService:
    """个性化推荐服务"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # 核心组件
        self.recommendation_engine = RecommendationEngine()
        self.behavior_analyzer = BehaviorAnalyzer()
        
        # 数据存储（生产环境应使用数据库）
        self.user_behaviors: List[UserBehavior] = []
        self.content_features: Dict[str, ContentFeature] = {}
        self.user_profiles: Dict[str, UserProfile] = {}
        self.trending_contents: List[TrendingContent] = []
        self.recommendation_feedbacks: List[RecommendationFeedback] = []
        
        # 缓存
        self.recommendation_cache: Dict[str, RecommendationResponse] = {}
        self.cache_ttl = 3600  # 1小时缓存
        
        # 启动后台任务
        self._start_background_tasks()
    
    def _start_background_tasks(self):
        """启动后台任务"""
        asyncio.create_task(self._update_user_profiles_task())
        asyncio.create_task(self._update_trending_contents_task())
        asyncio.create_task(self._cleanup_cache_task())
    
    # 用户行为管理
    async def record_behavior(self, request: RecordBehaviorRequest, user_id: str = None) -> bool:
        """记录用户行为"""
        try:
            behavior = UserBehavior(
                behavior_id=str(uuid4()),
                user_id=request.user_id,
                content_id=request.content_id,
                action_type=UserActionType(request.action_type),
                duration=request.duration,
                metadata=request.metadata
            )
            
            self.user_behaviors.append(behavior)
            
            # 清除相关缓存
            self._invalidate_user_cache(request.user_id)
            
            self.logger.info(f"记录用户行为: {request.user_id} -> {request.action_type} -> {request.content_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"记录用户行为失败: {e}")
            return False
    
    async def get_user_behaviors(self, user_id: str, limit: int = 100) -> List[UserBehavior]:
        """获取用户行为历史"""
        user_behaviors = [b for b in self.user_behaviors if b.user_id == user_id]
        user_behaviors.sort(key=lambda x: x.timestamp, reverse=True)
        return user_behaviors[:limit]
    
    # 内容特征管理
    async def register_content(self, content_feature: ContentFeature) -> bool:
        """注册内容特征"""
        try:
            self.content_features[content_feature.content_id] = content_feature
            
            self.logger.info(f"注册内容特征: {content_feature.title} ({content_feature.content_id})")
            return True
            
        except Exception as e:
            self.logger.error(f"注册内容特征失败: {e}")
            return False
    
    async def update_content_stats(self, content_id: str, action_type: UserActionType) -> bool:
        """更新内容统计"""
        content = self.content_features.get(content_id)
        if not content:
            return False
        
        # 更新统计计数
        if action_type == UserActionType.VIEW:
            content.view_count += 1
        elif action_type == UserActionType.LIKE:
            content.like_count += 1
        elif action_type == UserActionType.SHARE:
            content.share_count += 1
        elif action_type == UserActionType.COMMENT:
            content.comment_count += 1
        elif action_type == UserActionType.DOWNLOAD:
            content.download_count += 1
        
        content.updated_at = datetime.now()
        return True
    
    # 用户画像管理
    async def get_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """获取用户画像"""
        profile = self.user_profiles.get(user_id)
        if not profile:
            # 创建新的用户画像
            profile = UserProfile(user_id=user_id)
            self.user_profiles[user_id] = profile
        
        return profile
    
    async def update_user_preferences(self, request: UpdatePreferencesRequest, user_id: str = None) -> bool:
        """更新用户偏好"""
        try:
            profile = await self.get_user_profile(request.user_id)
            
            if not profile.preferences:
                profile.preferences = UserPreference(
                    user_id=request.user_id,
                    preference_type=PreferenceType.EXPLICIT
                )
            
            # 更新偏好设置
            if request.category_preferences:
                for category_str, score in request.category_preferences.items():
                    try:
                        category = ContentCategory(category_str)
                        profile.preferences.category_preferences[category] = score
                    except ValueError:
                        continue
            
            if request.tag_preferences:
                profile.preferences.tag_preferences.update(request.tag_preferences)
            
            if request.complexity_preference is not None:
                profile.preferences.complexity_preference = request.complexity_preference
            
            if request.quality_threshold is not None:
                profile.preferences.quality_threshold = request.quality_threshold
            
            profile.preferences.last_updated = datetime.now()
            profile.updated_at = datetime.now()
            
            # 清除缓存
            self._invalidate_user_cache(request.user_id)
            
            self.logger.info(f"更新用户偏好: {request.user_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新用户偏好失败: {e}")
            return False
    
    # 推荐生成
    async def get_recommendations(self, request: GetRecommendationsRequest, user_id: str = None) -> RecommendationResponse:
        """获取个性化推荐"""
        request_id = str(uuid4())
        
        try:
            # 检查缓存
            cache_key = f"{request.user_id}_{hash(str(request))}"
            cached_response = self.recommendation_cache.get(cache_key)
            if cached_response and self._is_cache_valid(cached_response):
                self.logger.info(f"返回缓存推荐: {request.user_id}")
                return cached_response
            
            # 获取用户画像
            user_profile = await self.get_user_profile(request.user_id)
            
            # 获取用户行为
            user_behaviors = await self.get_user_behaviors(request.user_id, 1000)
            
            # 构建推荐请求
            rec_request = RecommendationRequest(
                user_id=request.user_id,
                count=request.count,
                recommendation_types=[RecommendationType(t) for t in request.recommendation_types] if request.recommendation_types else [RecommendationType.HYBRID],
                categories=[ContentCategory(c) for c in request.categories] if request.categories else None,
                context=request.context
            )
            
            # 生成推荐
            start_time = datetime.now()
            
            recommendations = self.recommendation_engine.get_recommendations(
                user_profile=user_profile,
                user_behaviors=user_behaviors,
                content_features=list(self.content_features.values()),
                trending_contents=self.trending_contents,
                request=rec_request
            )
            
            computation_time = (datetime.now() - start_time).total_seconds()
            
            # 构建响应
            response = RecommendationResponse(
                user_id=request.user_id,
                request_id=request_id,
                recommendations=recommendations,
                total_candidates=len(self.content_features),
                filtered_count=len(recommendations),
                algorithms_used=["hybrid"],
                computation_time=computation_time
            )
            
            # 缓存结果
            self.recommendation_cache[cache_key] = response
            
            self.logger.info(f"生成推荐: {request.user_id}, 数量: {len(recommendations)}, 耗时: {computation_time:.3f}s")
            return response
            
        except Exception as e:
            self.logger.error(f"生成推荐失败: {e}")
            return RecommendationResponse(
                user_id=request.user_id,
                request_id=request_id,
                recommendations=[]
            )
    
    async def record_recommendation_feedback(
        self, 
        user_id: str,
        content_id: str,
        recommendation_id: str,
        feedback_type: str,
        rating: Optional[float] = None,
        metadata: Dict[str, Any] = None
    ) -> bool:
        """记录推荐反馈"""
        try:
            feedback = RecommendationFeedback(
                feedback_id=str(uuid4()),
                user_id=user_id,
                content_id=content_id,
                recommendation_id=recommendation_id,
                feedback_type=UserActionType(feedback_type),
                rating=rating,
                metadata=metadata or {}
            )
            
            self.recommendation_feedbacks.append(feedback)
            
            # 同时记录为用户行为
            await self.record_behavior(RecordBehaviorRequest(
                user_id=user_id,
                content_id=content_id,
                action_type=feedback_type,
                metadata=metadata or {}
            ))
            
            self.logger.info(f"记录推荐反馈: {user_id} -> {feedback_type} -> {content_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"记录推荐反馈失败: {e}")
            return False
    
    # 热门内容
    async def get_trending_content(self, request: GetTrendingRequest) -> List[TrendingContent]:
        """获取热门内容"""
        trending = self.trending_contents.copy()
        
        # 过滤条件
        if request.category:
            try:
                category = ContentCategory(request.category)
                trending = [t for t in trending if t.content_feature.category == category]
            except ValueError:
                pass
        
        # 按热度排序
        trending.sort(key=lambda x: x.trending_score, reverse=True)
        
        return trending[:request.count]
    
    # 相似内容推荐
    async def get_similar_content(self, content_id: str, count: int = 10) -> List[RecommendationItem]:
        """获取相似内容推荐"""
        target_content = self.content_features.get(content_id)
        if not target_content:
            return []
        
        similar_items = []
        
        for other_content in self.content_features.values():
            if other_content.content_id == content_id:
                continue
            
            # 计算相似度
            similarity = self.recommendation_engine.calculate_similarity(target_content, other_content)
            
            if similarity > 0.3:  # 相似度阈值
                item = RecommendationItem(
                    content_id=other_content.content_id,
                    content_feature=other_content,
                    score=similarity,
                    recommendation_type=RecommendationType.CONTENT_BASED,
                    reason=f"与《{target_content.title}》相似",
                    reason_details={"similarity_score": similarity}
                )
                similar_items.append(item)
        
        # 按相似度排序
        similar_items.sort(key=lambda x: x.score, reverse=True)
        
        return similar_items[:count]
    
    # 统计和分析
    async def get_recommendation_stats(self, user_id: str = None) -> RecommendationStatsResponse:
        """获取推荐统计"""
        if user_id:
            # 用户级统计
            user_feedbacks = [f for f in self.recommendation_feedbacks if f.user_id == user_id]
            user_behaviors = [b for b in self.user_behaviors if b.user_id == user_id]
            
            total_recommendations = len(user_feedbacks)
            total_feedback = len(user_feedbacks)
            
            ratings = [f.rating for f in user_feedbacks if f.rating is not None]
            avg_rating = sum(ratings) / len(ratings) if ratings else 0.0
            
            clicks = len([f for f in user_feedbacks if f.feedback_type == UserActionType.VIEW])
            click_through_rate = clicks / total_recommendations if total_recommendations > 0 else 0.0
            
        else:
            # 全局统计
            total_recommendations = len(self.recommendation_feedbacks)
            total_feedback = len(self.recommendation_feedbacks)
            
            ratings = [f.rating for f in self.recommendation_feedbacks if f.rating is not None]
            avg_rating = sum(ratings) / len(ratings) if ratings else 0.0
            
            clicks = len([f for f in self.recommendation_feedbacks if f.feedback_type == UserActionType.VIEW])
            click_through_rate = clicks / total_recommendations if total_recommendations > 0 else 0.0
        
        return RecommendationStatsResponse(
            total_recommendations=total_recommendations,
            total_feedback=total_feedback,
            avg_rating=avg_rating,
            click_through_rate=click_through_rate,
            conversion_rate=0.0,  # 需要根据业务定义
            diversity_score=0.0,  # 需要计算
            novelty_score=0.0     # 需要计算
        )
    
    # 后台任务
    async def _update_user_profiles_task(self):
        """更新用户画像任务"""
        while True:
            try:
                await asyncio.sleep(3600)  # 每小时更新一次
                
                for user_id, profile in self.user_profiles.items():
                    user_behaviors = [b for b in self.user_behaviors if b.user_id == user_id]
                    
                    if user_behaviors:
                        updated_profile = self.behavior_analyzer.update_user_profile(
                            profile, user_behaviors, self.content_features
                        )
                        self.user_profiles[user_id] = updated_profile
                
                self.logger.info("完成用户画像更新")
                
            except Exception as e:
                self.logger.error(f"更新用户画像任务失败: {e}")
    
    async def _update_trending_contents_task(self):
        """更新热门内容任务"""
        while True:
            try:
                await asyncio.sleep(1800)  # 每30分钟更新一次
                
                # 计算热门内容
                self.trending_contents = await self._calculate_trending_contents()
                
                self.logger.info(f"更新热门内容: {len(self.trending_contents)} 个")
                
            except Exception as e:
                self.logger.error(f"更新热门内容任务失败: {e}")
    
    async def _calculate_trending_contents(self) -> List[TrendingContent]:
        """计算热门内容"""
        # 统计最近24小时的行为
        now = datetime.now()
        recent_behaviors = [
            b for b in self.user_behaviors 
            if (now - b.timestamp).total_seconds() < 86400  # 24小时
        ]
        
        # 计算内容热度
        content_scores = defaultdict(lambda: {"views": 0, "likes": 0, "shares": 0, "comments": 0})
        
        for behavior in recent_behaviors:
            scores = content_scores[behavior.content_id]
            
            if behavior.action_type == UserActionType.VIEW:
                scores["views"] += 1
            elif behavior.action_type == UserActionType.LIKE:
                scores["likes"] += 1
            elif behavior.action_type == UserActionType.SHARE:
                scores["shares"] += 1
            elif behavior.action_type == UserActionType.COMMENT:
                scores["comments"] += 1
        
        # 计算热度分数
        trending_list = []
        for content_id, scores in content_scores.items():
            content = self.content_features.get(content_id)
            if not content:
                continue
            
            # 热度计算公式
            trending_score = (
                scores["views"] * 1.0 +
                scores["likes"] * 5.0 +
                scores["shares"] * 3.0 +
                scores["comments"] * 2.0
            )
            
            if trending_score > 0:
                trending = TrendingContent(
                    content_id=content_id,
                    content_feature=content,
                    trending_score=trending_score,
                    recent_views=scores["views"],
                    recent_likes=scores["likes"],
                    recent_shares=scores["shares"],
                    recent_comments=scores["comments"]
                )
                trending_list.append(trending)
        
        # 排序并设置排名
        trending_list.sort(key=lambda x: x.trending_score, reverse=True)
        for i, trending in enumerate(trending_list):
            trending.rank = i + 1
        
        return trending_list[:100]  # 返回前100个
    
    async def _cleanup_cache_task(self):
        """清理缓存任务"""
        while True:
            try:
                await asyncio.sleep(1800)  # 每30分钟清理一次
                
                now = datetime.now()
                expired_keys = []
                
                for key, response in self.recommendation_cache.items():
                    if (now - response.timestamp).total_seconds() > self.cache_ttl:
                        expired_keys.append(key)
                
                for key in expired_keys:
                    del self.recommendation_cache[key]
                
                if expired_keys:
                    self.logger.info(f"清理过期缓存: {len(expired_keys)} 个")
                
            except Exception as e:
                self.logger.error(f"清理缓存任务失败: {e}")
    
    def _invalidate_user_cache(self, user_id: str):
        """清除用户相关缓存"""
        keys_to_remove = [key for key in self.recommendation_cache.keys() if user_id in key]
        for key in keys_to_remove:
            del self.recommendation_cache[key]
    
    def _is_cache_valid(self, response: RecommendationResponse) -> bool:
        """检查缓存是否有效"""
        return (datetime.now() - response.timestamp).total_seconds() < self.cache_ttl
