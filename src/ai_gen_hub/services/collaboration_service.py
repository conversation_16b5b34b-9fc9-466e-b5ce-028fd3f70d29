"""
实时协作服务

提供协作会话管理、内容共享、版本控制等功能
"""

import asyncio
from typing import Any, Dict, List, Optional
from uuid import uuid4
from datetime import datetime, timedelta

from ai_gen_hub.core.logging import get_logger
from ai_gen_hub.core.exceptions import AIGenHubException
from ai_gen_hub.core.collaboration.models import (
    CollaborationSession,
    SharedContent,
    UserPresence,
    RealtimeOperation,
    ContentVersion,
    Comment,
    CollaborationPermission,
    CollaborationRole,
    SessionStatus,
    ContentType,
    ShareScope,
    OperationType,
    PresenceStatus,
    CreateSessionRequest,
    JoinSessionRequest,
    ShareContentRequest,
    OperationRequest
)
from ai_gen_hub.core.collaboration.websocket_manager import WebSocketManager
from ai_gen_hub.core.collaboration.operation_transform import OperationTransformEngine


class CollaborationService:
    """实时协作服务"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # 核心组件
        self.websocket_manager = WebSocketManager()
        self.operation_engine = OperationTransformEngine()
        
        # 数据存储（生产环境应使用数据库）
        self.sessions: Dict[str, CollaborationSession] = {}
        self.shared_contents: Dict[str, SharedContent] = {}
        self.comments: Dict[str, List[Comment]] = {}
        
        # 操作队列和处理
        self.operation_queue: asyncio.Queue = asyncio.Queue()
        self._start_operation_processor()
    
    def _start_operation_processor(self):
        """启动操作处理器"""
        asyncio.create_task(self._process_operations())
    
    # 会话管理
    async def create_session(self, request: CreateSessionRequest, user_id: str) -> CollaborationSession:
        """创建协作会话"""
        session_id = str(uuid4())
        
        # 检查内容是否存在
        content = self.shared_contents.get(request.content_id)
        if not content:
            raise AIGenHubException(f"内容不存在: {request.content_id}")
        
        # 检查权限
        if not content.has_permission(user_id, "read"):
            raise AIGenHubException("没有权限访问此内容")
        
        # 创建会话
        session = CollaborationSession(
            session_id=session_id,
            content_id=request.content_id,
            title=request.title,
            description=request.description,
            created_by=user_id,
            max_participants=request.max_participants,
            allow_anonymous=request.allow_anonymous,
            require_approval=request.require_approval
        )
        
        self.sessions[session_id] = session
        
        self.logger.info(f"创建协作会话: {session.title} ({session_id})")
        return session
    
    async def join_session(self, request: JoinSessionRequest) -> bool:
        """加入协作会话"""
        session = self.sessions.get(request.session_id)
        if not session:
            raise AIGenHubException(f"会话不存在: {request.session_id}")
        
        if session.status != SessionStatus.ACTIVE:
            raise AIGenHubException(f"会话状态不允许加入: {session.status}")
        
        # 检查内容权限
        content = self.shared_contents.get(session.content_id)
        if content and not content.has_permission(request.user_id, "read"):
            raise AIGenHubException("没有权限访问此内容")
        
        # 创建用户在线状态
        user_presence = UserPresence(
            user_id=request.user_id,
            username=request.username,
            avatar_url=request.avatar_url,
            status=PresenceStatus.ONLINE
        )
        
        # 添加到会话
        success = session.add_participant(user_presence)
        if not success:
            raise AIGenHubException("会话已满，无法加入")
        
        session.last_activity_at = datetime.now()
        
        self.logger.info(f"用户加入会话: {request.username} -> {session.title}")
        return True
    
    async def leave_session(self, session_id: str, user_id: str) -> bool:
        """离开协作会话"""
        session = self.sessions.get(session_id)
        if not session:
            return False
        
        success = session.remove_participant(user_id)
        if success:
            session.last_activity_at = datetime.now()
            self.logger.info(f"用户离开会话: {user_id} <- {session.title}")
        
        return success
    
    async def get_session(self, session_id: str, user_id: str = None) -> Optional[CollaborationSession]:
        """获取协作会话"""
        session = self.sessions.get(session_id)
        if not session:
            return None
        
        # 检查权限
        if user_id:
            content = self.shared_contents.get(session.content_id)
            if content and not content.has_permission(user_id, "read"):
                raise AIGenHubException("没有权限访问此会话")
        
        return session
    
    # 内容共享
    async def create_shared_content(
        self, 
        title: str, 
        content_type: ContentType, 
        content_data: str, 
        owner_id: str,
        share_scope: ShareScope = ShareScope.PRIVATE
    ) -> SharedContent:
        """创建共享内容"""
        content_id = str(uuid4())
        
        content = SharedContent(
            content_id=content_id,
            title=title,
            content_type=content_type,
            content_data=content_data,
            owner_id=owner_id,
            share_scope=share_scope
        )
        
        # 创建初始版本
        initial_version = ContentVersion(
            version_id=str(uuid4()),
            content_id=content_id,
            version_number=1,
            title="初始版本",
            content_snapshot=content_data,
            author_id=owner_id,
            commit_message="创建内容"
        )
        
        content.versions.append(initial_version)
        self.shared_contents[content_id] = content
        
        self.logger.info(f"创建共享内容: {title} ({content_id})")
        return content
    
    async def share_content(self, request: ShareContentRequest, user_id: str) -> SharedContent:
        """共享内容"""
        content = self.shared_contents.get(request.content_id)
        if not content:
            raise AIGenHubException(f"内容不存在: {request.content_id}")
        
        # 检查权限
        if not content.has_permission(user_id, "share") and content.owner_id != user_id:
            raise AIGenHubException("没有权限共享此内容")
        
        # 更新共享设置
        content.share_scope = ShareScope(request.share_scope)
        
        # 添加权限
        for perm_data in request.permissions:
            permission = CollaborationPermission(
                user_id=perm_data["user_id"],
                role=CollaborationRole(perm_data["role"]),
                granted_by=user_id,
                expires_at=request.expires_at
            )
            content.permissions.append(permission)
        
        content.updated_at = datetime.now()
        
        self.logger.info(f"共享内容: {content.title} -> {content.share_scope.value}")
        return content
    
    async def get_shared_content(self, content_id: str, user_id: str = None) -> Optional[SharedContent]:
        """获取共享内容"""
        content = self.shared_contents.get(content_id)
        if not content:
            return None
        
        # 检查权限
        if user_id and not content.has_permission(user_id, "read"):
            if content.share_scope not in [ShareScope.PUBLIC, ShareScope.LINK]:
                raise AIGenHubException("没有权限访问此内容")
        
        # 更新访问统计
        content.last_accessed_at = datetime.now()
        content.view_count += 1
        
        return content
    
    # 实时操作处理
    async def apply_operation(self, request: OperationRequest, user_id: str) -> RealtimeOperation:
        """应用实时操作"""
        session = self.sessions.get(request.session_id)
        if not session:
            raise AIGenHubException(f"会话不存在: {request.session_id}")
        
        # 检查权限
        content = self.shared_contents.get(session.content_id)
        if content and not content.has_permission(user_id, "write"):
            raise AIGenHubException("没有权限编辑此内容")
        
        # 创建操作
        operation = RealtimeOperation(
            operation_id=str(uuid4()),
            session_id=request.session_id,
            user_id=user_id,
            operation_type=OperationType(request.operation_type),
            position=request.position,
            content=request.content,
            length=request.length,
            sequence_number=session.operation_sequence + 1
        )
        
        # 加入操作队列
        await self.operation_queue.put(operation)
        
        return operation
    
    async def _process_operations(self):
        """处理操作队列"""
        while True:
            try:
                operation = await self.operation_queue.get()
                await self._handle_operation(operation)
            except Exception as e:
                self.logger.error(f"处理操作失败: {e}")
    
    async def _handle_operation(self, operation: RealtimeOperation):
        """处理单个操作"""
        session = self.sessions.get(operation.session_id)
        if not session:
            return
        
        content = self.shared_contents.get(session.content_id)
        if not content:
            return
        
        # 操作转换
        transformed_operations = []
        
        # 与未处理的操作进行转换
        for existing_op in reversed(session.operations[-10:]):  # 只考虑最近10个操作
            if existing_op.sequence_number > operation.sequence_number:
                operation, existing_op = self.operation_engine.transform_operations(operation, existing_op)
                transformed_operations.append(existing_op)
        
        # 验证操作
        if not self.operation_engine.validate_operation(content.content_data, operation):
            self.logger.error(f"无效操作: {operation.operation_id}")
            return
        
        # 应用操作到内容
        content.content_data = self.operation_engine.apply_operation(content.content_data, operation)
        content.updated_at = datetime.now()
        content.edit_count += 1
        
        # 更新会话
        session.operations.append(operation)
        session.operation_sequence = operation.sequence_number
        session.last_activity_at = datetime.now()
        
        # 广播操作到其他用户
        await self.websocket_manager.broadcast_to_session(
            operation.session_id,
            {
                "type": "operation_applied",
                "operation": {
                    "operation_id": operation.operation_id,
                    "user_id": operation.user_id,
                    "operation_type": operation.operation_type.value,
                    "position": operation.position,
                    "content": operation.content,
                    "length": operation.length,
                    "sequence_number": operation.sequence_number,
                    "timestamp": operation.timestamp.isoformat()
                },
                "transformed_operations": [
                    {
                        "operation_id": op.operation_id,
                        "position": op.position,
                        "content": op.content,
                        "length": op.length
                    }
                    for op in transformed_operations
                ]
            },
            exclude_connections={f"{operation.user_id}_{operation.session_id}"}
        )
        
        self.logger.info(f"应用操作: {operation.operation_type.value} by {operation.user_id}")
    
    # 版本控制
    async def create_version(
        self, 
        content_id: str, 
        commit_message: str, 
        user_id: str
    ) -> ContentVersion:
        """创建内容版本"""
        content = self.shared_contents.get(content_id)
        if not content:
            raise AIGenHubException(f"内容不存在: {content_id}")
        
        # 检查权限
        if not content.has_permission(user_id, "write"):
            raise AIGenHubException("没有权限创建版本")
        
        # 创建新版本
        version = ContentVersion(
            version_id=str(uuid4()),
            content_id=content_id,
            version_number=content.current_version + 1,
            content_snapshot=content.content_data,
            author_id=user_id,
            commit_message=commit_message
        )
        
        # 计算与上一版本的差异
        if content.versions:
            last_version = content.versions[-1]
            # 这里可以实现差异计算逻辑
            version.changes_count = abs(len(content.content_data) - len(last_version.content_snapshot))
        
        content.versions.append(version)
        content.current_version = version.version_number
        content.updated_at = datetime.now()
        
        self.logger.info(f"创建版本: {content.title} v{version.version_number}")
        return version
    
    async def revert_to_version(
        self, 
        content_id: str, 
        version_number: int, 
        user_id: str
    ) -> bool:
        """回滚到指定版本"""
        content = self.shared_contents.get(content_id)
        if not content:
            raise AIGenHubException(f"内容不存在: {content_id}")
        
        # 检查权限
        if not content.has_permission(user_id, "write"):
            raise AIGenHubException("没有权限回滚版本")
        
        # 查找目标版本
        target_version = None
        for version in content.versions:
            if version.version_number == version_number:
                target_version = version
                break
        
        if not target_version:
            raise AIGenHubException(f"版本不存在: {version_number}")
        
        # 回滚内容
        content.content_data = target_version.content_snapshot
        content.updated_at = datetime.now()
        
        # 创建回滚版本记录
        revert_version = ContentVersion(
            version_id=str(uuid4()),
            content_id=content_id,
            version_number=content.current_version + 1,
            content_snapshot=content.content_data,
            author_id=user_id,
            commit_message=f"回滚到版本 {version_number}"
        )
        
        content.versions.append(revert_version)
        content.current_version = revert_version.version_number
        
        self.logger.info(f"回滚版本: {content.title} -> v{version_number}")
        return True
    
    # 评论功能
    async def add_comment(
        self, 
        content_id: str, 
        text: str, 
        user_id: str,
        position: Optional[int] = None,
        parent_comment_id: Optional[str] = None
    ) -> Comment:
        """添加评论"""
        content = self.shared_contents.get(content_id)
        if not content:
            raise AIGenHubException(f"内容不存在: {content_id}")
        
        # 检查权限
        if not content.has_permission(user_id, "comment"):
            raise AIGenHubException("没有权限添加评论")
        
        comment = Comment(
            comment_id=str(uuid4()),
            content_id=content_id,
            text=text,
            position=position,
            author_id=user_id,
            parent_comment_id=parent_comment_id
        )
        
        if content_id not in self.comments:
            self.comments[content_id] = []
        
        self.comments[content_id].append(comment)
        content.comment_count += 1
        
        self.logger.info(f"添加评论: {content.title} by {user_id}")
        return comment
    
    async def get_comments(self, content_id: str, user_id: str = None) -> List[Comment]:
        """获取评论列表"""
        content = self.shared_contents.get(content_id)
        if not content:
            return []
        
        # 检查权限
        if user_id and not content.has_permission(user_id, "read"):
            raise AIGenHubException("没有权限查看评论")
        
        return self.comments.get(content_id, [])
    
    # 统计和监控
    async def get_collaboration_stats(self) -> Dict[str, Any]:
        """获取协作统计"""
        active_sessions = sum(1 for s in self.sessions.values() if s.status == SessionStatus.ACTIVE)
        total_participants = sum(len(s.participants) for s in self.sessions.values())
        
        return {
            "total_sessions": len(self.sessions),
            "active_sessions": active_sessions,
            "total_participants": total_participants,
            "total_shared_contents": len(self.shared_contents),
            "websocket_stats": self.websocket_manager.get_connection_stats()
        }
