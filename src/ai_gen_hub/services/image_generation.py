"""
AI Gen Hub 图像生成服务

提供统一的图像生成接口，包括：
- 图像生成和处理
- 结果存储管理
- 批量处理支持
- 进度查询功能
"""

import asyncio
import time
from typing import Any, Dict, List, Optional
from uuid import uuid4

from ai_gen_hub.cache import CacheInterface
from ai_gen_hub.config.settings import Settings
from ai_gen_hub.core.exceptions import (
    InvalidRequestError,
    ModelNotSupportedError,
)
from ai_gen_hub.core.interfaces import (
    ImageGenerationRequest,
    ImageGenerationResponse,
    ModelType,
)
from ai_gen_hub.core.logging import LoggerMixin, add_request_context
from ai_gen_hub.monitoring import metrics_collector
from ai_gen_hub.services.provider_manager import AIProviderManager
from ai_gen_hub.services.router import RequestRouter
from ai_gen_hub.utils import (
    RetryManager,
    DEFAULT_RETRY_CONFIG,
    circuit_breaker_manager,
)


class ImageGenerationService(LoggerMixin):
    """图像生成服务"""
    
    def __init__(
        self,
        settings: Settings,
        provider_manager: AIProviderManager,
        router: RequestRouter,
        cache: Optional[CacheInterface] = None
    ):
        """初始化图像生成服务
        
        Args:
            settings: 应用配置
            provider_manager: 供应商管理器
            router: 请求路由器
            cache: 缓存接口
        """
        self.settings = settings
        self.provider_manager = provider_manager
        self.router = router
        self.cache = cache
        
        # 重试管理器
        self.retry_manager = RetryManager(DEFAULT_RETRY_CONFIG)
        
        # 支持的功能
        self.features = {
            "caching": settings.features.enable_caching and cache is not None,
            "load_balancing": settings.features.enable_load_balancing,
            "circuit_breaker": settings.features.enable_circuit_breaker,
            "batch_processing": True,  # 图像生成支持批量处理
        }
        
        self.logger.info(
            "图像生成服务初始化完成",
            features=self.features
        )
    
    async def generate_image(
        self,
        request: ImageGenerationRequest,
        user_id: Optional[str] = None,
        request_id: Optional[str] = None
    ) -> ImageGenerationResponse:
        """生成图像
        
        Args:
            request: 图像生成请求
            user_id: 用户ID
            request_id: 请求ID
            
        Returns:
            图像生成响应
            
        Raises:
            InvalidRequestError: 请求参数无效
            ModelNotSupportedError: 模型不支持
        """
        # 设置请求上下文
        if request_id is None:
            request_id = str(uuid4())
        
        add_request_context(request_id=request_id, user_id=user_id)
        
        # 验证请求
        self._validate_request(request)
        
        # 记录请求开始
        start_time = time.time()
        self.logger.info(
            "开始图像生成",
            prompt=request.prompt[:100] + "..." if len(request.prompt) > 100 else request.prompt,
            model=request.model,
            size=request.size,
            n=request.n,
            quality=request.quality
        )
        
        try:
            # 检查缓存
            if self.features["caching"]:
                cached_response = await self._get_cached_response(request, user_id)
                if cached_response:
                    self.logger.info("返回缓存结果")
                    metrics_collector.record_cache_operation("image_generation", "l1", "hit")
                    return cached_response
                else:
                    metrics_collector.record_cache_operation("image_generation", "l1", "miss")
            
            # 路由到合适的供应商
            provider = await self.router.route_request(
                ModelType.IMAGE_GENERATION,
                request.model,
                request.dict(),
                user_id
            )
            
            # 记录路由开始
            await self.router.record_request_start(provider.name)
            
            # 执行图像生成
            if self.features["circuit_breaker"]:
                # 使用熔断器
                circuit_breaker = circuit_breaker_manager.get_circuit_breaker(
                    f"image_generation_{provider.name}"
                )
                
                response = await circuit_breaker.call(
                    self._generate_image_with_provider,
                    provider,
                    request
                )
            else:
                # 直接调用
                response = await self._generate_image_with_provider(provider, request)
            
            # 处理响应
            processed_response = await self._process_response(
                response, provider.name, start_time, request, user_id
            )
            
            # 缓存响应
            if self.features["caching"]:
                await self._cache_response(request, processed_response, user_id)
            
            return processed_response
            
        except Exception as e:
            # 记录错误
            duration = time.time() - start_time
            
            self.logger.error(
                "图像生成失败",
                error=str(e),
                error_type=type(e).__name__,
                duration=duration
            )
            
            # 记录指标
            metrics_collector.record_request(
                provider_name="unknown",
                model=request.model or "unknown",
                request_type="image_generation",
                duration=duration,
                status="error"
            )
            
            raise
    
    async def generate_images_batch(
        self,
        requests: List[ImageGenerationRequest],
        user_id: Optional[str] = None,
        batch_id: Optional[str] = None
    ) -> List[ImageGenerationResponse]:
        """批量生成图像
        
        Args:
            requests: 图像生成请求列表
            user_id: 用户ID
            batch_id: 批次ID
            
        Returns:
            图像生成响应列表
        """
        if batch_id is None:
            batch_id = str(uuid4())
        
        self.logger.info(
            "开始批量图像生成",
            batch_id=batch_id,
            request_count=len(requests)
        )
        
        # 并发执行所有请求
        tasks = []
        for i, request in enumerate(requests):
            request_id = f"{batch_id}_{i}"
            task = asyncio.create_task(
                self.generate_image(request, user_id, request_id)
            )
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        responses = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(
                    "批量图像生成中的单个请求失败",
                    batch_id=batch_id,
                    request_index=i,
                    error=str(result)
                )
                # 可以选择跳过失败的请求或者抛出异常
                continue
            else:
                responses.append(result)
        
        self.logger.info(
            "批量图像生成完成",
            batch_id=batch_id,
            success_count=len(responses),
            total_count=len(requests)
        )
        
        return responses
    
    def _validate_request(self, request: ImageGenerationRequest) -> None:
        """验证请求参数
        
        Args:
            request: 图像生成请求
            
        Raises:
            InvalidRequestError: 请求参数无效
        """
        # 检查提示词
        if not request.prompt or not request.prompt.strip():
            raise InvalidRequestError("提示词不能为空")
        
        if len(request.prompt) > 4000:  # 大多数供应商的限制
            raise InvalidRequestError("提示词长度不能超过4000字符")
        
        # 检查图像数量
        if request.n < 1 or request.n > 10:
            raise InvalidRequestError("图像数量必须在1-10之间")
        
        # 检查图像尺寸格式
        if request.size:
            if not self._is_valid_size(request.size):
                raise InvalidRequestError(
                    "图像尺寸格式无效，应为 'widthxheight' 格式，如 '1024x1024'"
                )
        
        # 检查质量参数
        if request.quality and request.quality not in ["standard", "hd"]:
            raise InvalidRequestError("质量参数必须是 'standard' 或 'hd'")
        
        # 检查响应格式
        if request.response_format not in ["url", "b64_json"]:
            raise InvalidRequestError("响应格式必须是 'url' 或 'b64_json'")
    
    def _is_valid_size(self, size: str) -> bool:
        """验证图像尺寸格式
        
        Args:
            size: 尺寸字符串
            
        Returns:
            是否有效
        """
        try:
            parts = size.split('x')
            if len(parts) != 2:
                return False
            
            width, height = int(parts[0]), int(parts[1])
            
            # 检查尺寸范围（一般限制）
            if width < 256 or height < 256:
                return False
            
            if width > 2048 or height > 2048:
                return False
            
            return True
            
        except ValueError:
            return False
    
    async def _get_cached_response(
        self,
        request: ImageGenerationRequest,
        user_id: Optional[str]
    ) -> Optional[ImageGenerationResponse]:
        """获取缓存的响应
        
        Args:
            request: 图像生成请求
            user_id: 用户ID
            
        Returns:
            缓存的响应或None
        """
        if not self.cache:
            return None
        
        try:
            # 生成缓存键
            cache_key = self.cache.key_generator.generate_key(
                "image_generation",
                request.model or "default",
                request.dict(),
                user_id
            )
            
            # 获取缓存
            cached_data = await self.cache.get(cache_key)
            
            if cached_data:
                # 反序列化为响应对象
                return ImageGenerationResponse(**cached_data)
            
            return None
            
        except Exception as e:
            self.logger.warning("获取缓存失败", error=str(e))
            return None
    
    async def _cache_response(
        self,
        request: ImageGenerationRequest,
        response: ImageGenerationResponse,
        user_id: Optional[str]
    ) -> None:
        """缓存响应
        
        Args:
            request: 图像生成请求
            response: 图像生成响应
            user_id: 用户ID
        """
        if not self.cache:
            return
        
        try:
            # 生成缓存键
            cache_key = self.cache.key_generator.generate_key(
                "image_generation",
                request.model or "default",
                request.dict(),
                user_id
            )
            
            # 缓存响应（图像生成结果通常缓存时间较长）
            await self.cache.set(
                cache_key,
                response.dict(),
                ttl=self.settings.cache.redis_cache_ttl * 2  # 双倍缓存时间
            )
            
        except Exception as e:
            self.logger.warning("缓存响应失败", error=str(e))
    
    async def _generate_image_with_provider(
        self,
        provider,
        request: ImageGenerationRequest
    ) -> ImageGenerationResponse:
        """使用指定供应商生成图像
        
        Args:
            provider: AI供应商
            request: 图像生成请求
            
        Returns:
            图像生成响应
        """
        # 使用重试机制
        return await self.retry_manager.execute_with_retry(
            provider.generate_image,
            request
        )
    
    async def _process_response(
        self,
        response: ImageGenerationResponse,
        provider_name: str,
        start_time: float,
        request: ImageGenerationRequest,
        user_id: Optional[str]
    ) -> ImageGenerationResponse:
        """处理响应
        
        Args:
            response: 原始响应
            provider_name: 供应商名称
            start_time: 开始时间
            request: 原始请求
            user_id: 用户ID
            
        Returns:
            处理后的响应
        """
        duration = time.time() - start_time
        
        # 更新响应信息
        response.processing_time = duration
        response.request_id = str(uuid4())
        
        # 记录路由结束
        await self.router.record_request_end(
            provider_name, True, duration
        )
        
        # 记录指标
        metrics_collector.record_request(
            provider=provider_name,
            model=request.model or "default",
            request_type="image_generation",
            duration=duration,
            status="success"
        )
        
        self.logger.info(
            "图像生成完成",
            provider=provider_name,
            model=request.model,
            duration=duration,
            image_count=len(response.data)
        )
        
        return response
    
    async def get_supported_models(self) -> Dict[str, List[str]]:
        """获取支持的模型列表
        
        Returns:
            按供应商分组的模型列表
        """
        supported_models = {}
        
        for provider_name in self.provider_manager.get_provider_list():
            provider = await self.provider_manager.get_provider(provider_name)
            if provider and provider.supports_model_type(ModelType.IMAGE_GENERATION):
                provider_info = await provider.get_provider_info()
                # 过滤出图像生成模型 - 扩展关键词以支持更多供应商
                image_keywords = [
                    "dall",           # OpenAI DALL-E 模型
                    "image",          # 通用图像关键词
                    "2.0-flash",      # Gemini 2.0 Flash 系列（支持图像生成）
                    "vision",         # 视觉模型
                    "img",            # 图像缩写
                ]

                image_models = []
                for model in provider_info.models:
                    model_lower = model.lower()
                    # 检查是否包含图像生成关键词
                    if any(keyword in model_lower for keyword in image_keywords):
                        image_models.append(model)
                    # 特殊处理：Gemini 2.0 Flash 系列都支持图像生成
                    elif provider_name == "google_ai" and "gemini-2.0-flash" in model_lower:
                        image_models.append(model)

                if image_models:
                    supported_models[provider_name] = image_models
        
        return supported_models
    
    async def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息
        
        Returns:
            服务统计信息
        """
        return {
            "service": "image_generation",
            "features": self.features,
            "supported_models": await self.get_supported_models(),
            "provider_stats": self.router.get_provider_statistics(),
        }
