"""
企业级部署和集成服务

提供企业配置管理、SSO集成、审计日志、合规性支持等功能
"""

import asyncio
from typing import Any, Dict, List, Optional
from uuid import uuid4
from datetime import datetime, timedelta

from ai_gen_hub.core.logging import get_logger
from ai_gen_hub.core.exceptions import AIGenHubException
from ai_gen_hub.core.enterprise.models import (
    EnterpriseConfig,
    SSOConfiguration,
    AuditLog,
    ComplianceReport,
    DataRetentionPolicy,
    PrivacySettings,
    DeploymentStatus,
    DeploymentType,
    SSOProvider,
    AuditEventType,
    ComplianceStandard,
    SecurityLevel,
    CreateEnterpriseConfigRequest,
    UpdateEnterpriseConfigRequest,
    CreateSSOConfigRequest,
    AuditLogQueryRequest,
    ComplianceReportRequest,
    EnterpriseConfigResponse,
    AuditLogResponse,
    ComplianceReportResponse
)
from ai_gen_hub.core.enterprise.sso_integration import SSOIntegrationService, LDAPIntegration, SSOTokenManager
from ai_gen_hub.core.enterprise.audit_service import AuditService


class EnterpriseService:
    """企业级服务"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # 核心组件
        self.sso_service = SSOIntegrationService()
        self.audit_service = AuditService()
        self.ldap_integration = LDAPIntegration()
        self.token_manager = SSOTokenManager()
        
        # 数据存储（生产环境应使用数据库）
        self.enterprise_configs: Dict[str, EnterpriseConfig] = {}
        self.compliance_reports: Dict[str, ComplianceReport] = {}
        self.retention_policies: Dict[str, DataRetentionPolicy] = {}
        self.privacy_settings: Dict[str, PrivacySettings] = {}
        self.deployment_statuses: Dict[str, DeploymentStatus] = {}
        
        # 启动后台任务
        self._start_background_tasks()
    
    def _start_background_tasks(self):
        """启动后台任务"""
        asyncio.create_task(self._health_check_task())
        asyncio.create_task(self._cleanup_task())
        asyncio.create_task(self._compliance_monitoring_task())
    
    # 企业配置管理
    async def create_enterprise_config(self, request: CreateEnterpriseConfigRequest, admin_user_id: str) -> EnterpriseConfig:
        """创建企业配置"""
        try:
            organization_id = str(uuid4())
            
            config = EnterpriseConfig(
                organization_id=organization_id,
                organization_name=request.organization_name,
                deployment_type=DeploymentType(request.deployment_type),
                domain=request.domain,
                contact_email=request.contact_email,
                security_level=SecurityLevel(request.security_level)
            )
            
            self.enterprise_configs[organization_id] = config
            
            # 注册到审计服务
            self.audit_service.register_enterprise_config(config)
            
            # 记录审计日志
            await self._log_audit_event(
                organization_id=organization_id,
                event_type=AuditEventType.CONFIG_CHANGE,
                event_name="创建企业配置",
                user_id=admin_user_id,
                action="create",
                resource_type="enterprise_config",
                resource_id=organization_id
            )
            
            # 创建默认隐私设置
            await self._create_default_privacy_settings(organization_id)
            
            # 初始化部署状态
            await self._initialize_deployment_status(organization_id, config.deployment_type)
            
            self.logger.info(f"创建企业配置: {config.organization_name} ({organization_id})")
            return config
            
        except Exception as e:
            self.logger.error(f"创建企业配置失败: {e}")
            raise AIGenHubException("创建企业配置失败")
    
    async def update_enterprise_config(self, request: UpdateEnterpriseConfigRequest, admin_user_id: str) -> EnterpriseConfig:
        """更新企业配置"""
        config = self.enterprise_configs.get(request.organization_id)
        if not config:
            raise AIGenHubException("企业配置不存在")
        
        try:
            # 记录旧值
            old_values = {}
            new_values = {}
            
            # 更新配置
            for key, value in request.config_updates.items():
                if hasattr(config, key):
                    old_values[key] = getattr(config, key)
                    setattr(config, key, value)
                    new_values[key] = value
            
            config.updated_at = datetime.now()
            
            # 记录审计日志
            await self._log_audit_event(
                organization_id=request.organization_id,
                event_type=AuditEventType.CONFIG_CHANGE,
                event_name="更新企业配置",
                user_id=admin_user_id,
                action="update",
                resource_type="enterprise_config",
                resource_id=request.organization_id,
                old_values=old_values,
                new_values=new_values
            )
            
            self.logger.info(f"更新企业配置: {config.organization_name}")
            return config
            
        except Exception as e:
            self.logger.error(f"更新企业配置失败: {e}")
            raise AIGenHubException("更新企业配置失败")
    
    async def get_enterprise_config(self, organization_id: str) -> Optional[EnterpriseConfig]:
        """获取企业配置"""
        return self.enterprise_configs.get(organization_id)
    
    # SSO集成管理
    async def create_sso_config(self, request: CreateSSOConfigRequest, admin_user_id: str) -> SSOConfiguration:
        """创建SSO配置"""
        try:
            config = SSOConfiguration(
                config_id=str(uuid4()),
                organization_id=request.organization_id,
                provider=SSOProvider(request.provider),
                name=request.name,
                endpoint_url=request.endpoint_url,
                client_id=request.client_id,
                client_secret=request.client_secret
            )
            
            # 注册到SSO服务
            success = self.sso_service.register_sso_config(config)
            if not success:
                raise AIGenHubException("SSO配置注册失败")
            
            # 记录审计日志
            await self._log_audit_event(
                organization_id=request.organization_id,
                event_type=AuditEventType.CONFIG_CHANGE,
                event_name="创建SSO配置",
                user_id=admin_user_id,
                action="create",
                resource_type="sso_config",
                resource_id=config.config_id
            )
            
            self.logger.info(f"创建SSO配置: {config.name} ({config.provider.value})")
            return config
            
        except Exception as e:
            self.logger.error(f"创建SSO配置失败: {e}")
            raise AIGenHubException("创建SSO配置失败")
    
    async def initiate_sso_login(self, organization_id: str, redirect_url: str = None) -> str:
        """发起SSO登录"""
        try:
            login_url = self.sso_service.generate_sso_login_url(organization_id, redirect_url)
            
            # 记录审计日志
            await self._log_audit_event(
                organization_id=organization_id,
                event_type=AuditEventType.USER_LOGIN,
                event_name="发起SSO登录",
                action="sso_login_initiated",
                metadata={"redirect_url": redirect_url}
            )
            
            return login_url
            
        except Exception as e:
            self.logger.error(f"发起SSO登录失败: {e}")
            raise AIGenHubException("SSO登录失败")
    
    async def process_sso_callback(self, organization_id: str, callback_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理SSO回调"""
        try:
            # 处理SSO回调
            user_info = self.sso_service.process_sso_callback(organization_id, callback_data)
            
            # 创建SSO令牌
            token = self.token_manager.create_sso_token(user_info)
            
            # 记录审计日志
            await self._log_audit_event(
                organization_id=organization_id,
                event_type=AuditEventType.USER_LOGIN,
                event_name="SSO登录成功",
                user_id=user_info.get('email'),
                user_email=user_info.get('email'),
                action="sso_login_success",
                result="success",
                metadata={"sso_provider": user_info.get('sso_provider')}
            )
            
            return {
                "user_info": user_info,
                "token": token,
                "expires_in": 3600
            }
            
        except Exception as e:
            self.logger.error(f"处理SSO回调失败: {e}")
            
            # 记录失败日志
            await self._log_audit_event(
                organization_id=organization_id,
                event_type=AuditEventType.USER_LOGIN,
                event_name="SSO登录失败",
                action="sso_login_failure",
                result="failure",
                error_message=str(e)
            )
            
            raise AIGenHubException("SSO登录失败")
    
    # 审计日志管理
    async def query_audit_logs(self, request: AuditLogQueryRequest) -> AuditLogResponse:
        """查询审计日志"""
        try:
            logs, total_count = self.audit_service.query_logs(request)
            
            has_more = (request.offset + len(logs)) < total_count
            
            return AuditLogResponse(
                logs=logs,
                total_count=total_count,
                has_more=has_more
            )
            
        except Exception as e:
            self.logger.error(f"查询审计日志失败: {e}")
            raise AIGenHubException("查询审计日志失败")
    
    async def export_audit_logs(
        self, 
        organization_id: str, 
        start_time: datetime, 
        end_time: datetime,
        format: str = "json",
        admin_user_id: str = None
    ) -> str:
        """导出审计日志"""
        try:
            exported_data = self.audit_service.export_audit_logs(
                organization_id, start_time, end_time, format
            )
            
            # 记录导出操作
            await self._log_audit_event(
                organization_id=organization_id,
                event_type=AuditEventType.DATA_ACCESS,
                event_name="导出审计日志",
                user_id=admin_user_id,
                action="export",
                resource_type="audit_logs",
                metadata={
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "format": format
                }
            )
            
            return exported_data
            
        except Exception as e:
            self.logger.error(f"导出审计日志失败: {e}")
            raise AIGenHubException("导出审计日志失败")
    
    async def get_audit_statistics(self, organization_id: str, days: int = 30) -> Dict[str, Any]:
        """获取审计统计"""
        try:
            return self.audit_service.get_audit_statistics(organization_id, days)
        except Exception as e:
            self.logger.error(f"获取审计统计失败: {e}")
            return {}
    
    # 合规性管理
    async def generate_compliance_report(self, request: ComplianceReportRequest, admin_user_id: str) -> ComplianceReport:
        """生成合规报告"""
        try:
            report_id = str(uuid4())
            
            # 获取审计数据
            audit_request = AuditLogQueryRequest(
                organization_id=request.organization_id,
                start_time=request.period_start,
                end_time=request.period_end,
                limit=10000
            )
            
            logs, _ = self.audit_service.query_logs(audit_request)
            
            # 分析合规性
            compliance_analysis = await self._analyze_compliance(
                ComplianceStandard(request.standard), 
                logs
            )
            
            # 创建报告
            report = ComplianceReport(
                report_id=report_id,
                organization_id=request.organization_id,
                standard=ComplianceStandard(request.standard),
                title=f"{request.standard.upper()} 合规报告",
                report_period_start=request.period_start,
                report_period_end=request.period_end,
                compliance_score=compliance_analysis['score'],
                total_controls=compliance_analysis['total_controls'],
                compliant_controls=compliance_analysis['compliant_controls'],
                non_compliant_controls=compliance_analysis['non_compliant_controls'],
                control_results=compliance_analysis['control_results'],
                findings=compliance_analysis['findings'],
                recommendations=compliance_analysis['recommendations'],
                generated_by=admin_user_id
            )
            
            self.compliance_reports[report_id] = report
            
            # 记录审计日志
            await self._log_audit_event(
                organization_id=request.organization_id,
                event_type=AuditEventType.COMPLIANCE_EVENT,
                event_name="生成合规报告",
                user_id=admin_user_id,
                action="generate",
                resource_type="compliance_report",
                resource_id=report_id,
                metadata={"standard": request.standard}
            )
            
            self.logger.info(f"生成合规报告: {request.standard} ({report_id})")
            return report
            
        except Exception as e:
            self.logger.error(f"生成合规报告失败: {e}")
            raise AIGenHubException("生成合规报告失败")
    
    async def get_compliance_report(self, report_id: str) -> Optional[ComplianceReport]:
        """获取合规报告"""
        return self.compliance_reports.get(report_id)
    
    # 数据保留和隐私
    async def create_retention_policy(self, organization_id: str, policy_data: Dict[str, Any], admin_user_id: str) -> DataRetentionPolicy:
        """创建数据保留策略"""
        try:
            policy = DataRetentionPolicy(
                policy_id=str(uuid4()),
                organization_id=organization_id,
                **policy_data
            )
            
            self.retention_policies[policy.policy_id] = policy
            
            # 记录审计日志
            await self._log_audit_event(
                organization_id=organization_id,
                event_type=AuditEventType.CONFIG_CHANGE,
                event_name="创建数据保留策略",
                user_id=admin_user_id,
                action="create",
                resource_type="retention_policy",
                resource_id=policy.policy_id
            )
            
            self.logger.info(f"创建数据保留策略: {policy.name}")
            return policy
            
        except Exception as e:
            self.logger.error(f"创建数据保留策略失败: {e}")
            raise AIGenHubException("创建数据保留策略失败")
    
    async def update_privacy_settings(self, organization_id: str, settings_data: Dict[str, Any], admin_user_id: str) -> PrivacySettings:
        """更新隐私设置"""
        try:
            settings = self.privacy_settings.get(organization_id)
            if not settings:
                settings = PrivacySettings(organization_id=organization_id)
            
            # 更新设置
            for key, value in settings_data.items():
                if hasattr(settings, key):
                    setattr(settings, key, value)
            
            settings.updated_at = datetime.now()
            self.privacy_settings[organization_id] = settings
            
            # 记录审计日志
            await self._log_audit_event(
                organization_id=organization_id,
                event_type=AuditEventType.CONFIG_CHANGE,
                event_name="更新隐私设置",
                user_id=admin_user_id,
                action="update",
                resource_type="privacy_settings",
                resource_id=organization_id
            )
            
            self.logger.info(f"更新隐私设置: {organization_id}")
            return settings
            
        except Exception as e:
            self.logger.error(f"更新隐私设置失败: {e}")
            raise AIGenHubException("更新隐私设置失败")
    
    # 部署状态管理
    async def get_deployment_status(self, organization_id: str) -> Optional[DeploymentStatus]:
        """获取部署状态"""
        return self.deployment_statuses.get(organization_id)
    
    async def update_deployment_status(self, organization_id: str, status_data: Dict[str, Any]):
        """更新部署状态"""
        status = self.deployment_statuses.get(organization_id)
        if status:
            for key, value in status_data.items():
                if hasattr(status, key):
                    setattr(status, key, value)
    
    # 私有方法
    async def _log_audit_event(self, **kwargs):
        """记录审计事件"""
        self.audit_service.log_event(**kwargs)
    
    async def _create_default_privacy_settings(self, organization_id: str):
        """创建默认隐私设置"""
        settings = PrivacySettings(organization_id=organization_id)
        self.privacy_settings[organization_id] = settings
    
    async def _initialize_deployment_status(self, organization_id: str, deployment_type: DeploymentType):
        """初始化部署状态"""
        status = DeploymentStatus(
            organization_id=organization_id,
            deployment_type=deployment_type,
            current_version="1.0.0",
            services_status={
                "api": "active",
                "web": "active",
                "database": "active",
                "cache": "active"
            }
        )
        self.deployment_statuses[organization_id] = status
    
    async def _analyze_compliance(self, standard: ComplianceStandard, logs: List[AuditLog]) -> Dict[str, Any]:
        """分析合规性"""
        # 简化的合规性分析
        analysis = {
            'score': 85.0,
            'total_controls': 20,
            'compliant_controls': 17,
            'non_compliant_controls': 3,
            'control_results': [],
            'findings': [],
            'recommendations': []
        }
        
        # 基于标准的具体分析
        if standard == ComplianceStandard.GDPR:
            analysis.update(self._analyze_gdpr_compliance(logs))
        elif standard == ComplianceStandard.SOC2:
            analysis.update(self._analyze_soc2_compliance(logs))
        
        return analysis
    
    def _analyze_gdpr_compliance(self, logs: List[AuditLog]) -> Dict[str, Any]:
        """分析GDPR合规性"""
        return {
            'findings': [
                {
                    'control': 'Data Access Logging',
                    'status': 'compliant',
                    'description': '数据访问日志记录完整'
                },
                {
                    'control': 'User Consent Management',
                    'status': 'non_compliant',
                    'description': '缺少用户同意管理记录'
                }
            ],
            'recommendations': [
                {
                    'priority': 'high',
                    'description': '实施用户同意管理系统',
                    'timeline': '30天'
                }
            ]
        }
    
    def _analyze_soc2_compliance(self, logs: List[AuditLog]) -> Dict[str, Any]:
        """分析SOC2合规性"""
        return {
            'findings': [
                {
                    'control': 'Access Control',
                    'status': 'compliant',
                    'description': '访问控制措施有效'
                }
            ],
            'recommendations': []
        }
    
    # 后台任务
    async def _health_check_task(self):
        """健康检查任务"""
        while True:
            try:
                await asyncio.sleep(300)  # 每5分钟检查一次
                
                for org_id, status in self.deployment_statuses.items():
                    # 模拟健康检查
                    status.health_score = 95.0 + (5.0 * (0.5 - hash(org_id) % 100 / 100))
                    status.last_health_check = datetime.now()
                    status.uptime_seconds += 300
                
            except Exception as e:
                self.logger.error(f"健康检查任务失败: {e}")
    
    async def _cleanup_task(self):
        """清理任务"""
        while True:
            try:
                await asyncio.sleep(3600)  # 每小时清理一次
                
                # 清理过期令牌
                self.token_manager.cleanup_expired_tokens()
                
                # 清理过期审计日志
                for org_id in self.enterprise_configs.keys():
                    self.audit_service.cleanup_old_logs(org_id)
                
            except Exception as e:
                self.logger.error(f"清理任务失败: {e}")
    
    async def _compliance_monitoring_task(self):
        """合规性监控任务"""
        while True:
            try:
                await asyncio.sleep(86400)  # 每天检查一次
                
                # 检查合规性状态
                for org_id, config in self.enterprise_configs.items():
                    if config.compliance_standards:
                        # 这里可以实施自动合规性检查
                        pass
                
            except Exception as e:
                self.logger.error(f"合规性监控任务失败: {e}")
