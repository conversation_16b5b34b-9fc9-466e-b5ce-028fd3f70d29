"""
Cohere 供应商适配器 - 基于最新 Cohere API 规范

实现 Cohere API 的完整适配，支持 Command R+ 和 Command R 系列模型。

主要功能：
- 文本生成：支持 Command R+、Command R、Command、Command Light 等模型
- 对话模式：支持多轮对话和系统消息
- 流式输出：实时流式响应支持，包括 SSE 格式处理
- 工具调用：支持 RAG 和函数调用功能
- 错误处理：完善的错误处理和重试机制

支持的模型系列：
- Command R+：最强大的模型，适合复杂任务
- Command R：平衡性能和成本的选择
- Command：基础对话模型
- Command Light：轻量级快速模型

免费额度：每月1000次API调用

更新日期：2025-08-14
API 版本：v1
"""

import json
import time
from typing import Any, AsyncIterator, Dict, List, Optional, Union
from uuid import uuid4

from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.core.interfaces import (
    ImageGenerationRequest,
    ImageGenerationResponse,
    Message,
    MessageRole,
    ModelType,
    TextGenerationChoice,
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
    Usage,
)
from ai_gen_hub.providers.base import BaseProvider
from ai_gen_hub.utils.key_manager import KeyManager
from ai_gen_hub.core.compatibility import ProviderCompatibilityManager, ProviderType


class CohereProvider(BaseProvider):
    """Cohere 供应商适配器

    基于最新 Cohere API 规范的完整实现，支持：

    核心功能：
    - 最新模型支持：Command R+、Command R、Command、Command Light
    - 对话处理：多轮对话和系统消息支持
    - 流式响应：实时 SSE 流式输出
    - 工具调用：RAG 和函数调用功能
    - 错误处理：完善的错误处理机制

    模型映射：
    - 自动将通用模型名称映射到 Cohere 特定模型
    - 支持性能优化的模型选择（fast/lite 别名）
    - 向后兼容旧模型名称

    配置选项：
    - temperature：控制输出随机性
    - max_tokens：限制输出长度
    - tools：工具调用配置
    """

    def __init__(self, config: ProviderConfig, key_manager: KeyManager):
        """初始化 Cohere 适配器

        Args:
            config: Cohere 配置对象，包含 API 设置
            key_manager: 密钥管理器，用于 API 密钥的安全管理
        """
        super().__init__("cohere", config, key_manager)

        # 设置 Cohere API 基础 URL
        self.base_url = "https://api.cohere.ai/v1"

        # 当前支持的模型类型
        self._supported_model_types = [
            ModelType.TEXT_GENERATION,  # 文本生成
        ]
        
        # 支持的模型列表 - 基于最新的 Cohere API 规范
        self._supported_models = [
            # Command R 系列 - 最新的高性能模型
            "command-r-plus",                    # 最强大的模型，适合复杂推理
            "command-r",                         # 平衡性能和成本的最佳选择
            
            # Command 系列 - 基础模型
            "command",                           # 标准对话模型
            "command-light",                     # 轻量级快速模型
            
            # 向后兼容的旧模型名称
            "command-xlarge",                    # 映射到 command-r-plus
            "command-medium",                    # 映射到 command-r
            "command-light-nightly",             # 映射到 command-light
        ]

        # 初始化兼容性管理器
        self._compatibility_manager = ProviderCompatibilityManager()

        # 模型映射 - 将旧模型名称映射到最新的对应模型
        self._model_mapping = {
            # 通用别名映射到最新的推荐模型
            "cohere-latest": "command-r-plus",        # 默认推荐最强模型
            "cohere": "command-r",                    # 简单别名映射到平衡模型
            
            # 旧模型名称映射到新模型（保持向后兼容）
            "command-xlarge": "command-r-plus",       # 大模型映射到 R+
            "command-medium": "command-r",            # 中等模型映射到 R
            "command-light-nightly": "command-light", # 夜间版本映射到正式版
            
            # 性能优化映射
            "cohere-fast": "command-r",               # 快速模型别名
            "cohere-lite": "command-light",           # 轻量级模型别名
            "cohere-best": "command-r-plus",          # 最佳模型别名
        }
    
    async def _perform_health_check(self, api_key: str) -> bool:
        """执行 API 健康检查

        通过发送一个简单的生成请求来验证 API 密钥的有效性和服务可用性。
        使用轻量级的 command-light 模型来减少配额消耗。

        Args:
            api_key: 要验证的 Cohere API 密钥

        Returns:
            bool: 如果 API 可用且密钥有效则返回 True，否则返回 False
        """
        try:
            # 构建健康检查请求
            url = f"{self.base_url}/chat"
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
            }
            
            # 使用最轻量级的模型进行健康检查
            request_data = {
                "model": "command-light",
                "message": "Hello",
                "max_tokens": 1,
            }
            
            response = await self._make_request("POST", url, headers=headers, json_data=request_data)
            return response.status_code == 200
            
        except Exception as e:
            # 记录健康检查失败的详细信息
            self.logger.warning(f"Cohere API 健康检查失败: {e}")
            return False
    
    async def _generate_text_impl(
        self,
        request: TextGenerationRequest,
        api_key: str
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """实现文本生成的核心逻辑

        根据请求类型（流式或非流式）调用相应的 Cohere API 端点。
        自动处理模型名称映射、请求构建和响应解析。

        Args:
            request: 文本生成请求对象，包含模型、消息、参数等
            api_key: 有效的 Cohere API 密钥

        Returns:
            Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
                非流式请求返回完整响应，流式请求返回响应块的异步迭代器
        """
        # 构建符合 Cohere API 规范的请求数据
        request_data = self._build_chat_request(request)

        # 设置请求头
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }

        if request.stream:
            # 流式生成：使用 chat 端点的流式模式
            url = f"{self.base_url}/chat"
            request_data["stream"] = True
            return self._handle_stream_response(request, url, headers, request_data)
        else:
            # 非流式生成：使用 chat 端点
            url = f"{self.base_url}/chat"
            response = await self._make_request("POST", url, headers=headers, json_data=request_data)
            response_data = response.json()

            # 检查响应中是否有错误
            if "error" in response_data or "message" in response_data:
                error_msg = self._handle_cohere_error(response_data)
                self.logger.error(f"Cohere API 返回错误: {error_msg}")
                # 根据错误类型抛出相应的异常
                from ai_gen_hub.core.exceptions import APIError
                raise APIError(error_msg)

            return self._parse_chat_response(response_data, request)
    
    def _build_chat_request(self, request: TextGenerationRequest) -> Dict[str, Any]:
        """构建聊天请求数据

        根据最新的 Cohere API 规范构建请求，支持：
        - 对话消息处理
        - 系统消息（preamble）
        - 工具调用配置
        - 生成参数设置
        """
        # 映射模型名称（处理别名和向后兼容）
        model_name = self.map_model_name(request.model)
        
        # 分离系统消息和对话消息
        system_messages = []
        conversation_messages = []

        for msg in request.messages:
            if msg.role == MessageRole.SYSTEM:
                system_messages.append(msg)
            else:
                conversation_messages.append(msg)

        # 构建基础请求数据
        request_data = {
            "model": model_name,
        }

        # 添加系统消息（preamble）
        if system_messages:
            # 合并所有系统消息为一个 preamble
            preamble = "\n".join([msg.content for msg in system_messages])
            request_data["preamble"] = preamble

        # 处理对话历史和当前消息
        if conversation_messages:
            # Cohere API 需要分离历史对话和当前消息
            if len(conversation_messages) == 1 and conversation_messages[0].role == MessageRole.USER:
                # 只有一个用户消息
                request_data["message"] = conversation_messages[0].content
            else:
                # 有对话历史
                chat_history = []
                current_message = None
                
                for i, msg in enumerate(conversation_messages):
                    if i == len(conversation_messages) - 1 and msg.role == MessageRole.USER:
                        # 最后一个用户消息作为当前消息
                        current_message = msg.content
                    else:
                        # 其他消息作为历史
                        role = "USER" if msg.role == MessageRole.USER else "CHATBOT"
                        chat_history.append({
                            "role": role,
                            "message": msg.content
                        })
                
                if chat_history:
                    request_data["chat_history"] = chat_history
                
                if current_message:
                    request_data["message"] = current_message
                else:
                    # 如果没有当前用户消息，使用默认消息
                    request_data["message"] = "继续对话"

        # 添加生成配置
        if request.max_tokens is not None:
            request_data["max_tokens"] = request.max_tokens
        if request.temperature is not None:
            request_data["temperature"] = request.temperature
        if request.top_p is not None:
            request_data["p"] = request.top_p
        if request.top_k is not None:
            request_data["k"] = request.top_k
        if request.stop is not None:
            if isinstance(request.stop, str):
                request_data["stop_sequences"] = [request.stop]
            else:
                request_data["stop_sequences"] = request.stop

        # 添加工具配置
        self._add_tools_config(request, request_data)
        
        return request_data

    def _add_tools_config(
        self,
        request: TextGenerationRequest,
        request_data: Dict[str, Any]
    ) -> None:
        """添加工具配置

        支持 Cohere API 的工具集成功能，包括：
        - 函数调用
        - RAG 功能
        - 自定义工具

        Args:
            request: 文本生成请求
            request_data: 请求数据字典
        """
        tools = []

        # 处理传统的 functions 参数（转换为 tools 格式）
        if request.functions:
            for func in request.functions:
                tool = {
                    "name": func.get("name", ""),
                    "description": func.get("description", ""),
                }
                
                # 转换参数定义
                if "parameters" in func:
                    tool["parameter_definitions"] = self._convert_openai_params_to_cohere(func["parameters"])
                
                tools.append(tool)

        # 处理新的 tools 参数
        if request.tools:
            for tool in request.tools:
                converted_tool = self._convert_tool_format(tool)
                if converted_tool:
                    tools.append(converted_tool)

        # 如果有工具，添加到请求中
        if tools:
            request_data["tools"] = tools

    def _convert_openai_params_to_cohere(self, openai_params: Dict[str, Any]) -> Dict[str, Any]:
        """将 OpenAI 格式的参数定义转换为 Cohere 格式

        Args:
            openai_params: OpenAI 格式的参数定义

        Returns:
            Dict[str, Any]: Cohere 格式的参数定义
        """
        cohere_params = {}
        
        if "properties" in openai_params:
            for param_name, param_def in openai_params["properties"].items():
                cohere_param = {
                    "type": param_def.get("type", "string"),
                    "description": param_def.get("description", ""),
                }
                
                # 处理必需参数
                if "required" in openai_params and param_name in openai_params["required"]:
                    cohere_param["required"] = True
                
                # 处理枚举值
                if "enum" in param_def:
                    cohere_param["enum"] = param_def["enum"]
                
                cohere_params[param_name] = cohere_param
        
        return cohere_params

    def _convert_tool_format(self, tool: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """转换工具格式为 Cohere API 支持的格式

        Args:
            tool: 工具定义

        Returns:
            Optional[Dict[str, Any]]: 转换后的工具定义，如果不支持则返回 None
        """
        # 处理函数调用工具
        if "function" in tool:
            func = tool["function"]
            return {
                "name": func.get("name", ""),
                "description": func.get("description", ""),
                "parameter_definitions": self._convert_openai_params_to_cohere(
                    func.get("parameters", {})
                )
            }
        
        # 其他工具类型暂不支持
        else:
            self.logger.warning(f"不支持的工具类型: {tool}")
            return None

    def _parse_chat_response(
        self,
        response_data: Dict[str, Any],
        request: TextGenerationRequest
    ) -> TextGenerationResponse:
        """解析聊天响应数据

        将 Cohere API 的响应格式转换为标准的 TextGenerationResponse 格式。
        支持工具调用、引用和元数据的处理。

        Args:
            response_data: Cohere API 返回的响应数据
            request: 原始请求对象

        Returns:
            TextGenerationResponse: 标准化的响应对象
        """
        # 提取生成的文本内容
        text_content = response_data.get("text", "")

        # 处理工具调用
        tool_calls = []
        if "tool_calls" in response_data:
            for tool_call in response_data["tool_calls"]:
                tool_calls.append({
                    "id": tool_call.get("id", str(uuid4())),
                    "type": "function",
                    "function": {
                        "name": tool_call.get("name", ""),
                        "arguments": json.dumps(tool_call.get("parameters", {}))
                    }
                })

        # 构建消息对象
        message = Message(
            role=MessageRole.ASSISTANT,
            content=text_content,
            tool_calls=tool_calls if tool_calls else None
        )

        # 构建选择对象
        choice = TextGenerationChoice(
            index=0,
            message=message,
            finish_reason=self._map_finish_reason(response_data.get("finish_reason"))
        )

        # 构建使用统计
        usage = Usage(
            prompt_tokens=response_data.get("meta", {}).get("billed_units", {}).get("input_tokens", 0),
            completion_tokens=response_data.get("meta", {}).get("billed_units", {}).get("output_tokens", 0),
            total_tokens=0
        )
        usage.total_tokens = usage.prompt_tokens + usage.completion_tokens

        return TextGenerationResponse(
            id=response_data.get("generation_id", str(uuid4())),
            object="chat.completion",
            created=int(time.time()),
            model=request.model,
            choices=[choice],
            usage=usage
        )

    async def _handle_stream_response(
        self,
        request: TextGenerationRequest,
        url: str,
        headers: Dict[str, str],
        request_data: Dict[str, Any]
    ) -> AsyncIterator[TextGenerationStreamChunk]:
        """处理流式响应

        处理 Cohere API 的 SSE 流式响应，实时返回生成的文本块。
        支持工具调用和完成状态的流式处理。

        Args:
            request: 原始请求对象
            url: API 端点 URL
            headers: 请求头
            request_data: 请求数据

        Yields:
            TextGenerationStreamChunk: 流式响应块
        """
        try:
            async with self.http_client.stream("POST", url, headers=headers, json=request_data) as response:
                if response.status_code != 200:
                    error_data = await response.aread()
                    error_msg = f"Cohere API 流式请求失败: {response.status_code} - {error_data.decode()}"
                    self.logger.error(error_msg)
                    from ai_gen_hub.core.exceptions import APIError
                    raise APIError(error_msg)

                buffer = ""
                async for chunk in response.aiter_text():
                    buffer += chunk

                    # 处理 SSE 格式的数据
                    while "\n\n" in buffer:
                        line, buffer = buffer.split("\n\n", 1)

                        if line.startswith("data: "):
                            data_str = line[6:]  # 移除 "data: " 前缀

                            # 跳过空行和结束标记
                            if not data_str.strip() or data_str.strip() == "[DONE]":
                                continue

                            try:
                                data = json.loads(data_str)
                                stream_chunk = self._parse_stream_chunk(data, request)
                                if stream_chunk:
                                    yield stream_chunk
                            except json.JSONDecodeError as e:
                                self.logger.warning(f"解析流式响应数据失败: {e}, 数据: {data_str}")
                                continue

        except Exception as e:
            self.logger.error(f"处理 Cohere 流式响应时发生错误: {e}")
            from ai_gen_hub.core.exceptions import APIError
            raise APIError(f"流式响应处理失败: {e}")

    def _parse_stream_chunk(
        self,
        chunk_data: Dict[str, Any],
        request: TextGenerationRequest
    ) -> Optional[TextGenerationStreamChunk]:
        """解析流式响应块

        将 Cohere 流式响应块转换为标准格式。

        Args:
            chunk_data: Cohere 流式响应块数据
            request: 原始请求对象

        Returns:
            Optional[TextGenerationStreamChunk]: 标准化的流式响应块，如果无效则返回 None
        """
        # 处理不同类型的流式事件
        event_type = chunk_data.get("event_type", "")

        if event_type == "text-generation":
            # 文本生成事件
            text = chunk_data.get("text", "")
            if text:
                choice = TextGenerationChoice(
                    index=0,
                    delta=Message(role=MessageRole.ASSISTANT, content=text),
                    finish_reason=None
                )

                return TextGenerationStreamChunk(
                    id=chunk_data.get("generation_id", str(uuid4())),
                    object="chat.completion.chunk",
                    created=int(time.time()),
                    model=request.model,
                    choices=[choice]
                )

        elif event_type == "stream-end":
            # 流结束事件
            finish_reason = self._map_finish_reason(chunk_data.get("finish_reason"))
            choice = TextGenerationChoice(
                index=0,
                delta=Message(role=MessageRole.ASSISTANT, content=""),
                finish_reason=finish_reason
            )

            return TextGenerationStreamChunk(
                id=chunk_data.get("generation_id", str(uuid4())),
                object="chat.completion.chunk",
                created=int(time.time()),
                model=request.model,
                choices=[choice]
            )

        elif event_type == "tool-calls-generation":
            # 工具调用事件
            tool_calls = chunk_data.get("tool_calls", [])
            if tool_calls:
                # 转换工具调用格式
                converted_tool_calls = []
                for tool_call in tool_calls:
                    converted_tool_calls.append({
                        "id": tool_call.get("id", str(uuid4())),
                        "type": "function",
                        "function": {
                            "name": tool_call.get("name", ""),
                            "arguments": json.dumps(tool_call.get("parameters", {}))
                        }
                    })

                choice = TextGenerationChoice(
                    index=0,
                    delta=Message(
                        role=MessageRole.ASSISTANT,
                        content="",
                        tool_calls=converted_tool_calls
                    ),
                    finish_reason=None
                )

                return TextGenerationStreamChunk(
                    id=chunk_data.get("generation_id", str(uuid4())),
                    object="chat.completion.chunk",
                    created=int(time.time()),
                    model=request.model,
                    choices=[choice]
                )

        # 其他事件类型暂时忽略
        return None

    def _map_finish_reason(self, cohere_reason: Optional[str]) -> Optional[str]:
        """映射完成原因

        将 Cohere API 的完成原因映射为标准格式。

        Args:
            cohere_reason: Cohere API 的完成原因

        Returns:
            Optional[str]: 标准化的完成原因
        """
        if not cohere_reason:
            return None

        # Cohere 完成原因映射
        reason_mapping = {
            "COMPLETE": "stop",           # 正常完成
            "MAX_TOKENS": "length",       # 达到最大长度
            "STOP_SEQUENCE": "stop",      # 遇到停止序列
            "ERROR": "error",             # 发生错误
            "USER_CANCEL": "stop",        # 用户取消
        }

        return reason_mapping.get(cohere_reason.upper(), "stop")

    def _handle_cohere_error(self, response_data: Dict[str, Any]) -> str:
        """处理 Cohere API 错误

        解析 Cohere API 返回的错误信息，提供详细的错误描述。

        Args:
            response_data: 包含错误信息的响应数据

        Returns:
            str: 格式化的错误消息
        """
        # 检查标准错误格式
        if "message" in response_data:
            error_msg = response_data["message"]
            error_type = response_data.get("error_type", "unknown")
            return f"Cohere API 错误 ({error_type}): {error_msg}"

        # 检查详细错误格式
        if "error" in response_data:
            error_info = response_data["error"]
            if isinstance(error_info, dict):
                error_msg = error_info.get("message", "未知错误")
                error_code = error_info.get("code", "unknown")
                return f"Cohere API 错误 ({error_code}): {error_msg}"
            else:
                return f"Cohere API 错误: {error_info}"

        # 通用错误处理
        return f"Cohere API 返回未知错误: {response_data}"

    async def _generate_image_impl(
        self,
        request: ImageGenerationRequest,
        api_key: str
    ) -> ImageGenerationResponse:
        """图像生成实现

        注意：Cohere 目前不支持图像生成功能。
        此方法保留用于未来可能的功能扩展。

        Args:
            request: 图像生成请求
            api_key: API 密钥

        Raises:
            NotImplementedError: Cohere 不支持图像生成
        """
        raise NotImplementedError("Cohere 供应商目前不支持图像生成功能")

    async def generate_text_optimized(
        self,
        request: "OptimizedTextGenerationRequest",
        api_key: Optional[str] = None
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """优化版本的文本生成实现

        支持v2接口的OptimizedTextGenerationRequest格式，
        自动处理参数适配和兼容性检查。

        Args:
            request: 优化版本的文本生成请求
            api_key: API密钥，如果不提供则使用配置的密钥

        Returns:
            Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
                文本生成响应或流式响应迭代器
        """
        # 导入OptimizedTextGenerationRequest类型
        from ai_gen_hub.core.interfaces import OptimizedTextGenerationRequest

        # 获取API密钥
        if api_key is None:
            api_key = await self.key_manager.get_api_key(self.name)

        # 验证兼容性
        compatibility_report = self._compatibility_manager.validate_request_compatibility(
            request, "cohere"
        )

        # 记录兼容性警告
        for warning in compatibility_report.get("warnings", []):
            self.logger.warning(f"Cohere兼容性警告: {warning}")

        # 如果有严重错误，抛出异常
        if not compatibility_report.get("compatible", True):
            errors = compatibility_report.get("errors", [])
            error_msg = f"请求与Cohere供应商不兼容: {'; '.join(errors)}"
            from ai_gen_hub.core.exceptions import InvalidRequestError
            raise InvalidRequestError(error_msg)

        # 使用兼容性管理器适配请求
        adapted_params = self._compatibility_manager.adapt_request_for_provider(
            request, "cohere"
        )

        # 设置请求头
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }

        if request.stream and request.stream.enabled:
            # 流式生成
            url = f"{self.base_url}/chat"
            adapted_params["stream"] = True
            return self._handle_stream_response_optimized(request, url, headers, adapted_params)
        else:
            # 非流式生成
            url = f"{self.base_url}/chat"
            response = await self._make_request("POST", url, headers=headers, json_data=adapted_params)
            response_data = response.json()

            # 检查响应中是否有错误
            if "error" in response_data or "message" in response_data:
                error_msg = self._handle_cohere_error(response_data)
                self.logger.error(f"Cohere API 返回错误: {error_msg}")
                from ai_gen_hub.core.exceptions import APIError
                raise APIError(error_msg)

            return self._parse_chat_response_optimized(response_data, request)

    async def _handle_stream_response_optimized(
        self,
        request: "OptimizedTextGenerationRequest",
        url: str,
        headers: Dict[str, str],
        request_data: Dict[str, Any]
    ) -> AsyncIterator[TextGenerationStreamChunk]:
        """处理优化版本的流式响应"""
        try:
            async with self.http_client.stream("POST", url, headers=headers, json=request_data) as response:
                if response.status_code != 200:
                    error_data = await response.aread()
                    error_msg = f"Cohere API 流式请求失败: {response.status_code} - {error_data.decode()}"
                    self.logger.error(error_msg)
                    from ai_gen_hub.core.exceptions import APIError
                    raise APIError(error_msg)

                buffer = ""
                async for chunk in response.aiter_text():
                    buffer += chunk

                    while "\n\n" in buffer:
                        line, buffer = buffer.split("\n\n", 1)

                        if line.startswith("data: "):
                            data_str = line[6:]

                            if not data_str.strip() or data_str.strip() == "[DONE]":
                                continue

                            try:
                                data = json.loads(data_str)
                                stream_chunk = self._parse_stream_chunk_optimized(data, request)
                                if stream_chunk:
                                    yield stream_chunk
                            except json.JSONDecodeError as e:
                                self.logger.warning(f"解析流式响应数据失败: {e}, 数据: {data_str}")
                                continue

        except Exception as e:
            self.logger.error(f"处理 Cohere 优化版本流式响应时发生错误: {e}")
            from ai_gen_hub.core.exceptions import APIError
            raise APIError(f"流式响应处理失败: {e}")

    def _parse_chat_response_optimized(
        self,
        response_data: Dict[str, Any],
        request: "OptimizedTextGenerationRequest"
    ) -> TextGenerationResponse:
        """解析优化版本的聊天响应数据"""
        # 使用现有的解析逻辑，但传入模型名称
        from ai_gen_hub.core.interfaces import TextGenerationRequest

        # 创建一个临时的传统请求对象用于解析
        temp_request = TextGenerationRequest(
            messages=request.messages,
            model=request.model
        )

        return self._parse_chat_response(response_data, temp_request)

    def _parse_stream_chunk_optimized(
        self,
        chunk_data: Dict[str, Any],
        request: "OptimizedTextGenerationRequest"
    ) -> Optional[TextGenerationStreamChunk]:
        """解析优化版本的流式响应块"""
        from ai_gen_hub.core.interfaces import TextGenerationRequest

        # 创建一个临时的传统请求对象用于解析
        temp_request = TextGenerationRequest(
            messages=request.messages,
            model=request.model
        )

        return self._parse_stream_chunk(chunk_data, temp_request)
