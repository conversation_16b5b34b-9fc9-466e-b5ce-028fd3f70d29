"""
AI Gen Hub 供应商适配器模块

提供各种AI供应商的适配器实现，包括：
- OpenAI适配器：支持GPT系列模型
- Google AI适配器：支持Gemini系列模型
- Anthropic适配器：支持Claude系列模型
- Cohere适配器：支持Command R系列模型
- Hugging Face适配器：支持开源模型生态
- DashScope适配器：支持阿里云模型
- Dangbei适配器：支持当贝AI模型（DeepSeek等）
- 基础适配器类：提供通用功能

新增免费AI供应商支持：
- Cohere：每月1000次免费API调用
- Hugging Face：免费用户每月$0.10额度，支持大量开源模型
- Dangbei：当贝AI平台，支持DeepSeek-R1等先进模型
"""

from ai_gen_hub.providers.anthropic_provider import AnthropicProvider
from ai_gen_hub.providers.base import BaseProvider
from ai_gen_hub.providers.cohere_provider import CohereProvider
from ai_gen_hub.providers.dangbei_provider import DangbeiProvider
from ai_gen_hub.providers.dashscope_provider import DashScopeProvider
from ai_gen_hub.providers.google_ai_provider import GoogleAIProvider
from ai_gen_hub.providers.huggingface_provider import HuggingFaceProvider
from ai_gen_hub.providers.openai_provider import OpenAIProvider

__all__ = [
    "BaseProvider",
    "OpenAIProvider",
    "GoogleAIProvider",
    "AnthropicProvider",
    "DashScopeProvider",
    "CohereProvider",
    "HuggingFaceProvider",
    "DangbeiProvider",
]