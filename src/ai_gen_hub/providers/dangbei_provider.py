"""
当贝 AI 供应商适配器

实现当贝 AI API 的完整适配，包括：
- 文本生成（Chat API）
- 流式输出支持
- 模型列表获取
- 错误处理和重试

当贝 AI 提供以下主要功能：
- 聊天对话接口：支持多轮对话和流式响应
- 文本生成接口：支持各种文本生成任务
- 模型选项配置：支持深度思考、联网搜索等选项
"""

import json
import time
from typing import Any, AsyncIterator, Dict, List, Optional, Union
from uuid import uuid4

from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.core.interfaces import (
    ImageGenerationRequest,
    ImageGenerationResponse,
    Message,
    ModelType,
    TextGenerationChoice,
    TextGenerationRequest,
    TextGenerationResponse,
    TextGenerationStreamChunk,
    Usage,
    FinishReason,
)
from ai_gen_hub.providers.base import BaseProvider
from ai_gen_hub.utils.key_manager import KeyManager


class DangbeiProvider(BaseProvider):
    """当贝 AI 供应商适配器
    
    提供当贝 AI API 的完整集成，支持聊天对话和文本生成功能。
    当贝 AI 是一个提供多种 AI 模型服务的平台，主要特点包括：
    - 支持 DeepSeek 等先进模型
    - 提供深度思考和联网搜索功能
    - 支持流式和非流式响应
    """
    
    def __init__(self, config: ProviderConfig, key_manager: KeyManager):
        """初始化当贝 AI 适配器
        
        Args:
            config: 当贝 AI 配置
            key_manager: 密钥管理器
        """
        super().__init__("dangbei", config, key_manager)
        
        # 设置基础URL - 需要从配置中获取
        self.base_url = config.base_url or "https://api.dangbei.com"
        
        # 支持的模型类型
        self._supported_model_types = [
            ModelType.TEXT_GENERATION
        ]
        
        # 默认支持的模型列表（将通过API动态获取）
        self._supported_models = [
            "deepseek",  # DeepSeek-R1最新版
        ]
        
        # 模型映射
        self._model_mapping = {
            "deepseek-latest": "deepseek",
            "deepseek-r1": "deepseek",
        }
        
        # 缓存的模型列表
        self._cached_models: Optional[List[Dict[str, Any]]] = None
        self._models_cache_time: Optional[float] = None
        self._models_cache_ttl = 3600  # 1小时缓存
    
    async def _perform_health_check(self, api_key: str) -> bool:
        """执行健康检查
        
        通过调用当贝 AI 模型列表 API 来验证 API 密钥的有效性
        
        Args:
            api_key: 当贝 AI API 密钥
            
        Returns:
            bool: 健康检查是否成功
        """
        try:
            # 发送模型列表请求来验证 API 密钥
            headers = {"Authorization": f"Bearer {api_key}"}
            response = await self._make_request(
                "GET",
                f"{self.base_url}/api/models",
                headers=headers
            )
            
            if response.status_code == 200:
                # 尝试解析响应以确保格式正确
                data = response.json()
                return data.get("success", False) and "data" in data
            return False
            
        except Exception as e:
            self.logger.warning(f"当贝 AI 健康检查失败: {e}")
            return False
    
    async def get_models(self, api_key: str, force_refresh: bool = False) -> List[Dict[str, Any]]:
        """获取可用的模型列表
        
        Args:
            api_key: API 密钥
            force_refresh: 是否强制刷新缓存
            
        Returns:
            模型列表
        """
        # 检查缓存
        current_time = time.time()
        if (not force_refresh and 
            self._cached_models is not None and 
            self._models_cache_time is not None and
            current_time - self._models_cache_time < self._models_cache_ttl):
            return self._cached_models
        
        try:
            headers = {"Authorization": f"Bearer {api_key}"}
            response = await self._make_request(
                "GET",
                f"{self.base_url}/api/models",
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success") and "data" in data:
                    models = data["data"].get("models", [])
                    
                    # 更新支持的模型列表
                    self._supported_models = [model["id"] for model in models]
                    
                    # 更新缓存
                    self._cached_models = models
                    self._models_cache_time = current_time
                    
                    self.logger.info(f"成功获取 {len(models)} 个当贝 AI 模型")
                    return models
            
            self.logger.warning("获取当贝 AI 模型列表失败")
            return []
            
        except Exception as e:
            self.logger.error(f"获取当贝 AI 模型列表时发生错误: {e}")
            return []
    
    async def _generate_text_impl(
        self,
        request: TextGenerationRequest,
        api_key: str
    ) -> Union[TextGenerationResponse, AsyncIterator[TextGenerationStreamChunk]]:
        """实现文本生成
        
        Args:
            request: 文本生成请求
            api_key: API 密钥
            
        Returns:
            文本生成响应或流式响应迭代器
        """
        # 构建请求数据
        request_data = self._build_chat_request(request)
        
        # 设置请求头
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # 构建URL
        url = f"{self.base_url}/api/chat"
        
        try:
            if request.stream:
                # 流式响应
                return self._handle_stream_response(request, headers, url, request_data)
            else:
                # 普通响应
                response = await self._make_request(
                    "POST",
                    url,
                    headers=headers,
                    json_data=request_data
                )
                
                response_data = response.json()
                return self._parse_text_response(response_data, request)
                
        except Exception as e:
            # 处理当贝 AI 特定错误
            if hasattr(e, 'response_data') and e.response_data:
                self._handle_dangbei_errors(e.response_data, getattr(e, 'status_code', 500))
            raise
    
    def _build_chat_request(self, request: TextGenerationRequest) -> Dict[str, Any]:
        """构建聊天请求数据
        
        Args:
            request: 文本生成请求
            
        Returns:
            当贝 AI 聊天请求数据
        """
        # 转换消息格式
        messages = []
        for msg in request.messages:
            message_dict = {
                "role": msg.role,
                "content": msg.content
            }
            messages.append(message_dict)
        
        # 构建基础请求
        request_data = {
            "messages": messages,
            "model": self.map_model_name(request.model),
            "stream": request.stream,
        }
        
        # 添加可选参数
        if request.max_tokens is not None:
            request_data["max_tokens"] = request.max_tokens
        if request.temperature is not None:
            request_data["temperature"] = request.temperature
        if request.top_p is not None:
            request_data["top_p"] = request.top_p
        if request.user is not None:
            request_data["user"] = request.user
        
        # 处理当贝 AI 特有的选项
        options = {}
        if request.provider_params:
            # 从 provider_params 中提取当贝 AI 特有参数
            if "deep_thinking" in request.provider_params:
                options["deep_thinking"] = request.provider_params["deep_thinking"]
            if "online_search" in request.provider_params:
                options["online_search"] = request.provider_params["online_search"]
            
            # 其他参数直接添加到请求中
            for key, value in request.provider_params.items():
                if key not in ["deep_thinking", "online_search"]:
                    request_data[key] = value
        
        # 默认启用深度思考
        if not options:
            options["deep_thinking"] = True
            options["online_search"] = False
        
        if options:
            request_data["options"] = options
        
        return request_data

    def _parse_text_response(
        self,
        response_data: Dict[str, Any],
        request: TextGenerationRequest
    ) -> TextGenerationResponse:
        """解析文本生成响应

        Args:
            response_data: 当贝 AI 响应数据
            request: 原始请求

        Returns:
            标准化的文本生成响应
        """
        if not response_data.get("success"):
            error_msg = response_data.get("error", "未知错误")
            raise Exception(f"当贝 AI 请求失败: {error_msg}")

        data = response_data.get("data", {})
        message = data.get("message", {})

        # 构建选择项
        choice = TextGenerationChoice(
            index=0,
            message=Message(
                role=message.get("role", "assistant"),
                content=message.get("content", ""),
                name=None
            ),
            finish_reason=self._map_finish_reason(data.get("finish_reason", "stop"))
        )

        # 构建使用量信息
        usage_data = data.get("usage", {})
        usage = Usage(
            prompt_tokens=usage_data.get("prompt_tokens", 0),
            completion_tokens=usage_data.get("completion_tokens", 0),
            total_tokens=usage_data.get("total_tokens", 0)
        )

        return TextGenerationResponse(
            id=data.get("message_id", str(uuid4())),
            object="chat.completion",
            created=int(data.get("timestamp", time.time() * 1000) / 1000),
            model=data.get("model", request.model),
            choices=[choice],
            usage=usage,
            system_fingerprint=None
        )

    async def _handle_stream_response(
        self,
        request: TextGenerationRequest,
        headers: Dict[str, str],
        url: str,
        request_data: Dict[str, Any]
    ) -> AsyncIterator[TextGenerationStreamChunk]:
        """处理流式响应

        Args:
            request: 原始请求
            headers: 请求头
            url: 请求URL
            request_data: 请求数据

        Yields:
            流式响应块
        """
        stream = await self._make_request(
            "POST",
            url,
            headers=headers,
            json_data=request_data,
            stream=True
        )

        async for chunk in stream:
            if chunk:
                # 解析 Server-Sent Events 格式
                chunk_str = chunk.decode('utf-8').strip()
                if not chunk_str:
                    continue

                # 处理 SSE 格式的数据
                for line in chunk_str.split('\n'):
                    line = line.strip()
                    if line.startswith('data: '):
                        data_str = line[6:]  # 移除 'data: ' 前缀

                        # 检查是否是结束标记
                        if data_str == '[DONE]':
                            return

                        try:
                            # 解析 JSON 数据
                            chunk_data = json.loads(data_str)
                            stream_chunk = self._parse_stream_chunk(chunk_data, request)
                            if stream_chunk:
                                yield stream_chunk
                        except json.JSONDecodeError:
                            # 忽略无法解析的数据块
                            continue

    def _parse_stream_chunk(
        self,
        chunk_data: Dict[str, Any],
        request: TextGenerationRequest
    ) -> Optional[TextGenerationStreamChunk]:
        """解析流式数据块

        Args:
            chunk_data: 流式数据块
            request: 原始请求

        Returns:
            标准化的流式响应块
        """
        try:
            # 检查是否有选择项
            choices = chunk_data.get("choices", [])
            if not choices:
                return None

            choice_data = choices[0]
            delta = choice_data.get("delta", {})

            # 构建流式选择项
            from ai_gen_hub.core.interfaces import StreamChoice

            stream_choice = StreamChoice(
                index=choice_data.get("index", 0),
                delta=Message(
                    role=delta.get("role"),
                    content=delta.get("content", ""),
                    name=None
                ),
                finish_reason=self._map_finish_reason(choice_data.get("finish_reason"))
            )

            return TextGenerationStreamChunk(
                id=chunk_data.get("id", str(uuid4())),
                object=chunk_data.get("object", "chat.completion.chunk"),
                created=chunk_data.get("created", int(time.time())),
                model=chunk_data.get("model", request.model),
                choices=[stream_choice],
                system_fingerprint=None
            )

        except Exception as e:
            self.logger.warning(f"解析流式数据块失败: {e}")
            return None

    def _map_finish_reason(self, reason: Optional[str]) -> Optional[FinishReason]:
        """映射完成原因

        Args:
            reason: 当贝 AI 的完成原因

        Returns:
            标准化的完成原因
        """
        if not reason:
            return None

        reason_mapping = {
            "stop": FinishReason.STOP,
            "length": FinishReason.LENGTH,
            "content_filter": FinishReason.CONTENT_FILTER,
            "tool_calls": FinishReason.TOOL_CALLS,
            "function_call": FinishReason.FUNCTION_CALL,
        }

        return reason_mapping.get(reason, FinishReason.STOP)

    def _handle_dangbei_errors(self, error_data: Dict[str, Any], status_code: int) -> None:
        """处理当贝 AI 特定的错误类型

        Args:
            error_data: 错误响应数据
            status_code: HTTP 状态码

        Raises:
            相应的异常类型
        """
        from ai_gen_hub.core.exceptions import (
            APIError, AuthenticationError, AuthorizationError,
            RateLimitError, QuotaExceededError, ModelNotSupportedError
        )

        error_message = self._extract_error_message(error_data)

        # 当贝 AI 特定错误处理
        if "api key" in error_message.lower() or "unauthorized" in error_message.lower():
            raise AuthenticationError(f"当贝 AI API 密钥无效: {error_message}")
        elif "quota" in error_message.lower() or "billing" in error_message.lower():
            raise QuotaExceededError(f"当贝 AI 配额不足: {error_message}")
        elif "model" in error_message.lower() and "not found" in error_message.lower():
            raise ModelNotSupportedError("", self.name, f"当贝 AI 模型不支持: {error_message}")
        elif "rate limit" in error_message.lower():
            raise RateLimitError(f"当贝 AI 请求频率超限: {error_message}")
        else:
            # 默认处理
            raise APIError(f"当贝 AI API 错误: {error_message}", status_code=status_code)

    async def _generate_image_impl(
        self,
        request: ImageGenerationRequest,
        api_key: str
    ) -> ImageGenerationResponse:
        """实现图像生成（当贝 AI 暂不支持图像生成）

        Args:
            request: 图像生成请求
            api_key: API 密钥

        Returns:
            图像生成响应

        Raises:
            ModelNotSupportedError: 当贝 AI 不支持图像生成
        """
        from ai_gen_hub.core.exceptions import ModelNotSupportedError
        raise ModelNotSupportedError(
            request.model or "default",
            self.name,
            "当贝 AI 暂不支持图像生成功能"
        )
