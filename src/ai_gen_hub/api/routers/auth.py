"""
认证与授权API路由

提供用户认证、注册、权限管理等API端点
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm

from ai_gen_hub.core.models.auth import (
    User, UserCreate, UserUpdate, UserLogin, PasswordChange,
    APIKey, APIKeyCreate, Token, Permission, UserRole
)
from ai_gen_hub.core.auth import (
    get_auth_service, get_current_user, get_current_active_user, 
    get_current_admin_user, require_permission, require_any_permission
)
from ai_gen_hub.core.exceptions import ValidationError, AuthenticationError
from ai_gen_hub.services.auth_service import AuthService
from structlog import get_logger


router = APIRouter()
logger = get_logger(__name__)


# =============================================================================
# 认证端点
# =============================================================================

@router.post("/login", response_model=Token)
async def login(
    login_data: UserLogin,
    auth_service: AuthService = Depends(get_auth_service)
):
    """用户登录
    
    支持用户名或邮箱登录，返回JWT Token
    """
    try:
        token = await auth_service.login(login_data)
        logger.info("用户登录成功", username=login_data.username)
        return token
    except AuthenticationError as e:
        logger.warning("用户登录失败", username=login_data.username, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )


@router.post("/login/oauth2", response_model=Token)
async def login_oauth2(
    form_data: OAuth2PasswordRequestForm = Depends(),
    auth_service: AuthService = Depends(get_auth_service)
):
    """OAuth2兼容的登录端点
    
    兼容标准OAuth2密码流程
    """
    login_data = UserLogin(
        username=form_data.username,
        password=form_data.password,
        remember_me=False
    )
    
    try:
        token = await auth_service.login(login_data)
        logger.info("OAuth2登录成功", username=form_data.username)
        return token
    except AuthenticationError as e:
        logger.warning("OAuth2登录失败", username=form_data.username, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.post("/refresh", response_model=dict)
async def refresh_token(
    refresh_token: str,
    auth_service: AuthService = Depends(get_auth_service)
):
    """刷新访问令牌"""
    try:
        new_access_token = auth_service.refresh_access_token(refresh_token)
        return {
            "access_token": new_access_token,
            "token_type": "bearer"
        }
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )


@router.post("/logout")
async def logout(
    current_user: User = Depends(get_current_active_user)
):
    """用户登出
    
    注意：JWT是无状态的，实际的登出需要客户端删除token
    """
    logger.info("用户登出", user_id=str(current_user.id))
    return {"message": "登出成功"}


# =============================================================================
# 用户管理端点
# =============================================================================

@router.post("/register", response_model=User)
async def register(
    user_data: UserCreate,
    auth_service: AuthService = Depends(get_auth_service),
    _: User = Depends(require_permission(Permission.USER_CREATE))
):
    """注册新用户
    
    需要USER_CREATE权限
    """
    try:
        user = auth_service.create_user(user_data)
        logger.info("用户注册成功", user_id=str(user.id), username=user.username)
        return user
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/me", response_model=User)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """获取当前用户信息"""
    return current_user


@router.put("/me", response_model=User)
async def update_current_user(
    user_data: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """更新当前用户信息
    
    用户只能更新自己的基本信息，不能修改角色和权限
    """
    # 限制用户只能更新特定字段
    allowed_fields = {"username", "email", "full_name"}
    update_data = user_data.dict(exclude_unset=True)
    
    # 过滤不允许的字段
    filtered_data = {k: v for k, v in update_data.items() if k in allowed_fields}
    
    if not filtered_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="没有可更新的字段"
        )
    
    try:
        filtered_update = UserUpdate(**filtered_data)
        updated_user = auth_service.update_user(current_user.id, filtered_update)
        logger.info("用户信息更新成功", user_id=str(current_user.id))
        return updated_user
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/me/change-password")
async def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """修改当前用户密码"""
    try:
        auth_service.change_password(current_user.id, password_data)
        logger.info("密码修改成功", user_id=str(current_user.id))
        return {"message": "密码修改成功"}
    except (ValidationError, AuthenticationError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


# =============================================================================
# 管理员用户管理端点
# =============================================================================

@router.get("/users", response_model=List[User])
async def list_users(
    skip: int = 0,
    limit: int = 100,
    auth_service: AuthService = Depends(get_auth_service),
    _: User = Depends(require_permission(Permission.USER_READ))
):
    """获取用户列表
    
    需要USER_READ权限
    """
    users = auth_service.list_users(skip=skip, limit=limit)
    return users


@router.get("/users/{user_id}", response_model=User)
async def get_user(
    user_id: UUID,
    auth_service: AuthService = Depends(get_auth_service),
    _: User = Depends(require_permission(Permission.USER_READ))
):
    """获取指定用户信息
    
    需要USER_READ权限
    """
    user = auth_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return user


@router.put("/users/{user_id}", response_model=User)
async def update_user(
    user_id: UUID,
    user_data: UserUpdate,
    auth_service: AuthService = Depends(get_auth_service),
    _: User = Depends(require_permission(Permission.USER_UPDATE))
):
    """更新用户信息
    
    需要USER_UPDATE权限
    """
    try:
        updated_user = auth_service.update_user(user_id, user_data)
        logger.info("管理员更新用户信息", user_id=str(user_id))
        return updated_user
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/users/{user_id}")
async def delete_user(
    user_id: UUID,
    auth_service: AuthService = Depends(get_auth_service),
    current_user: User = Depends(require_permission(Permission.USER_DELETE))
):
    """删除用户
    
    需要USER_DELETE权限，不能删除自己
    """
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己"
        )
    
    success = auth_service.delete_user(user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    logger.info("用户删除成功", user_id=str(user_id), admin_id=str(current_user.id))
    return {"message": "用户删除成功"}


# =============================================================================
# API密钥管理端点
# =============================================================================

@router.get("/me/api-keys", response_model=List[APIKey])
async def list_my_api_keys(
    current_user: User = Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """获取当前用户的API密钥列表"""
    api_keys = auth_service.list_api_keys(current_user.id)
    return api_keys


@router.post("/me/api-keys")
async def create_my_api_key(
    key_data: APIKeyCreate,
    current_user: User = Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """创建API密钥"""
    try:
        api_key, key = auth_service.create_api_key(current_user.id, key_data)
        logger.info("API密钥创建成功", user_id=str(current_user.id), key_name=key_data.name)
        
        # 返回密钥信息（包含完整密钥，只显示一次）
        return {
            "api_key": api_key,
            "key": key,
            "message": "API密钥创建成功，请妥善保存，密钥只显示一次"
        }
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/me/api-keys/{key_id}")
async def delete_my_api_key(
    key_id: UUID,
    current_user: User = Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """删除API密钥"""
    success = auth_service.delete_api_key(current_user.id, key_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API密钥不存在"
        )
    
    logger.info("API密钥删除成功", user_id=str(current_user.id), key_id=str(key_id))
    return {"message": "API密钥删除成功"}


# =============================================================================
# 权限和角色管理端点
# =============================================================================

@router.get("/permissions", response_model=List[str])
async def list_permissions(
    _: User = Depends(get_current_admin_user)
):
    """获取所有权限列表
    
    需要管理员权限
    """
    return [p.value for p in Permission]


@router.get("/roles", response_model=List[str])
async def list_roles(
    _: User = Depends(get_current_admin_user)
):
    """获取所有角色列表
    
    需要管理员权限
    """
    return [r.value for r in UserRole]


@router.get("/users/{user_id}/permissions", response_model=List[str])
async def get_user_permissions(
    user_id: UUID,
    auth_service: AuthService = Depends(get_auth_service),
    _: User = Depends(require_permission(Permission.USER_READ))
):
    """获取用户权限列表
    
    需要USER_READ权限
    """
    user = auth_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return [p.value for p in user.permissions]


# =============================================================================
# 系统状态端点
# =============================================================================

@router.get("/status")
async def get_auth_status(
    auth_service: AuthService = Depends(get_auth_service),
    _: User = Depends(get_current_admin_user)
):
    """获取认证系统状态
    
    需要管理员权限
    """
    users = auth_service.list_users()
    
    return {
        "total_users": len(users),
        "active_users": len([u for u in users if u.is_active()]),
        "admin_users": len([u for u in users if u.is_admin()]),
        "total_api_keys": sum(len(auth_service.list_api_keys(u.id)) for u in users),
        "auth_service_initialized": True
    }
