"""
AI工作流API路由

提供工作流创建、管理、执行等API接口
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field

from ai_gen_hub.core.auth import get_current_user
from ai_gen_hub.core.rate_limiting import rate_limit
from ai_gen_hub.core.logging import get_logger
from ai_gen_hub.core.workflow.models import (
    WorkflowMetadata,
    WorkflowCreateRequest,
    WorkflowUpdateRequest,
    WorkflowExecuteRequest,
    NodeType,
    WorkflowStatus
)
from ai_gen_hub.services.workflow_service import WorkflowService


# API模型定义
class WorkflowCreateAPIRequest(BaseModel):
    """创建工作流API请求"""
    name: str = Field(..., description="工作流名称")
    description: str = Field("", description="工作流描述")
    category: str = Field("general", description="工作流类别")
    tags: List[str] = Field(default_factory=list, description="标签")
    nodes: List[Dict[str, Any]] = Field(..., description="节点列表")
    connections: List[Dict[str, Any]] = Field(..., description="连接列表")
    is_public: bool = Field(False, description="是否公开")


class WorkflowUpdateAPIRequest(BaseModel):
    """更新工作流API请求"""
    name: Optional[str] = Field(None, description="工作流名称")
    description: Optional[str] = Field(None, description="工作流描述")
    category: Optional[str] = Field(None, description="工作流类别")
    tags: Optional[List[str]] = Field(None, description="标签")
    nodes: Optional[List[Dict[str, Any]]] = Field(None, description="节点列表")
    connections: Optional[List[Dict[str, Any]]] = Field(None, description="连接列表")
    is_public: Optional[bool] = Field(None, description="是否公开")


class WorkflowExecuteAPIRequest(BaseModel):
    """执行工作流API请求"""
    input_data: Dict[str, Any] = Field(default_factory=dict, description="输入数据")
    context: Dict[str, Any] = Field(default_factory=dict, description="执行上下文")
    async_execution: bool = Field(False, description="是否异步执行")


class TemplateCreateAPIRequest(BaseModel):
    """创建模板API请求"""
    name: str = Field(..., description="模板名称")
    description: str = Field("", description="模板描述")


class WorkflowFromTemplateAPIRequest(BaseModel):
    """从模板创建工作流API请求"""
    name: str = Field(..., description="工作流名称")


# 创建路由器
router = APIRouter(prefix="/api/v1/workflow", tags=["AI工作流"])
logger = get_logger(__name__)

# 服务实例
workflow_service = WorkflowService()


@router.post("/workflows", summary="创建工作流")
@rate_limit(requests=10, window=60)
async def create_workflow(
    request: WorkflowCreateAPIRequest,
    current_user = Depends(get_current_user)
):
    """创建新的工作流"""
    try:
        # 构建元数据
        metadata = WorkflowMetadata(
            name=request.name,
            description=request.description,
            category=request.category,
            tags=request.tags,
            author=current_user.id if current_user else "anonymous",
            is_public=request.is_public
        )
        
        # 构建创建请求
        create_request = WorkflowCreateRequest(
            metadata=metadata,
            nodes=request.nodes,
            connections=request.connections
        )
        
        workflow = await workflow_service.create_workflow(
            create_request,
            user_id=current_user.id if current_user else None
        )
        
        return {
            "success": True,
            "data": {
                "id": workflow.id,
                "name": workflow.metadata.name,
                "description": workflow.metadata.description,
                "status": workflow.status.value,
                "created_at": workflow.created_at.isoformat(),
                "nodes_count": len(workflow.nodes),
                "connections_count": len(workflow.connections)
            }
        }
        
    except Exception as e:
        logger.error(f"创建工作流失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/workflows/{workflow_id}", summary="获取工作流")
async def get_workflow(
    workflow_id: str,
    current_user = Depends(get_current_user)
):
    """获取工作流详情"""
    try:
        workflow = await workflow_service.get_workflow(
            workflow_id,
            user_id=current_user.id if current_user else None
        )
        
        if not workflow:
            raise HTTPException(status_code=404, detail="工作流不存在")
        
        return {
            "success": True,
            "data": {
                "id": workflow.id,
                "metadata": {
                    "name": workflow.metadata.name,
                    "description": workflow.metadata.description,
                    "version": workflow.metadata.version,
                    "author": workflow.metadata.author,
                    "category": workflow.metadata.category,
                    "tags": workflow.metadata.tags,
                    "is_public": workflow.metadata.is_public
                },
                "status": workflow.status.value,
                "created_at": workflow.created_at.isoformat(),
                "updated_at": workflow.updated_at.isoformat(),
                "execution_count": workflow.execution_count,
                "success_count": workflow.success_count,
                "failure_count": workflow.failure_count,
                "nodes": [
                    {
                        "id": node.id,
                        "type": node.type.value,
                        "config": {
                            "name": node.config.name,
                            "description": node.config.description,
                            "enabled": node.config.enabled,
                            "parameters": node.config.parameters
                        },
                        "position": {
                            "x": node.position.x,
                            "y": node.position.y
                        },
                        "status": node.status.value
                    }
                    for node in workflow.nodes
                ],
                "connections": [
                    {
                        "source_node_id": conn.source_node_id,
                        "target_node_id": conn.target_node_id,
                        "source_port": conn.source_port,
                        "target_port": conn.target_port,
                        "condition": conn.condition
                    }
                    for conn in workflow.connections
                ]
            }
        }
        
    except Exception as e:
        logger.error(f"获取工作流失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/workflows", summary="列出工作流")
async def list_workflows(
    category: Optional[str] = Query(None, description="类别过滤"),
    tags: Optional[str] = Query(None, description="标签过滤（逗号分隔）"),
    current_user = Depends(get_current_user)
):
    """列出工作流"""
    try:
        tag_list = tags.split(",") if tags else None
        
        workflows = await workflow_service.list_workflows(
            user_id=current_user.id if current_user else None,
            category=category,
            tags=tag_list
        )
        
        return {
            "success": True,
            "data": [
                {
                    "id": workflow.id,
                    "name": workflow.metadata.name,
                    "description": workflow.metadata.description,
                    "category": workflow.metadata.category,
                    "tags": workflow.metadata.tags,
                    "status": workflow.status.value,
                    "created_at": workflow.created_at.isoformat(),
                    "execution_count": workflow.execution_count,
                    "success_rate": workflow.success_count / max(workflow.execution_count, 1),
                    "nodes_count": len(workflow.nodes)
                }
                for workflow in workflows
            ]
        }
        
    except Exception as e:
        logger.error(f"列出工作流失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/workflows/{workflow_id}/execute", summary="执行工作流")
@rate_limit(requests=5, window=60)
async def execute_workflow(
    workflow_id: str,
    request: WorkflowExecuteAPIRequest,
    current_user = Depends(get_current_user)
):
    """执行工作流"""
    try:
        execute_request = WorkflowExecuteRequest(
            workflow_id=workflow_id,
            input_data=request.input_data,
            context=request.context,
            async_execution=request.async_execution
        )
        
        execution = await workflow_service.execute_workflow(
            execute_request,
            user_id=current_user.id if current_user else None
        )
        
        return {
            "success": True,
            "data": {
                "execution_id": execution.id,
                "workflow_id": execution.workflow_id,
                "status": execution.status.value,
                "start_time": execution.start_time.isoformat(),
                "input_data": execution.input_data,
                "async_execution": request.async_execution
            }
        }
        
    except Exception as e:
        logger.error(f"执行工作流失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/executions/{execution_id}", summary="获取执行状态")
async def get_execution(
    execution_id: str,
    current_user = Depends(get_current_user)
):
    """获取工作流执行状态"""
    try:
        execution = await workflow_service.get_execution(
            execution_id,
            user_id=current_user.id if current_user else None
        )
        
        if not execution:
            raise HTTPException(status_code=404, detail="执行实例不存在")
        
        result = {
            "execution_id": execution.id,
            "workflow_id": execution.workflow_id,
            "status": execution.status.value,
            "start_time": execution.start_time.isoformat(),
            "input_data": execution.input_data,
            "context": execution.context,
            "executor_id": execution.executor_id
        }
        
        if execution.end_time:
            result["end_time"] = execution.end_time.isoformat()
            result["duration"] = execution.duration
        
        if execution.output_data:
            result["output_data"] = execution.output_data
        
        if execution.error_message:
            result["error_message"] = execution.error_message
        
        if execution.node_executions:
            result["node_executions"] = execution.node_executions
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        logger.error(f"获取执行状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/executions/{execution_id}/cancel", summary="取消执行")
async def cancel_execution(
    execution_id: str,
    current_user = Depends(get_current_user)
):
    """取消工作流执行"""
    try:
        success = await workflow_service.cancel_execution(
            execution_id,
            user_id=current_user.id if current_user else None
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="执行实例不存在或无法取消")
        
        return {
            "success": True,
            "message": "执行已取消"
        }
        
    except Exception as e:
        logger.error(f"取消执行失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/templates", summary="获取工作流模板")
async def get_templates(
    category: Optional[str] = Query(None, description="类别过滤"),
    tags: Optional[str] = Query(None, description="标签过滤（逗号分隔）")
):
    """获取工作流模板列表"""
    try:
        tag_list = tags.split(",") if tags else None
        
        templates = await workflow_service.get_templates(
            category=category,
            tags=tag_list
        )
        
        return {
            "success": True,
            "data": [
                {
                    "id": template.id,
                    "name": template.name,
                    "description": template.description,
                    "category": template.category,
                    "author": template.author,
                    "version": template.version,
                    "tags": template.tags,
                    "usage_count": template.usage_count,
                    "rating": template.rating,
                    "is_public": template.is_public
                }
                for template in templates
            ]
        }
        
    except Exception as e:
        logger.error(f"获取模板失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/workflows/{workflow_id}/template", summary="创建模板")
async def create_template(
    workflow_id: str,
    request: TemplateCreateAPIRequest,
    current_user = Depends(get_current_user)
):
    """从工作流创建模板"""
    try:
        template = await workflow_service.create_template(
            workflow_id=workflow_id,
            template_name=request.name,
            template_description=request.description,
            user_id=current_user.id if current_user else None
        )
        
        return {
            "success": True,
            "data": {
                "template_id": template.id,
                "name": template.name,
                "description": template.description,
                "category": template.category
            }
        }
        
    except Exception as e:
        logger.error(f"创建模板失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/templates/{template_id}/workflow", summary="从模板创建工作流")
async def create_workflow_from_template(
    template_id: str,
    request: WorkflowFromTemplateAPIRequest,
    current_user = Depends(get_current_user)
):
    """从模板创建工作流"""
    try:
        workflow = await workflow_service.create_workflow_from_template(
            template_id=template_id,
            workflow_name=request.name,
            user_id=current_user.id if current_user else None
        )
        
        return {
            "success": True,
            "data": {
                "workflow_id": workflow.id,
                "name": workflow.metadata.name,
                "status": workflow.status.value
            }
        }
        
    except Exception as e:
        logger.error(f"从模板创建工作流失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics", summary="获取统计信息")
async def get_statistics():
    """获取工作流统计信息"""
    try:
        stats = await workflow_service.get_workflow_statistics()
        
        return {
            "success": True,
            "data": stats
        }
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/node-types", summary="获取节点类型")
async def get_node_types():
    """获取支持的节点类型"""
    try:
        node_types = [
            {
                "type": node_type.value,
                "name": node_type.value.replace("_", " ").title(),
                "category": _get_node_category(node_type),
                "description": _get_node_description(node_type)
            }
            for node_type in NodeType
        ]
        
        return {
            "success": True,
            "data": node_types
        }
        
    except Exception as e:
        logger.error(f"获取节点类型失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


def _get_node_category(node_type: NodeType) -> str:
    """获取节点类别"""
    categories = {
        NodeType.INPUT: "输入输出",
        NodeType.OUTPUT: "输入输出",
        NodeType.TEXT_GENERATION: "AI服务",
        NodeType.IMAGE_GENERATION: "AI服务",
        NodeType.IMAGE_EDIT: "AI服务",
        NodeType.SPEECH_TO_TEXT: "AI服务",
        NodeType.TEXT_TO_SPEECH: "AI服务",
        NodeType.IMAGE_ANALYSIS: "AI服务",
        NodeType.TRANSFORM: "数据处理",
        NodeType.FILTER: "数据处理",
        NodeType.MERGE: "数据处理",
        NodeType.SPLIT: "数据处理",
        NodeType.CONDITION: "流程控制",
        NodeType.LOOP: "流程控制",
        NodeType.HTTP_REQUEST: "工具",
        NodeType.DATABASE: "工具",
        NodeType.FILE_OPERATION: "工具",
        NodeType.EMAIL: "工具",
        NodeType.WEBHOOK: "工具"
    }
    return categories.get(node_type, "其他")


def _get_node_description(node_type: NodeType) -> str:
    """获取节点描述"""
    descriptions = {
        NodeType.INPUT: "工作流输入节点",
        NodeType.OUTPUT: "工作流输出节点",
        NodeType.TEXT_GENERATION: "AI文本生成",
        NodeType.IMAGE_GENERATION: "AI图像生成",
        NodeType.IMAGE_EDIT: "AI图像编辑",
        NodeType.SPEECH_TO_TEXT: "语音转文本",
        NodeType.TEXT_TO_SPEECH: "文本转语音",
        NodeType.IMAGE_ANALYSIS: "图像分析理解",
        NodeType.TRANSFORM: "数据转换处理",
        NodeType.FILTER: "数据过滤筛选",
        NodeType.MERGE: "数据合并",
        NodeType.SPLIT: "数据分割",
        NodeType.CONDITION: "条件判断",
        NodeType.LOOP: "循环处理",
        NodeType.HTTP_REQUEST: "HTTP请求",
        NodeType.DATABASE: "数据库操作",
        NodeType.FILE_OPERATION: "文件操作",
        NodeType.EMAIL: "邮件发送",
        NodeType.WEBHOOK: "Webhook调用"
    }
    return descriptions.get(node_type, "未知节点类型")
