"""
多模态API端点

提供图像生成、语音处理、图像理解等多模态AI服务的API接口
"""

import asyncio
import base64
import io
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

from ai_gen_hub.core.auth import get_current_user
from ai_gen_hub.core.rate_limiting import rate_limit
from ai_gen_hub.core.logging import get_logger
from ai_gen_hub.services.multimodal.image_enhanced import (
    EnhancedImageGenerationService,
    ImageEditRequest,
    ImageStyleTransferRequest,
    ImageUpscaleRequest
)
from ai_gen_hub.services.multimodal.speech_service import (
    SpeechService,
    TTSRequest,
    STTRequest,
    VoiceCloneRequest,
    AudioFormat,
    Language,
    VoiceType
)
from ai_gen_hub.services.multimodal.vision_service import (
    VisionService,
    VisionRequest,
    VisionTask,
    DetailLevel
)
from ai_gen_hub.services.multimodal.file_manager import (
    MultimodalFileManager,
    FileUploadRequest,
    FileType
)


# API模型定义
class ImageEditAPIRequest(BaseModel):
    """图像编辑API请求"""
    image: str = Field(..., description="Base64编码的图像")
    mask: Optional[str] = Field(None, description="Base64编码的遮罩图像")
    instruction: str = Field(..., description="编辑指令")
    edit_type: str = Field("inpaint", description="编辑类型")
    model: str = Field("dall-e-3", description="使用的模型")
    n: int = Field(1, description="生成数量")


class StyleTransferAPIRequest(BaseModel):
    """风格转换API请求"""
    source_image: str = Field(..., description="源图像")
    style_reference: Optional[str] = Field(None, description="风格参考图像")
    style_prompt: Optional[str] = Field(None, description="风格描述")
    strength: float = Field(0.8, description="风格强度")
    model: str = Field("stable-diffusion", description="使用的模型")


class TTSAPIRequest(BaseModel):
    """文本转语音API请求"""
    text: str = Field(..., description="要合成的文本")
    voice: str = Field("default", description="语音名称")
    language: str = Field("zh-CN", description="语言")
    voice_type: str = Field("female", description="语音类型")
    speed: float = Field(1.0, description="语速")
    pitch: float = Field(0.0, description="音调")
    volume: float = Field(0.0, description="音量")
    format: str = Field("mp3", description="输出格式")


class STTAPIRequest(BaseModel):
    """语音转文本API请求"""
    audio_data: str = Field(..., description="Base64编码的音频数据")
    language: str = Field("zh-CN", description="语言")
    format: str = Field("wav", description="音频格式")
    enable_punctuation: bool = Field(True, description="启用标点符号")


class VisionAPIRequest(BaseModel):
    """图像理解API请求"""
    image: str = Field(..., description="Base64编码的图像")
    task: str = Field("describe", description="任务类型")
    prompt: Optional[str] = Field(None, description="任务提示")
    detail_level: str = Field("medium", description="详细程度")
    language: str = Field("zh-CN", description="输出语言")


# 创建路由器
router = APIRouter(prefix="/api/v1/multimodal", tags=["多模态AI"])
logger = get_logger(__name__)

# 服务实例
image_service = EnhancedImageGenerationService()
speech_service = SpeechService()
vision_service = VisionService()
file_manager = MultimodalFileManager()


@router.post("/image/edit", summary="图像编辑")
@rate_limit(requests=10, window=60)
async def edit_image(
    request: ImageEditAPIRequest,
    current_user = Depends(get_current_user)
):
    """
    编辑图像
    
    支持的编辑类型：
    - inpaint: 修复/替换图像区域
    - outpaint: 扩展图像边界
    - variation: 生成变体
    - upscale: 超分辨率
    """
    try:
        edit_request = ImageEditRequest(
            prompt=request.instruction,
            image=request.image,
            mask=request.mask,
            edit_instruction=request.instruction,
            edit_type=request.edit_type,
            model=request.model,
            n=request.n
        )
        
        response = await image_service.edit_image(
            edit_request,
            user_id=current_user.id if current_user else None
        )
        
        return {
            "success": True,
            "data": {
                "id": response.id,
                "images": response.data,
                "created": response.created
            }
        }
        
    except Exception as e:
        logger.error(f"图像编辑失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/image/style-transfer", summary="图像风格转换")
@rate_limit(requests=5, window=60)
async def transfer_style(
    request: StyleTransferAPIRequest,
    current_user = Depends(get_current_user)
):
    """图像风格转换"""
    try:
        style_request = ImageStyleTransferRequest(
            prompt="Style transfer",
            source_image=request.source_image,
            style_reference=request.style_reference,
            style_prompt=request.style_prompt,
            strength=request.strength,
            model=request.model
        )
        
        response = await image_service.transfer_style(
            style_request,
            user_id=current_user.id if current_user else None
        )
        
        return {
            "success": True,
            "data": {
                "id": response.id,
                "images": response.data,
                "created": response.created
            }
        }
        
    except Exception as e:
        logger.error(f"风格转换失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/image/variations", summary="生成图像变体")
@rate_limit(requests=5, window=60)
async def generate_variations(
    image: str = Field(..., description="Base64编码的图像"),
    n: int = Field(3, description="变体数量"),
    model: str = Field("dall-e-3", description="使用的模型"),
    current_user = Depends(get_current_user)
):
    """生成图像变体"""
    try:
        variations = await image_service.generate_image_variations(
            image=image,
            n=n,
            model=model,
            user_id=current_user.id if current_user else None
        )
        
        return {
            "success": True,
            "data": {
                "variations": [
                    {
                        "id": var.id,
                        "images": var.data,
                        "created": var.created
                    }
                    for var in variations
                ]
            }
        }
        
    except Exception as e:
        logger.error(f"变体生成失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/speech/text-to-speech", summary="文本转语音")
@rate_limit(requests=20, window=60)
async def text_to_speech(
    request: TTSAPIRequest,
    current_user = Depends(get_current_user)
):
    """文本转语音"""
    try:
        tts_request = TTSRequest(
            text=request.text,
            voice=request.voice,
            language=Language(request.language),
            voice_type=VoiceType(request.voice_type),
            speed=request.speed,
            pitch=request.pitch,
            volume=request.volume,
            format=AudioFormat(request.format)
        )
        
        response = await speech_service.text_to_speech(
            tts_request,
            user_id=current_user.id if current_user else None
        )
        
        return {
            "success": True,
            "data": {
                "id": response.id,
                "audio_url": response.audio_url,
                "audio_data": response.audio_data,
                "duration": response.duration,
                "format": response.format.value,
                "file_size": response.file_size
            }
        }
        
    except Exception as e:
        logger.error(f"文本转语音失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/speech/speech-to-text", summary="语音转文本")
@rate_limit(requests=20, window=60)
async def speech_to_text(
    request: STTAPIRequest,
    current_user = Depends(get_current_user)
):
    """语音转文本"""
    try:
        stt_request = STTRequest(
            audio_data=request.audio_data,
            language=Language(request.language),
            format=AudioFormat(request.format),
            enable_punctuation=request.enable_punctuation
        )
        
        response = await speech_service.speech_to_text(
            stt_request,
            user_id=current_user.id if current_user else None
        )
        
        return {
            "success": True,
            "data": {
                "id": response.id,
                "text": response.text,
                "confidence": response.confidence,
                "language": response.language.value,
                "duration": response.duration
            }
        }
        
    except Exception as e:
        logger.error(f"语音转文本失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/speech/voices", summary="获取可用语音")
async def get_available_voices(
    language: Optional[str] = None
):
    """获取可用语音列表"""
    try:
        lang = Language(language) if language else None
        voices = await speech_service.get_available_voices(lang)
        
        return {
            "success": True,
            "data": voices
        }
        
    except Exception as e:
        logger.error(f"获取语音列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/vision/analyze", summary="图像理解")
@rate_limit(requests=15, window=60)
async def analyze_image(
    request: VisionAPIRequest,
    current_user = Depends(get_current_user)
):
    """图像理解分析"""
    try:
        vision_request = VisionRequest(
            image=request.image,
            task=VisionTask(request.task),
            prompt=request.prompt,
            detail_level=DetailLevel(request.detail_level),
            language=request.language
        )
        
        response = await vision_service.analyze_image(
            vision_request,
            user_id=current_user.id if current_user else None
        )
        
        result = {
            "id": response.id,
            "task": response.task.value,
            "confidence": response.confidence,
            "processing_time": response.processing_time
        }
        
        # 根据任务类型添加相应结果
        if response.description:
            result["description"] = response.description
        if response.answer:
            result["answer"] = response.answer
        if response.objects:
            result["objects"] = [
                {
                    "label": obj.label,
                    "confidence": obj.confidence,
                    "bbox": {
                        "x": obj.bbox.x,
                        "y": obj.bbox.y,
                        "width": obj.bbox.width,
                        "height": obj.bbox.height
                    },
                    "attributes": obj.attributes
                }
                for obj in response.objects
            ]
        if response.ocr_results:
            result["ocr_results"] = [
                {
                    "text": ocr.text,
                    "confidence": ocr.confidence,
                    "bbox": {
                        "x": ocr.bbox.x,
                        "y": ocr.bbox.y,
                        "width": ocr.bbox.width,
                        "height": ocr.bbox.height
                    },
                    "language": ocr.language
                }
                for ocr in response.ocr_results
            ]
        if response.categories:
            result["categories"] = response.categories
        if response.moderation:
            result["moderation"] = response.moderation
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        logger.error(f"图像分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/files/upload", summary="上传文件")
@rate_limit(requests=10, window=60)
async def upload_file(
    file: UploadFile = File(...),
    generate_thumbnail: bool = Form(True),
    compress: bool = Form(True),
    public: bool = Form(False),
    current_user = Depends(get_current_user)
):
    """上传多媒体文件"""
    try:
        # 读取文件数据
        file_data = await file.read()
        
        upload_request = FileUploadRequest(
            filename=file.filename,
            content_type=file.content_type,
            file_data=file_data,
            generate_thumbnail=generate_thumbnail,
            compress=compress,
            public=public
        )
        
        response = await file_manager.upload_file(
            upload_request,
            user_id=current_user.id if current_user else None
        )
        
        return {
            "success": True,
            "data": {
                "file_id": response.file_id,
                "filename": response.filename,
                "file_type": response.file_type.value,
                "size": response.size,
                "url": response.url,
                "thumbnail_url": response.thumbnail_url,
                "metadata": {
                    "width": response.metadata.width,
                    "height": response.metadata.height,
                    "duration": response.metadata.duration,
                    "mime_type": response.metadata.mime_type
                } if response.metadata else None
            }
        }
        
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/files/{file_id}", summary="获取文件信息")
async def get_file_info(
    file_id: str,
    current_user = Depends(get_current_user)
):
    """获取文件信息"""
    try:
        metadata = await file_manager.get_file(file_id)
        if not metadata:
            raise HTTPException(status_code=404, detail="文件不存在")
        
        return {
            "success": True,
            "data": {
                "file_id": metadata.file_id,
                "filename": metadata.filename,
                "file_type": metadata.file_type.value,
                "mime_type": metadata.mime_type,
                "size": metadata.size,
                "width": metadata.width,
                "height": metadata.height,
                "duration": metadata.duration,
                "created_at": metadata.created_at,
                "public_url": metadata.public_url,
                "thumbnail_url": metadata.thumbnail_url
            }
        }
        
    except Exception as e:
        logger.error(f"获取文件信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/files/{file_id}", summary="删除文件")
async def delete_file(
    file_id: str,
    current_user = Depends(get_current_user)
):
    """删除文件"""
    try:
        success = await file_manager.delete_file(file_id)
        if not success:
            raise HTTPException(status_code=404, detail="文件不存在或删除失败")
        
        return {
            "success": True,
            "message": "文件删除成功"
        }
        
    except Exception as e:
        logger.error(f"文件删除失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
