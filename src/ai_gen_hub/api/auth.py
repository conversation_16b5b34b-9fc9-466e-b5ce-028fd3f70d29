"""
认证API路由

提供完整的认证和授权API端点，包括：
- OAuth2/OIDC登录流程
- API Token管理
- 用户会话管理
- 权限管理
"""

import secrets
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request, Response, status
from fastapi.responses import RedirectResponse
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field

from ai_gen_hub.auth import (
    AuthenticationError,
    AuthorizationError,
    TokenError,
    IdPError,
    SecurityError,
    AuthUser,
    APIToken,
    TokenScope,
    Permission,
    create_idp_provider,
)
from ai_gen_hub.auth.middleware import require_auth, require_permissions
from ai_gen_hub.auth.tokens import APITokenManager, JWTTokenManager
from ai_gen_hub.auth.rbac import RBACManager
from ai_gen_hub.auth.security import SecurityManager
from ai_gen_hub.config.settings import get_settings
from ai_gen_hub.core.logging import LoggerMixin


# 请求和响应模型
class LoginRequest(BaseModel):
    """登录请求"""
    idp_name: Optional[str] = Field(None, description="身份提供商名称")
    redirect_url: Optional[str] = Field(None, description="登录成功后重定向URL")


class LoginResponse(BaseModel):
    """登录响应"""
    authorization_url: str = Field(..., description="授权URL")
    state: str = Field(..., description="状态参数")


class TokenResponse(BaseModel):
    """令牌响应"""
    access_token: str = Field(..., description="访问令牌")
    refresh_token: Optional[str] = Field(None, description="刷新令牌")
    token_type: str = Field("bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求"""
    refresh_token: str = Field(..., description="刷新令牌")


class CreateAPITokenRequest(BaseModel):
    """创建API令牌请求"""
    name: str = Field(..., min_length=1, max_length=100, description="令牌名称")
    scopes: List[TokenScope] = Field(default_factory=list, description="权限范围")
    permissions: List[Permission] = Field(default_factory=list, description="具体权限")
    expires_in_days: Optional[int] = Field(None, ge=1, le=365, description="过期天数")
    rate_limit: int = Field(100, ge=1, le=10000, description="速率限制（每分钟）")


class APITokenResponse(BaseModel):
    """API令牌响应"""
    id: UUID = Field(..., description="令牌ID")
    name: str = Field(..., description="令牌名称")
    token: str = Field(..., description="令牌值（仅创建时返回）")
    token_prefix: str = Field(..., description="令牌前缀")
    scopes: List[TokenScope] = Field(..., description="权限范围")
    permissions: List[Permission] = Field(..., description="具体权限")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    created_at: datetime = Field(..., description="创建时间")


class APITokenListResponse(BaseModel):
    """API令牌列表响应"""
    id: UUID = Field(..., description="令牌ID")
    name: str = Field(..., description="令牌名称")
    token_prefix: str = Field(..., description="令牌前缀")
    scopes: List[TokenScope] = Field(..., description="权限范围")
    permissions: List[Permission] = Field(..., description="具体权限")
    is_active: bool = Field(..., description="是否活跃")
    last_used_at: Optional[datetime] = Field(None, description="最后使用时间")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    created_at: datetime = Field(..., description="创建时间")


class UserInfoResponse(BaseModel):
    """用户信息响应"""
    id: UUID = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    email: str = Field(..., description="邮箱")
    full_name: Optional[str] = Field(None, description="全名")
    role: str = Field(..., description="用户角色")
    permissions: List[Permission] = Field(..., description="用户权限")
    status: str = Field(..., description="用户状态")
    created_at: datetime = Field(..., description="创建时间")
    last_login_at: Optional[datetime] = Field(None, description="最后登录时间")


class AuthController(LoggerMixin):
    """认证控制器"""
    
    def __init__(
        self,
        jwt_manager: JWTTokenManager,
        api_token_manager: APITokenManager,
        rbac_manager: RBACManager,
        security_manager: SecurityManager,
        user_service: Any  # 用户服务接口
    ):
        super().__init__()
        self.jwt_manager = jwt_manager
        self.api_token_manager = api_token_manager
        self.rbac_manager = rbac_manager
        self.security_manager = security_manager
        self.user_service = user_service
        self.settings = get_settings()
        
        # 会话存储（生产环境应使用Redis等持久化存储）
        self._sessions: Dict[str, Dict[str, Any]] = {}
    
    async def initiate_login(self, request: LoginRequest) -> LoginResponse:
        """发起登录流程"""
        # 确定使用的IdP
        idp_name = request.idp_name or self.settings.auth.default_idp
        if not idp_name:
            raise HTTPException(
                status_code=400,
                detail="未指定身份提供商且无默认配置"
            )
        
        # 获取IdP配置
        idp_config = self.settings.auth.idp_configs.get(idp_name)
        if not idp_config or not idp_config.enabled:
            raise HTTPException(
                status_code=400,
                detail=f"身份提供商 {idp_name} 未配置或已禁用"
            )
        
        try:
            # 创建IdP提供商
            idp_provider = create_idp_provider(idp_config)
            await idp_provider.initialize()
            
            # 生成授权URL
            authorization_url, state, nonce = idp_provider.generate_authorization_url()
            
            # 存储会话信息
            session_data = {
                "idp_name": idp_name,
                "state": state,
                "nonce": nonce,
                "redirect_url": request.redirect_url,
                "created_at": datetime.utcnow(),
                "expires_at": datetime.utcnow() + timedelta(minutes=10)
            }
            self._sessions[state] = session_data
            
            self.logger.info(
                "登录流程发起成功",
                idp_name=idp_name,
                state=state[:8] + "..."
            )
            
            return LoginResponse(
                authorization_url=authorization_url,
                state=state
            )
            
        except IdPError as e:
            self.logger.error(f"IdP错误: {e}")
            raise HTTPException(status_code=500, detail=str(e))
        except Exception as e:
            self.logger.error(f"登录发起失败: {e}")
            raise HTTPException(status_code=500, detail="登录发起失败")
    
    async def handle_callback(
        self,
        idp_name: str,
        code: str,
        state: str,
        request: Request
    ) -> TokenResponse:
        """处理OAuth回调"""
        # 验证会话
        session_data = self._sessions.get(state)
        if not session_data:
            raise HTTPException(status_code=400, detail="无效的状态参数")
        
        if session_data["idp_name"] != idp_name:
            raise HTTPException(status_code=400, detail="身份提供商不匹配")
        
        if datetime.utcnow() > session_data["expires_at"]:
            raise HTTPException(status_code=400, detail="会话已过期")
        
        try:
            # 获取IdP配置和提供商
            idp_config = self.settings.auth.idp_configs.get(idp_name)
            idp_provider = create_idp_provider(idp_config)
            await idp_provider.initialize()
            
            # 交换授权码获取令牌
            tokens = await idp_provider.exchange_code_for_tokens(code, state)
            
            # 获取用户信息
            user_info = await idp_provider.get_user_info(tokens["access_token"])
            
            # 验证ID令牌（如果是OIDC）
            if "id_token" in tokens:
                await idp_provider.verify_id_token(
                    tokens["id_token"], 
                    session_data["nonce"]
                )
            
            # 映射用户信息
            auth_user = idp_provider.map_user_info(user_info)
            
            # 查找或创建用户
            user = await self.user_service.find_or_create_user(auth_user)
            
            # 更新最后登录时间
            user.last_login_at = datetime.utcnow()
            await self.user_service.update_user(user)
            
            # 生成JWT令牌
            access_token = self.jwt_manager.create_access_token(user)
            refresh_token = self.jwt_manager.create_refresh_token(user)
            
            # 记录登录事件
            self.security_manager.audit_logger.log_authentication_event(
                user_id=user.id,
                event_type="login_success",
                success=True,
                ip_address=self._get_client_ip(request),
                user_agent=request.headers.get("User-Agent"),
                details={"idp_name": idp_name}
            )
            
            # 清理会话
            del self._sessions[state]
            
            self.logger.info(
                "用户登录成功",
                user_id=str(user.id),
                username=user.username,
                idp_name=idp_name
            )
            
            return TokenResponse(
                access_token=access_token,
                refresh_token=refresh_token,
                token_type="bearer",
                expires_in=self.settings.auth.jwt_access_token_expire_minutes * 60
            )
            
        except Exception as e:
            # 记录登录失败事件
            self.security_manager.audit_logger.log_authentication_event(
                user_id=None,
                event_type="login_failed",
                success=False,
                ip_address=self._get_client_ip(request),
                user_agent=request.headers.get("User-Agent"),
                details={"idp_name": idp_name, "error": str(e)}
            )
            
            self.logger.error(f"登录回调处理失败: {e}")
            raise HTTPException(status_code=500, detail="登录处理失败")
    
    async def refresh_token(self, request: RefreshTokenRequest, user: AuthUser) -> TokenResponse:
        """刷新访问令牌"""
        try:
            new_access_token = self.jwt_manager.refresh_access_token(
                request.refresh_token, user
            )
            
            self.logger.info(
                "令牌刷新成功",
                user_id=str(user.id),
                username=user.username
            )
            
            return TokenResponse(
                access_token=new_access_token,
                token_type="bearer",
                expires_in=self.settings.auth.jwt_access_token_expire_minutes * 60
            )
            
        except TokenError as e:
            self.logger.warning(f"令牌刷新失败: {e}")
            raise HTTPException(status_code=401, detail=str(e))
    
    async def logout(self, user: AuthUser, refresh_token: Optional[str] = None) -> Dict[str, str]:
        """用户登出"""
        try:
            # 撤销刷新令牌
            if refresh_token:
                self.jwt_manager.revoke_refresh_token(refresh_token)
            
            # 记录登出事件
            self.security_manager.audit_logger.log_authentication_event(
                user_id=user.id,
                event_type="logout",
                success=True
            )
            
            self.logger.info(
                "用户登出成功",
                user_id=str(user.id),
                username=user.username
            )
            
            return {"message": "登出成功"}
            
        except Exception as e:
            self.logger.error(f"登出失败: {e}")
            raise HTTPException(status_code=500, detail="登出失败")
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"

    async def create_api_token(
        self,
        request: CreateAPITokenRequest,
        user: AuthUser
    ) -> APITokenResponse:
        """创建API令牌"""
        try:
            # 检查权限
            self.rbac_manager.require_permission(user.id, Permission.API_TOKEN_MANAGE)

            # 验证权限范围
            if request.scopes:
                user_permissions = self.rbac_manager.get_user_permissions(user.id)
                for scope in request.scopes:
                    if scope == TokenScope.ADMIN and Permission.SYSTEM_ADMIN not in user_permissions:
                        raise HTTPException(
                            status_code=403,
                            detail="无权限创建管理员级别的令牌"
                        )

            # 创建API令牌
            api_token, token_value = self.api_token_manager.generate_api_token(
                user_id=user.id,
                name=request.name,
                scopes=set(request.scopes) if request.scopes else {TokenScope.API_ACCESS},
                permissions=set(request.permissions) if request.permissions else set(),
                expires_in_days=request.expires_in_days,
                rate_limit=request.rate_limit
            )

            # 记录审计日志
            self.security_manager.audit_logger.log_security_event(
                "api_token_created",
                "medium",
                user_id=user.id,
                details={
                    "token_id": str(api_token.id),
                    "token_name": request.name,
                    "scopes": request.scopes,
                    "expires_in_days": request.expires_in_days
                }
            )

            self.logger.info(
                "API令牌创建成功",
                user_id=str(user.id),
                token_id=str(api_token.id),
                token_name=request.name
            )

            return APITokenResponse(
                id=api_token.id,
                name=api_token.name,
                token=token_value,  # 仅在创建时返回
                token_prefix=api_token.token_prefix,
                scopes=list(api_token.scopes),
                permissions=list(api_token.permissions),
                expires_at=api_token.expires_at,
                created_at=api_token.created_at
            )

        except Exception as e:
            self.logger.error(f"API令牌创建失败: {e}")
            if isinstance(e, HTTPException):
                raise
            raise HTTPException(status_code=500, detail="API令牌创建失败")

    async def list_api_tokens(self, user: AuthUser) -> List[APITokenListResponse]:
        """列出用户的API令牌"""
        try:
            # 获取用户的所有令牌
            tokens = self.api_token_manager.get_user_tokens(user.id)

            return [
                APITokenListResponse(
                    id=token.id,
                    name=token.name,
                    token_prefix=token.token_prefix,
                    scopes=list(token.scopes),
                    permissions=list(token.permissions),
                    is_active=token.is_active,
                    last_used_at=token.last_used_at,
                    expires_at=token.expires_at,
                    created_at=token.created_at
                )
                for token in tokens
            ]

        except Exception as e:
            self.logger.error(f"API令牌列表获取失败: {e}")
            raise HTTPException(status_code=500, detail="API令牌列表获取失败")

    async def revoke_api_token(self, token_id: UUID, user: AuthUser) -> Dict[str, str]:
        """撤销API令牌"""
        try:
            # 检查权限
            self.rbac_manager.require_permission(user.id, Permission.API_TOKEN_MANAGE)

            # 撤销令牌
            success = self.api_token_manager.revoke_api_token(token_id)

            if not success:
                raise HTTPException(status_code=404, detail="令牌不存在")

            # 记录审计日志
            self.security_manager.audit_logger.log_security_event(
                "api_token_revoked",
                "medium",
                user_id=user.id,
                details={"token_id": str(token_id)}
            )

            self.logger.info(
                "API令牌撤销成功",
                user_id=str(user.id),
                token_id=str(token_id)
            )

            return {"message": "令牌撤销成功"}

        except Exception as e:
            self.logger.error(f"API令牌撤销失败: {e}")
            if isinstance(e, HTTPException):
                raise
            raise HTTPException(status_code=500, detail="API令牌撤销失败")

    async def get_user_info(self, user: AuthUser) -> UserInfoResponse:
        """获取当前用户信息"""
        try:
            # 获取用户权限
            user_permissions = self.rbac_manager.get_user_permissions(user.id)

            return UserInfoResponse(
                id=user.id,
                username=user.username,
                email=user.email,
                full_name=user.full_name,
                role=user.role.value,
                permissions=list(user_permissions),
                status=user.status.value,
                created_at=user.created_at,
                last_login_at=user.last_login_at
            )

        except Exception as e:
            self.logger.error(f"用户信息获取失败: {e}")
            raise HTTPException(status_code=500, detail="用户信息获取失败")


# 创建路由器
def create_auth_router(
    jwt_manager: JWTTokenManager,
    api_token_manager: APITokenManager,
    rbac_manager: RBACManager,
    security_manager: SecurityManager,
    user_service: Any
) -> APIRouter:
    """创建认证路由器"""
    router = APIRouter(prefix="/auth", tags=["认证"])
    controller = AuthController(
        jwt_manager, api_token_manager, rbac_manager, security_manager, user_service
    )

    @router.post("/login", response_model=LoginResponse, summary="发起登录")
    async def login(request: LoginRequest):
        """发起OAuth2/OIDC登录流程"""
        return await controller.initiate_login(request)

    @router.get("/callback/{idp_name}", response_model=TokenResponse, summary="OAuth回调")
    async def callback(
        idp_name: str,
        code: str,
        state: str,
        request: Request
    ):
        """处理OAuth2/OIDC回调"""
        return await controller.handle_callback(idp_name, code, state, request)

    @router.post("/refresh", response_model=TokenResponse, summary="刷新令牌")
    async def refresh_token(
        request: RefreshTokenRequest,
        user: AuthUser = Depends(require_auth())
    ):
        """刷新访问令牌"""
        return await controller.refresh_token(request, user)

    @router.post("/logout", summary="用户登出")
    async def logout(
        refresh_token: Optional[str] = None,
        user: AuthUser = Depends(require_auth())
    ):
        """用户登出"""
        return await controller.logout(user, refresh_token)

    @router.get("/me", response_model=UserInfoResponse, summary="获取用户信息")
    async def get_user_info(user: AuthUser = Depends(require_auth())):
        """获取当前用户信息"""
        return await controller.get_user_info(user)

    @router.post("/tokens", response_model=APITokenResponse, summary="创建API令牌")
    async def create_api_token(
        request: CreateAPITokenRequest,
        user: AuthUser = Depends(require_permissions(Permission.API_TOKEN_MANAGE))
    ):
        """创建API令牌"""
        return await controller.create_api_token(request, user)

    @router.get("/tokens", response_model=List[APITokenListResponse], summary="列出API令牌")
    async def list_api_tokens(user: AuthUser = Depends(require_auth())):
        """列出用户的API令牌"""
        return await controller.list_api_tokens(user)

    @router.delete("/tokens/{token_id}", summary="撤销API令牌")
    async def revoke_api_token(
        token_id: UUID,
        user: AuthUser = Depends(require_permissions(Permission.API_TOKEN_MANAGE))
    ):
        """撤销API令牌"""
        return await controller.revoke_api_token(token_id, user)

    return router
