"""
管理控制台路由

提供Web管理界面的路由和视图函数
"""

import os
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Any

from fastapi import APIRouter, Request, Depends, HTTPException, status
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from structlog import get_logger

from ai_gen_hub.core.models.auth import User
from ai_gen_hub.database.connection import get_database_manager, check_database_health
from ai_gen_hub.database.repositories import UserRepository, UsageRepository


# 创建路由器
router = APIRouter()
logger = get_logger(__name__)


# 简化的管理员认证（开发环境）
async def get_current_admin_user() -> User:
    """获取当前管理员用户（简化版）"""
    # 在开发环境中，创建一个虚拟的管理员用户
    from ai_gen_hub.core.models.auth import UserRole, UserStatus
    from uuid import uuid4
    from datetime import datetime

    # 创建虚拟管理员用户
    admin_user = User(
        id=uuid4(),
        username="admin",
        email="<EMAIL>",
        password_hash="dummy_hash",
        role=UserRole.ADMIN,
        status=UserStatus.ACTIVE,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    return admin_user

# 模板配置
templates_dir = os.path.join(os.path.dirname(__file__), "templates")
if not os.path.exists(templates_dir):
    # 如果模板目录不存在，创建一个临时的内存模板
    templates = None
    logger.warning(f"模板目录不存在: {templates_dir}")
else:
    templates = Jinja2Templates(directory=templates_dir)


@router.get("/dashboard", response_class=HTMLResponse)
async def admin_dashboard(
    request: Request,
    current_user: User = Depends(get_current_admin_user)
):
    """管理仪表板"""
    try:
        # 获取系统统计数据
        stats = await get_dashboard_stats()

        # 获取供应商状态
        providers = await get_provider_status()

        # 获取最近活动
        recent_activities = await get_recent_activities()

        # 获取系统信息
        system_info = await get_system_info()

        # 获取图表数据
        chart_data = await get_chart_data()

        # 如果模板不可用，返回简单的HTML
        if templates is None:
            html_content = f"""
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>AI Gen Hub 管理控制台</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            </head>
            <body>
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
                                <div class="container-fluid">
                                    <a class="navbar-brand" href="#">
                                        <i class="fas fa-robot me-2"></i>AI Gen Hub 管理控制台
                                    </a>
                                </div>
                            </nav>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="container">
                                <h1 class="mb-4">系统仪表板</h1>

                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h5 class="card-title">总用户数</h5>
                                                <h2 class="text-primary">{stats.get('total_users', 0)}</h2>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h5 class="card-title">今日请求</h5>
                                                <h2 class="text-success">{stats.get('today_requests', 0)}</h2>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h5 class="card-title">活跃供应商</h5>
                                                <h2 class="text-info">{stats.get('active_providers', 0)}</h2>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h5 class="card-title">系统负载</h5>
                                                <h2 class="text-warning">{stats.get('system_load', 0):.1f}%</h2>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5>供应商状态</h5>
                                            </div>
                                            <div class="card-body">
                                                {''.join([f'<p><span class="badge bg-{"success" if p.get("is_available") else "danger"}">{p.get("provider_name")}</span> - {p.get("model_name")}</p>' for p in providers])}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5>系统信息</h5>
                                            </div>
                                            <div class="card-body">
                                                <p><strong>版本:</strong> {system_info.get('version', '1.0.0')}</p>
                                                <p><strong>运行时间:</strong> {system_info.get('uptime', '未知')}</p>
                                                <p><strong>内存使用:</strong> {system_info.get('memory_usage', '未知')}</p>
                                                <p><strong>数据库状态:</strong>
                                                   <span class="badge bg-{'success' if system_info.get('database_status') == 'healthy' else 'danger'}">
                                                       {system_info.get('database_status', 'unknown')}
                                                   </span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <p class="text-muted">
                                        <i class="fas fa-info-circle me-2"></i>
                                        这是一个简化版的管理控制台界面。完整的模板功能正在开发中。
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
            </body>
            </html>
            """
            return HTMLResponse(content=html_content)

        return templates.TemplateResponse("dashboard.html", {
            "request": request,
            "stats": stats,
            "providers": providers,
            "recent_activities": recent_activities,
            "system_info": system_info,
            "chart_data": chart_data
        })

    except Exception as e:
        logger.error("获取仪表板数据失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取仪表板数据失败"
        )


@router.get("/api/dashboard-data")
async def get_dashboard_data(
    current_user: User = Depends(get_current_admin_user)
):
    """获取仪表板数据（API）"""
    try:
        return {
            "stats": await get_dashboard_stats(),
            "providers": await get_provider_status(),
            "recent_activities": await get_recent_activities(),
            "system_info": await get_system_info()
        }
    except Exception as e:
        logger.error("获取仪表板数据失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取仪表板数据失败"
        )


@router.get("/api/chart-data")
async def get_chart_data_api(
    period: str = "day",
    current_user: User = Depends(get_current_admin_user)
):
    """获取图表数据（API）"""
    try:
        return await get_chart_data(period)
    except Exception as e:
        logger.error("获取图表数据失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取图表数据失败"
        )


@router.get("/users", response_class=HTMLResponse)
async def admin_users(
    request: Request,
    current_user: User = Depends(get_current_admin_user)
):
    """用户管理页面"""
    try:
        # 获取用户列表
        users = await get_users_list()

        # 如果模板不可用，返回简单的HTML
        if templates is None:
            html_content = f"""
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>用户管理 - AI Gen Hub</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            </head>
            <body>
                <div class="container-fluid">
                    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
                        <div class="container-fluid">
                            <a class="navbar-brand" href="/admin/dashboard">
                                <i class="fas fa-robot me-2"></i>AI Gen Hub 管理控制台
                            </a>
                            <div class="navbar-nav">
                                <a class="nav-link" href="/admin/dashboard">仪表板</a>
                                <a class="nav-link active" href="/admin/users">用户管理</a>
                                <a class="nav-link" href="/admin/providers">供应商管理</a>
                            </div>
                        </div>
                    </nav>

                    <div class="container mt-4">
                        <h1 class="mb-4">用户管理</h1>

                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-users me-2"></i>用户列表 ({len(users)} 个用户)</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>用户名</th>
                                                <th>邮箱</th>
                                                <th>角色</th>
                                                <th>状态</th>
                                                <th>API配额</th>
                                                <th>注册时间</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {''.join([f'''
                                            <tr>
                                                <td>{user.get('username', '')}</td>
                                                <td>{user.get('email', '')}</td>
                                                <td><span class="badge bg-{'danger' if user.get('role') == 'admin' else 'primary'}">{user.get('role', '')}</span></td>
                                                <td><span class="badge bg-{'success' if user.get('status') == 'active' else 'secondary'}">{user.get('status', '')}</span></td>
                                                <td>{user.get('api_quota_used', 0)} / {user.get('api_quota', 0)}</td>
                                                <td>{user.get('created_at', '')}</td>
                                            </tr>
                                            ''' for user in users])}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
            </body>
            </html>
            """
            return HTMLResponse(content=html_content)

        return templates.TemplateResponse("users.html", {
            "request": request,
            "users": users
        })

    except Exception as e:
        logger.error("获取用户列表失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户列表失败"
        )


@router.get("/providers", response_class=HTMLResponse)
async def admin_providers(
    request: Request,
    current_user: User = Depends(get_current_admin_user)
):
    """供应商管理页面"""
    try:
        providers = await get_provider_status()
        
        return templates.TemplateResponse("providers.html", {
            "request": request,
            "providers": providers
        })
        
    except Exception as e:
        logger.error("获取供应商列表失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取供应商列表失败"
        )


# =============================================================================
# 辅助函数
# =============================================================================

async def get_dashboard_stats() -> Dict[str, Any]:
    """获取仪表板统计数据"""
    stats = {
        "total_users": 0,
        "today_requests": 0,
        "active_providers": 0,
        "system_load": 0.0
    }
    
    try:
        db_manager = get_database_manager()
        if db_manager:
            async with db_manager.get_session() as session:
                user_repo = UserRepository(session)
                usage_repo = UsageRepository(session)
                
                # 获取用户统计
                user_stats = await user_repo.get_user_stats()
                stats["total_users"] = user_stats.get("total_users", 0)
                
                # 获取今日请求数
                today = datetime.utcnow().date()
                today_start = datetime.combine(today, datetime.min.time())
                today_end = datetime.combine(today, datetime.max.time())
                
                # 这里需要实现获取今日请求数的逻辑
                # stats["today_requests"] = await usage_repo.get_requests_count(today_start, today_end)
                stats["today_requests"] = 1234  # 临时数据
        
        # 获取系统负载
        stats["system_load"] = psutil.cpu_percent(interval=1)
        
        # 获取活跃供应商数量
        providers = await get_provider_status()
        stats["active_providers"] = len([p for p in providers if p.get("is_available", False)])
        
    except Exception as e:
        logger.error("获取统计数据失败", error=str(e))
    
    return stats


async def get_provider_status() -> List[Dict[str, Any]]:
    """获取供应商状态"""
    providers = [
        {
            "provider_name": "OpenAI",
            "model_name": "gpt-3.5-turbo",
            "is_available": True,
            "success_rate": 0.98,
            "avg_response_time": 1.2,
            "last_check_at": datetime.utcnow().isoformat()
        },
        {
            "provider_name": "Google AI",
            "model_name": "gemini-pro",
            "is_available": True,
            "success_rate": 0.95,
            "avg_response_time": 2.1,
            "last_check_at": datetime.utcnow().isoformat()
        },
        {
            "provider_name": "Anthropic",
            "model_name": "claude-3-sonnet",
            "is_available": False,
            "success_rate": 0.0,
            "avg_response_time": 0.0,
            "last_check_at": datetime.utcnow().isoformat()
        }
    ]
    
    # 这里应该从数据库获取真实的供应商状态
    try:
        db_manager = get_database_manager()
        if db_manager:
            # 从数据库获取供应商状态
            pass
    except Exception as e:
        logger.error("获取供应商状态失败", error=str(e))
    
    return providers


async def get_recent_activities() -> List[Dict[str, Any]]:
    """获取最近活动"""
    activities = [
        {
            "action": "用户登录",
            "description": "管理员用户登录系统",
            "timestamp": "2分钟前"
        },
        {
            "action": "API调用",
            "description": "文本生成API被调用100次",
            "timestamp": "5分钟前"
        },
        {
            "action": "系统监控",
            "description": "系统健康检查完成",
            "timestamp": "10分钟前"
        },
        {
            "action": "用户注册",
            "description": "新用户注册账号",
            "timestamp": "15分钟前"
        }
    ]
    
    # 这里应该从数据库获取真实的活动记录
    return activities


async def get_system_info() -> Dict[str, Any]:
    """获取系统信息"""
    info = {
        "version": "1.0.0",
        "uptime": "未知",
        "memory_usage": "未知",
        "cpu_usage": "未知",
        "database_status": "unknown",
        "cache_status": "unknown"
    }
    
    try:
        # 获取系统资源使用情况
        memory = psutil.virtual_memory()
        info["memory_usage"] = f"{memory.percent:.1f}% ({memory.used // 1024 // 1024}MB / {memory.total // 1024 // 1024}MB)"
        info["cpu_usage"] = f"{psutil.cpu_percent(interval=1):.1f}%"
        
        # 获取系统运行时间
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time
        days = uptime.days
        hours, remainder = divmod(uptime.seconds, 3600)
        minutes, _ = divmod(remainder, 60)
        info["uptime"] = f"{days}天 {hours}小时 {minutes}分钟"
        
        # 检查数据库状态
        db_health = await check_database_health()
        info["database_status"] = db_health.get("status", "unknown")
        
        # 检查缓存状态
        try:
            # 简单的缓存状态检查
            info["cache_status"] = "healthy"
        except Exception:
            info["cache_status"] = "unhealthy"
        
    except Exception as e:
        logger.error("获取系统信息失败", error=str(e))
    
    return info


async def get_chart_data(period: str = "day") -> Dict[str, Any]:
    """获取图表数据"""
    # 生成示例数据
    if period == "hour":
        labels = [f"{i:02d}:00" for i in range(24)]
        data = [50 + i * 2 + (i % 3) * 10 for i in range(24)]
    elif period == "week":
        labels = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        data = [120, 190, 300, 500, 200, 300, 450]
    else:  # day
        labels = [f"{i+1}日" for i in range(30)]
        data = [100 + i * 5 + (i % 7) * 20 for i in range(30)]
    
    return {
        "labels": labels,
        "data": data
    }


async def get_users_list() -> List[Dict[str, Any]]:
    """获取用户列表"""
    users = []
    
    try:
        db_manager = get_database_manager()
        if db_manager:
            async with db_manager.get_session() as session:
                user_repo = UserRepository(session)
                db_users = await user_repo.list_users(limit=100)
                
                for db_user in db_users:
                    users.append({
                        "id": str(db_user.id),
                        "username": db_user.username,
                        "email": db_user.email,
                        "role": db_user.role,
                        "status": db_user.status,
                        "api_quota": db_user.api_quota,
                        "api_quota_used": db_user.api_quota_used,
                        "created_at": db_user.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                        "last_login_at": db_user.last_login_at.strftime("%Y-%m-%d %H:%M:%S") if db_user.last_login_at else "从未登录"
                    })
    except Exception as e:
        logger.error("获取用户列表失败", error=str(e))
    
    return users
