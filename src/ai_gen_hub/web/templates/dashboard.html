{% extends "base.html" %}

{% block title %}仪表板 - AI Gen Hub 管理控制台{% endblock %}
{% block page_title %}系统仪表板{% endblock %}

{% block content %}
<!-- 系统状态概览 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            总用户数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-users">
                            {{ stats.total_users or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            今日请求数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-requests">
                            {{ stats.today_requests or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            活跃供应商
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-providers">
                            {{ stats.active_providers or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-cloud fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            系统负载
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="system-load">
                            {{ "%.1f"|format(stats.system_load or 0) }}%
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-server fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <!-- 请求量趋势图 -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-white">请求量趋势</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow">
                        <a class="dropdown-item" href="#" onclick="updateChart('hour')">按小时</a>
                        <a class="dropdown-item" href="#" onclick="updateChart('day')">按天</a>
                        <a class="dropdown-item" href="#" onclick="updateChart('week')">按周</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="requestChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 供应商状态 -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-white">供应商状态</h6>
            </div>
            <div class="card-body">
                <div id="provider-status">
                    {% for provider in providers %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            {% if provider.is_available %}
                            <span class="badge bg-success status-badge">
                                <i class="fas fa-check-circle me-1"></i>正常
                            </span>
                            {% else %}
                            <span class="badge bg-danger status-badge">
                                <i class="fas fa-times-circle me-1"></i>异常
                            </span>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold">{{ provider.provider_name }}</div>
                            <small class="text-muted">{{ provider.model_name }}</small>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">
                                {{ "%.0f"|format(provider.success_rate * 100) if provider.success_rate else 0 }}%
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动和系统信息 -->
<div class="row">
    <!-- 最近活动 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-white">最近活动</h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush" id="recent-activities">
                    {% for activity in recent_activities %}
                    <div class="list-group-item d-flex justify-content-between align-items-start">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">{{ activity.action }}</div>
                            <small class="text-muted">{{ activity.description }}</small>
                        </div>
                        <small class="text-muted">{{ activity.timestamp }}</small>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统信息 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-white">系统信息</h6>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tbody>
                        <tr>
                            <td><strong>系统版本</strong></td>
                            <td>{{ system_info.version or "1.0.0" }}</td>
                        </tr>
                        <tr>
                            <td><strong>运行时间</strong></td>
                            <td id="uptime">{{ system_info.uptime or "未知" }}</td>
                        </tr>
                        <tr>
                            <td><strong>内存使用</strong></td>
                            <td>{{ system_info.memory_usage or "未知" }}</td>
                        </tr>
                        <tr>
                            <td><strong>CPU使用率</strong></td>
                            <td>{{ system_info.cpu_usage or "未知" }}</td>
                        </tr>
                        <tr>
                            <td><strong>数据库状态</strong></td>
                            <td>
                                {% if system_info.database_status == "healthy" %}
                                <span class="badge bg-success">正常</span>
                                {% else %}
                                <span class="badge bg-danger">异常</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td><strong>缓存状态</strong></td>
                            <td>
                                {% if system_info.cache_status == "healthy" %}
                                <span class="badge bg-success">正常</span>
                                {% else %}
                                <span class="badge bg-danger">异常</span>
                                {% endif %}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// 请求量趋势图
let requestChart;

function initRequestChart() {
    const ctx = document.getElementById('requestChart').getContext('2d');
    requestChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: {{ chart_data.labels | tojson }},
            datasets: [{
                label: '请求数',
                data: {{ chart_data.data | tojson }},
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

function updateChart(period) {
    // 这里可以通过AJAX更新图表数据
    fetch(`/admin/api/chart-data?period=${period}`)
        .then(response => response.json())
        .then(data => {
            requestChart.data.labels = data.labels;
            requestChart.data.datasets[0].data = data.data;
            requestChart.update();
        })
        .catch(error => {
            console.error('更新图表失败:', error);
            showError('更新图表失败');
        });
}

// 自动刷新数据
function refreshDashboard() {
    fetch('/admin/api/dashboard-data')
        .then(response => response.json())
        .then(data => {
            // 更新统计数据
            document.getElementById('total-users').textContent = data.stats.total_users;
            document.getElementById('today-requests').textContent = data.stats.today_requests;
            document.getElementById('active-providers').textContent = data.stats.active_providers;
            document.getElementById('system-load').textContent = data.stats.system_load.toFixed(1) + '%';
            
            // 更新供应商状态
            updateProviderStatus(data.providers);
            
            // 更新最近活动
            updateRecentActivities(data.recent_activities);
        })
        .catch(error => {
            console.error('刷新仪表板失败:', error);
        });
}

function updateProviderStatus(providers) {
    const container = document.getElementById('provider-status');
    container.innerHTML = '';
    
    providers.forEach(provider => {
        const statusBadge = provider.is_available 
            ? '<span class="badge bg-success status-badge"><i class="fas fa-check-circle me-1"></i>正常</span>'
            : '<span class="badge bg-danger status-badge"><i class="fas fa-times-circle me-1"></i>异常</span>';
        
        const successRate = provider.success_rate ? (provider.success_rate * 100).toFixed(0) : 0;
        
        const html = `
            <div class="d-flex align-items-center mb-3">
                <div class="me-3">${statusBadge}</div>
                <div class="flex-grow-1">
                    <div class="fw-bold">${provider.provider_name}</div>
                    <small class="text-muted">${provider.model_name}</small>
                </div>
                <div class="text-end">
                    <small class="text-muted">${successRate}%</small>
                </div>
            </div>
        `;
        container.innerHTML += html;
    });
}

function updateRecentActivities(activities) {
    const container = document.getElementById('recent-activities');
    container.innerHTML = '';
    
    activities.forEach(activity => {
        const html = `
            <div class="list-group-item d-flex justify-content-between align-items-start">
                <div class="ms-2 me-auto">
                    <div class="fw-bold">${activity.action}</div>
                    <small class="text-muted">${activity.description}</small>
                </div>
                <small class="text-muted">${activity.timestamp}</small>
            </div>
        `;
        container.innerHTML += html;
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initRequestChart();
    
    // 每30秒刷新一次数据
    setInterval(refreshDashboard, 30000);
});
</script>
{% endblock %}
