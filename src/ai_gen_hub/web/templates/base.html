<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AI Gen Hub 管理控制台{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 0.5rem;
            margin: 0.2rem 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1rem 1rem 0 0 !important;
        }
        .metric-card {
            transition: transform 0.2s;
        }
        .metric-card:hover {
            transform: translateY(-2px);
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .navbar-brand {
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-robot me-2"></i>
                            AI Gen Hub
                        </h4>
                        <small class="text-white-50">管理控制台</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/admin/dashboard' %}active{% endif %}" 
                               href="/admin/dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/admin/users' in request.url.path %}active{% endif %}" 
                               href="/admin/users">
                                <i class="fas fa-users me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/admin/providers' in request.url.path %}active{% endif %}" 
                               href="/admin/providers">
                                <i class="fas fa-cloud me-2"></i>
                                供应商管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/admin/monitoring' in request.url.path %}active{% endif %}" 
                               href="/admin/monitoring">
                                <i class="fas fa-chart-line me-2"></i>
                                系统监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/admin/settings' in request.url.path %}active{% endif %}" 
                               href="/admin/settings">
                                <i class="fas fa-cog me-2"></i>
                                系统设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/admin/logs' in request.url.path %}active{% endif %}" 
                               href="/admin/logs">
                                <i class="fas fa-file-alt me-2"></i>
                                系统日志
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="text-white-50">
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/api/v1/auth/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 顶部导航栏 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}仪表板{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="fas fa-sync-alt me-1"></i>
                                刷新
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                    data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                管理员
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/admin/profile">个人资料</a></li>
                                <li><a class="dropdown-item" href="/admin/settings">系统设置</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/api/v1/auth/logout">退出登录</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- 页面内容 -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JavaScript -->
    <script>
        // 自动刷新功能
        function enableAutoRefresh(interval = 30000) {
            setInterval(() => {
                if (document.visibilityState === 'visible') {
                    location.reload();
                }
            }, interval);
        }
        
        // 格式化数字
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }
        
        // 格式化时间
        function formatTime(timestamp) {
            return new Date(timestamp).toLocaleString('zh-CN');
        }
        
        // 显示成功消息
        function showSuccess(message) {
            showAlert(message, 'success');
        }
        
        // 显示错误消息
        function showError(message) {
            showAlert(message, 'danger');
        }
        
        // 显示警告消息
        function showWarning(message) {
            showAlert(message, 'warning');
        }
        
        // 显示通用警告
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    </script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>
