{% extends "base.html" %}

{% block title %}用户管理 - AI Gen Hub 管理控制台{% endblock %}
{% block page_title %}用户管理{% endblock %}

{% block content %}
<!-- 操作按钮 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="fas fa-plus me-2"></i>添加用户
                </button>
                <button type="button" class="btn btn-outline-secondary ms-2" onclick="exportUsers()">
                    <i class="fas fa-download me-2"></i>导出用户
                </button>
            </div>
            <div class="input-group" style="width: 300px;">
                <input type="text" class="form-control" placeholder="搜索用户..." id="searchInput">
                <button class="btn btn-outline-secondary" type="button" onclick="searchUsers()">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 用户列表 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-white">
                    <i class="fas fa-users me-2"></i>用户列表
                    <span class="badge bg-light text-dark ms-2">{{ users|length }} 个用户</span>
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="usersTable">
                        <thead class="table-light">
                            <tr>
                                <th>用户名</th>
                                <th>邮箱</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>API配额</th>
                                <th>注册时间</th>
                                <th>最后登录</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr data-user-id="{{ user.id }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ user.username }}</div>
                                            {% if user.full_name %}
                                            <small class="text-muted">{{ user.full_name }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>{{ user.email }}</td>
                                <td>
                                    {% if user.role == "admin" %}
                                    <span class="badge bg-danger">管理员</span>
                                    {% elif user.role == "developer" %}
                                    <span class="badge bg-warning">开发者</span>
                                    {% elif user.role == "user" %}
                                    <span class="badge bg-primary">用户</span>
                                    {% else %}
                                    <span class="badge bg-secondary">访客</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.status == "active" %}
                                    <span class="badge bg-success">活跃</span>
                                    {% elif user.status == "inactive" %}
                                    <span class="badge bg-secondary">非活跃</span>
                                    {% elif user.status == "suspended" %}
                                    <span class="badge bg-warning">暂停</span>
                                    {% else %}
                                    <span class="badge bg-danger">封禁</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        {% set usage_percent = (user.api_quota_used / user.api_quota * 100) if user.api_quota > 0 else 0 %}
                                        <div class="progress-bar 
                                            {% if usage_percent < 50 %}bg-success
                                            {% elif usage_percent < 80 %}bg-warning
                                            {% else %}bg-danger{% endif %}" 
                                            style="width: {{ usage_percent }}%">
                                            {{ user.api_quota_used }} / {{ user.api_quota }}
                                        </div>
                                    </div>
                                    <small class="text-muted">{{ "%.1f"|format(usage_percent) }}%</small>
                                </td>
                                <td>
                                    <small>{{ user.created_at }}</small>
                                </td>
                                <td>
                                    <small>{{ user.last_login_at }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="editUser('{{ user.id }}')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="viewUserDetails('{{ user.id }}')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% if user.status != "banned" %}
                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                onclick="suspendUser('{{ user.id }}')">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                        {% else %}
                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                onclick="activateUser('{{ user.id }}')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        {% endif %}
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteUser('{{ user.id }}', '{{ user.username }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="用户列表分页">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">上一页</a>
                        </li>
                        <li class="page-item active">
                            <a class="page-link" href="#">1</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">2</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">3</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">下一页</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 添加用户模态框 -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加新用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="fullName" class="form-label">全名</label>
                        <input type="text" class="form-control" id="fullName" name="full_name">
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">角色</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="user">用户</option>
                            <option value="developer">开发者</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="apiQuota" class="form-label">API配额</label>
                        <input type="number" class="form-control" id="apiQuota" name="api_quota" value="1000" min="0">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitAddUser()">添加用户</button>
            </div>
        </div>
    </div>
</div>

<!-- 用户详情模态框 -->
<div class="modal fade" id="userDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">用户详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailsContent">
                <!-- 用户详情内容将通过JavaScript动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// 搜索用户
function searchUsers() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const table = document.getElementById('usersTable');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    
    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const username = row.cells[0].textContent.toLowerCase();
        const email = row.cells[1].textContent.toLowerCase();
        
        if (username.includes(searchTerm) || email.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    }
}

// 添加用户
function submitAddUser() {
    const form = document.getElementById('addUserForm');
    const formData = new FormData(form);
    const userData = Object.fromEntries(formData);
    
    fetch('/admin/api/users', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('用户添加成功');
            location.reload();
        } else {
            showError(data.message || '添加用户失败');
        }
    })
    .catch(error => {
        console.error('添加用户失败:', error);
        showError('添加用户失败');
    });
}

// 编辑用户
function editUser(userId) {
    // 这里可以打开编辑用户的模态框
    showWarning('编辑用户功能开发中...');
}

// 查看用户详情
function viewUserDetails(userId) {
    fetch(`/admin/api/users/${userId}`)
        .then(response => response.json())
        .then(user => {
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-borderless">
                            <tr><td><strong>用户名:</strong></td><td>${user.username}</td></tr>
                            <tr><td><strong>邮箱:</strong></td><td>${user.email}</td></tr>
                            <tr><td><strong>全名:</strong></td><td>${user.full_name || '未设置'}</td></tr>
                            <tr><td><strong>角色:</strong></td><td>${user.role}</td></tr>
                            <tr><td><strong>状态:</strong></td><td>${user.status}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>使用统计</h6>
                        <table class="table table-borderless">
                            <tr><td><strong>API配额:</strong></td><td>${user.api_quota}</td></tr>
                            <tr><td><strong>已使用:</strong></td><td>${user.api_quota_used}</td></tr>
                            <tr><td><strong>注册时间:</strong></td><td>${user.created_at}</td></tr>
                            <tr><td><strong>最后登录:</strong></td><td>${user.last_login_at}</td></tr>
                        </table>
                    </div>
                </div>
            `;
            document.getElementById('userDetailsContent').innerHTML = content;
            new bootstrap.Modal(document.getElementById('userDetailsModal')).show();
        })
        .catch(error => {
            console.error('获取用户详情失败:', error);
            showError('获取用户详情失败');
        });
}

// 暂停用户
function suspendUser(userId) {
    if (confirm('确定要暂停这个用户吗？')) {
        updateUserStatus(userId, 'suspended');
    }
}

// 激活用户
function activateUser(userId) {
    if (confirm('确定要激活这个用户吗？')) {
        updateUserStatus(userId, 'active');
    }
}

// 删除用户
function deleteUser(userId, username) {
    if (confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复！`)) {
        fetch(`/admin/api/users/${userId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess('用户删除成功');
                location.reload();
            } else {
                showError(data.message || '删除用户失败');
            }
        })
        .catch(error => {
            console.error('删除用户失败:', error);
            showError('删除用户失败');
        });
    }
}

// 更新用户状态
function updateUserStatus(userId, status) {
    fetch(`/admin/api/users/${userId}/status`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('用户状态更新成功');
            location.reload();
        } else {
            showError(data.message || '更新用户状态失败');
        }
    })
    .catch(error => {
        console.error('更新用户状态失败:', error);
        showError('更新用户状态失败');
    });
}

// 导出用户
function exportUsers() {
    window.open('/admin/api/users/export', '_blank');
}

// 搜索框回车事件
document.getElementById('searchInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        searchUsers();
    }
});
</script>
{% endblock %}
