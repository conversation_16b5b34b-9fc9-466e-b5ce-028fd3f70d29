#!/usr/bin/env python3
"""
当贝 AI 供应商适配器独立测试脚本

这个脚本不依赖完整的项目环境，可以独立运行来测试 dangbei 供应商的基本功能。
"""

import sys
import os
import asyncio
from unittest.mock import MagicMock, AsyncMock
from typing import Dict, Any

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 模拟必要的依赖
class MockProviderConfig:
    def __init__(self, **kwargs):
        self.name = kwargs.get('name', 'dangbei')
        self.base_url = kwargs.get('base_url', 'https://api.dangbei.com')
        self.timeout = kwargs.get('timeout', 30)
        self.max_retries = kwargs.get('max_retries', 3)
        self.retry_delay = kwargs.get('retry_delay', 1.0)
        self.rate_limit = kwargs.get('rate_limit', 100)

class MockKeyManager:
    def __init__(self):
        self.key = MagicMock()
        self.key.key = "test-api-key"
    
    async def get_key(self, provider_name):
        return self.key
    
    async def record_request(self, provider_name, key, success, response_time, error):
        pass

class MockMessage:
    def __init__(self, role, content, name=None):
        self.role = role
        self.content = content
        self.name = name

class MockTextGenerationRequest:
    def __init__(self, **kwargs):
        self.model = kwargs.get('model', 'deepseek')
        self.messages = kwargs.get('messages', [])
        self.max_tokens = kwargs.get('max_tokens')
        self.temperature = kwargs.get('temperature')
        self.top_p = kwargs.get('top_p')
        self.stream = kwargs.get('stream', False)
        self.user = kwargs.get('user')
        self.provider_params = kwargs.get('provider_params', {})

# 模拟导入
sys.modules['ai_gen_hub.config.settings'] = MagicMock()
sys.modules['ai_gen_hub.core.interfaces'] = MagicMock()
sys.modules['ai_gen_hub.providers.base'] = MagicMock()
sys.modules['ai_gen_hub.utils.key_manager'] = MagicMock()

# 设置模拟的类和枚举
from unittest.mock import MagicMock
import types

# 创建模拟的接口模块
interfaces_module = types.ModuleType('interfaces')
interfaces_module.ImageGenerationRequest = MagicMock
interfaces_module.ImageGenerationResponse = MagicMock
interfaces_module.Message = MockMessage
interfaces_module.ModelType = MagicMock()
interfaces_module.ModelType.TEXT_GENERATION = "text_generation"
interfaces_module.TextGenerationChoice = MagicMock
interfaces_module.TextGenerationRequest = MockTextGenerationRequest
interfaces_module.TextGenerationResponse = MagicMock
interfaces_module.TextGenerationStreamChunk = MagicMock
interfaces_module.Usage = MagicMock
interfaces_module.FinishReason = MagicMock()
interfaces_module.FinishReason.STOP = "stop"
interfaces_module.FinishReason.LENGTH = "length"
interfaces_module.FinishReason.CONTENT_FILTER = "content_filter"
interfaces_module.FinishReason.TOOL_CALLS = "tool_calls"
interfaces_module.FinishReason.FUNCTION_CALL = "function_call"
interfaces_module.StreamChoice = MagicMock

sys.modules['ai_gen_hub.core.interfaces'] = interfaces_module

# 创建模拟的基础供应商类
class MockBaseProvider:
    def __init__(self, name, config, key_manager):
        self.name = name
        self.config = config
        self.key_manager = key_manager
        self._supported_model_types = []
        self._supported_models = []
        self._model_mapping = {}
        self.logger = MagicMock()
    
    def map_model_name(self, model):
        return self._model_mapping.get(model, model)
    
    async def _make_request(self, method, url, headers=None, json_data=None, stream=False):
        # 模拟HTTP请求
        mock_response = MagicMock()
        mock_response.status_code = 200
        
        if stream:
            # 模拟流式响应
            async def mock_stream():
                chunks = [
                    'data: {"id":"msg_123","object":"chat.completion.chunk","created":**********,"model":"deepseek","choices":[{"index":0,"delta":{"role":"assistant"},"finish_reason":null}]}\n'.encode('utf-8'),
                    'data: {"id":"msg_123","object":"chat.completion.chunk","created":**********,"model":"deepseek","choices":[{"index":0,"delta":{"content":"hello"},"finish_reason":null}]}\n'.encode('utf-8'),
                    'data: {"id":"msg_123","object":"chat.completion.chunk","created":**********,"model":"deepseek","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}\n'.encode('utf-8'),
                    'data: [DONE]\n'.encode('utf-8')
                ]
                for chunk in chunks:
                    yield chunk
            return mock_stream()
        else:
            # 模拟普通响应
            if "/api/models" in url:
                mock_response.json.return_value = {
                    "success": True,
                    "data": {
                        "defaultModel": "deepseek",
                        "models": [
                            {
                                "id": "deepseek",
                                "name": "DeepSeek-R1最新版",
                                "description": "专注逻辑推理与深度分析"
                            }
                        ]
                    }
                }
            elif "/api/chat" in url:
                mock_response.json.return_value = {
                    "success": True,
                    "data": {
                        "message": {
                            "role": "assistant",
                            "content": "你好！我是当贝AI助手。"
                        },
                        "message_id": "msg_123456",
                        "model": "deepseek",
                        "finish_reason": "stop",
                        "usage": {
                            "prompt_tokens": 10,
                            "completion_tokens": 20,
                            "total_tokens": 30
                        },
                        "timestamp": **********000
                    }
                }
            return mock_response
    
    def _extract_error_message(self, error_data):
        return error_data.get("error", "未知错误")

base_module = types.ModuleType('base')
base_module.BaseProvider = MockBaseProvider
sys.modules['ai_gen_hub.providers.base'] = base_module

# 直接导入供应商文件，避免循环导入
import importlib.util
spec = importlib.util.spec_from_file_location("dangbei_provider", "src/ai_gen_hub/providers/dangbei_provider.py")
dangbei_module = importlib.util.module_from_spec(spec)
sys.modules["dangbei_provider"] = dangbei_module
spec.loader.exec_module(dangbei_module)

DangbeiProvider = dangbei_module.DangbeiProvider

async def test_dangbei_provider():
    """测试当贝 AI 供应商的基本功能"""
    print("开始测试当贝 AI 供应商...")
    
    # 创建配置和密钥管理器
    config = MockProviderConfig()
    key_manager = MockKeyManager()
    
    # 创建供应商实例
    provider = DangbeiProvider(config, key_manager)
    
    print(f"✓ 供应商初始化成功: {provider.name}")
    print(f"✓ 基础URL: {provider.base_url}")
    print(f"✓ 支持的模型: {provider._supported_models}")
    
    # 测试健康检查
    try:
        health_result = await provider._perform_health_check("test-api-key")
        print(f"✓ 健康检查: {'通过' if health_result else '失败'}")
    except Exception as e:
        print(f"✗ 健康检查失败: {e}")
    
    # 测试获取模型列表
    try:
        models = await provider.get_models("test-api-key")
        print(f"✓ 获取模型列表成功: {len(models)} 个模型")
        for model in models:
            print(f"  - {model['id']}: {model['name']}")
    except Exception as e:
        print(f"✗ 获取模型列表失败: {e}")
    
    # 测试构建请求
    try:
        request = MockTextGenerationRequest(
            model="deepseek",
            messages=[MockMessage("user", "你好")],
            provider_params={"deep_thinking": True}
        )
        request_data = provider._build_chat_request(request)
        print("✓ 构建聊天请求成功")
        print(f"  - 模型: {request_data['model']}")
        print(f"  - 消息数量: {len(request_data['messages'])}")
        print(f"  - 选项: {request_data.get('options', {})}")
    except Exception as e:
        print(f"✗ 构建聊天请求失败: {e}")
    
    # 测试解析响应
    try:
        response_data = {
            "success": True,
            "data": {
                "message": {
                    "role": "assistant",
                    "content": "你好！我是当贝AI助手。"
                },
                "message_id": "msg_123456",
                "model": "deepseek",
                "finish_reason": "stop",
                "usage": {
                    "prompt_tokens": 10,
                    "completion_tokens": 20,
                    "total_tokens": 30
                }
            }
        }
        response = provider._parse_text_response(response_data, request)
        print("✓ 解析文本响应成功")
    except Exception as e:
        print(f"✗ 解析文本响应失败: {e}")
    
    # 测试流式数据解析
    try:
        chunk_data = {
            "id": "msg_123",
            "object": "chat.completion.chunk",
            "created": **********,
            "model": "deepseek",
            "choices": [
                {
                    "index": 0,
                    "delta": {"content": "你好"},
                    "finish_reason": None
                }
            ]
        }
        chunk = provider._parse_stream_chunk(chunk_data, request)
        print("✓ 解析流式数据块成功")
    except Exception as e:
        print(f"✗ 解析流式数据块失败: {e}")
    
    # 测试完成原因映射
    try:
        from ai_gen_hub.core.interfaces import FinishReason
        reason = provider._map_finish_reason("stop")
        print("✓ 完成原因映射成功")
    except Exception as e:
        print(f"✗ 完成原因映射失败: {e}")
    
    print("\n当贝 AI 供应商测试完成！")

if __name__ == "__main__":
    asyncio.run(test_dangbei_provider())
