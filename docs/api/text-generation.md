# 文本生成API

文本生成API提供了强大的AI文本生成功能，支持对话、补全、代码生成、翻译等多种场景。

## 📋 端点列表

| 方法 | 端点 | 描述 |
|------|------|------|
| POST | `/api/v1/text/generate` | 生成文本 |
| POST | `/api/v1/text/stream` | 流式生成文本 |
| GET | `/api/v1/text/models` | 获取可用模型列表 |
| POST | `/api/v1/text/chat` | 对话生成 |
| POST | `/api/v1/text/complete` | 文本补全 |

## 🚀 生成文本

### 请求

```http
POST /api/v1/text/generate
Content-Type: application/json
Authorization: Bearer <token>
```

#### 请求体

```json
{
  "messages": [
    {
      "role": "system",
      "content": "你是一个有用的AI助手。"
    },
    {
      "role": "user", 
      "content": "请解释什么是人工智能"
    }
  ],
  "model": "gemini-pro",
  "max_tokens": 500,
  "temperature": 0.7,
  "top_p": 0.9,
  "frequency_penalty": 0.0,
  "presence_penalty": 0.0,
  "stop": null,
  "stream": false
}
```

#### 参数说明

| 参数 | 类型 | 必需 | 默认值 | 描述 |
|------|------|------|--------|------|
| `messages` | Array | ✅ | - | 对话消息列表 |
| `model` | String | ✅ | - | 使用的模型名称 |
| `max_tokens` | Integer | ❌ | 1000 | 最大生成token数 |
| `temperature` | Float | ❌ | 0.7 | 控制随机性 (0.0-2.0) |
| `top_p` | Float | ❌ | 1.0 | 核采样参数 (0.0-1.0) |
| `frequency_penalty` | Float | ❌ | 0.0 | 频率惩罚 (-2.0-2.0) |
| `presence_penalty` | Float | ❌ | 0.0 | 存在惩罚 (-2.0-2.0) |
| `stop` | Array/String | ❌ | null | 停止序列 |
| `stream` | Boolean | ❌ | false | 是否流式输出 |

#### 消息格式

```json
{
  "role": "system|user|assistant",
  "content": "消息内容",
  "name": "可选的发送者名称"
}
```

### 响应

#### 成功响应

```json
{
  "success": true,
  "data": {
    "id": "gen_123456789",
    "content": "人工智能（AI）是计算机科学的一个分支...",
    "model": "gemini-pro",
    "usage": {
      "prompt_tokens": 25,
      "completion_tokens": 150,
      "total_tokens": 175
    },
    "finish_reason": "stop",
    "created_at": "2024-01-01T12:00:00Z",
    "provider": "google_ai"
  },
  "request_id": "req_123456789"
}
```

#### 响应字段说明

| 字段 | 类型 | 描述 |
|------|------|------|
| `id` | String | 生成结果的唯一ID |
| `content` | String | 生成的文本内容 |
| `model` | String | 使用的模型名称 |
| `usage` | Object | Token使用统计 |
| `finish_reason` | String | 结束原因 |
| `created_at` | String | 创建时间 |
| `provider` | String | 使用的AI供应商 |

#### 结束原因

| 值 | 描述 |
|----|------|
| `stop` | 自然结束 |
| `length` | 达到最大长度 |
| `content_filter` | 内容过滤 |
| `function_call` | 函数调用 |

## 🌊 流式生成

### 请求

```http
POST /api/v1/text/stream
Content-Type: application/json
Authorization: Bearer <token>
Accept: text/event-stream
```

请求体与普通生成相同，但会返回Server-Sent Events流。

### 响应

```
data: {"type": "start", "id": "gen_123456789"}

data: {"type": "content", "content": "人工智能"}

data: {"type": "content", "content": "（AI）是"}

data: {"type": "content", "content": "计算机科学"}

data: {"type": "end", "usage": {"total_tokens": 175}, "finish_reason": "stop"}
```

## 📋 获取模型列表

### 请求

```http
GET /api/v1/text/models
Authorization: Bearer <token>
```

### 响应

```json
{
  "success": true,
  "data": {
    "models": [
      {
        "id": "gemini-pro",
        "name": "Gemini Pro",
        "provider": "google_ai",
        "max_tokens": 32768,
        "supports_streaming": true,
        "supports_functions": true,
        "pricing": {
          "input": 0.0005,
          "output": 0.0015
        }
      },
      {
        "id": "gpt-3.5-turbo",
        "name": "GPT-3.5 Turbo",
        "provider": "openai",
        "max_tokens": 4096,
        "supports_streaming": true,
        "supports_functions": true,
        "pricing": {
          "input": 0.0015,
          "output": 0.002
        }
      }
    ]
  }
}
```

## 💬 对话生成

### 请求

```http
POST /api/v1/text/chat
Content-Type: application/json
Authorization: Bearer <token>
```

```json
{
  "conversation_id": "conv_123456789",
  "message": "今天天气怎么样？",
  "model": "gemini-pro",
  "context_length": 10
}
```

### 响应

```json
{
  "success": true,
  "data": {
    "conversation_id": "conv_123456789",
    "message_id": "msg_123456789",
    "content": "我无法获取实时天气信息...",
    "usage": {
      "total_tokens": 85
    }
  }
}
```

## ✏️ 文本补全

### 请求

```http
POST /api/v1/text/complete
Content-Type: application/json
Authorization: Bearer <token>
```

```json
{
  "prompt": "def fibonacci(n):",
  "model": "gemini-pro",
  "max_tokens": 200,
  "temperature": 0.1
}
```

### 响应

```json
{
  "success": true,
  "data": {
    "completion": "\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)",
    "usage": {
      "total_tokens": 45
    }
  }
}
```

## 🔧 使用示例

### cURL示例

```bash
# 生成文本
curl -X POST "http://localhost:8001/api/v1/text/generate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "messages": [
      {"role": "user", "content": "写一首关于春天的诗"}
    ],
    "model": "gemini-pro",
    "max_tokens": 500,
    "temperature": 0.7
  }'

# 流式生成文本
curl -X POST "http://localhost:8001/api/v1/text/stream" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Accept: text/event-stream" \
  -d '{
    "messages": [
      {"role": "user", "content": "详细解释机器学习"}
    ],
    "model": "gemini-pro",
    "stream": true
  }'

# 获取模型列表
curl -X GET "http://localhost:8001/api/v1/text/models" \
  -H "Authorization: Bearer your_jwt_token"
```

### Python示例

```python
import requests

def generate_text(prompt, model="gemini-pro"):
    url = "http://localhost:8001/api/v1/text/generate"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer your_token"
    }
    
    data = {
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "model": model,
        "max_tokens": 500,
        "temperature": 0.7
    }
    
    response = requests.post(url, headers=headers, json=data)
    
    if response.status_code == 200:
        result = response.json()
        return result["data"]["content"]
    else:
        raise Exception(f"API请求失败: {response.text}")

# 使用示例
text = generate_text("写一首关于春天的诗")
print(text)
```

### JavaScript示例

```javascript
async function generateText(prompt, model = 'gemini-pro') {
  const response = await fetch('http://localhost:8001/api/v1/text/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer your_token'
    },
    body: JSON.stringify({
      messages: [
        { role: 'user', content: prompt }
      ],
      model: model,
      max_tokens: 500,
      temperature: 0.7
    })
  });

  if (!response.ok) {
    throw new Error(`API请求失败: ${response.statusText}`);
  }

  const result = await response.json();
  return result.data.content;
}

// 使用示例
generateText('解释量子计算的基本原理')
  .then(text => console.log(text))
  .catch(error => console.error(error));
```

### 流式处理示例

```python
import requests
import json

def stream_generate(prompt):
    url = "http://localhost:8001/api/v1/text/stream"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer your_token",
        "Accept": "text/event-stream"
    }
    
    data = {
        "messages": [{"role": "user", "content": prompt}],
        "model": "gemini-pro",
        "stream": True
    }
    
    response = requests.post(url, headers=headers, json=data, stream=True)
    
    for line in response.iter_lines():
        if line:
            line = line.decode('utf-8')
            if line.startswith('data: '):
                data = json.loads(line[6:])
                if data['type'] == 'content':
                    print(data['content'], end='', flush=True)
                elif data['type'] == 'end':
                    print(f"\n\n使用token: {data['usage']['total_tokens']}")
                    break

# 使用示例
stream_generate("详细解释机器学习的工作原理")
```

## ❌ 错误处理

### 常见错误

| 错误代码 | HTTP状态码 | 描述 | 解决方案 |
|----------|------------|------|----------|
| `INVALID_MODEL` | 400 | 无效的模型名称 | 检查模型名称是否正确 |
| `TOKEN_LIMIT_EXCEEDED` | 400 | 超过token限制 | 减少输入长度或max_tokens |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 | 降低请求频率 |
| `QUOTA_EXCEEDED` | 429 | 配额超限 | 检查账户配额 |
| `PROVIDER_ERROR` | 502 | AI供应商错误 | 稍后重试 |

### 错误响应示例

```json
{
  "success": false,
  "error": {
    "code": "TOKEN_LIMIT_EXCEEDED",
    "message": "请求的token数量超过限制",
    "details": {
      "max_tokens": 4096,
      "requested_tokens": 5000
    }
  },
  "request_id": "req_123456789"
}
```

## 💡 最佳实践

1. **合理设置参数**
   - temperature: 创意任务用0.7-1.0，事实性任务用0.1-0.3
   - max_tokens: 根据实际需要设置，避免浪费

2. **错误处理**
   - 实现重试机制
   - 处理网络超时
   - 监控API配额

3. **性能优化**
   - 使用流式输出提升用户体验
   - 缓存常用结果
   - 批量处理多个请求

4. **安全考虑**
   - 验证用户输入
   - 过滤敏感内容
   - 保护API密钥
