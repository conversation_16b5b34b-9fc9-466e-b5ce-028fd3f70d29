# AI Gen Hub API 参考文档

本文档提供了AI Gen Hub所有API端点的详细说明，包括请求格式、响应格式、错误处理等。

## 📋 目录

- [认证API](./auth.md) - 用户认证、注册、token管理
- [文本生成API](./text-generation.md) - 文本生成服务
- [图像生成API](./image-generation.md) - 图像生成服务
- [用户管理API](./user-management.md) - 用户信息管理
- [系统API](./system.md) - 健康检查、监控指标
- [WebSocket API](./websocket.md) - 实时通信接口
- [管理API](./admin.md) - 管理控制台接口

## 🌐 基础信息

### 基础URL

```
生产环境: https://api.ai-gen-hub.com
开发环境: http://localhost:8001
```

### 认证方式

AI Gen Hub 支持两种认证方式：

1. **JWT Token认证** (推荐)
```http
Authorization: Bearer <your_jwt_token>
```

2. **API Key认证**
```http
X-API-Key: <your_api_key>
```

### 请求格式

- 所有API请求都使用JSON格式
- Content-Type必须设置为 `application/json`
- 字符编码使用UTF-8

### 响应格式

#### 成功响应

```json
{
  "success": true,
  "data": {
    // 具体的响应数据
  },
  "message": "操作成功",
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "req_123456789"
}
```

#### 错误响应

```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "请求参数无效",
    "details": {
      "field": "messages",
      "reason": "不能为空"
    }
  },
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "req_123456789"
}
```

### HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 资源创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 429 | 请求频率超限 |
| 500 | 服务器内部错误 |
| 502 | 上游服务错误 |
| 503 | 服务暂时不可用 |

### 错误代码

| 错误代码 | 说明 |
|----------|------|
| `INVALID_REQUEST` | 请求参数无效 |
| `AUTHENTICATION_FAILED` | 认证失败 |
| `PERMISSION_DENIED` | 权限不足 |
| `RESOURCE_NOT_FOUND` | 资源不存在 |
| `RATE_LIMIT_EXCEEDED` | 请求频率超限 |
| `QUOTA_EXCEEDED` | 配额超限 |
| `PROVIDER_ERROR` | AI供应商错误 |
| `INTERNAL_ERROR` | 服务器内部错误 |

### 分页

对于返回列表的API，支持分页参数：

```http
GET /api/v1/users?page=1&limit=20&sort=created_at&order=desc
```

分页响应格式：

```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

### 请求限制

| 限制类型 | 默认值 | 说明 |
|----------|--------|------|
| 请求频率 | 100/分钟 | 每个用户每分钟最大请求数 |
| 请求大小 | 10MB | 单个请求最大大小 |
| 并发连接 | 10 | 每个用户最大并发连接数 |
| Token长度 | 32K | 单次请求最大token数 |

### 版本控制

API使用URL路径进行版本控制：

```
/api/v1/...  # 当前版本
/api/v2/...  # 未来版本
```

### 内容类型

支持的Content-Type：

- `application/json` - JSON格式（默认）
- `multipart/form-data` - 文件上传
- `text/event-stream` - 流式响应

### 时间格式

所有时间字段都使用ISO 8601格式：

```
2024-01-01T12:00:00Z        # UTC时间
2024-01-01T12:00:00+08:00   # 带时区
```

### 示例代码

#### cURL

```bash
curl -X POST "http://localhost:8001/api/v1/text/generate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "messages": [
      {"role": "user", "content": "Hello, world!"}
    ],
    "model": "gemini-pro",
    "max_tokens": 100
  }'
```

#### Python

```python
import requests

headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer your_jwt_token"
}

data = {
    "messages": [
        {"role": "user", "content": "Hello, world!"}
    ],
    "model": "gemini-pro",
    "max_tokens": 100
}

response = requests.post(
    "http://localhost:8001/api/v1/text/generate",
    headers=headers,
    json=data
)

print(response.json())
```

#### JavaScript

```javascript
const response = await fetch('http://localhost:8001/api/v1/text/generate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your_jwt_token'
  },
  body: JSON.stringify({
    messages: [
      { role: 'user', content: 'Hello, world!' }
    ],
    model: 'gemini-pro',
    max_tokens: 100
  })
});

const result = await response.json();
console.log(result);
```

## 🔗 相关链接

- [快速开始指南](../guides/quickstart.md)
- [SDK文档](../sdk/)
- [错误处理指南](../guides/error-handling.md)
- [最佳实践](../guides/best-practices.md)
- [OpenAPI规范](http://localhost:8001/docs) - 交互式API文档

## 📞 支持

如果您在使用API时遇到问题，请：

1. 查看[常见问题](../guides/faq.md)
2. 查看[错误处理指南](../guides/error-handling.md)
3. 提交[Issue](https://github.com/your-org/ai-gen-hub/issues)
4. 联系技术支持：<EMAIL>
