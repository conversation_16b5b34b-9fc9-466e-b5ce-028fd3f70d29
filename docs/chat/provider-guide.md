# AI Gen Hub Chat 供应商对接指南

## 概述

本指南详细说明如何为AI Gen Hub统一Chat接口添加新的AI供应商支持。通过遵循本指南，开发者可以轻松地集成新的AI服务提供商。

## 供应商适配器架构

### 核心组件

1. **ChatProvider接口**：所有供应商适配器必须实现的基础接口
2. **BaseChatProvider**：提供通用功能的基础实现类
3. **参数映射**：统一参数到供应商特定参数的转换
4. **消息格式转换**：统一消息格式到供应商格式的转换
5. **响应解析**：供应商响应到统一格式的转换

### 实现步骤

#### 1. 创建供应商适配器类

```python
from ai_gen_hub.core.chat_base import BaseChatProvider
from ai_gen_hub.core.chat_interfaces import (
    ChatMessage, ChatConfig, ChatResponse, ChatStreamChunk
)

class NewProviderChatProvider(BaseChatProvider):
    """新供应商Chat适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        # 设置默认配置
        config.setdefault("base_url", "https://api.newprovider.com/v1")
        config.setdefault("timeout", 30.0)
        
        super().__init__("new_provider", config)
        
        # 支持的模型列表
        self._supported_models = [
            "new-model-1",
            "new-model-2",
            "new-model-3"
        ]
```

#### 2. 实现请求头生成

```python
def _get_headers(self) -> Dict[str, str]:
    """获取API请求头"""
    return {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {self.api_key}",
        "User-Agent": "AI-Gen-Hub/New-Provider-Chat"
    }
```

#### 3. 实现消息格式映射

```python
def _map_messages(self, messages: List[ChatMessage]) -> Any:
    """映射消息格式到供应商特定格式"""
    provider_messages = []
    
    for message in messages:
        # 根据供应商API要求转换消息格式
        provider_message = {
            "role": self._map_role(message.role),
            "content": message.content
        }
        
        # 处理特殊字段
        if message.tool_calls:
            provider_message["tool_calls"] = self._map_tool_calls(message.tool_calls)
        
        provider_messages.append(provider_message)
    
    return provider_messages

def _map_role(self, role: ChatMessageRole) -> str:
    """映射消息角色"""
    role_mapping = {
        ChatMessageRole.SYSTEM: "system",
        ChatMessageRole.USER: "user", 
        ChatMessageRole.ASSISTANT: "assistant",
        ChatMessageRole.TOOL: "tool"
    }
    return role_mapping.get(role, "user")
```

#### 4. 实现参数映射

```python
def _map_parameters(self, config: ChatConfig) -> Dict[str, Any]:
    """映射配置参数到供应商格式"""
    params = {}
    
    # 基础参数映射
    if config.max_tokens is not None:
        params["max_tokens"] = config.max_tokens
    
    params["temperature"] = config.temperature
    
    if config.top_p is not None:
        params["top_p"] = config.top_p
    
    # 供应商特定参数
    if config.provider_params:
        params.update(config.provider_params)
    
    return params
```

#### 5. 实现响应解析

```python
def _parse_response(self, response_data: Dict[str, Any], model: str) -> ChatResponse:
    """解析供应商响应为统一格式"""
    # 提取响应内容
    content = response_data.get("content", "")
    
    # 构建响应消息
    response_message = ChatMessage(
        role=ChatMessageRole.ASSISTANT,
        content=content
    )
    
    # 构建使用量统计
    usage_data = response_data.get("usage", {})
    usage = ChatUsage(
        prompt_tokens=usage_data.get("input_tokens", 0),
        completion_tokens=usage_data.get("output_tokens", 0),
        total_tokens=usage_data.get("total_tokens", 0)
    )
    
    # 映射完成原因
    finish_reason = self._map_finish_reason(response_data.get("stop_reason"))
    
    return ChatResponse(
        id=response_data.get("id", str(uuid4())),
        model=model,
        provider=self.provider_name,
        message=response_message,
        finish_reason=finish_reason,
        usage=usage
    )
```

#### 6. 实现Chat方法

```python
async def chat(
    self,
    messages: List[ChatMessage],
    config: ChatConfig
) -> ChatResponse:
    """发送Chat请求并获取响应"""
    # 验证输入
    self._validate_messages(messages)
    self._validate_config(config)
    
    # 构建请求参数
    provider_messages = self._map_messages(messages)
    params = self._map_parameters(config)
    params["messages"] = provider_messages
    
    # 选择模型
    model = config.provider_params.get("model", "new-model-1") if config.provider_params else "new-model-1"
    params["model"] = model
    
    try:
        client = await self._get_client()
        headers = self._get_headers()
        
        self.logger.info(f"发送{self.provider_name} Chat请求，模型: {model}")
        
        response = await client.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=params
        )
        
        if response.status_code != 200:
            await self._handle_http_error(response)
        
        response_data = response.json()
        chat_response = self._parse_response(response_data, model)
        
        self.logger.info(f"{self.provider_name} Chat请求成功")
        return chat_response
        
    except Exception as e:
        error_msg = f"请求失败: {str(e)}"
        self.logger.error(error_msg)
        raise ChatProviderError(error_msg, provider=self.provider_name)
```

#### 7. 实现流式Chat方法

```python
async def chat_stream(
    self,
    messages: List[ChatMessage],
    config: ChatConfig
) -> AsyncIterator[ChatStreamChunk]:
    """发送Chat请求并获取流式响应"""
    # 类似于chat方法，但处理流式响应
    # 具体实现取决于供应商的流式API格式
    pass
```

## 供应商特定处理

### 消息格式差异处理

不同供应商的消息格式可能有显著差异：

#### OpenAI格式
```json
{
  "messages": [
    {"role": "user", "content": "Hello"}
  ]
}
```

#### Google AI格式
```json
{
  "contents": [
    {
      "role": "user",
      "parts": [{"text": "Hello"}]
    }
  ]
}
```

#### Anthropic格式
```json
{
  "messages": [
    {"role": "user", "content": "Hello"}
  ],
  "system": "You are a helpful assistant"
}
```

### 参数名称映射

| 统一参数 | OpenAI | Google AI | Anthropic | 新供应商 |
|---------|--------|-----------|-----------|----------|
| max_tokens | max_tokens | maxOutputTokens | max_tokens | max_length |
| temperature | temperature | temperature | temperature | temp |
| top_p | top_p | topP | top_p | nucleus_p |

### 流式响应格式

不同供应商的流式响应格式也不同：

#### OpenAI SSE格式
```
data: {"choices":[{"delta":{"content":"Hello"}}]}
data: [DONE]
```

#### Google AI格式
```json
{"candidates":[{"content":{"parts":[{"text":"Hello"}]}}]}
```

#### Anthropic格式
```
event: content_block_delta
data: {"delta":{"text":"Hello"}}
```

## 测试新供应商适配器

### 单元测试

```python
import pytest
from your_provider_chat import NewProviderChatProvider

class TestNewProviderChatProvider:
    def test_create_provider(self):
        provider = NewProviderChatProvider({"api_key": "test-key"})
        assert provider.provider_name == "new_provider"
    
    def test_message_mapping(self):
        provider = NewProviderChatProvider({"api_key": "test-key"})
        messages = [ChatMessage(role=ChatMessageRole.USER, content="test")]
        mapped = provider._map_messages(messages)
        assert mapped[0]["role"] == "user"
        assert mapped[0]["content"] == "test"
    
    def test_parameter_mapping(self):
        provider = NewProviderChatProvider({"api_key": "test-key"})
        config = ChatConfig(temperature=0.7, max_tokens=100)
        params = provider._map_parameters(config)
        assert params["temperature"] == 0.7
        assert params["max_tokens"] == 100
```

### 集成测试

```python
@pytest.mark.asyncio
async def test_real_api_call():
    """测试真实API调用（需要有效的API密钥）"""
    provider = NewProviderChatProvider({"api_key": "real-api-key"})
    
    messages = [ChatMessage(role=ChatMessageRole.USER, content="Hello")]
    config = ChatConfig(temperature=0.7, max_tokens=50)
    
    response = await provider.chat(messages, config)
    
    assert response.provider == "new_provider"
    assert response.message.role == ChatMessageRole.ASSISTANT
    assert len(response.message.content) > 0
```

## 注册新供应商

### 1. 添加到管理器

```python
from ai_gen_hub.chat import UnifiedChatManager
from your_provider_chat import NewProviderChatProvider

manager = UnifiedChatManager({})
new_provider = NewProviderChatProvider({"api_key": "your-api-key"})
manager.register_provider(new_provider, weight=1.0)
```

### 2. 添加便捷函数

```python
# 在 ai_gen_hub/chat/__init__.py 中添加
def create_new_provider(api_key: str, **kwargs) -> NewProviderChatProvider:
    """创建新供应商的便捷函数"""
    config = {"api_key": api_key}
    config.update(kwargs)
    return NewProviderChatProvider(config)

# 添加到 __all__ 列表
__all__.append("create_new_provider")
```

### 3. 更新文档

在主文档中添加新供应商的说明：

```markdown
### 新供应商适配器

```python
from ai_gen_hub.chat import create_new_provider

provider = create_new_provider("your-api-key", timeout=60)
```

**支持的模型**:
- `new-model-1`
- `new-model-2`
- `new-model-3`
```

## 最佳实践

### 1. 错误处理
- 实现完整的HTTP状态码处理
- 提供有意义的错误消息
- 正确分类不同类型的错误

### 2. 日志记录
- 记录请求和响应的关键信息
- 使用适当的日志级别
- 包含供应商特定的调试信息

### 3. 性能优化
- 复用HTTP连接
- 实现适当的超时和重试
- 优化大响应的处理

### 4. 安全考虑
- 不在日志中记录敏感信息
- 验证输入参数
- 正确处理认证信息

### 5. 兼容性
- 支持供应商API的多个版本
- 处理API变更和弃用
- 提供向后兼容性

## 常见问题

### Q: 如何处理供应商特有的功能？
A: 可以在`provider_params`中传递供应商特定的参数，或者扩展统一接口来支持新功能。

### Q: 如何处理不同的认证方式？
A: 在`_get_headers()`方法中实现供应商特定的认证逻辑。

### Q: 如何支持多模态输入？
A: 可以扩展`ChatMessage`模型的`attachments`字段来支持多模态内容。

### Q: 如何处理流式响应的差异？
A: 在`_parse_stream_chunk()`方法中实现供应商特定的流式响应解析逻辑。

## 贡献指南

1. Fork项目仓库
2. 创建新的供应商适配器
3. 编写完整的测试用例
4. 更新相关文档
5. 提交Pull Request

欢迎为AI Gen Hub贡献新的供应商支持！
