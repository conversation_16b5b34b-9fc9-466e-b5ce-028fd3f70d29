# AI Gen Hub Chat API 参考文档

## 核心接口

### ChatProvider 抽象基类

所有Chat供应商适配器的基类，定义了标准的接口规范。

```python
class ChatProvider(ABC):
    """Chat供应商抽象基类"""
    
    def __init__(self, provider_name: str, config: Dict[str, Any]):
        """初始化供应商
        
        Args:
            provider_name: 供应商名称
            config: 供应商配置字典
        """
    
    @abstractmethod
    async def chat(
        self,
        messages: List[ChatMessage],
        config: ChatConfig
    ) -> ChatResponse:
        """发送Chat请求并获取响应
        
        Args:
            messages: 对话消息列表
            config: Chat配置参数
            
        Returns:
            ChatResponse: Chat响应
            
        Raises:
            ChatError: Chat相关错误
        """
    
    @abstractmethod
    async def chat_stream(
        self,
        messages: List[ChatMessage],
        config: ChatConfig
    ) -> AsyncIterator[ChatStreamChunk]:
        """发送Chat请求并获取流式响应
        
        Args:
            messages: 对话消息列表
            config: Chat配置参数
            
        Yields:
            ChatStreamChunk: Chat流式响应块
            
        Raises:
            ChatError: Chat相关错误
        """
    
    @abstractmethod
    async def validate_config(self, config: ChatConfig) -> bool:
        """验证配置参数是否有效"""
    
    @abstractmethod
    async def get_supported_models(self) -> List[str]:
        """获取支持的模型列表"""
    
    async def health_check(self) -> bool:
        """健康检查"""
```

### ChatManager 抽象基类

Chat管理器的基类，定义了管理多个供应商的接口。

```python
class ChatManager(ABC):
    """Chat管理器抽象基类"""
    
    @abstractmethod
    async def chat(
        self,
        messages: List[ChatMessage],
        config: ChatConfig,
        provider: Optional[str] = None
    ) -> ChatResponse:
        """发送Chat请求
        
        Args:
            messages: 对话消息列表
            config: Chat配置参数
            provider: 指定供应商（可选）
            
        Returns:
            ChatResponse: Chat响应
        """
    
    @abstractmethod
    async def chat_stream(
        self,
        messages: List[ChatMessage],
        config: ChatConfig,
        provider: Optional[str] = None
    ) -> AsyncIterator[ChatStreamChunk]:
        """发送Chat流式请求"""
    
    @abstractmethod
    async def get_available_providers(self) -> List[str]:
        """获取可用的供应商列表"""
```

## 数据模型

### ChatMessage

对话消息模型，表示对话中的单条消息。

```python
class ChatMessage(BaseModel):
    """统一的Chat消息模型"""
    
    role: ChatMessageRole                           # 消息角色
    content: str                                    # 消息内容
    name: Optional[str] = None                      # 发送者名称
    tool_calls: Optional[List[Dict[str, Any]]] = None  # 工具调用信息
    tool_call_id: Optional[str] = None              # 工具调用ID
    attachments: Optional[List[Dict[str, Any]]] = None  # 附件信息
    metadata: Optional[Dict[str, Any]] = None       # 消息元数据
```

**字段说明**:
- `role`: 消息角色，可选值：`system`、`user`、`assistant`、`tool`、`function`
- `content`: 消息的文本内容
- `name`: 消息发送者的名称（可选）
- `tool_calls`: 工具调用信息，用于AI请求调用外部工具
- `tool_call_id`: 工具调用的唯一标识，用于工具响应消息
- `attachments`: 附件信息，支持多模态内容（图片、文件等）
- `metadata`: 消息的元数据，可存储额外信息

### ChatConfig

Chat配置参数模型，控制AI的生成行为。

```python
class ChatConfig(BaseModel):
    """Chat配置参数模型"""
    
    # 基础生成参数
    max_tokens: Optional[int] = None                # 最大生成token数
    temperature: float = 0.7                        # 生成温度
    top_p: Optional[float] = None                   # 核采样参数
    top_k: Optional[int] = None                     # Top-K采样参数
    
    # 惩罚参数
    frequency_penalty: float = 0.0                  # 频率惩罚
    presence_penalty: float = 0.0                   # 存在惩罚
    
    # 停止条件
    stop_sequences: Optional[List[str]] = None      # 停止序列列表
    
    # 流式配置
    stream: bool = False                            # 是否启用流式输出
    
    # 工具和函数调用
    tools: Optional[List[Dict[str, Any]]] = None    # 可用工具列表
    tool_choice: Optional[Union[str, Dict[str, Any]]] = None  # 工具选择策略
    
    # 安全和过滤
    safety_settings: Optional[List[Dict[str, Any]]] = None  # 安全设置
    
    # 供应商特定参数
    provider_params: Optional[Dict[str, Any]] = None  # 供应商特定参数
```

**参数说明**:
- `max_tokens`: 限制AI生成的最大token数量
- `temperature`: 控制生成的随机性，0.0最确定，2.0最随机
- `top_p`: 核采样，只考虑累积概率达到p的token
- `top_k`: 只考虑概率最高的k个token
- `frequency_penalty`: 降低重复词汇的概率
- `presence_penalty`: 鼓励谈论新话题
- `stop_sequences`: 遇到这些序列时停止生成
- `stream`: 是否使用流式输出
- `tools`: 可供AI调用的工具定义
- `tool_choice`: 工具选择策略（"auto"、"none"或具体工具）
- `safety_settings`: 内容安全过滤设置
- `provider_params`: 传递给特定供应商的额外参数

### ChatResponse

Chat响应模型，包含AI的回复和相关信息。

```python
class ChatResponse(BaseModel):
    """Chat响应模型"""
    
    id: str                                         # 响应唯一标识
    created: datetime                               # 创建时间
    model: str                                      # 使用的模型名称
    provider: str                                   # 供应商名称
    message: ChatMessage                            # AI助手的回复消息
    finish_reason: ChatFinishReason                 # 完成原因
    usage: ChatUsage                               # token使用量统计
    metadata: Optional[Dict[str, Any]] = None       # 响应元数据
```

### ChatStreamChunk

流式响应块模型，表示流式输出中的单个数据块。

```python
class ChatStreamChunk(BaseModel):
    """Chat流式响应块模型"""
    
    id: str                                         # 响应唯一标识
    event_type: ChatStreamEventType                 # 事件类型
    delta_content: Optional[str] = None             # 内容增量
    delta_tool_calls: Optional[List[Dict[str, Any]]] = None  # 工具调用增量
    finish_reason: Optional[ChatFinishReason] = None  # 完成原因
    usage: Optional[ChatUsage] = None               # 使用量统计
    accumulated_content: Optional[str] = None       # 累积的完整内容
    metadata: Optional[Dict[str, Any]] = None       # 块元数据
```

### ChatUsage

Token使用量统计模型。

```python
class ChatUsage(BaseModel):
    """Chat使用量统计模型"""
    
    prompt_tokens: int = 0                          # 输入token数量
    completion_tokens: int = 0                      # 输出token数量
    total_tokens: int = 0                          # 总token数量
    cache_creation_input_tokens: Optional[int] = None  # 缓存创建输入token数
    cache_read_input_tokens: Optional[int] = None   # 缓存读取输入token数
```

## 枚举类型

### ChatMessageRole

消息角色枚举。

```python
class ChatMessageRole(str, Enum):
    SYSTEM = "system"        # 系统消息，设置AI行为
    USER = "user"           # 用户消息
    ASSISTANT = "assistant"  # AI助手消息
    FUNCTION = "function"    # 函数调用结果（已弃用）
    TOOL = "tool"           # 工具调用结果
```

### ChatFinishReason

对话完成原因枚举。

```python
class ChatFinishReason(str, Enum):
    STOP = "stop"                    # 自然停止
    LENGTH = "length"                # 达到长度限制
    CONTENT_FILTER = "content_filter"  # 内容被过滤
    TOOL_CALLS = "tool_calls"        # 请求工具调用
    FUNCTION_CALL = "function_call"   # 请求函数调用（已弃用）
    ERROR = "error"                  # 发生错误
```

### ChatStreamEventType

流式事件类型枚举。

```python
class ChatStreamEventType(str, Enum):
    MESSAGE_START = "message_start"      # 消息开始
    CONTENT_DELTA = "content_delta"      # 内容增量
    TOOL_CALL_DELTA = "tool_call_delta"  # 工具调用增量
    MESSAGE_STOP = "message_stop"        # 消息结束
    ERROR = "error"                      # 错误事件
```

## 异常类型

### ChatError

Chat相关错误的基类。

```python
class ChatError(Exception):
    """Chat相关错误的基类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, provider: Optional[str] = None):
        self.message = message          # 错误消息
        self.error_code = error_code    # 错误代码
        self.provider = provider        # 供应商名称
```

### ChatValidationError

参数验证错误。

```python
class ChatValidationError(ChatError):
    """Chat参数验证错误"""
```

### ChatProviderError

供应商相关错误。

```python
class ChatProviderError(ChatError):
    """Chat供应商错误"""
```

### ChatRateLimitError

请求频率限制错误。

```python
class ChatRateLimitError(ChatError):
    """Chat速率限制错误"""
```

### ChatContentFilterError

内容过滤错误。

```python
class ChatContentFilterError(ChatError):
    """Chat内容过滤错误"""
```

## 供应商适配器

### OpenAIChatProvider

OpenAI Chat适配器，支持GPT系列模型。

```python
class OpenAIChatProvider(BaseChatProvider):
    """OpenAI Chat供应商适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化OpenAI适配器
        
        Args:
            config: 配置字典，支持以下字段：
                - api_key: OpenAI API密钥（必需）
                - base_url: API基础URL（可选）
                - organization: 组织ID（可选）
                - timeout: 请求超时时间（可选）
                - max_retries: 最大重试次数（可选）
        """
```

**支持的模型**:
- `gpt-4o`
- `gpt-4o-mini`
- `gpt-4-turbo`
- `gpt-4`
- `gpt-3.5-turbo`

### GoogleAIChatProvider

Google AI Chat适配器，支持Gemini系列模型。

```python
class GoogleAIChatProvider(BaseChatProvider):
    """Google AI Chat供应商适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化Google AI适配器
        
        Args:
            config: 配置字典，支持以下字段：
                - api_key: Google AI API密钥（必需）
                - base_url: API基础URL（可选）
                - timeout: 请求超时时间（可选）
                - max_retries: 最大重试次数（可选）
        """
```

**支持的模型**:
- `gemini-2.5-pro`
- `gemini-2.5-flash`
- `gemini-2.0-flash`
- `gemini-1.5-pro`
- `gemini-1.5-flash`

### AnthropicChatProvider

Anthropic Chat适配器，支持Claude系列模型。

```python
class AnthropicChatProvider(BaseChatProvider):
    """Anthropic Chat供应商适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化Anthropic适配器
        
        Args:
            config: 配置字典，支持以下字段：
                - api_key: Anthropic API密钥（必需）
                - base_url: API基础URL（可选）
                - timeout: 请求超时时间（可选）
                - max_retries: 最大重试次数（可选）
        """
```

**支持的模型**:
- `claude-3-5-sonnet-20241022`
- `claude-3-opus-20240229`
- `claude-3-sonnet-20240229`
- `claude-3-haiku-20240307`
