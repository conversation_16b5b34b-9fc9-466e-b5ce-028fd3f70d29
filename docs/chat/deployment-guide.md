# AI Gen Hub Chat 部署指南

## 概述

本指南详细说明如何在生产环境中部署AI Gen Hub统一Chat接口系统，包括环境配置、性能优化、监控告警等关键内容。

## 环境要求

### 系统要求

- **操作系统**：Linux (推荐 Ubuntu 20.04+, CentOS 8+)
- **Python版本**：Python 3.8+
- **内存**：最低 2GB，推荐 4GB+
- **CPU**：最低 2核，推荐 4核+
- **存储**：最低 10GB 可用空间
- **网络**：稳定的互联网连接，支持HTTPS

### Python依赖

```bash
# 核心依赖
pip install httpx>=0.24.0
pip install pydantic>=2.0.0
pip install asyncio

# 可选依赖（用于监控和日志）
pip install prometheus-client>=0.16.0
pip install structlog>=22.0.0
pip install uvloop>=0.17.0  # Linux性能优化
```

## 安装部署

### 1. 源码安装

```bash
# 克隆仓库
git clone https://github.com/aier/ai-gen-hub.git
cd ai-gen-hub

# 安装依赖
pip install -r requirements.txt

# 安装项目
pip install -e .
```

### 2. Docker部署

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制源码
COPY src/ ./src/
COPY setup.py .

# 安装项目
RUN pip install -e .

# 创建非root用户
RUN useradd -m -u 1000 appuser
USER appuser

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "ai_gen_hub.chat.server"]
```

```bash
# 构建镜像
docker build -t ai-gen-hub-chat:latest .

# 运行容器
docker run -d \
  --name ai-gen-hub-chat \
  -p 8000:8000 \
  -e OPENAI_API_KEY=your-openai-key \
  -e GOOGLE_AI_API_KEY=your-google-key \
  -e ANTHROPIC_API_KEY=your-anthropic-key \
  ai-gen-hub-chat:latest
```

### 3. Kubernetes部署

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-gen-hub-chat
  labels:
    app: ai-gen-hub-chat
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-gen-hub-chat
  template:
    metadata:
      labels:
        app: ai-gen-hub-chat
    spec:
      containers:
      - name: ai-gen-hub-chat
        image: ai-gen-hub-chat:latest
        ports:
        - containerPort: 8000
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-api-keys
              key: openai-key
        - name: GOOGLE_AI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-api-keys
              key: google-key
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-api-keys
              key: anthropic-key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: ai-gen-hub-chat-service
spec:
  selector:
    app: ai-gen-hub-chat
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
```

## 配置管理

### 1. 环境变量配置

```bash
# API密钥配置
export OPENAI_API_KEY="your-openai-api-key"
export GOOGLE_AI_API_KEY="your-google-ai-api-key"
export ANTHROPIC_API_KEY="your-anthropic-api-key"

# 服务配置
export CHAT_SERVER_HOST="0.0.0.0"
export CHAT_SERVER_PORT="8000"
export CHAT_LOG_LEVEL="INFO"

# 性能配置
export CHAT_MAX_CONCURRENT_REQUESTS="100"
export CHAT_REQUEST_TIMEOUT="30"
export CHAT_MAX_RETRIES="3"

# 监控配置
export CHAT_ENABLE_METRICS="true"
export CHAT_METRICS_PORT="9090"
```

### 2. 配置文件

```yaml
# config/production.yaml
chat:
  server:
    host: "0.0.0.0"
    port: 8000
    workers: 4
  
  providers:
    openai:
      api_key: "${OPENAI_API_KEY}"
      base_url: "https://api.openai.com/v1"
      timeout: 30
      max_retries: 3
      weight: 1.0
    
    google_ai:
      api_key: "${GOOGLE_AI_API_KEY}"
      base_url: "https://generativelanguage.googleapis.com/v1beta"
      timeout: 30
      max_retries: 3
      weight: 1.5
    
    anthropic:
      api_key: "${ANTHROPIC_API_KEY}"
      base_url: "https://api.anthropic.com"
      timeout: 30
      max_retries: 3
      weight: 0.8
  
  manager:
    default_provider: "openai"
    max_retries: 3
    retry_delay: 1.0
    health_check_interval: 60
    failure_threshold: 3
    recovery_time: 300
  
  logging:
    level: "INFO"
    format: "json"
    file: "/var/log/ai-gen-hub/chat.log"
  
  metrics:
    enabled: true
    port: 9090
    path: "/metrics"
```

### 3. 安全配置

```yaml
# config/security.yaml
security:
  api_keys:
    # API密钥加密存储
    encryption_key: "${ENCRYPTION_KEY}"
    
  rate_limiting:
    # 请求频率限制
    requests_per_minute: 1000
    burst_size: 100
    
  cors:
    # 跨域配置
    allowed_origins:
      - "https://your-frontend.com"
      - "https://api.your-domain.com"
    allowed_methods: ["GET", "POST"]
    allowed_headers: ["Content-Type", "Authorization"]
    
  tls:
    # TLS配置
    cert_file: "/etc/ssl/certs/ai-gen-hub.crt"
    key_file: "/etc/ssl/private/ai-gen-hub.key"
    min_version: "1.2"
```

## 性能优化

### 1. 连接池配置

```python
# 优化HTTP连接池
import httpx

client_config = {
    "timeout": httpx.Timeout(30.0),
    "limits": httpx.Limits(
        max_connections=200,        # 最大连接数
        max_keepalive_connections=50,  # 保持连接数
        keepalive_expiry=30.0      # 连接保持时间
    ),
    "http2": True,                 # 启用HTTP/2
}
```

### 2. 异步优化

```python
# 使用uvloop提升性能（Linux）
import asyncio
import uvloop

# 设置事件循环
asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

# 并发控制
semaphore = asyncio.Semaphore(100)  # 限制并发请求数

async def rate_limited_request():
    async with semaphore:
        # 执行请求
        pass
```

### 3. 缓存策略

```python
# Redis缓存配置
import redis.asyncio as redis

cache_config = {
    "host": "redis-server",
    "port": 6379,
    "db": 0,
    "max_connections": 20,
    "retry_on_timeout": True,
    "socket_keepalive": True,
    "socket_keepalive_options": {},
}

# 缓存常用响应
cache_ttl = {
    "model_list": 3600,      # 模型列表缓存1小时
    "health_check": 60,      # 健康检查缓存1分钟
    "provider_stats": 300,   # 统计信息缓存5分钟
}
```

## 监控告警

### 1. Prometheus指标

```python
# metrics.py
from prometheus_client import Counter, Histogram, Gauge

# 请求计数器
chat_requests_total = Counter(
    'chat_requests_total',
    'Total chat requests',
    ['provider', 'model', 'status']
)

# 响应时间直方图
chat_request_duration = Histogram(
    'chat_request_duration_seconds',
    'Chat request duration',
    ['provider', 'model']
)

# 活跃连接数
active_connections = Gauge(
    'chat_active_connections',
    'Number of active connections',
    ['provider']
)

# 供应商健康状态
provider_health = Gauge(
    'chat_provider_health',
    'Provider health status (1=healthy, 0=unhealthy)',
    ['provider']
)
```

### 2. 健康检查端点

```python
# health.py
from fastapi import FastAPI, Response

app = FastAPI()

@app.get("/health")
async def health_check():
    """基础健康检查"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/ready")
async def readiness_check():
    """就绪检查 - 检查所有依赖服务"""
    checks = {
        "database": await check_database(),
        "redis": await check_redis(),
        "providers": await check_providers()
    }
    
    all_healthy = all(checks.values())
    status_code = 200 if all_healthy else 503
    
    return Response(
        content=json.dumps({"ready": all_healthy, "checks": checks}),
        status_code=status_code,
        media_type="application/json"
    )

@app.get("/metrics")
async def metrics():
    """Prometheus指标端点"""
    from prometheus_client import generate_latest
    return Response(
        content=generate_latest(),
        media_type="text/plain"
    )
```

### 3. 日志配置

```python
# logging_config.py
import structlog
import logging

# 结构化日志配置
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer(ensure_ascii=False)
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

# 日志轮转配置
from logging.handlers import RotatingFileHandler

handler = RotatingFileHandler(
    "/var/log/ai-gen-hub/chat.log",
    maxBytes=100*1024*1024,  # 100MB
    backupCount=10
)
```

## 高可用部署

### 1. 负载均衡

```nginx
# nginx.conf
upstream ai_gen_hub_chat {
    least_conn;
    server chat-server-1:8000 weight=1 max_fails=3 fail_timeout=30s;
    server chat-server-2:8000 weight=1 max_fails=3 fail_timeout=30s;
    server chat-server-3:8000 weight=1 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name api.your-domain.com;
    
    location /chat/ {
        proxy_pass http://ai_gen_hub_chat;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    location /health {
        access_log off;
        proxy_pass http://ai_gen_hub_chat;
    }
}
```

### 2. 数据库集群

```yaml
# PostgreSQL主从配置
postgresql:
  primary:
    host: "postgres-primary"
    port: 5432
    database: "ai_gen_hub"
    username: "chat_user"
    password: "${DB_PASSWORD}"
    max_connections: 20
    
  replica:
    host: "postgres-replica"
    port: 5432
    database: "ai_gen_hub"
    username: "chat_readonly"
    password: "${DB_READONLY_PASSWORD}"
    max_connections: 10
```

### 3. Redis集群

```yaml
# Redis Sentinel配置
redis:
  sentinel:
    hosts:
      - "redis-sentinel-1:26379"
      - "redis-sentinel-2:26379"
      - "redis-sentinel-3:26379"
    master_name: "ai-gen-hub-master"
    socket_timeout: 0.5
    socket_connect_timeout: 0.5
```

## 安全加固

### 1. 网络安全

```bash
# 防火墙配置
ufw allow 22/tcp      # SSH
ufw allow 80/tcp      # HTTP
ufw allow 443/tcp     # HTTPS
ufw allow 9090/tcp    # Metrics (限制来源IP)
ufw --force enable
```

### 2. 容器安全

```dockerfile
# 安全的Dockerfile
FROM python:3.11-slim

# 创建非特权用户
RUN groupadd -r appgroup && useradd -r -g appgroup appuser

# 设置安全的文件权限
COPY --chown=appuser:appgroup . /app
WORKDIR /app

# 切换到非特权用户
USER appuser

# 只暴露必要端口
EXPOSE 8000
```

### 3. API安全

```python
# 认证中间件
from fastapi import HTTPException, Depends
from fastapi.security import HTTPBearer

security = HTTPBearer()

async def verify_api_key(token: str = Depends(security)):
    """验证API密钥"""
    if not is_valid_api_key(token.credentials):
        raise HTTPException(
            status_code=401,
            detail="Invalid API key"
        )
    return token.credentials

# 速率限制
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

@app.post("/chat")
@limiter.limit("100/minute")
async def chat_endpoint(request: Request, api_key: str = Depends(verify_api_key)):
    # Chat处理逻辑
    pass
```

## 故障排除

### 1. 常见问题

#### 连接超时
```bash
# 检查网络连接
curl -v https://api.openai.com/v1/models

# 检查DNS解析
nslookup api.openai.com

# 检查防火墙
iptables -L
```

#### 内存不足
```bash
# 监控内存使用
free -h
top -p $(pgrep -f "ai-gen-hub")

# 检查内存泄漏
valgrind --tool=memcheck python -m ai_gen_hub.chat.server
```

#### 高CPU使用率
```bash
# 性能分析
py-spy top --pid $(pgrep -f "ai-gen-hub")
py-spy record -o profile.svg --pid $(pgrep -f "ai-gen-hub")
```

### 2. 日志分析

```bash
# 查看错误日志
tail -f /var/log/ai-gen-hub/chat.log | grep ERROR

# 分析请求模式
grep "chat_request" /var/log/ai-gen-hub/chat.log | \
  jq '.provider' | sort | uniq -c

# 监控响应时间
grep "response_time" /var/log/ai-gen-hub/chat.log | \
  jq '.response_time' | awk '{sum+=$1; count++} END {print sum/count}'
```

## 维护操作

### 1. 滚动更新

```bash
# Kubernetes滚动更新
kubectl set image deployment/ai-gen-hub-chat \
  ai-gen-hub-chat=ai-gen-hub-chat:v1.1.0

# 检查更新状态
kubectl rollout status deployment/ai-gen-hub-chat

# 回滚（如果需要）
kubectl rollout undo deployment/ai-gen-hub-chat
```

### 2. 数据备份

```bash
# 配置备份
tar -czf config-backup-$(date +%Y%m%d).tar.gz /etc/ai-gen-hub/

# 日志归档
find /var/log/ai-gen-hub/ -name "*.log" -mtime +30 -exec gzip {} \;

# 数据库备份
pg_dump ai_gen_hub > backup-$(date +%Y%m%d).sql
```

### 3. 性能调优

```bash
# 系统调优
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65535' >> /etc/sysctl.conf
sysctl -p

# 文件描述符限制
echo '* soft nofile 65535' >> /etc/security/limits.conf
echo '* hard nofile 65535' >> /etc/security/limits.conf
```

通过遵循本部署指南，您可以在生产环境中安全、稳定地运行AI Gen Hub统一Chat接口系统。
