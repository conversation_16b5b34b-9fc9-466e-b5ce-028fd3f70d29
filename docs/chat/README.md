# AI Gen Hub 统一Chat接口文档

## 概述

AI Gen Hub 统一Chat接口是一个强大的抽象层，旨在为多个AI供应商提供一致的对话功能接口。通过这个统一接口，开发者可以轻松地在不同的AI供应商之间切换，而无需修改业务逻辑代码。

## 主要特性

### 🔄 多供应商支持
- **OpenAI**: 支持GPT-4、GPT-3.5等主流模型
- **Google AI**: 支持Gemini 2.5、Gemini 1.5等系列模型
- **Anthropic**: 支持Claude 3.5、Claude 3等系列模型

### 🛡️ 统一接口设计
- 标准化的消息格式和数据结构
- 一致的配置参数和响应格式
- 统一的错误处理和异常体系
- 完整的类型注解支持

### ⚡ 高级功能
- **流式响应**: 支持实时流式对话
- **工具调用**: 支持函数调用和工具集成
- **负载均衡**: 智能的供应商选择和负载分配
- **故障转移**: 自动故障检测和切换
- **健康监控**: 实时监控供应商状态

### 🔧 易于使用
- 简洁的API设计
- 丰富的便捷函数
- 详细的中文文档
- 完整的示例代码

## 快速开始

### 安装依赖

```bash
pip install httpx pydantic
```

### 基础使用示例

```python
import asyncio
from ai_gen_hub.chat import (
    create_chat_manager,
    create_openai_provider,
    ChatMessage,
    ChatConfig,
    ChatMessageRole
)

async def main():
    # 创建Chat管理器
    manager = create_chat_manager()
    
    # 创建并注册OpenAI供应商
    openai_provider = create_openai_provider("your-openai-api-key")
    manager.register_provider(openai_provider)
    
    # 准备对话消息
    messages = [
        ChatMessage(role=ChatMessageRole.SYSTEM, content="你是一个有用的AI助手。"),
        ChatMessage(role=ChatMessageRole.USER, content="请介绍一下人工智能的发展历史。")
    ]
    
    # 配置Chat参数
    config = ChatConfig(
        temperature=0.7,
        max_tokens=1000,
        provider_params={"model": "gpt-3.5-turbo"}
    )
    
    # 发送Chat请求
    response = await manager.chat(messages, config)
    
    print(f"AI回复: {response.message.content}")
    print(f"使用token: {response.usage.total_tokens}")

# 运行示例
asyncio.run(main())
```

### 多供应商设置

```python
from ai_gen_hub.chat import (
    create_chat_manager,
    create_openai_provider,
    create_google_ai_provider,
    create_anthropic_provider
)

async def setup_multi_provider():
    # 创建管理器
    manager = create_chat_manager({
        "default_provider": "openai",
        "max_retries": 3,
        "failure_threshold": 5
    })
    
    # 注册多个供应商
    openai_provider = create_openai_provider("your-openai-key")
    google_provider = create_google_ai_provider("your-google-key")
    anthropic_provider = create_anthropic_provider("your-anthropic-key")
    
    manager.register_provider(openai_provider, weight=1.0)
    manager.register_provider(google_provider, weight=1.5)
    manager.register_provider(anthropic_provider, weight=0.8)
    
    # 设置模型到供应商的映射
    manager.set_model_provider_mapping({
        "gpt-4": "openai",
        "gpt-3.5-turbo": "openai",
        "gemini-2.5-pro": "google_ai",
        "gemini-2.5-flash": "google_ai",
        "claude-3-5-sonnet": "anthropic",
        "claude-3-opus": "anthropic"
    })
    
    return manager
```

### 流式对话示例

```python
async def stream_chat_example():
    manager = await setup_multi_provider()
    
    messages = [
        ChatMessage(role=ChatMessageRole.USER, content="请写一首关于春天的诗。")
    ]
    
    config = ChatConfig(
        temperature=0.8,
        max_tokens=200,
        stream=True,
        provider_params={"model": "gpt-3.5-turbo"}
    )
    
    print("AI正在创作...")
    async for chunk in manager.chat_stream(messages, config):
        if chunk.delta_content:
            print(chunk.delta_content, end="", flush=True)
        
        if chunk.finish_reason:
            print(f"\n\n完成原因: {chunk.finish_reason}")
            if chunk.usage:
                print(f"总计使用token: {chunk.usage.total_tokens}")
```

## 核心组件

### 数据模型

#### ChatMessage - 对话消息
```python
message = ChatMessage(
    role=ChatMessageRole.USER,           # 消息角色
    content="你好，AI助手！",              # 消息内容
    name="用户名",                       # 发送者名称（可选）
    tool_calls=[...],                   # 工具调用信息（可选）
    metadata={"source": "web"}          # 元数据（可选）
)
```

#### ChatConfig - 配置参数
```python
config = ChatConfig(
    max_tokens=1000,                    # 最大生成token数
    temperature=0.7,                    # 生成温度（0.0-2.0）
    top_p=0.9,                         # 核采样参数
    frequency_penalty=0.0,              # 频率惩罚
    presence_penalty=0.0,               # 存在惩罚
    stop_sequences=["停止", "结束"],      # 停止序列
    stream=False,                       # 是否流式输出
    tools=[...],                       # 可用工具列表
    provider_params={"model": "gpt-4"}  # 供应商特定参数
)
```

#### ChatResponse - 响应结果
```python
# 响应包含以下信息：
response.id                    # 响应唯一标识
response.model                 # 使用的模型名称
response.provider              # 供应商名称
response.message               # AI助手的回复消息
response.finish_reason         # 完成原因
response.usage                 # token使用量统计
response.created               # 创建时间
```

### 供应商适配器

#### OpenAI适配器
```python
from ai_gen_hub.chat import OpenAIChatProvider

provider = OpenAIChatProvider({
    "api_key": "your-openai-api-key",
    "base_url": "https://api.openai.com/v1",  # 可选
    "organization": "your-org-id",            # 可选
    "timeout": 30.0,                          # 可选
    "max_retries": 3                          # 可选
})

# 支持的模型
models = await provider.get_supported_models()
# ['gpt-4o', 'gpt-4-turbo', 'gpt-3.5-turbo', ...]
```

#### Google AI适配器
```python
from ai_gen_hub.chat import GoogleAIChatProvider

provider = GoogleAIChatProvider({
    "api_key": "your-google-ai-api-key",
    "base_url": "https://generativelanguage.googleapis.com/v1beta",  # 可选
    "timeout": 30.0,                                                 # 可选
    "max_retries": 3                                                 # 可选
})

# 支持的模型
models = await provider.get_supported_models()
# ['gemini-2.5-pro', 'gemini-2.5-flash', 'gemini-1.5-pro', ...]
```

#### Anthropic适配器
```python
from ai_gen_hub.chat import AnthropicChatProvider

provider = AnthropicChatProvider({
    "api_key": "your-anthropic-api-key",
    "base_url": "https://api.anthropic.com",  # 可选
    "timeout": 30.0,                          # 可选
    "max_retries": 3                          # 可选
})

# 支持的模型
models = await provider.get_supported_models()
# ['claude-3-5-sonnet-20241022', 'claude-3-opus-20240229', ...]
```

### 统一管理器

#### 创建和配置
```python
from ai_gen_hub.chat import UnifiedChatManager

manager = UnifiedChatManager({
    "default_provider": "openai",           # 默认供应商
    "max_retries": 3,                      # 最大重试次数
    "retry_delay": 1.0,                    # 重试延迟（秒）
    "health_check_interval": 60,           # 健康检查间隔（秒）
    "failure_threshold": 3,                # 故障阈值
    "recovery_time": 300                   # 恢复时间（秒）
})
```

#### 供应商管理
```python
# 注册供应商
manager.register_provider(openai_provider, weight=1.0)
manager.register_provider(google_provider, weight=1.5)

# 注销供应商
manager.unregister_provider("openai")

# 获取可用供应商
available = await manager.get_available_providers()

# 获取统计信息
stats = manager.get_provider_stats()
print(f"OpenAI成功率: {stats['openai']['success_rate']:.2%}")

# 重置统计信息
manager.reset_provider_stats("openai")
```

## 高级功能

### 工具调用支持

```python
# 定义工具
tools = [
    {
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "获取指定城市的天气信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "city": {
                        "type": "string",
                        "description": "城市名称"
                    }
                },
                "required": ["city"]
            }
        }
    }
]

# 配置工具调用
config = ChatConfig(
    tools=tools,
    tool_choice="auto",  # 或 "none" 或具体工具名
    provider_params={"model": "gpt-4"}
)

# 发送请求
response = await manager.chat(messages, config)

# 处理工具调用
if response.message.tool_calls:
    for tool_call in response.message.tool_calls:
        function_name = tool_call["function"]["name"]
        arguments = tool_call["function"]["arguments"]
        print(f"AI请求调用工具: {function_name}({arguments})")
```

### 错误处理

```python
from ai_gen_hub.chat import (
    ChatError,
    ChatValidationError,
    ChatProviderError,
    ChatRateLimitError,
    ChatContentFilterError
)

try:
    response = await manager.chat(messages, config)
except ChatValidationError as e:
    print(f"参数验证错误: {e.message}")
except ChatRateLimitError as e:
    print(f"请求频率超限: {e.message}")
except ChatContentFilterError as e:
    print(f"内容被过滤: {e.message}")
except ChatProviderError as e:
    print(f"供应商错误: {e.message}")
except ChatError as e:
    print(f"通用Chat错误: {e.message}")
```

### 监控和统计

```python
# 获取详细统计信息
stats = manager.get_provider_stats()

for provider_name, provider_stats in stats.items():
    print(f"\n供应商: {provider_name}")
    print(f"  总请求数: {provider_stats['total_requests']}")
    print(f"  成功请求数: {provider_stats['successful_requests']}")
    print(f"  失败请求数: {provider_stats['failed_requests']}")
    print(f"  成功率: {provider_stats['success_rate']:.2%}")
    print(f"  平均响应时间: {provider_stats['average_response_time']:.2f}秒")
    print(f"  连续失败次数: {provider_stats['consecutive_failures']}")
    print(f"  是否被禁用: {provider_stats['is_disabled']}")
    print(f"  负载均衡权重: {provider_stats['weight']}")
```

## 最佳实践

### 1. 供应商配置
- 为不同供应商设置合适的权重
- 根据模型特性配置超时时间
- 设置合理的重试次数和延迟

### 2. 错误处理
- 始终使用try-catch处理Chat请求
- 根据不同错误类型采取相应措施
- 记录详细的错误日志用于调试

### 3. 性能优化
- 使用流式响应提升用户体验
- 合理设置max_tokens避免不必要的开销
- 定期检查供应商统计信息优化配置

### 4. 安全考虑
- 妥善保管API密钥，不要硬编码
- 使用环境变量或安全的配置管理
- 注意内容过滤和安全设置

## 故障排除

### 常见问题

#### 1. API密钥错误
```
ChatProviderError: 认证失败: Invalid API key
```
**解决方案**: 检查API密钥是否正确，是否有足够的权限。

#### 2. 网络连接问题
```
ChatProviderError: 网络请求失败: Connection timeout
```
**解决方案**: 检查网络连接，增加超时时间，或使用代理。

#### 3. 请求频率超限
```
ChatRateLimitError: 请求频率超限: Rate limit exceeded
```
**解决方案**: 降低请求频率，升级API套餐，或使用多个供应商分散负载。

#### 4. 内容被过滤
```
ChatContentFilterError: 内容被安全过滤器阻止
```
**解决方案**: 修改输入内容，调整安全设置，或使用其他供应商。

### 调试技巧

1. **启用详细日志**:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. **检查供应商状态**:
```python
available = await manager.get_available_providers()
print(f"可用供应商: {available}")
```

3. **查看统计信息**:
```python
stats = manager.get_provider_stats()
for name, stat in stats.items():
    if stat['consecutive_failures'] > 0:
        print(f"{name} 连续失败: {stat['consecutive_failures']}")
```

## 更新日志

### v1.0.0 (2025-08-24)
- 🎉 首次发布统一Chat接口
- ✅ 支持OpenAI、Google AI、Anthropic三大供应商
- ✅ 实现统一的数据模型和接口定义
- ✅ 提供完整的负载均衡和故障转移功能
- ✅ 包含全面的单元测试和集成测试
- ✅ 提供详细的中文文档和示例代码

## 贡献指南

欢迎为AI Gen Hub统一Chat接口贡献代码！请参考以下步骤：

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件。

## 联系我们

- 项目主页: [AI Gen Hub](https://github.com/aier/ai-gen-hub)
- 问题反馈: [Issues](https://github.com/aier/ai-gen-hub/issues)
- 邮箱: <EMAIL>

---

**AI Gen Hub Team** - 让AI集成更简单！

## 相关文档

- [API参考文档](api-reference.md) - 详细的API接口说明和数据模型
- [供应商对接指南](provider-guide.md) - 如何添加新的AI供应商适配器
- [部署指南](deployment-guide.md) - 生产环境部署和运维指南
- [性能优化指南](performance-guide.md) - 性能调优和最佳实践

### 快速导航

- **新手入门**：从[快速开始](#快速开始)部分开始
- **开发者**：查看[API参考文档](api-reference.md)和[供应商对接指南](provider-guide.md)
- **运维人员**：参考[部署指南](deployment-guide.md)和[性能优化指南](performance-guide.md)
- **问题排查**：查看[故障排除](#故障排除)部分
