# 当贝 AI 供应商适配器

当贝 AI 供应商适配器为 AI Gen Hub 提供了对当贝 AI 平台的完整支持。当贝 AI 是一个提供多种先进 AI 模型服务的平台，主要特点包括支持 DeepSeek-R1 等先进模型、提供深度思考和联网搜索功能、支持流式和非流式响应。

## 功能特性

### 支持的功能
- ✅ 文本生成（聊天对话）
- ✅ 流式响应
- ✅ 模型列表动态获取
- ✅ 深度思考模式
- ✅ 联网搜索功能
- ❌ 图像生成（暂不支持）

### 支持的模型
- `deepseek` - DeepSeek-R1最新版，专注逻辑推理与深度分析
- 更多模型通过 API 动态获取

## 配置说明

### 基础配置
```python
from ai_gen_hub.config.settings import ProviderConfig

config = ProviderConfig(
    name="dangbei",
    base_url="https://api.dangbei.com",  # 当贝 AI API 基础URL
    timeout=30,
    max_retries=3,
    retry_delay=1.0,
    rate_limit=100
)
```

### API 密钥配置
当贝 AI 使用 Bearer Token 认证方式：

```python
# 环境变量方式
export DANGBEI_API_KEY="your-api-key-here"

# 或在代码中配置
from ai_gen_hub.utils.key_manager import KeyManager

key_manager = KeyManager()
await key_manager.add_key("dangbei", "your-api-key-here")
```

## 使用示例

### 基础文本生成
```python
import asyncio
from ai_gen_hub.providers.dangbei_provider import DangbeiProvider
from ai_gen_hub.core.interfaces import Message, MessageRole, TextGenerationRequest

async def basic_text_generation():
    # 初始化供应商
    provider = DangbeiProvider(config, key_manager)
    await provider.initialize()
    
    # 创建请求
    request = TextGenerationRequest(
        model="deepseek",
        messages=[
            Message(role=MessageRole.USER, content="请解释什么是人工智能")
        ],
        max_tokens=1000,
        temperature=0.7
    )
    
    # 生成文本
    response = await provider.generate_text(request)
    print(f"回复: {response.choices[0].message.content}")
    
    await provider.cleanup()

# 运行示例
asyncio.run(basic_text_generation())
```

### 启用深度思考模式
```python
async def deep_thinking_example():
    provider = DangbeiProvider(config, key_manager)
    await provider.initialize()
    
    request = TextGenerationRequest(
        model="deepseek",
        messages=[
            Message(role=MessageRole.USER, content="请分析量子计算的发展前景")
        ],
        provider_params={
            "deep_thinking": True,  # 启用深度思考
            "online_search": False  # 禁用联网搜索
        }
    )
    
    response = await provider.generate_text(request)
    print(f"深度思考回复: {response.choices[0].message.content}")
    
    await provider.cleanup()
```

### 流式响应示例
```python
async def streaming_example():
    provider = DangbeiProvider(config, key_manager)
    await provider.initialize()
    
    request = TextGenerationRequest(
        model="deepseek",
        messages=[
            Message(role=MessageRole.USER, content="请写一首关于春天的诗")
        ],
        stream=True,  # 启用流式响应
        provider_params={
            "deep_thinking": True,
            "online_search": False
        }
    )
    
    # 处理流式响应
    stream = await provider.generate_text(request)
    async for chunk in stream:
        if chunk.choices and chunk.choices[0].delta.content:
            print(chunk.choices[0].delta.content, end="", flush=True)
    
    print()  # 换行
    await provider.cleanup()
```

### 获取可用模型列表
```python
async def list_models_example():
    provider = DangbeiProvider(config, key_manager)
    await provider.initialize()
    
    # 获取API密钥
    key = await key_manager.get_key("dangbei")
    
    # 获取模型列表
    models = await provider.get_models(key.key)
    
    print("可用模型:")
    for model in models:
        print(f"- {model['id']}: {model['name']}")
        print(f"  描述: {model['description']}")
        if model.get('recommended'):
            print("  (推荐)")
        print()
    
    await provider.cleanup()
```

## 高级配置

### 自定义选项配置
```python
request = TextGenerationRequest(
    model="deepseek",
    messages=[Message(role=MessageRole.USER, content="你好")],
    provider_params={
        # 当贝 AI 特有选项
        "deep_thinking": True,      # 启用深度思考
        "online_search": True,      # 启用联网搜索
        "conversation_id": "conv_123",  # 对话ID（可选）
        
        # 其他自定义参数
        "custom_param": "value"
    }
)
```

### 错误处理
```python
from ai_gen_hub.core.exceptions import (
    AuthenticationError,
    QuotaExceededError,
    RateLimitError,
    ModelNotSupportedError
)

async def error_handling_example():
    provider = DangbeiProvider(config, key_manager)
    await provider.initialize()
    
    try:
        request = TextGenerationRequest(
            model="deepseek",
            messages=[Message(role=MessageRole.USER, content="你好")]
        )
        
        response = await provider.generate_text(request)
        print(response.choices[0].message.content)
        
    except AuthenticationError:
        print("API 密钥无效")
    except QuotaExceededError:
        print("配额已用完")
    except RateLimitError as e:
        print(f"请求频率超限，请等待 {e.retry_after} 秒")
    except ModelNotSupportedError:
        print("模型不支持")
    except Exception as e:
        print(f"其他错误: {e}")
    finally:
        await provider.cleanup()
```

## API 接口说明

### 聊天对话接口
当贝 AI 的聊天接口 (`/api/chat`) 支持以下功能：
- 多轮对话
- 流式和非流式响应
- 深度思考模式
- 联网搜索功能
- 对话状态管理

### 文本生成接口
当贝 AI 还提供独立的文本生成接口 (`/api/text/generate`)，支持：
- 单次文本生成
- 任务类型指定
- 样式和格式控制
- 语言指定

## 注意事项

1. **API 密钥安全**: 请妥善保管您的 API 密钥，不要在代码中硬编码
2. **请求频率**: 注意遵守 API 的请求频率限制
3. **模型可用性**: 模型列表可能会动态变化，建议定期刷新
4. **流式响应**: 流式响应使用 Server-Sent Events 格式
5. **错误处理**: 建议实现完善的错误处理机制

## 故障排除

### 常见问题

**Q: 健康检查失败**
A: 检查 API 密钥是否正确，网络连接是否正常

**Q: 模型不可用**
A: 使用 `get_models()` 方法获取最新的模型列表

**Q: 流式响应中断**
A: 检查网络连接稳定性，考虑增加重试机制

**Q: 配额不足**
A: 检查账户余额和使用量限制

### 调试技巧

1. 启用详细日志记录
2. 使用健康检查验证连接
3. 测试简单请求后再尝试复杂功能
4. 检查 API 响应格式是否符合预期

## 更新日志

### v1.0.0 (2024-08-24)
- 初始版本发布
- 支持基础文本生成功能
- 支持流式响应
- 支持深度思考和联网搜索选项
- 完整的错误处理机制
