# 企业级部署和集成系统说明

AI Gen Hub 企业级部署和集成系统为大型组织提供完整的企业级功能，包括多租户管理、SSO集成、审计日志、合规性支持和安全监控，满足企业级应用的所有需求。

## 🚀 功能概览

### 1. 企业配置管理
- **多租户架构**: 支持多个组织的独立配置和数据隔离
- **灵活部署**: 支持云部署、私有化部署、混合部署等多种模式
- **安全配置**: 企业级安全策略和权限控制
- **资源管理**: 用户数量、API调用、存储空间等资源限制

### 2. SSO单点登录集成
- **多供应商支持**: Azure AD、Google Workspace、Okta、SAML、OIDC等
- **无缝集成**: 与现有企业身份系统深度集成
- **自动用户同步**: 自动创建和同步用户账户
- **安全令牌管理**: JWT令牌生成、验证和管理

### 3. 全面审计日志
- **完整记录**: 记录所有用户操作和系统事件
- **实时监控**: 实时安全事件检测和告警
- **风险评估**: 智能风险评分和异常检测
- **长期保留**: 可配置的日志保留策略

### 4. 合规性支持
- **多标准支持**: GDPR、SOC2、HIPAA、ISO27001等
- **自动化检查**: 自动合规性检查和报告生成
- **证据收集**: 完整的合规证据链
- **持续监控**: 实时合规状态监控

## 📋 核心组件

### 企业配置模型
```python
@dataclass
class EnterpriseConfig:
    organization_id: str
    organization_name: str
    deployment_type: DeploymentType  # CLOUD, ON_PREMISE, HYBRID
    security_level: SecurityLevel   # LOW, MEDIUM, HIGH, CRITICAL
    
    # 安全配置
    enable_mfa: bool = True
    password_policy: Dict[str, Any]
    session_timeout: int = 3600
    
    # SSO配置
    sso_enabled: bool = False
    sso_provider: Optional[SSOProvider] = None
    
    # 审计配置
    audit_enabled: bool = True
    audit_retention_days: int = 365
    
    # 合规配置
    compliance_standards: List[ComplianceStandard]
    data_residency: str
    encryption_at_rest: bool = True
    encryption_in_transit: bool = True
```

### SSO配置模型
```python
@dataclass
class SSOConfiguration:
    config_id: str
    organization_id: str
    provider: SSOProvider
    
    # 连接配置
    endpoint_url: str
    client_id: str
    client_secret: str
    
    # SAML配置
    saml_entity_id: Optional[str] = None
    saml_sso_url: Optional[str] = None
    saml_x509_cert: Optional[str] = None
    
    # 属性映射
    attribute_mapping: Dict[str, str]
    auto_provision_users: bool = True
    default_role: str = "user"
```

### 审计日志模型
```python
@dataclass
class AuditLog:
    log_id: str
    organization_id: str
    event_type: AuditEventType
    event_name: str
    
    # 用户信息
    user_id: Optional[str] = None
    user_email: Optional[str] = None
    user_ip: Optional[str] = None
    
    # 资源信息
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    action: str = ""
    result: str = "success"
    
    # 安全信息
    risk_score: float = 0.0
    security_flags: List[str]
    
    # 时间和元数据
    timestamp: datetime
    metadata: Dict[str, Any]
```

## 🔧 API接口

### 企业配置管理

#### 创建企业配置
```http
POST /api/v1/enterprise/config
Content-Type: application/json
Authorization: Bearer <admin_token>

{
  "organization_name": "科技有限公司",
  "deployment_type": "cloud",
  "domain": "company.com",
  "contact_email": "<EMAIL>",
  "security_level": "high",
  "features_enabled": {
    "sso": true,
    "audit": true,
    "compliance": true
  }
}
```

#### 更新企业配置
```http
PUT /api/v1/enterprise/config/{organization_id}
Content-Type: application/json

{
  "config_updates": {
    "security_level": "critical",
    "enable_mfa": true,
    "session_timeout": 1800,
    "audit_retention_days": 2555
  }
}
```

#### 获取企业配置
```http
GET /api/v1/enterprise/config/{organization_id}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "organization_id": "org_12345",
    "organization_name": "科技有限公司",
    "deployment_type": "cloud",
    "domain": "company.com",
    "security_level": "high",
    "enable_mfa": true,
    "password_policy": {
      "min_length": 12,
      "require_uppercase": true,
      "require_lowercase": true,
      "require_numbers": true,
      "require_special_chars": true,
      "max_age_days": 90
    },
    "sso_enabled": true,
    "audit_enabled": true,
    "audit_retention_days": 365,
    "compliance_standards": ["gdpr", "soc2"],
    "max_users": 1000,
    "max_api_calls_per_day": 100000,
    "created_at": "2024-01-15T10:00:00Z",
    "updated_at": "2024-01-15T14:30:00Z"
  }
}
```

### SSO集成管理

#### 创建SSO配置
```http
POST /api/v1/enterprise/sso/config
Content-Type: application/json

{
  "organization_id": "org_12345",
  "provider": "azure_ad",
  "name": "Azure AD集成",
  "endpoint_url": "https://login.microsoftonline.com/tenant-id",
  "client_id": "azure-client-123",
  "client_secret": "azure-secret-456",
  "attribute_mapping": {
    "email": "mail",
    "first_name": "givenName",
    "last_name": "surname",
    "display_name": "displayName"
  },
  "auto_provision_users": true,
  "default_role": "user"
}
```

#### 发起SSO登录
```http
GET /api/v1/enterprise/sso/login/{organization_id}?redirect_url=https://app.company.com/dashboard
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "login_url": "https://login.microsoftonline.com/tenant-id/oauth2/v2.0/authorize?response_type=code&client_id=azure-client-123&redirect_uri=https://api.ai-gen-hub.com/sso/callback/org_12345&scope=openid+profile+email&state=abc123",
    "state": "abc123",
    "expires_in": 300
  }
}
```

#### 处理SSO回调
```http
POST /api/v1/enterprise/sso/callback/{organization_id}
Content-Type: application/json

{
  "code": "authorization_code_from_provider",
  "state": "abc123"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "user_info": {
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "display_name": "John Doe",
      "role": "user",
      "organization_id": "org_12345",
      "sso_provider": "azure_ad"
    },
    "token": "jwt_token_here",
    "expires_in": 3600,
    "refresh_token": "refresh_token_here"
  }
}
```

### 审计日志管理

#### 查询审计日志
```http
GET /api/v1/enterprise/audit/logs?organization_id=org_12345&event_types=user_login,config_change&start_time=2024-01-01T00:00:00Z&end_time=2024-01-31T23:59:59Z&limit=100&offset=0
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "log_id": "audit_12345",
        "organization_id": "org_12345",
        "event_type": "user_login",
        "event_name": "用户登录",
        "timestamp": "2024-01-15T10:30:00Z",
        "user_id": "user_456",
        "user_email": "<EMAIL>",
        "user_ip": "*************",
        "action": "sso_login",
        "result": "success",
        "risk_score": 1.0,
        "security_flags": [],
        "metadata": {
          "sso_provider": "azure_ad",
          "device": "desktop",
          "browser": "Chrome"
        }
      }
    ],
    "total_count": 1250,
    "has_more": true
  }
}
```

#### 导出审计日志
```http
POST /api/v1/enterprise/audit/export
Content-Type: application/json

{
  "organization_id": "org_12345",
  "start_time": "2024-01-01T00:00:00Z",
  "end_time": "2024-01-31T23:59:59Z",
  "format": "json",
  "event_types": ["user_login", "data_access", "config_change"]
}
```

#### 获取审计统计
```http
GET /api/v1/enterprise/audit/statistics/{organization_id}?days=30
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "total_events": 15420,
    "security_events": 23,
    "failed_events": 156,
    "time_range": {
      "start": "2024-01-01T00:00:00Z",
      "end": "2024-01-31T23:59:59Z",
      "days": 30
    },
    "event_types": {
      "user_login": 8520,
      "user_logout": 8234,
      "data_access": 2156,
      "config_change": 89,
      "security_event": 23
    },
    "risk_distribution": {
      "low": 14850,
      "medium": 520,
      "high": 45,
      "critical": 5
    },
    "top_users": [
      ["user_123", 245],
      ["user_456", 198],
      ["user_789", 167]
    ],
    "daily_counts": {
      "2024-01-15": 520,
      "2024-01-16": 485,
      "2024-01-17": 612
    }
  }
}
```

## 📞 技术支持

如有问题或建议，请联系：
- 📧 邮箱: <EMAIL>
- 📱 微信: ai-enterprise-support
- 🌐 官网: https://ai-gen-hub.com/enterprise
- 📚 文档: https://docs.ai-gen-hub.com/enterprise
