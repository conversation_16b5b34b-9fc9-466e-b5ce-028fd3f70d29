# 自定义 AI Provider 接入指南

本指南详细介绍如何在 AI Gen Hub 中接入自定义的 AI provider，包括完整的实现步骤、配置方法和测试验证。

## 📋 目录

- [概述](#概述)
- [实现步骤](#实现步骤)
- [创建自定义 Provider](#创建自定义-provider)
- [配置系统集成](#配置系统集成)
- [注册和使用](#注册和使用)
- [测试验证](#测试验证)
- [最佳实践](#最佳实践)

## 🌟 概述

### 架构说明

AI Gen Hub 采用插件化的 provider 架构，支持轻松接入新的 AI 服务商：

```
BaseProvider (基类)
├── 通用功能：HTTP客户端、错误处理、重试机制
├── 抽象方法：需要子类实现的核心功能
└── 接口规范：统一的请求/响应格式

CustomProvider (自定义实现)
├── 继承 BaseProvider
├── 实现抽象方法
├── 定义支持的模型和功能
└── 处理特定 API 格式
```

### 核心组件

- **BaseProvider**: 提供通用功能的基类
- **ProviderConfig**: 配置管理
- **ProviderManager**: 供应商生命周期管理
- **KeyManager**: API 密钥安全管理
- **接口规范**: 统一的请求/响应格式

## 🚀 实现步骤

### 步骤 1: 创建 Provider 类

继承 `BaseProvider` 并实现必要的抽象方法：

```python
from ai_gen_hub.providers.base import BaseProvider
from ai_gen_hub.core.interfaces import (
    TextGenerationRequest,
    TextGenerationResponse,
    ModelType
)

class CustomProvider(BaseProvider):
    """自定义 AI Provider 实现"""
    
    def __init__(self, config: ProviderConfig, key_manager: KeyManager):
        super().__init__("custom_provider", config, key_manager)
        
        # 设置 API 基础 URL
        self.base_url = config.base_url or "https://api.custom-ai.com/v1"
        
        # 定义支持的模型类型
        self._supported_model_types = [
            ModelType.TEXT_GENERATION,
            # ModelType.IMAGE_GENERATION,  # 如果支持图像生成
        ]
        
        # 定义支持的模型列表
        self._supported_models = [
            "custom-model-v1",
            "custom-model-v2",
            "custom-model-lite",
        ]
```

### 步骤 2: 实现核心方法

必须实现的抽象方法：

```python
async def _perform_health_check(self, api_key: str) -> bool:
    """执行健康检查"""
    try:
        headers = {"Authorization": f"Bearer {api_key}"}
        response = await self._client.get(
            f"{self.base_url}/models",
            headers=headers
        )
        return response.status_code == 200
    except Exception:
        return False

async def generate_text(self, request: TextGenerationRequest) -> TextGenerationResponse:
    """生成文本"""
    # 获取 API 密钥
    api_key = await self.key_manager.get_key(self.name)
    if not api_key:
        raise AuthenticationError("未找到有效的 API 密钥")
    
    # 构建请求数据
    request_data = self._build_request(request)
    
    # 发送请求
    headers = {"Authorization": f"Bearer {api_key.key}"}
    response = await self._make_request(
        "POST", 
        f"{self.base_url}/chat/completions",
        headers=headers,
        json_data=request_data
    )
    
    # 解析响应
    return self._parse_response(response.json())
```

### 步骤 3: 配置系统集成

在 `src/ai_gen_hub/config/settings.py` 中添加配置支持：

```python
class Settings(BaseSettings):
    # 现有配置...
    
    # 添加自定义 provider 配置
    custom_provider: ProviderConfig = Field(default_factory=ProviderConfig)
    
    def _build_provider_configs(self):
        # 现有配置构建...
        
        # 添加自定义 provider 配置构建
        self.custom_provider = self._build_single_provider_config(
            "custom_provider",
            self.custom_provider_api_keys,
            self.custom_provider_base_url,
            self.custom_provider_timeout,
            self.custom_provider_max_retries,
            self.custom_provider_enabled
        )
```

## 📝 完整示例

### 步骤 4: 注册到 Provider Manager

在 `src/ai_gen_hub/services/provider_manager.py` 中添加初始化逻辑：

```python
async def _initialize_providers(self) -> None:
    """初始化所有配置的供应商"""
    # 现有供应商初始化...

    # 自定义 Provider
    if self.settings.custom_provider.enabled and self.settings.custom_provider.api_keys:
        try:
            provider = CustomProvider(self.settings.custom_provider, self.key_manager)
            await provider.initialize()
            self.providers["custom_provider"] = provider
            self.logger.info("自定义供应商初始化成功")
        except Exception as e:
            self.logger.error("自定义供应商初始化失败", error=str(e))
```

### 步骤 5: 环境变量配置

设置环境变量以配置自定义 provider：

```bash
# 基础配置
export CUSTOM_PROVIDER_ENABLED=true
export CUSTOM_PROVIDER_API_KEYS="your-api-key-1,your-api-key-2"
export CUSTOM_PROVIDER_BASE_URL="https://api.custom-ai.com/v1"
export CUSTOM_PROVIDER_TIMEOUT=120
export CUSTOM_PROVIDER_MAX_RETRIES=3

# 可选配置
export CUSTOM_PROVIDER_RATE_LIMIT=100  # 每分钟请求数
```

## 🔧 配置系统集成

### 修改配置类

需要在配置系统中添加对新 provider 的支持：

```python
# src/ai_gen_hub/config/settings.py

class Settings(BaseSettings):
    # 自定义 Provider 环境变量
    custom_provider_api_keys: List[str] = Field(
        default_factory=list,
        env="CUSTOM_PROVIDER_API_KEYS"
    )
    custom_provider_base_url: Optional[str] = Field(
        None,
        env="CUSTOM_PROVIDER_BASE_URL"
    )
    custom_provider_enabled: bool = Field(
        True,
        env="CUSTOM_PROVIDER_ENABLED"
    )
    custom_provider_timeout: int = Field(
        120,
        env="CUSTOM_PROVIDER_TIMEOUT"
    )
    custom_provider_max_retries: int = Field(
        3,
        env="CUSTOM_PROVIDER_MAX_RETRIES"
    )

    # Provider 配置对象
    custom_provider: ProviderConfig = Field(default_factory=ProviderConfig)

    def get_provider_config(self, provider_name: str) -> Optional[ProviderConfig]:
        """获取指定供应商的配置"""
        provider_configs = {
            "openai": self.openai,
            "google_ai": self.google_ai,
            "anthropic": self.anthropic,
            "dashscope": self.dashscope,
            "azure": self.azure,
            "custom_provider": self.custom_provider,  # 添加自定义 provider
        }
        return provider_configs.get(provider_name.lower())
```

## 📦 注册和使用

### 动态注册 Provider

除了在配置中静态注册，还可以动态注册：

```python
from ai_gen_hub.services.provider_manager import ProviderManagerImpl
from your_custom_provider import CustomProvider

async def register_custom_provider():
    """动态注册自定义 provider"""
    # 创建 provider manager
    provider_manager = ProviderManagerImpl()

    # 创建自定义 provider 实例
    config = ProviderConfig(
        api_keys=["your-api-key"],
        base_url="https://api.custom-ai.com/v1",
        enabled=True
    )
    key_manager = KeyManager()
    custom_provider = CustomProvider(config, key_manager)

    # 注册到管理器
    await provider_manager.register_provider(custom_provider)

    print("自定义 provider 注册成功！")
```

### 使用自定义 Provider

```python
from ai_gen_hub.core.interfaces import TextGenerationRequest, Message, MessageRole

async def use_custom_provider():
    """使用自定义 provider 生成文本"""
    # 创建请求
    request = TextGenerationRequest(
        model="custom-model-v1",
        messages=[
            Message(role=MessageRole.USER, content="你好，请介绍一下你的能力")
        ],
        temperature=0.7,
        max_tokens=1000
    )

    # 获取 provider 并生成文本
    provider = await provider_manager.get_provider("custom-model-v1")
    response = await provider.generate_text(request)

    print(response.choices[0].message.content)
```

## 🧪 测试验证

### 单元测试

创建完整的测试套件验证 provider 功能：

```python
# tests/test_custom_provider.py
import pytest
from unittest.mock import AsyncMock, MagicMock

from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.core.interfaces import TextGenerationRequest, Message, MessageRole
from ai_gen_hub.utils.key_manager import KeyManager
from your_custom_provider import CustomAIProvider


class TestCustomProvider:
    """自定义 Provider 单元测试"""

    @pytest.fixture
    async def provider(self):
        """创建测试用的 provider 实例"""
        config = ProviderConfig(
            api_keys=["test-api-key"],
            base_url="https://api.test.com/v1",
            enabled=True
        )
        key_manager = MagicMock(spec=KeyManager)
        provider = CustomAIProvider(config, key_manager)
        await provider.initialize()
        return provider

    async def test_initialization(self, provider):
        """测试初始化"""
        assert provider.name == "custom_ai"
        assert provider.base_url == "https://api.test.com/v1"
        assert len(provider._supported_models) > 0

    async def test_model_support(self, provider):
        """测试模型支持检查"""
        assert provider.supports_model("custom-gpt-4")
        assert not provider.supports_model("unsupported-model")

    async def test_model_mapping(self, provider):
        """测试模型名称映射"""
        mapped = provider.map_model_name("gpt-4")
        assert mapped == "custom-gpt-4"

    @pytest.mark.asyncio
    async def test_text_generation(self, provider):
        """测试文本生成"""
        # Mock HTTP 客户端
        provider._client = AsyncMock()
        provider._client.post.return_value.json.return_value = {
            "id": "test-id",
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": "测试响应"
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 5,
                "total_tokens": 15
            }
        }

        request = TextGenerationRequest(
            model="custom-gpt-4",
            messages=[Message(role=MessageRole.USER, content="测试")]
        )

        response = await provider.generate_text(request)
        assert response.choices[0].message.content == "测试响应"
```

### 集成测试

```python
# tests/test_integration.py
import pytest
from ai_gen_hub.services.provider_manager import ProviderManagerImpl

@pytest.mark.integration
async def test_provider_integration():
    """测试 provider 与系统的集成"""
    # 创建 provider manager
    provider_manager = ProviderManagerImpl()

    # 注册自定义 provider
    custom_provider = CustomAIProvider(config, key_manager)
    await provider_manager.register_provider(custom_provider)

    # 测试通过 manager 获取 provider
    provider = await provider_manager.get_provider("custom-gpt-4")
    assert provider is not None
    assert provider.name == "custom_ai"
```

### 性能测试

```python
# tests/test_performance.py
import asyncio
import time
import pytest

@pytest.mark.performance
async def test_concurrent_requests():
    """测试并发请求性能"""
    provider = CustomAIProvider(config, key_manager)

    async def make_request():
        request = TextGenerationRequest(
            model="custom-gpt-4",
            messages=[Message(role=MessageRole.USER, content="测试")]
        )
        return await provider.generate_text(request)

    # 并发执行多个请求
    start_time = time.time()
    tasks = [make_request() for _ in range(10)]
    responses = await asyncio.gather(*tasks)
    end_time = time.time()

    # 验证性能
    assert len(responses) == 10
    assert end_time - start_time < 30  # 30秒内完成
```

## 🎯 最佳实践

### 1. 错误处理

```python
class CustomAIProvider(BaseProvider):
    async def _handle_api_error(self, response_data: Dict[str, Any]):
        """统一的 API 错误处理"""
        if "error" in response_data:
            error_info = response_data["error"]
            error_code = error_info.get("code", "unknown")
            error_message = error_info.get("message", "未知错误")

            # 根据错误类型抛出相应异常
            if error_code == "invalid_api_key":
                from ai_gen_hub.core.exceptions import AuthenticationError
                raise AuthenticationError(f"API 密钥无效: {error_message}")
            elif error_code == "rate_limit_exceeded":
                from ai_gen_hub.core.exceptions import RateLimitError
                raise RateLimitError(f"速率限制: {error_message}")
            elif error_code == "insufficient_quota":
                from ai_gen_hub.core.exceptions import QuotaExceededError
                raise QuotaExceededError(f"配额不足: {error_message}")
            else:
                from ai_gen_hub.core.exceptions import APIError
                raise APIError(f"API 错误 ({error_code}): {error_message}")
```

### 2. 日志记录

```python
async def generate_text(self, request: TextGenerationRequest):
    """带详细日志的文本生成"""
    self.logger.info(
        "开始文本生成",
        model=request.model,
        message_count=len(request.messages),
        temperature=request.temperature,
        max_tokens=request.max_tokens
    )

    start_time = time.time()
    try:
        response = await self._generate_text_impl(request)

        duration = time.time() - start_time
        self.logger.info(
            "文本生成成功",
            duration=f"{duration:.2f}s",
            response_tokens=response.usage.completion_tokens if response.usage else 0
        )

        return response

    except Exception as e:
        duration = time.time() - start_time
        self.logger.error(
            "文本生成失败",
            duration=f"{duration:.2f}s",
            error=str(e),
            error_type=type(e).__name__
        )
        raise
```

### 3. 配置验证

```python
def __init__(self, config: ProviderConfig, key_manager: KeyManager):
    """带配置验证的初始化"""
    super().__init__("custom_ai", config, key_manager)

    # 验证必要配置
    if not config.api_keys:
        raise ValueError("自定义 AI Provider 需要至少一个 API 密钥")

    if not config.base_url:
        self.logger.warning("未设置 base_url，使用默认值")
        self.base_url = "https://api.custom-ai.com/v1"
    else:
        self.base_url = config.base_url.rstrip('/')

    # 验证 URL 格式
    if not self.base_url.startswith(('http://', 'https://')):
        raise ValueError(f"无效的 base_url 格式: {self.base_url}")
```

### 4. 资源管理

```python
async def cleanup(self):
    """完善的资源清理"""
    self.logger.info("开始清理自定义 AI Provider 资源")

    try:
        # 关闭 HTTP 客户端
        if self._client:
            await self._client.aclose()
            self._client = None

        # 清理缓存（如果有）
        if hasattr(self, '_cache'):
            self._cache.clear()

        # 更新状态
        self._status = ProviderStatus.UNHEALTHY

        self.logger.info("自定义 AI Provider 资源清理完成")

    except Exception as e:
        self.logger.error(f"资源清理过程中出现错误: {e}")
```

### 5. 监控和指标

```python
from ai_gen_hub.core.monitoring import MetricsCollector

class CustomAIProvider(BaseProvider):
    def __init__(self, config: ProviderConfig, key_manager: KeyManager):
        super().__init__("custom_ai", config, key_manager)
        self.metrics = MetricsCollector("custom_ai_provider")

    async def generate_text(self, request: TextGenerationRequest):
        """带监控指标的文本生成"""
        with self.metrics.timer("text_generation_duration"):
            self.metrics.increment("text_generation_requests")

            try:
                response = await self._generate_text_impl(request)
                self.metrics.increment("text_generation_success")

                if response.usage:
                    self.metrics.histogram("tokens_generated", response.usage.completion_tokens)

                return response

            except Exception as e:
                self.metrics.increment("text_generation_errors")
                self.metrics.increment(f"error_{type(e).__name__.lower()}")
                raise
```

## 🚀 部署和生产环境

### 环境配置

```bash
# 生产环境配置
export CUSTOM_AI_API_KEY="prod-api-key-1,prod-api-key-2"
export CUSTOM_AI_BASE_URL="https://api.custom-ai.com/v1"
export CUSTOM_AI_TIMEOUT=180
export CUSTOM_AI_MAX_RETRIES=5
export CUSTOM_AI_RATE_LIMIT=1000

# 启用监控
export AI_GEN_HUB_MONITORING_ENABLED=true
export AI_GEN_HUB_LOG_LEVEL=INFO
```

### Docker 部署

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制代码
COPY src/ ./src/
COPY examples/ ./examples/

# 设置环境变量
ENV PYTHONPATH=/app/src

# 运行应用
CMD ["python", "-m", "ai_gen_hub.main"]
```

### Kubernetes 配置

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-gen-hub
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-gen-hub
  template:
    metadata:
      labels:
        app: ai-gen-hub
    spec:
      containers:
      - name: ai-gen-hub
        image: ai-gen-hub:latest
        env:
        - name: CUSTOM_AI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-secrets
              key: custom-ai-api-key
        - name: CUSTOM_AI_BASE_URL
          value: "https://api.custom-ai.com/v1"
        ports:
        - containerPort: 8000
```

## 🔧 故障排除

### 常见问题

#### 1. Provider 初始化失败

**问题**: Provider 无法正确初始化

**解决方案**:
```python
# 检查配置
config = ProviderConfig(...)
print(f"API Keys: {len(config.api_keys)}")
print(f"Base URL: {config.base_url}")
print(f"Enabled: {config.enabled}")

# 检查网络连接
import httpx
async with httpx.AsyncClient() as client:
    response = await client.get(config.base_url + "/health")
    print(f"Health check: {response.status_code}")
```

#### 2. API 密钥问题

**问题**: 认证失败或密钥无效

**解决方案**:
```python
# 验证 API 密钥
from ai_gen_hub.utils.key_manager import KeyManager

key_manager = KeyManager()
key = await key_manager.get_key("custom_ai")
if key:
    print(f"找到密钥: {key.key[:10]}...")
else:
    print("未找到有效密钥")
```

#### 3. 模型不支持

**问题**: 请求的模型不被支持

**解决方案**:
```python
# 检查支持的模型
provider = CustomAIProvider(config, key_manager)
print(f"支持的模型: {provider._supported_models}")
print(f"模型映射: {provider._model_mapping}")

# 测试特定模型
model_name = "gpt-4"
if provider.supports_model(model_name):
    mapped_name = provider.map_model_name(model_name)
    print(f"模型 {model_name} 映射为 {mapped_name}")
else:
    print(f"不支持模型 {model_name}")
```

#### 4. 网络超时

**问题**: 请求超时或网络错误

**解决方案**:
```python
# 调整超时设置
config = ProviderConfig(
    timeout=300,  # 增加到 5 分钟
    max_retries=5,  # 增加重试次数
    retry_delay=2.0  # 增加重试延迟
)

# 检查网络连接
import asyncio
import httpx

async def test_connection():
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            response = await client.get("https://api.custom-ai.com/v1/models")
            print(f"连接测试: {response.status_code}")
        except Exception as e:
            print(f"连接失败: {e}")

asyncio.run(test_connection())
```

### 调试技巧

#### 1. 启用详细日志

```python
import logging

# 设置日志级别
logging.basicConfig(level=logging.DEBUG)

# 或者只针对特定模块
logger = logging.getLogger("ai_gen_hub.providers.custom_ai")
logger.setLevel(logging.DEBUG)
```

#### 2. 使用调试模式

```python
# 在开发环境中启用调试
os.environ["AI_GEN_HUB_DEBUG"] = "true"
os.environ["AI_GEN_HUB_LOG_LEVEL"] = "DEBUG"
```

#### 3. 监控 HTTP 请求

```python
import httpx

# 创建带日志的 HTTP 客户端
class LoggingTransport(httpx.HTTPTransport):
    def handle_request(self, request):
        print(f"请求: {request.method} {request.url}")
        response = super().handle_request(request)
        print(f"响应: {response.status_code}")
        return response

# 在 provider 中使用
self._client = httpx.AsyncClient(transport=LoggingTransport())
```

## 📚 参考资源

### 相关文档

- [BaseProvider API 文档](../api/base_provider.md)
- [配置系统指南](../config/settings_guide.md)
- [错误处理最佳实践](../best_practices/error_handling.md)
- [监控和日志指南](../monitoring/logging_guide.md)

### 示例代码

- [完整 Provider 实现示例](../examples/custom_provider_example.py)
- [测试套件示例](../examples/test_custom_provider.py)
- [配置示例](../config/custom_provider_config.yaml)

### 社区资源

- [GitHub Issues](https://github.com/your-org/ai-gen-hub/issues)
- [讨论区](https://github.com/your-org/ai-gen-hub/discussions)
- [贡献指南](../CONTRIBUTING.md)

---

## 🎉 总结

通过本指南，您已经学会了如何：

1. ✅ 创建自定义 AI Provider 类
2. ✅ 实现必要的抽象方法
3. ✅ 集成到配置系统
4. ✅ 注册到 Provider Manager
5. ✅ 编写完整的测试套件
6. ✅ 部署到生产环境
7. ✅ 处理常见问题和故障排除

现在您可以轻松地将任何 AI 服务商接入到 AI Gen Hub 系统中，享受统一的接口和强大的功能！

如果您在实施过程中遇到任何问题，请参考故障排除部分或在社区中寻求帮助。
```
```
