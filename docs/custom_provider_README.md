# 自定义 AI Provider 接入指南

欢迎使用 AI Gen Hub 自定义 Provider 接入功能！本指南将帮助您快速将任何 AI 服务商接入到系统中。

## 🚀 快速开始

### 5 分钟快速接入

1. **复制模板文件**
   ```bash
   cp examples/quick_start_custom_provider.py your_provider.py
   ```

2. **修改基础配置**
   ```python
   # 在 __init__ 方法中修改
   self.base_url = "https://api.your-ai-service.com/v1"
   self._supported_models = ["your-model-1", "your-model-2"]
   ```

3. **实现 API 适配**
   ```python
   # 修改 _build_api_request 和 _parse_api_response 方法
   # 根据您的 AI 服务 API 格式调整
   ```

4. **设置环境变量**
   ```bash
   export YOUR_AI_API_KEY="your-api-key"
   export YOUR_AI_BASE_URL="https://api.your-ai-service.com/v1"
   ```

5. **测试运行**
   ```bash
   python your_provider.py
   ```

## 📁 文件结构

```
docs/
├── custom_provider_guide.md           # 📖 完整详细指南
└── custom_provider_README.md          # 📋 本文件（快速参考）

examples/
├── quick_start_custom_provider.py     # 🚀 5分钟快速开始模板
├── custom_provider_example.py         # 📚 完整实现示例
└── test_custom_provider.py           # 🧪 测试套件

config/
└── custom_provider_config_example.yaml # ⚙️ 配置示例
```

## 🎯 核心概念

### 必须实现的方法

```python
class YourCustomProvider(BaseProvider):
    async def _perform_health_check(self, api_key: str) -> bool:
        """健康检查 - 验证 API 连接"""
        pass
    
    async def generate_text(self, request: TextGenerationRequest) -> TextGenerationResponse:
        """文本生成 - 核心功能"""
        pass
```

### 配置要素

```python
def __init__(self, config: ProviderConfig, key_manager: KeyManager):
    super().__init__("your_provider_name", config, key_manager)
    
    # 🌐 API 基础 URL
    self.base_url = config.base_url or "https://api.your-service.com/v1"
    
    # 🎯 支持的功能类型
    self._supported_model_types = [ModelType.TEXT_GENERATION]
    
    # 📝 支持的模型列表
    self._supported_models = ["model-1", "model-2"]
    
    # 🔄 模型名称映射（可选）
    self._model_mapping = {"gpt-4": "your-gpt-4-equivalent"}
```

## 🔧 常见适配场景

### OpenAI 兼容 API

如果您的 AI 服务使用 OpenAI 兼容的 API 格式：

```python
def _build_api_request(self, request: TextGenerationRequest) -> Dict[str, Any]:
    return {
        "model": self.map_model_name(request.model),
        "messages": [{"role": msg.role.value, "content": msg.content} for msg in request.messages],
        "temperature": request.temperature,
        "max_tokens": request.max_tokens,
    }
```

### 自定义 API 格式

如果您的 AI 服务使用自定义格式：

```python
def _build_api_request(self, request: TextGenerationRequest) -> Dict[str, Any]:
    # 根据您的 API 文档调整格式
    return {
        "model_name": self.map_model_name(request.model),
        "prompt": self._convert_messages_to_prompt(request.messages),
        "config": {
            "temperature": request.temperature or 0.7,
            "max_length": request.max_tokens or 1000,
        }
    }
```

### 流式响应支持

```python
async def generate_text(self, request: TextGenerationRequest):
    if request.stream:
        return self._generate_text_stream(request)
    else:
        return await self._generate_text_sync(request)
```

## 🧪 测试和验证

### 基础测试

```bash
# 运行快速测试
python examples/quick_start_custom_provider.py

# 运行完整测试套件
python examples/test_custom_provider.py
```

### 单元测试

```python
# 创建测试文件
pytest tests/test_your_provider.py -v
```

### 集成测试

```python
# 测试与 Provider Manager 的集成
python -c "
import asyncio
from your_provider import YourCustomProvider
from ai_gen_hub.services.provider_manager import ProviderManagerImpl

async def test():
    manager = ProviderManagerImpl()
    provider = YourCustomProvider(config, key_manager)
    await manager.register_provider(provider)
    print('集成测试通过！')

asyncio.run(test())
"
```

## ⚙️ 配置管理

### 环境变量配置

```bash
# 基础配置
export YOUR_PROVIDER_ENABLED=true
export YOUR_PROVIDER_API_KEYS="key1,key2,key3"
export YOUR_PROVIDER_BASE_URL="https://api.your-service.com/v1"
export YOUR_PROVIDER_TIMEOUT=120
export YOUR_PROVIDER_MAX_RETRIES=3
```

### YAML 配置文件

```yaml
# config/your_provider.yaml
your_provider:
  enabled: true
  api_keys: ["${YOUR_PROVIDER_API_KEY}"]
  base_url: "https://api.your-service.com/v1"
  timeout: 120
  max_retries: 3
  rate_limit: 1000
```

### 代码中使用配置

```python
from ai_gen_hub.config.settings import get_settings

settings = get_settings()
config = settings.get_provider_config("your_provider")
provider = YourCustomProvider(config, key_manager)
```

## 🚀 部署到生产环境

### Docker 部署

```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
ENV PYTHONPATH=/app/src
CMD ["python", "-m", "ai_gen_hub.main"]
```

### 环境变量设置

```bash
# 生产环境
export AI_GEN_HUB_ENVIRONMENT=production
export YOUR_PROVIDER_API_KEY="prod-api-key"
export YOUR_PROVIDER_RATE_LIMIT=2000
```

## 🔍 故障排除

### 常见问题

| 问题 | 解决方案 |
|------|----------|
| 初始化失败 | 检查 API 密钥和 base_url 配置 |
| 认证错误 | 验证 API 密钥格式和权限 |
| 超时错误 | 增加 timeout 配置值 |
| 模型不支持 | 检查 _supported_models 列表 |
| 响应解析错误 | 检查 _parse_api_response 方法 |

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查配置
print(f"API Keys: {len(config.api_keys)}")
print(f"Base URL: {config.base_url}")
print(f"Supported Models: {provider._supported_models}")

# 测试网络连接
import httpx
async with httpx.AsyncClient() as client:
    response = await client.get(f"{base_url}/health")
    print(f"Health check: {response.status_code}")
```

## 📚 参考资源

### 文档链接

- [📖 完整实现指南](custom_provider_guide.md) - 详细的实现说明
- [🔧 API 参考](../api/base_provider.md) - BaseProvider API 文档
- [⚙️ 配置指南](../config/settings_guide.md) - 配置系统说明

### 示例代码

- [🚀 快速开始模板](../examples/quick_start_custom_provider.py)
- [📚 完整实现示例](../examples/custom_provider_example.py)
- [🧪 测试套件](../examples/test_custom_provider.py)

### 社区支持

- [GitHub Issues](https://github.com/your-org/ai-gen-hub/issues) - 问题报告
- [讨论区](https://github.com/your-org/ai-gen-hub/discussions) - 技术讨论
- [贡献指南](../CONTRIBUTING.md) - 如何贡献代码

## 🎉 成功案例

已成功接入的 AI 服务商：

- ✅ OpenAI (GPT 系列)
- ✅ Google AI (Gemini 系列)
- ✅ Anthropic (Claude 系列)
- ✅ 阿里云 DashScope (通义千问)
- ✅ Cohere (Command R 系列)
- ✅ Hugging Face (开源模型)

您的自定义 provider 也可以加入这个列表！

---

## 💡 小贴士

1. **从简单开始**: 使用 `quick_start_custom_provider.py` 模板快速原型
2. **参考现有实现**: 查看 `src/ai_gen_hub/providers/` 下的现有 provider
3. **充分测试**: 使用提供的测试套件确保功能正确
4. **关注日志**: 启用详细日志帮助调试问题
5. **社区求助**: 遇到问题时在 GitHub 讨论区寻求帮助

开始您的自定义 provider 开发之旅吧！🚀
