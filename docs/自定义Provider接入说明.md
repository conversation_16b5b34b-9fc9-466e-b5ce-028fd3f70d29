# AI Gen Hub 自定义 Provider 接入完整说明

## 📖 概述

AI Gen Hub 提供了完整的自定义 AI Provider 接入解决方案，让您可以轻松将任何 AI 服务商集成到系统中。本文档提供了从基础概念到生产部署的完整指导。

## 🏗️ 系统架构

### 核心组件说明

```
AI Gen Hub 架构
├── BaseProvider (基础适配器类)
│   ├── 通用功能：HTTP客户端、错误处理、重试机制
│   ├── 抽象方法：健康检查、文本生成等核心功能
│   └── 接口规范：统一的请求/响应格式
│
├── ProviderManager (供应商管理器)
│   ├── 供应商注册和注销
│   ├── 健康检查和状态管理
│   ├── 负载均衡和故障转移
│   └── 供应商发现和选择
│
├── 配置系统
│   ├── ProviderConfig：供应商配置类
│   ├── Settings：全局配置管理
│   └── 环境变量和YAML配置支持
│
└── 工具组件
    ├── KeyManager：API密钥安全管理
    ├── 日志系统：结构化日志记录
    └── 监控指标：性能和状态监控
```

## 🚀 快速接入流程

### 第一步：创建 Provider 类

继承 `BaseProvider` 基类并实现必要方法：

```python
from ai_gen_hub.providers.base import BaseProvider
from ai_gen_hub.core.interfaces import TextGenerationRequest, TextGenerationResponse

class MyCustomProvider(BaseProvider):
    """我的自定义 AI Provider"""
    
    def __init__(self, config: ProviderConfig, key_manager: KeyManager):
        super().__init__("my_custom_ai", config, key_manager)
        
        # 配置 API 基础信息
        self.base_url = config.base_url or "https://api.my-ai.com/v1"
        
        # 定义支持的模型类型和模型列表
        self._supported_model_types = [ModelType.TEXT_GENERATION]
        self._supported_models = ["my-model-v1", "my-model-v2"]
    
    async def _perform_health_check(self, api_key: str) -> bool:
        """实现健康检查逻辑"""
        # 调用您的 AI 服务健康检查端点
        pass
    
    async def generate_text(self, request: TextGenerationRequest) -> TextGenerationResponse:
        """实现文本生成逻辑"""
        # 调用您的 AI 服务文本生成 API
        pass
```

### 第二步：配置系统集成

在配置系统中添加新 provider 支持：

```python
# src/ai_gen_hub/config/settings.py
class Settings(BaseSettings):
    # 添加自定义 provider 配置
    my_custom_ai: ProviderConfig = Field(default_factory=ProviderConfig)
    
    # 环境变量定义
    my_custom_ai_api_keys: List[str] = Field(default_factory=list, env="MY_CUSTOM_AI_API_KEYS")
    my_custom_ai_base_url: Optional[str] = Field(None, env="MY_CUSTOM_AI_BASE_URL")
    my_custom_ai_enabled: bool = Field(True, env="MY_CUSTOM_AI_ENABLED")
```

### 第三步：注册到 Provider Manager

```python
# src/ai_gen_hub/services/provider_manager.py
async def _initialize_providers(self) -> None:
    # 添加自定义 provider 初始化逻辑
    if self.settings.my_custom_ai.enabled and self.settings.my_custom_ai.api_keys:
        try:
            provider = MyCustomProvider(self.settings.my_custom_ai, self.key_manager)
            await provider.initialize()
            self.providers["my_custom_ai"] = provider
            self.logger.info("自定义AI供应商初始化成功")
        except Exception as e:
            self.logger.error("自定义AI供应商初始化失败", error=str(e))
```

### 第四步：环境配置

设置必要的环境变量：

```bash
# 基础配置
export MY_CUSTOM_AI_ENABLED=true
export MY_CUSTOM_AI_API_KEYS="your-api-key-1,your-api-key-2"
export MY_CUSTOM_AI_BASE_URL="https://api.my-ai.com/v1"
export MY_CUSTOM_AI_TIMEOUT=120
export MY_CUSTOM_AI_MAX_RETRIES=3
```

## 🔧 详细实现指南

### 核心方法实现

#### 1. 健康检查实现

```python
async def _perform_health_check(self, api_key: str) -> bool:
    """健康检查实现示例"""
    try:
        headers = {"Authorization": f"Bearer {api_key}"}
        response = await self._client.get(
            f"{self.base_url}/models",  # 或其他健康检查端点
            headers=headers,
            timeout=10.0
        )
        return response.status_code == 200
    except Exception as e:
        self.logger.error(f"健康检查失败: {e}")
        return False
```

#### 2. 文本生成实现

```python
async def generate_text(self, request: TextGenerationRequest) -> TextGenerationResponse:
    """文本生成实现示例"""
    # 获取API密钥
    api_key = await self.key_manager.get_key(self.name)
    if not api_key:
        raise AuthenticationError("未找到有效的API密钥")
    
    # 构建API请求
    request_data = self._build_api_request(request)
    
    # 发送请求
    headers = {"Authorization": f"Bearer {api_key.key}"}
    response = await self._make_request(
        "POST", 
        f"{self.base_url}/chat/completions",
        headers=headers,
        json_data=request_data
    )
    
    # 解析响应
    return self._parse_api_response(response.json())
```

#### 3. API 请求构建

```python
def _build_api_request(self, request: TextGenerationRequest) -> Dict[str, Any]:
    """构建符合您的AI服务API格式的请求"""
    return {
        "model": self.map_model_name(request.model),
        "messages": [
            {"role": msg.role.value, "content": msg.content} 
            for msg in request.messages
        ],
        "temperature": request.temperature,
        "max_tokens": request.max_tokens,
        # 根据您的API添加其他参数
    }
```

#### 4. 响应解析

```python
def _parse_api_response(self, response_data: Dict[str, Any]) -> TextGenerationResponse:
    """解析API响应为标准格式"""
    # 检查错误
    if "error" in response_data:
        raise APIError(response_data["error"]["message"])
    
    # 解析选择项
    choices = []
    for choice_data in response_data.get("choices", []):
        choice = TextGenerationChoice(
            index=choice_data.get("index", 0),
            message=Message(
                role=MessageRole(choice_data["message"]["role"]),
                content=choice_data["message"]["content"]
            ),
            finish_reason=choice_data.get("finish_reason")
        )
        choices.append(choice)
    
    # 解析使用统计
    usage_data = response_data.get("usage", {})
    usage = Usage(
        prompt_tokens=usage_data.get("prompt_tokens", 0),
        completion_tokens=usage_data.get("completion_tokens", 0),
        total_tokens=usage_data.get("total_tokens", 0)
    )
    
    return TextGenerationResponse(
        id=response_data.get("id", str(uuid4())),
        choices=choices,
        usage=usage,
        model=response_data.get("model", ""),
        created=response_data.get("created", int(time.time()))
    )
```

## 🧪 测试和验证

### 单元测试

```python
import pytest
from unittest.mock import AsyncMock

class TestMyCustomProvider:
    @pytest.fixture
    async def provider(self):
        config = ProviderConfig(api_keys=["test-key"], base_url="https://test.api.com")
        key_manager = AsyncMock()
        provider = MyCustomProvider(config, key_manager)
        await provider.initialize()
        return provider
    
    async def test_health_check(self, provider):
        """测试健康检查功能"""
        result = await provider.health_check()
        assert isinstance(result, bool)
    
    async def test_text_generation(self, provider):
        """测试文本生成功能"""
        request = TextGenerationRequest(
            model="my-model-v1",
            messages=[Message(role=MessageRole.USER, content="测试")]
        )
        response = await provider.generate_text(request)
        assert response is not None
        assert len(response.choices) > 0
```

### 集成测试

```python
async def test_provider_integration():
    """测试与系统的集成"""
    # 创建provider manager
    provider_manager = ProviderManagerImpl()
    
    # 注册自定义provider
    custom_provider = MyCustomProvider(config, key_manager)
    await provider_manager.register_provider(custom_provider)
    
    # 测试通过manager获取provider
    provider = await provider_manager.get_provider("my-model-v1")
    assert provider is not None
    assert provider.name == "my_custom_ai"
```

## 📊 监控和日志

### 日志记录最佳实践

```python
async def generate_text(self, request: TextGenerationRequest):
    """带详细日志的文本生成"""
    self.logger.info(
        "开始文本生成",
        model=request.model,
        message_count=len(request.messages),
        temperature=request.temperature
    )
    
    start_time = time.time()
    try:
        response = await self._generate_text_impl(request)
        duration = time.time() - start_time
        
        self.logger.info(
            "文本生成成功",
            duration=f"{duration:.2f}s",
            tokens=response.usage.completion_tokens if response.usage else 0
        )
        return response
        
    except Exception as e:
        duration = time.time() - start_time
        self.logger.error(
            "文本生成失败",
            duration=f"{duration:.2f}s",
            error=str(e),
            error_type=type(e).__name__
        )
        raise
```

### 性能监控

```python
from ai_gen_hub.core.monitoring import MetricsCollector

class MyCustomProvider(BaseProvider):
    def __init__(self, config: ProviderConfig, key_manager: KeyManager):
        super().__init__("my_custom_ai", config, key_manager)
        self.metrics = MetricsCollector("my_custom_ai_provider")
    
    async def generate_text(self, request: TextGenerationRequest):
        with self.metrics.timer("text_generation_duration"):
            self.metrics.increment("text_generation_requests")
            
            try:
                response = await self._generate_text_impl(request)
                self.metrics.increment("text_generation_success")
                return response
            except Exception as e:
                self.metrics.increment("text_generation_errors")
                raise
```

## 🚀 生产环境部署

### 配置管理

```yaml
# config/production.yaml
my_custom_ai:
  enabled: true
  api_keys: ["${MY_CUSTOM_AI_API_KEY}"]
  base_url: "https://api.my-ai.com/v1"
  timeout: 180
  max_retries: 5
  rate_limit: 2000
  
  # 生产环境特定配置
  security:
    validate_ssl: true
    require_https: true
  
  # 高可用配置
  high_availability:
    enable_failover: true
    backup_endpoints:
      - "https://api-backup.my-ai.com/v1"
```

### Docker 部署

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制源码
COPY src/ ./src/
COPY config/ ./config/

# 设置环境变量
ENV PYTHONPATH=/app/src
ENV AI_GEN_HUB_ENVIRONMENT=production

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD python -c "import asyncio; from src.ai_gen_hub.main import health_check; asyncio.run(health_check())"

# 运行应用
CMD ["python", "-m", "ai_gen_hub.main"]
```

## 🔍 故障排除

### 常见问题及解决方案

| 问题类型 | 症状 | 解决方案 |
|---------|------|----------|
| 初始化失败 | Provider无法启动 | 检查API密钥、base_url配置 |
| 认证错误 | 401/403错误 | 验证API密钥格式和权限 |
| 网络超时 | 请求超时 | 增加timeout配置，检查网络连接 |
| 模型不支持 | 模型未找到错误 | 检查_supported_models列表 |
| 响应解析错误 | 解析失败 | 检查API响应格式，调整解析逻辑 |

### 调试工具

```python
# 启用调试日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 配置检查工具
def check_provider_config(provider):
    print(f"Provider名称: {provider.name}")
    print(f"API密钥数量: {len(provider.config.api_keys)}")
    print(f"基础URL: {provider.base_url}")
    print(f"支持的模型: {provider._supported_models}")
    print(f"当前状态: {provider.status}")

# 网络连接测试
async def test_api_connection(base_url, api_key):
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(
                f"{base_url}/models",
                headers={"Authorization": f"Bearer {api_key}"},
                timeout=10.0
            )
            print(f"连接测试: {response.status_code}")
            return response.status_code == 200
        except Exception as e:
            print(f"连接失败: {e}")
            return False
```

## 📚 完整示例

查看以下文件获取完整的实现示例：

- **快速开始**: `examples/quick_start_custom_provider.py`
- **完整实现**: `examples/custom_provider_example.py`
- **测试套件**: `examples/test_custom_provider.py`
- **配置示例**: `config/custom_provider_config_example.yaml`

## 🎯 最佳实践总结

1. **从简单开始**: 使用快速开始模板进行原型开发
2. **充分测试**: 编写完整的单元测试和集成测试
3. **错误处理**: 实现完善的错误处理和重试机制
4. **日志记录**: 添加详细的结构化日志
5. **监控指标**: 集成性能监控和健康检查
6. **配置管理**: 使用环境变量管理敏感配置
7. **文档完善**: 为您的provider编写清晰的使用文档

通过遵循本指南，您可以成功将任何AI服务商集成到AI Gen Hub系统中，享受统一的接口和强大的功能！
