# AI Gen Hub 认证系统部署指南

本指南详细介绍如何部署和配置 AI Gen Hub 的身份认证和授权系统。

## 📋 目录

- [系统架构](#系统架构)
- [部署前准备](#部署前准备)
- [配置身份提供商](#配置身份提供商)
- [环境变量设置](#环境变量设置)
- [Docker部署](#docker部署)
- [Kubernetes部署](#kubernetes部署)
- [安全配置](#安全配置)
- [监控和日志](#监控和日志)
- [故障排除](#故障排除)

## 🏗️ 系统架构

### 认证架构概览

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   控制台用户    │    │   API客户端     │    │   管理员用户    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ OAuth2/OIDC          │ API Token             │ OAuth2/OIDC
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    AI Gen Hub 认证系统                          │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ 认证中间件  │  │ 授权中间件  │  │ 安全中间件  │  │ 审计日志│ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ JWT管理器   │  │ API Token   │  │ RBAC管理器  │  │ 安全管理│ │
│  │             │  │ 管理器      │  │             │  │ 器      │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                      IdP 集成层                                 │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │ Auth0   │ │Keycloak │ │Azure AD │ │ Google  │ │ GitHub  │   │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 核心组件

1. **认证层**: 处理用户身份验证
2. **授权层**: 管理权限和访问控制
3. **安全层**: 提供安全保护和监控
4. **IdP集成**: 支持多种身份提供商

## 🚀 部署前准备

### 系统要求

- **Python**: 3.11+
- **数据库**: PostgreSQL 13+ 或 MySQL 8.0+
- **缓存**: Redis 6.0+ (可选，用于会话存储)
- **HTTPS**: 生产环境必须使用HTTPS

### 依赖安装

```bash
# 安装认证相关依赖
pip install -r requirements-auth.txt

# 或者使用poetry
poetry install --extras auth
```

### 数据库初始化

```sql
-- 创建认证相关表
CREATE TABLE auth_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    external_id VARCHAR(255),
    idp_type VARCHAR(50),
    role VARCHAR(50) NOT NULL DEFAULT 'user',
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP,
    metadata JSONB DEFAULT '{}'
);

CREATE TABLE auth_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth_users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE
);

CREATE TABLE api_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth_users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    token_prefix VARCHAR(20) NOT NULL,
    scopes JSONB DEFAULT '[]',
    permissions JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP,
    expires_at TIMESTAMP,
    metadata JSONB DEFAULT '{}'
);

-- 创建索引
CREATE INDEX idx_auth_users_email ON auth_users(email);
CREATE INDEX idx_auth_users_external_id ON auth_users(external_id);
CREATE INDEX idx_auth_sessions_user_id ON auth_sessions(user_id);
CREATE INDEX idx_auth_sessions_token ON auth_sessions(session_token);
CREATE INDEX idx_api_tokens_user_id ON api_tokens(user_id);
CREATE INDEX idx_api_tokens_hash ON api_tokens(token_hash);
```

## 🔐 配置身份提供商

### Auth0 配置

1. **创建 Auth0 应用**:
   ```bash
   # 登录 Auth0 Dashboard
   # 创建新的 Single Page Application
   # 记录 Domain, Client ID, Client Secret
   ```

2. **配置回调URL**:
   ```
   Allowed Callback URLs: https://your-domain.com/auth/callback/auth0
   Allowed Logout URLs: https://your-domain.com/auth/logout
   Allowed Web Origins: https://your-domain.com
   ```

3. **环境变量设置**:
   ```bash
   export AUTH0_ENABLED=true
   export AUTH0_DOMAIN="your-domain.auth0.com"
   export AUTH0_CLIENT_ID="your-client-id"
   export AUTH0_CLIENT_SECRET="your-client-secret"
   export AUTH0_AUDIENCE="https://your-api.com"
   ```

### Keycloak 配置

1. **创建 Keycloak 客户端**:
   ```bash
   # 在 Keycloak Admin Console 中
   # 创建新的 Realm 或使用现有的
   # 创建新的 Client (OpenID Connect)
   ```

2. **客户端配置**:
   ```
   Client ID: ai-gen-hub
   Client Protocol: openid-connect
   Access Type: confidential
   Valid Redirect URIs: https://your-domain.com/auth/callback/keycloak
   ```

3. **环境变量设置**:
   ```bash
   export KEYCLOAK_ENABLED=true
   export KEYCLOAK_BASE_URL="https://keycloak.your-domain.com"
   export KEYCLOAK_REALM="your-realm"
   export KEYCLOAK_CLIENT_ID="ai-gen-hub"
   export KEYCLOAK_CLIENT_SECRET="your-client-secret"
   ```

### Azure AD 配置

1. **注册应用程序**:
   ```bash
   # 在 Azure Portal 中
   # Azure Active Directory > App registrations > New registration
   ```

2. **配置权限**:
   ```
   API permissions:
   - Microsoft Graph > User.Read
   - Microsoft Graph > openid
   - Microsoft Graph > profile
   - Microsoft Graph > email
   ```

3. **环境变量设置**:
   ```bash
   export AZURE_AD_ENABLED=true
   export AZURE_AD_TENANT_ID="your-tenant-id"
   export AZURE_AD_CLIENT_ID="your-client-id"
   export AZURE_AD_CLIENT_SECRET="your-client-secret"
   ```

## 🌍 环境变量设置

### 必需的环境变量

```bash
# JWT配置
export AUTH_JWT_SECRET_KEY="your-super-secret-jwt-key-minimum-32-characters"

# 数据库配置
export DATABASE_URL="postgresql://user:password@localhost:5432/ai_gen_hub"

# Redis配置（可选）
export REDIS_URL="redis://localhost:6379/0"

# 应用配置
export AI_GEN_HUB_ENVIRONMENT="production"
export API_HOST="your-domain.com"
export API_PORT="443"
```

### 安全相关环境变量

```bash
# 安全配置
export AUTH_RATE_LIMIT_ENABLED=true
export AUTH_RATE_LIMIT_REQUESTS_PER_MINUTE=100
export AUTH_AUDIT_LOG_ENABLED=true

# 会话配置
export AUTH_SESSION_COOKIE_SECURE=true
export AUTH_SESSION_COOKIE_HTTPONLY=true
export AUTH_SESSION_EXPIRE_HOURS=24

# 密码策略
export AUTH_PASSWORD_MIN_LENGTH=8
export AUTH_PASSWORD_REQUIRE_UPPERCASE=true
export AUTH_PASSWORD_REQUIRE_NUMBERS=true
```

## 🐳 Docker 部署

### Dockerfile

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY src/ ./src/
COPY config/ ./config/

# 设置环境变量
ENV PYTHONPATH=/app/src
ENV AI_GEN_HUB_CONFIG_PATH=/app/config/auth_config.yaml

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD python -c "import requests; requests.get('http://localhost:8000/health')"

# 暴露端口
EXPOSE 8000

# 启动应用
CMD ["python", "-m", "ai_gen_hub.main"]
```

### docker-compose.yml

```yaml
version: '3.8'

services:
  ai-gen-hub:
    build: .
    ports:
      - "8000:8000"
    environment:
      - AUTH_JWT_SECRET_KEY=${AUTH_JWT_SECRET_KEY}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - AUTH0_DOMAIN=${AUTH0_DOMAIN}
      - AUTH0_CLIENT_ID=${AUTH0_CLIENT_ID}
      - AUTH0_CLIENT_SECRET=${AUTH0_CLIENT_SECRET}
    depends_on:
      - postgres
      - redis
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=ai_gen_hub
      - POSTGRES_USER=ai_gen_hub
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### 部署命令

```bash
# 设置环境变量
cp .env.example .env
# 编辑 .env 文件设置实际值

# 构建和启动
docker-compose up -d

# 查看日志
docker-compose logs -f ai-gen-hub

# 停止服务
docker-compose down
```

## ☸️ Kubernetes 部署

### 命名空间和配置

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: ai-gen-hub
---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-gen-hub-config
  namespace: ai-gen-hub
data:
  auth_config.yaml: |
    auth:
      jwt_algorithm: "HS256"
      jwt_access_token_expire_minutes: 30
      jwt_refresh_token_expire_days: 7
      session_expire_hours: 24
      rate_limit_enabled: true
      rate_limit_requests_per_minute: 100
      audit_log_enabled: true
```

### 密钥管理

```yaml
# secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: ai-gen-hub-secrets
  namespace: ai-gen-hub
type: Opaque
data:
  jwt-secret-key: <base64-encoded-jwt-secret>
  auth0-client-secret: <base64-encoded-auth0-secret>
  database-url: <base64-encoded-database-url>
  redis-url: <base64-encoded-redis-url>
```

### 应用部署

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-gen-hub
  namespace: ai-gen-hub
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-gen-hub
  template:
    metadata:
      labels:
        app: ai-gen-hub
    spec:
      containers:
      - name: ai-gen-hub
        image: ai-gen-hub:latest
        ports:
        - containerPort: 8000
        env:
        - name: AUTH_JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: ai-gen-hub-secrets
              key: jwt-secret-key
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: ai-gen-hub-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: ai-gen-hub-secrets
              key: redis-url
        - name: AUTH0_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-gen-hub-secrets
              key: auth0-client-secret
        volumeMounts:
        - name: config
          mountPath: /app/config
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config
        configMap:
          name: ai-gen-hub-config
---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: ai-gen-hub-service
  namespace: ai-gen-hub
spec:
  selector:
    app: ai-gen-hub
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: ClusterIP
---
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-gen-hub-ingress
  namespace: ai-gen-hub
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - api.your-domain.com
    secretName: ai-gen-hub-tls
  rules:
  - host: api.your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-gen-hub-service
            port:
              number: 80
```

### 部署命令

```bash
# 应用配置
kubectl apply -f namespace.yaml
kubectl apply -f secrets.yaml
kubectl apply -f configmap.yaml
kubectl apply -f deployment.yaml

# 检查部署状态
kubectl get pods -n ai-gen-hub
kubectl logs -f deployment/ai-gen-hub -n ai-gen-hub

# 扩缩容
kubectl scale deployment ai-gen-hub --replicas=5 -n ai-gen-hub
```

## 🔒 安全配置

### HTTPS 配置

```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name api.your-domain.com;

    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;

    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头部
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 防火墙配置

```bash
# UFW 防火墙配置
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# 限制数据库访问
sudo ufw allow from 10.0.0.0/8 to any port 5432
sudo ufw allow from **********/12 to any port 5432
sudo ufw allow from ***********/16 to any port 5432
```

### 数据库安全

```sql
-- 创建专用数据库用户
CREATE USER ai_gen_hub_auth WITH PASSWORD 'strong-password';
GRANT CONNECT ON DATABASE ai_gen_hub TO ai_gen_hub_auth;
GRANT USAGE ON SCHEMA public TO ai_gen_hub_auth;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO ai_gen_hub_auth;

-- 启用行级安全
ALTER TABLE auth_users ENABLE ROW LEVEL SECURITY;
CREATE POLICY user_isolation ON auth_users
    USING (id = current_setting('app.current_user_id')::uuid);
```

## 📊 监控和日志

### 日志配置

```yaml
# logging.yaml
version: 1
disable_existing_loggers: false

formatters:
  detailed:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  json:
    format: '{"timestamp": "%(asctime)s", "logger": "%(name)s", "level": "%(levelname)s", "message": "%(message)s"}'

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: detailed
    stream: ext://sys.stdout

  file:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: json
    filename: /app/logs/ai-gen-hub.log
    maxBytes: 10485760  # 10MB
    backupCount: 5

  audit:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: json
    filename: /app/logs/audit.log
    maxBytes: 10485760
    backupCount: 10

loggers:
  ai_gen_hub.auth:
    level: DEBUG
    handlers: [console, file, audit]
    propagate: false

  ai_gen_hub.security:
    level: INFO
    handlers: [console, audit]
    propagate: false

root:
  level: INFO
  handlers: [console, file]
```

### Prometheus 监控

```python
# metrics.py
from prometheus_client import Counter, Histogram, Gauge

# 认证指标
auth_requests_total = Counter(
    'auth_requests_total',
    'Total authentication requests',
    ['method', 'status']
)

auth_duration_seconds = Histogram(
    'auth_duration_seconds',
    'Authentication request duration'
)

active_sessions = Gauge(
    'active_sessions_total',
    'Number of active user sessions'
)

# API Token 指标
api_token_requests_total = Counter(
    'api_token_requests_total',
    'Total API token requests',
    ['token_type', 'status']
)

rate_limit_exceeded_total = Counter(
    'rate_limit_exceeded_total',
    'Total rate limit exceeded events',
    ['identifier_type']
)
```

### Grafana 仪表板

```json
{
  "dashboard": {
    "title": "AI Gen Hub 认证系统监控",
    "panels": [
      {
        "title": "认证请求率",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(auth_requests_total[5m])",
            "legendFormat": "{{method}} - {{status}}"
          }
        ]
      },
      {
        "title": "活跃会话数",
        "type": "singlestat",
        "targets": [
          {
            "expr": "active_sessions_total"
          }
        ]
      },
      {
        "title": "频率限制事件",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(rate_limit_exceeded_total[5m])",
            "legendFormat": "{{identifier_type}}"
          }
        ]
      }
    ]
  }
}
```

## 🔧 故障排除

### 常见问题

#### 1. JWT 令牌验证失败

**症状**: 用户无法通过JWT令牌认证

**解决方案**:
```bash
# 检查JWT密钥配置
echo $AUTH_JWT_SECRET_KEY

# 验证令牌格式
python -c "
import jwt
token = 'your-jwt-token'
try:
    payload = jwt.decode(token, verify=False)
    print('Token payload:', payload)
except Exception as e:
    print('Token error:', e)
"

# 检查时钟同步
sudo ntpdate -s time.nist.gov
```

#### 2. IdP 回调失败

**症状**: OAuth回调返回错误

**解决方案**:
```bash
# 检查回调URL配置
curl -I https://your-domain.com/auth/callback/auth0

# 验证IdP配置
python -c "
from ai_gen_hub.auth.idp import create_idp_provider
from ai_gen_hub.auth.models import IdPConfig, IdPType

config = IdPConfig(
    name='auth0',
    type=IdPType.AUTH0,
    client_id='your-client-id',
    # ... 其他配置
)

provider = create_idp_provider(config)
print('Provider created successfully')
"
```

#### 3. 数据库连接问题

**症状**: 认证系统无法连接数据库

**解决方案**:
```bash
# 测试数据库连接
psql $DATABASE_URL -c "SELECT 1;"

# 检查数据库表
psql $DATABASE_URL -c "\dt"

# 验证用户权限
psql $DATABASE_URL -c "SELECT current_user, session_user;"
```

#### 4. 频率限制问题

**症状**: 用户请求被频率限制阻止

**解决方案**:
```bash
# 检查Redis连接
redis-cli -u $REDIS_URL ping

# 查看频率限制状态
redis-cli -u $REDIS_URL keys "rate_limit:*"

# 重置用户频率限制
redis-cli -u $REDIS_URL del "rate_limit:user:user-id"
```

### 调试工具

```bash
# 启用调试日志
export AI_GEN_HUB_LOG_LEVEL=DEBUG

# 检查认证中间件
curl -H "Authorization: Bearer your-token" \
     -H "X-Debug: true" \
     https://your-domain.com/auth/me

# 验证权限检查
python -c "
from ai_gen_hub.auth.rbac import RBACManager
from ai_gen_hub.auth.models import Permission
from uuid import UUID

rbac = RBACManager()
user_id = UUID('your-user-id')
has_perm = rbac.check_permission(user_id, Permission.API_TEXT_GENERATE)
print(f'Has permission: {has_perm}')
"
```

### 性能优化

```bash
# 数据库查询优化
EXPLAIN ANALYZE SELECT * FROM auth_users WHERE email = '<EMAIL>';

# Redis 内存使用
redis-cli -u $REDIS_URL info memory

# 应用性能分析
python -m cProfile -o profile.stats -m ai_gen_hub.main
```

---

## 📚 相关文档

- [认证API文档](auth_api_reference.md)
- [安全最佳实践](security_best_practices.md)
- [故障排除指南](troubleshooting.md)
- [性能调优指南](performance_tuning.md)

## 🆘 获取帮助

如果您在部署过程中遇到问题：

1. 查看[故障排除](#故障排除)部分
2. 检查应用日志和审计日志
3. 在GitHub Issues中搜索相关问题
4. 联系技术支持团队

---

**注意**: 本指南假设您已经熟悉Docker、Kubernetes和相关技术。在生产环境中部署前，请确保充分测试所有配置。
```
