# 新增免费AI供应商使用指南

本指南介绍如何使用AI Gen Hub新增的免费AI供应商：Cohere和Hugging Face。

## 📋 目录

- [概述](#概述)
- [Cohere供应商](#cohere供应商)
- [Hugging Face供应商](#hugging-face供应商)
- [v2接口兼容性](#v2接口兼容性)
- [配置示例](#配置示例)
- [使用示例](#使用示例)
- [故障排除](#故障排除)

## 🌟 概述

### 新增供应商特点

| 供应商 | 免费额度 | 主要特色 | 支持功能 |
|--------|----------|----------|----------|
| **Cohere** | 每月1000次调用 | Command R系列模型，企业级RAG | 文本生成、工具调用、流式输出 |
| **Hugging Face** | 每月$0.10免费额度 | 大量开源模型，FLUX图像生成 | 文本生成、图像生成、多模态 |

### v2接口增强

- **自动兼容性检查**：自动验证请求与供应商的兼容性
- **参数自动适配**：智能映射不同供应商的参数格式
- **优化建议**：提供性能和兼容性优化建议
- **向后兼容**：完全兼容v1接口

## 🚀 Cohere供应商

### 特色功能

- **Command R+**：最强大的模型，适合复杂推理任务
- **Command R**：平衡性能和成本的最佳选择
- **工具调用**：支持RAG和函数调用功能
- **流式输出**：实时响应流式处理

### 支持的模型

```python
# 推荐模型
"command-r-plus"     # 最强大，适合复杂任务
"command-r"          # 平衡选择，日常使用
"command"            # 基础模型
"command-light"      # 轻量级，快速响应

# 便捷别名
"cohere-latest"      # 自动选择最新最强模型
"cohere"             # 默认推荐模型
"cohere-fast"        # 快速响应模型
"cohere-best"        # 最佳质量模型
```

### API密钥获取

1. 访问 [Cohere Dashboard](https://dashboard.cohere.ai/)
2. 注册账户并验证邮箱
3. 在API Keys页面创建新的API密钥
4. 设置环境变量：`export COHERE_API_KEY="your-api-key"`

### 使用示例

```python
# v2接口使用示例
from ai_gen_hub.core.interfaces import OptimizedTextGenerationRequest, GenerationConfig, Message, MessageRole

# 创建优化版本请求
request = OptimizedTextGenerationRequest(
    messages=[
        Message(role=MessageRole.USER, content="解释一下量子计算的基本原理")
    ],
    model="command-r-plus",
    generation=GenerationConfig(
        temperature=0.7,
        max_tokens=1000,
        top_p=0.9,
        top_k=50  # Cohere支持top_k
    )
)

# 发送请求
response = await text_service.generate_text_optimized(request)
```

## 🤗 Hugging Face供应商

### 特色功能

- **开源模型生态**：支持Llama、Mistral、DeepSeek等顶级开源模型
- **多模态支持**：文本生成、图像生成、视觉理解
- **OpenAI兼容**：使用标准OpenAI API格式
- **自动路由**：智能选择最佳模型实例

### 支持的模型

#### 文本生成模型
```python
# Llama系列（Meta）
"meta-llama/Llama-3.1-70B-Instruct"     # 最强开源模型
"meta-llama/Llama-3.1-8B-Instruct"      # 平衡选择
"meta-llama/Llama-3.2-3B-Instruct"      # 轻量级
"meta-llama/Llama-3.2-11B-Vision-Instruct"  # 多模态

# DeepSeek系列（中文优化）
"deepseek-ai/DeepSeek-V3-0324"           # 最新中文模型
"deepseek-ai/deepseek-coder-33b-instruct"  # 代码专用

# Mistral系列（欧洲）
"mistralai/Mistral-7B-Instruct-v0.3"    # 高效模型
"mistralai/Mixtral-8x7B-Instruct-v0.1"  # 混合专家

# 便捷别名
"huggingface-latest"    # 最新最强模型
"huggingface-chinese"   # 中文优化模型
"huggingface-code"      # 代码专用模型
```

#### 图像生成模型
```python
# FLUX系列（最新）
"black-forest-labs/FLUX.1-dev"          # 开发版，质量最高
"black-forest-labs/FLUX.1-schnell"      # 快速版

# Stable Diffusion系列
"stabilityai/stable-diffusion-3.5-large"  # SD 3.5大模型
"stabilityai/stable-diffusion-xl-base-1.0"  # SDXL基础版
```

### API密钥获取

1. 访问 [Hugging Face](https://huggingface.co/)
2. 注册账户并验证邮箱
3. 在Settings > Access Tokens页面创建新token
4. 选择"Read"权限即可
5. 设置环境变量：`export HUGGINGFACE_API_KEY="hf_your-token"`

### 使用示例

```python
# 文本生成示例
request = OptimizedTextGenerationRequest(
    messages=[
        Message(role=MessageRole.USER, content="用中文解释机器学习的基本概念")
    ],
    model="deepseek-ai/DeepSeek-V3-0324",  # 中文优化模型
    generation=GenerationConfig(
        temperature=0.7,
        max_tokens=2048,
        top_p=0.9
        # 注意：Hugging Face不支持top_k
    )
)

# 图像生成示例
from ai_gen_hub.core.interfaces import ImageGenerationRequest

image_request = ImageGenerationRequest(
    prompt="一只可爱的橘色小猫坐在花园里，数字艺术风格",
    model="black-forest-labs/FLUX.1-dev",
    n=1,
    size="1024x1024"
)

image_response = await image_service.generate_image(image_request)
```

## 🔧 v2接口兼容性

### 兼容性检查

```python
# 检查请求兼容性
from ai_gen_hub.core.compatibility import ProviderCompatibilityManager

compatibility_manager = ProviderCompatibilityManager()

# 验证Cohere兼容性
cohere_report = compatibility_manager.validate_request_compatibility(
    request, "cohere"
)

print("兼容性报告：")
print(f"兼容: {cohere_report['compatible']}")
print(f"警告: {cohere_report['warnings']}")
print(f"建议: {cohere_report['suggestions']}")
```

### 参数映射

| v2接口参数 | Cohere参数 | Hugging Face参数 | 说明 |
|------------|------------|------------------|------|
| `top_p` | `p` | `top_p` | 核采样参数 |
| `top_k` | `k` | ❌不支持 | Top-K采样 |
| `stop` | `stop_sequences` | `stop` | 停止序列 |
| `max_tokens` | `max_tokens` | `max_tokens` | 最大token数 |

### 自动适配示例

```python
# v2接口会自动处理参数适配
request = OptimizedTextGenerationRequest(
    messages=[Message(role=MessageRole.USER, content="Hello")],
    model="command-r",
    generation=GenerationConfig(
        top_p=0.9,      # 自动映射为Cohere的"p"参数
        top_k=50,       # 自动映射为Cohere的"k"参数
        stop=["END"]    # 自动映射为Cohere的"stop_sequences"
    )
)

# 系统会自动：
# 1. 检查兼容性
# 2. 映射参数名称
# 3. 转换消息格式
# 4. 过滤不支持的参数
```

## ⚙️ 配置示例

### 环境变量配置

```bash
# 设置API密钥
export COHERE_API_KEY="your-cohere-api-key"
export HUGGINGFACE_API_KEY="hf_your-huggingface-token"

# 可选：设置自定义配置
export AI_GEN_HUB_CONFIG_PATH="/path/to/your/config.yaml"
```

### YAML配置文件

```yaml
# config/providers.yaml
cohere:
  enabled: true
  api_key: "${COHERE_API_KEY}"
  default_params:
    temperature: 0.7
    max_tokens: 1000

huggingface:
  enabled: true
  api_key: "${HUGGINGFACE_API_KEY}"
  default_params:
    temperature: 0.7
    max_tokens: 2048

# 启用兼容性检查
compatibility:
  auto_parameter_adaptation: true
  show_compatibility_warnings: true
```

## 🔍 故障排除

### 常见问题

#### 1. Cohere API密钥无效
```
错误：Cohere API 健康检查失败
解决：检查API密钥格式，确保在Cohere Dashboard中正确生成
```

#### 2. Hugging Face模型不可用
```
错误：模型 'meta-llama/Llama-3.1-70B-Instruct' 不可用
解决：某些模型可能需要申请访问权限，或选择其他可用模型
```

#### 3. 参数不兼容警告
```
警告：供应商不支持top_k参数，将被忽略
解决：这是正常的兼容性处理，系统会自动忽略不支持的参数
```

### 调试技巧

```python
# 启用详细日志
import logging
logging.getLogger("ai_gen_hub").setLevel(logging.DEBUG)

# 检查供应商能力
capabilities = compatibility_manager.get_all_provider_capabilities()
print(f"Cohere能力: {capabilities['cohere']}")
print(f"Hugging Face能力: {capabilities['huggingface']}")

# 测试兼容性
report = compatibility_manager.validate_request_compatibility(request, "cohere")
if not report['compatible']:
    print(f"兼容性问题: {report['errors']}")
```

### 性能优化建议

1. **模型选择**：
   - 简单任务使用轻量级模型（`command-light`, `Llama-3.2-3B`）
   - 复杂任务使用强大模型（`command-r-plus`, `Llama-3.1-70B`）

2. **参数调优**：
   - 降低`temperature`提高一致性
   - 适当设置`max_tokens`避免超时
   - 使用`top_p`而非`top_k`（更好的兼容性）

3. **缓存策略**：
   - 启用模型列表缓存
   - 缓存兼容性检查结果
   - 使用负载均衡分散请求

## 📚 更多资源

- [Cohere API文档](https://docs.cohere.ai/)
- [Hugging Face API文档](https://huggingface.co/docs/api-inference/)
- [AI Gen Hub完整文档](./api.md)
- [v2接口详细说明](./v2_api_guide.md)
