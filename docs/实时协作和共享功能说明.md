# 实时协作和共享功能系统说明

AI Gen Hub 实时协作和共享功能系统提供了强大的多用户协作编辑、内容共享、版本控制和实时同步能力，支持团队高效协作和知识共享。

## 🚀 功能概览

### 1. 实时协作编辑
- **多用户同步**: 支持多个用户同时编辑同一文档
- **操作转换**: 智能的冲突检测和解决机制
- **实时预览**: 所见即所得的协作编辑体验
- **光标同步**: 实时显示其他用户的编辑位置

### 2. 内容共享管理
- **灵活权限**: 支持所有者、编辑者、查看者等多种角色
- **共享范围**: 私有、团队、组织、公开等多种共享模式
- **链接共享**: 生成安全的共享链接，支持访问控制
- **权限继承**: 智能的权限继承和传播机制

### 3. 版本控制系统
- **Git-like版本管理**: 完整的版本历史记录
- **分支和合并**: 支持并行开发和版本合并
- **差异对比**: 可视化的版本差异显示
- **一键回滚**: 快速回滚到任意历史版本

### 4. 实时通信
- **WebSocket连接**: 低延迟的实时数据传输
- **在线状态**: 实时显示用户在线状态和活动
- **即时消息**: 内置聊天和评论功能
- **通知系统**: 智能的协作通知和提醒

## 📋 核心组件

### 协作会话模型
```python
@dataclass
class CollaborationSession:
    session_id: str
    content_id: str
    title: str
    participants: List[UserPresence]
    status: SessionStatus
    max_participants: int = 50
    allow_anonymous: bool = False
    enable_chat: bool = True
```

### 共享内容模型
```python
@dataclass
class SharedContent:
    content_id: str
    title: str
    content_type: ContentType
    content_data: str
    owner_id: str
    share_scope: ShareScope
    permissions: List[CollaborationPermission]
    current_version: int
```

### 实时操作模型
```python
@dataclass
class RealtimeOperation:
    operation_id: str
    operation_type: OperationType  # INSERT, DELETE, REPLACE
    position: int
    content: str
    user_id: str
    timestamp: datetime
    sequence_number: int
```

## 🔧 API接口

### WebSocket连接

#### 建立连接
```javascript
// 连接到协作会话
const ws = new WebSocket('ws://localhost:8000/ws/collaboration/{session_id}?user_id={user_id}');

ws.onopen = function(event) {
    console.log('连接建立');
};

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    handleMessage(message);
};
```

#### 消息类型
```javascript
// 发送操作
ws.send(JSON.stringify({
    type: 'operation',
    operation_type: 'insert',
    position: 10,
    content: 'Hello World',
    timestamp: new Date().toISOString()
}));

// 发送光标位置
ws.send(JSON.stringify({
    type: 'cursor',
    position: 15,
    selection_start: 10,
    selection_end: 20
}));

// 发送在线状态
ws.send(JSON.stringify({
    type: 'presence',
    status: 'online'
}));
```

### REST API

#### 创建协作会话
```http
POST /api/v1/collaboration/sessions
Content-Type: application/json

{
  "content_id": "content_12345",
  "title": "团队协作文档",
  "description": "项目规划文档协作",
  "max_participants": 20,
  "allow_anonymous": false,
  "require_approval": true,
  "enable_chat": true,
  "enable_voice": false,
  "enable_video": false
}
```

#### 加入协作会话
```http
POST /api/v1/collaboration/sessions/{session_id}/join
Content-Type: application/json

{
  "user_id": "user_12345",
  "username": "Alice",
  "avatar_url": "https://example.com/avatar.jpg"
}
```

#### 创建共享内容
```http
POST /api/v1/collaboration/content
Content-Type: application/json

{
  "title": "项目计划书",
  "content_type": "document",
  "content_data": "# 项目计划\n\n这是项目计划的初始内容...",
  "share_scope": "team",
  "permissions": [
    {
      "user_id": "user_456",
      "role": "editor"
    },
    {
      "user_id": "user_789",
      "role": "viewer"
    }
  ]
}
```

#### 获取协作会话信息
```http
GET /api/v1/collaboration/sessions/{session_id}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "session_id": "session_12345",
    "content_id": "content_67890",
    "title": "团队协作文档",
    "status": "active",
    "participants": [
      {
        "user_id": "user_123",
        "username": "Alice",
        "avatar_url": "https://example.com/alice.jpg",
        "status": "online",
        "cursor_position": 150,
        "joined_at": "2024-01-15T10:30:00Z",
        "last_seen": "2024-01-15T11:45:00Z"
      },
      {
        "user_id": "user_456",
        "username": "Bob",
        "status": "away",
        "cursor_position": 75,
        "joined_at": "2024-01-15T10:35:00Z",
        "last_seen": "2024-01-15T11:40:00Z"
      }
    ],
    "created_at": "2024-01-15T10:00:00Z",
    "last_activity_at": "2024-01-15T11:45:00Z",
    "operation_sequence": 127
  }
}
```

#### 创建内容版本
```http
POST /api/v1/collaboration/content/{content_id}/versions
Content-Type: application/json

{
  "commit_message": "添加项目里程碑章节",
  "description": "详细描述了项目的各个重要里程碑"
}
```

#### 获取版本历史
```http
GET /api/v1/collaboration/content/{content_id}/versions
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "content_id": "content_12345",
    "current_version": 5,
    "versions": [
      {
        "version_id": "version_001",
        "version_number": 1,
        "title": "初始版本",
        "commit_message": "创建文档",
        "author_id": "user_123",
        "author_name": "Alice",
        "created_at": "2024-01-15T10:00:00Z",
        "changes_count": 0,
        "content_size": 1250
      },
      {
        "version_id": "version_005",
        "version_number": 5,
        "title": "里程碑版本",
        "commit_message": "添加项目里程碑章节",
        "author_id": "user_456",
        "author_name": "Bob",
        "created_at": "2024-01-15T11:30:00Z",
        "changes_count": 15,
        "additions": 12,
        "deletions": 3,
        "content_size": 2100
      }
    ]
  }
}
```

## 🎯 操作转换算法

### 基本原理
操作转换（Operational Transformation）是实时协作编辑的核心技术，用于解决并发编辑冲突。

### 转换规则

#### 插入 vs 插入
```python
def transform_insert_insert(op1, op2):
    if op1.position <= op2.position:
        # op1在op2之前，op2位置后移
        op2.position += len(op1.content)
    else:
        # op2在op1之前，op1位置后移
        op1.position += len(op2.content)
    return op1, op2
```

#### 插入 vs 删除
```python
def transform_insert_delete(insert_op, delete_op):
    if insert_op.position <= delete_op.position:
        # 插入在删除之前
        delete_op.position += len(insert_op.content)
    elif insert_op.position >= delete_op.position + delete_op.length:
        # 插入在删除之后
        insert_op.position -= delete_op.length
    else:
        # 插入在删除范围内
        insert_op.position = delete_op.position
    return insert_op, delete_op
```

#### 删除 vs 删除
```python
def transform_delete_delete(op1, op2):
    # 计算重叠部分
    overlap_start = max(op1.position, op2.position)
    overlap_end = min(op1.position + op1.length, op2.position + op2.length)
    overlap_length = max(0, overlap_end - overlap_start)
    
    # 调整操作参数
    if overlap_length > 0:
        # 有重叠，需要调整长度
        op1.length -= overlap_length
        op2.length -= overlap_length
    
    return op1, op2
```

## 🔒 权限管理

### 角色定义
- **所有者 (Owner)**: 完全控制权限，可以管理内容和权限
- **编辑者 (Editor)**: 可以编辑内容和添加评论
- **查看者 (Viewer)**: 只能查看内容
- **评论者 (Commenter)**: 可以查看内容和添加评论

### 权限矩阵
| 操作 | 所有者 | 编辑者 | 评论者 | 查看者 |
|------|--------|--------|--------|--------|
| 查看内容 | ✅ | ✅ | ✅ | ✅ |
| 编辑内容 | ✅ | ✅ | ❌ | ❌ |
| 添加评论 | ✅ | ✅ | ✅ | ❌ |
| 管理权限 | ✅ | ❌ | ❌ | ❌ |
| 删除内容 | ✅ | ❌ | ❌ | ❌ |
| 创建版本 | ✅ | ✅ | ❌ | ❌ |

### 共享范围
- **私有 (Private)**: 仅所有者和被授权用户可访问
- **团队 (Team)**: 团队成员可访问
- **组织 (Organization)**: 组织内所有成员可访问
- **公开 (Public)**: 所有人可访问
- **链接 (Link)**: 拥有链接的人可访问

## 📊 实时同步机制

### 状态同步
```javascript
// 用户在线状态同步
{
  "type": "presence_update",
  "user_id": "user_123",
  "status": "online",
  "cursor_position": 150,
  "selection_start": 140,
  "selection_end": 160,
  "editing_section": "paragraph_5"
}

// 内容变更同步
{
  "type": "content_changed",
  "operation": {
    "operation_id": "op_12345",
    "type": "insert",
    "position": 100,
    "content": "新增内容",
    "user_id": "user_456",
    "timestamp": "2024-01-15T11:30:00Z"
  }
}
```

### 冲突解决
```javascript
// 操作转换结果
{
  "type": "operation_transformed",
  "original_operation": {
    "operation_id": "op_12345",
    "position": 100,
    "content": "原始内容"
  },
  "transformed_operation": {
    "operation_id": "op_12345",
    "position": 105,
    "content": "原始内容"
  },
  "reason": "position_adjusted_for_concurrent_insert"
}
```

## 🎯 使用场景

### 1. 文档协作
```python
# 创建文档协作会话
session = await collaboration_service.create_session(
    content_id="doc_123",
    title="产品需求文档协作",
    max_participants=10,
    enable_chat=True
)

# 多人同时编辑
await collaboration_service.apply_operation(
    session_id=session.session_id,
    user_id="product_manager",
    operation_type=OperationType.INSERT,
    position=0,
    content="# 产品需求文档\n\n"
)
```

### 2. 代码协作
```python
# 代码文件协作
code_content = await collaboration_service.create_shared_content(
    title="main.py",
    content_type=ContentType.TEXT,
    content_data="# Python代码文件\n",
    owner_id="developer_1"
)

# 实时代码编辑
await collaboration_service.apply_operation(
    session_id=session.session_id,
    user_id="developer_2",
    operation_type=OperationType.INSERT,
    position=20,
    content="import asyncio\n"
)
```

### 3. 设计协作
```python
# 设计文件共享
design_content = await collaboration_service.create_shared_content(
    title="UI设计稿",
    content_type=ContentType.IMAGE,
    content_url="https://example.com/design.png",
    owner_id="designer"
)

# 添加设计评论
await collaboration_service.add_comment(
    content_id=design_content.content_id,
    text="这个按钮的颜色需要调整",
    user_id="product_manager",
    position={"x": 100, "y": 200}
)
```

## 📈 性能优化

### 操作批处理
```python
# 批量处理操作，减少网络传输
batch_operations = [
    {"type": "insert", "position": 10, "content": "Hello"},
    {"type": "insert", "position": 15, "content": " World"},
    {"type": "format", "position": 10, "length": 11, "style": "bold"}
]

await collaboration_service.apply_batch_operations(
    session_id=session.session_id,
    operations=batch_operations,
    user_id="user_123"
)
```

### 增量同步
```python
# 只同步变更部分，减少数据传输
{
  "type": "incremental_sync",
  "since_sequence": 100,
  "operations": [
    {"sequence": 101, "type": "insert", "position": 50, "content": "新内容"},
    {"sequence": 102, "type": "delete", "position": 80, "length": 5}
  ]
}
```

### 离线支持
```python
# 离线操作缓存
offline_operations = [
    {"type": "insert", "position": 20, "content": "离线编辑内容"},
    {"type": "delete", "position": 50, "length": 10}
]

# 重新连接后同步
await collaboration_service.sync_offline_operations(
    session_id=session.session_id,
    operations=offline_operations,
    user_id="user_123"
)
```

## 🔧 部署和扩展

### 水平扩展
- **负载均衡**: 支持多实例部署和WebSocket负载均衡
- **状态共享**: 使用Redis集群共享会话状态
- **消息队列**: 使用消息队列处理操作转换
- **数据分片**: 按内容ID进行数据分片

### 监控指标
- **连接数**: 实时WebSocket连接数量
- **操作延迟**: 操作处理和同步延迟
- **冲突率**: 操作冲突和转换频率
- **用户活跃度**: 用户参与协作的活跃程度

## 📞 技术支持

如有问题或建议，请联系：
- 📧 邮箱: <EMAIL>
- 📱 微信: ai-collaboration-support
- 🌐 官网: https://ai-gen-hub.com/collaboration
- 📚 文档: https://docs.ai-gen-hub.com/collaboration
