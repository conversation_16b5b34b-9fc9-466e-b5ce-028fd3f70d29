"""
Swagger/OpenAPI 流式输出支持方案

基于调研结果，OpenAPI 3.0+ 可以通过 text/event-stream 媒体类型来描述流式输出，
但需要一些扩展和最佳实践来完整支持流式输出的文档化和测试。
"""

from typing import Any, Dict, List, Optional
from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi


def create_streaming_openapi_schema(app: FastAPI) -> Dict[str, Any]:
    """创建支持流式输出的 OpenAPI 规范"""
    
    # 获取基础 OpenAPI 规范
    openapi_schema = get_openapi(
        title="AI Gen Hub API",
        version="1.0.0",
        description="高性能AI服务聚合平台，支持流式输出",
        routes=app.routes,
    )
    
    # 添加流式输出相关的组件定义
    streaming_components = {
        "schemas": {
            # 流式响应块
            "StreamChunk": {
                "type": "object",
                "description": "流式响应数据块",
                "required": ["id", "object", "created", "choices"],
                "properties": {
                    "id": {
                        "type": "string",
                        "description": "响应唯一标识",
                        "example": "chatcmpl-123"
                    },
                    "object": {
                        "type": "string",
                        "enum": ["chat.completion.chunk"],
                        "description": "对象类型"
                    },
                    "created": {
                        "type": "integer",
                        "description": "创建时间戳",
                        "example": **********
                    },
                    "model": {
                        "type": "string",
                        "description": "使用的模型",
                        "example": "gpt-4"
                    },
                    "choices": {
                        "type": "array",
                        "items": {
                            "$ref": "#/components/schemas/StreamChoice"
                        },
                        "description": "流式选择数据"
                    },
                    "provider": {
                        "type": "string",
                        "description": "供应商名称",
                        "example": "openai"
                    },
                    "request_id": {
                        "type": "string",
                        "description": "请求ID"
                    },
                    "chunk_index": {
                        "type": "integer",
                        "description": "块索引",
                        "minimum": 0
                    },
                    "is_final": {
                        "type": "boolean",
                        "description": "是否为最后一块",
                        "default": False
                    },
                    "usage": {
                        "$ref": "#/components/schemas/Usage",
                        "description": "使用量统计（仅在最后一块中包含）"
                    }
                }
            },
            
            # 流式选择
            "StreamChoice": {
                "type": "object",
                "required": ["index", "delta"],
                "properties": {
                    "index": {
                        "type": "integer",
                        "description": "选择索引"
                    },
                    "delta": {
                        "$ref": "#/components/schemas/StreamDelta",
                        "description": "增量数据"
                    },
                    "finish_reason": {
                        "type": "string",
                        "enum": ["stop", "length", "content_filter", "function_call", "tool_calls"],
                        "description": "完成原因",
                        "nullable": True
                    }
                }
            },
            
            # 流式增量数据
            "StreamDelta": {
                "type": "object",
                "properties": {
                    "role": {
                        "type": "string",
                        "enum": ["assistant"],
                        "description": "消息角色"
                    },
                    "content": {
                        "type": "string",
                        "description": "增量内容",
                        "nullable": True
                    },
                    "function_call": {
                        "type": "object",
                        "description": "函数调用增量",
                        "nullable": True
                    },
                    "tool_calls": {
                        "type": "array",
                        "items": {
                            "type": "object"
                        },
                        "description": "工具调用增量",
                        "nullable": True
                    }
                }
            },
            
            # 流式事件包装器
            "StreamEvent": {
                "type": "object",
                "description": "Server-Sent Events 事件格式",
                "required": ["event", "data"],
                "properties": {
                    "event": {
                        "type": "string",
                        "enum": ["chunk", "error", "done"],
                        "description": "事件类型"
                    },
                    "data": {
                        "oneOf": [
                            {"$ref": "#/components/schemas/StreamChunk"},
                            {"$ref": "#/components/schemas/ErrorInfo"},
                            {"type": "string", "enum": ["[DONE]"]}
                        ],
                        "description": "事件数据"
                    },
                    "id": {
                        "type": "string",
                        "description": "事件ID（可选）"
                    },
                    "retry": {
                        "type": "integer",
                        "description": "重试间隔（毫秒）"
                    }
                }
            },
            
            # 错误信息
            "ErrorInfo": {
                "type": "object",
                "required": ["error_code", "message"],
                "properties": {
                    "error_code": {
                        "type": "string",
                        "description": "错误代码"
                    },
                    "message": {
                        "type": "string",
                        "description": "错误消息"
                    },
                    "details": {
                        "type": "string",
                        "description": "详细错误信息"
                    },
                    "retryable": {
                        "type": "boolean",
                        "description": "是否可重试",
                        "default": False
                    }
                }
            },
            
            # 心跳事件
            "HeartbeatEvent": {
                "type": "object",
                "description": "心跳事件，用于保持连接活跃",
                "required": ["event"],
                "properties": {
                    "event": {
                        "type": "string",
                        "const": "ping"
                    },
                    "timestamp": {
                        "type": "string",
                        "format": "date-time",
                        "description": "心跳时间戳"
                    }
                }
            }
        }
    }
    
    # 合并组件定义
    if "components" not in openapi_schema:
        openapi_schema["components"] = {}
    
    if "schemas" not in openapi_schema["components"]:
        openapi_schema["components"]["schemas"] = {}
    
    openapi_schema["components"]["schemas"].update(streaming_components["schemas"])
    
    # 更新文本生成端点以支持流式输出文档
    if "paths" in openapi_schema and "/api/v1/text/generate" in openapi_schema["paths"]:
        text_generate_path = openapi_schema["paths"]["/api/v1/text/generate"]["post"]
        
        # 更新响应定义
        text_generate_path["responses"]["200"] = {
            "description": "成功的文本生成响应",
            "content": {
                "application/json": {
                    "description": "同步响应（当 stream=false 时）",
                    "schema": {
                        "$ref": "#/components/schemas/TextGenerationResponse"
                    }
                },
                "text/event-stream": {
                    "description": "流式响应（当 stream=true 时）",
                    "schema": {
                        "$ref": "#/components/schemas/StreamEvent"
                    },
                    "examples": {
                        "chunk_event": {
                            "summary": "数据块事件",
                            "value": {
                                "event": "chunk",
                                "data": {
                                    "id": "chatcmpl-123",
                                    "object": "chat.completion.chunk",
                                    "created": **********,
                                    "model": "gpt-4",
                                    "choices": [{
                                        "index": 0,
                                        "delta": {"content": "Hello"},
                                        "finish_reason": None
                                    }],
                                    "provider": "openai",
                                    "request_id": "req-123",
                                    "chunk_index": 0
                                }
                            }
                        },
                        "done_event": {
                            "summary": "完成事件",
                            "value": {
                                "event": "done",
                                "data": "[DONE]"
                            }
                        },
                        "error_event": {
                            "summary": "错误事件",
                            "value": {
                                "event": "error",
                                "data": {
                                    "error_code": "RATE_LIMIT_EXCEEDED",
                                    "message": "请求频率超限",
                                    "retryable": True
                                }
                            }
                        }
                    },
                    # 添加 Speakeasy 扩展
                    "x-speakeasy-streaming": True,
                    "x-speakeasy-sse-sentinel": "[DONE]"
                }
            }
        }
        
        # 添加流式输出相关的参数说明
        if "requestBody" in text_generate_path:
            request_schema = text_generate_path["requestBody"]["content"]["application/json"]["schema"]
            if "$ref" in request_schema:
                # 如果使用引用，需要在 components 中更新
                pass
            else:
                # 直接更新 schema
                if "properties" in request_schema and "stream" in request_schema["properties"]:
                    request_schema["properties"]["stream"]["description"] = (
                        "是否启用流式输出。当设置为 true 时，响应将使用 Server-Sent Events (SSE) 格式，"
                        "Content-Type 为 text/event-stream"
                    )
    
    # 添加流式输出相关的扩展信息
    openapi_schema["info"]["x-streaming-support"] = {
        "description": "此API支持流式输出",
        "protocols": ["Server-Sent Events (SSE)"],
        "content_type": "text/event-stream",
        "event_format": {
            "chunk": "数据块事件，包含增量响应数据",
            "error": "错误事件，包含错误信息",
            "done": "完成事件，标识流式输出结束",
            "ping": "心跳事件，保持连接活跃"
        }
    }
    
    return openapi_schema


def add_streaming_examples_to_openapi(openapi_schema: Dict[str, Any]) -> Dict[str, Any]:
    """为 OpenAPI 规范添加流式输出示例"""
    
    # 添加流式输出的代码示例
    streaming_examples = {
        "x-code-samples": [
            {
                "lang": "JavaScript",
                "label": "使用 EventSource API",
                "source": """
// 创建 EventSource 连接
const eventSource = new EventSource('/api/v1/text/generate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-api-key'
  },
  body: JSON.stringify({
    model: 'gpt-4',
    messages: [{ role: 'user', content: 'Hello' }],
    stream: true
  })
});

// 监听消息事件
eventSource.onmessage = function(event) {
  if (event.data === '[DONE]') {
    eventSource.close();
    return;
  }
  
  const chunk = JSON.parse(event.data);
  if (chunk.choices && chunk.choices[0].delta.content) {
    console.log(chunk.choices[0].delta.content);
  }
};

// 监听错误事件
eventSource.onerror = function(error) {
  console.error('Stream error:', error);
  eventSource.close();
};
"""
            },
            {
                "lang": "Python",
                "label": "使用 httpx 流式客户端",
                "source": """
import httpx
import json

async def stream_text_generation():
    async with httpx.AsyncClient() as client:
        async with client.stream(
            'POST',
            '/api/v1/text/generate',
            json={
                'model': 'gpt-4',
                'messages': [{'role': 'user', 'content': 'Hello'}],
                'stream': True
            },
            headers={'Accept': 'text/event-stream'}
        ) as response:
            async for line in response.aiter_lines():
                if line.startswith('data: '):
                    data = line[6:]  # 移除 'data: ' 前缀
                    if data == '[DONE]':
                        break
                    
                    chunk = json.loads(data)
                    if chunk['choices'][0]['delta'].get('content'):
                        print(chunk['choices'][0]['delta']['content'], end='')
"""
            },
            {
                "lang": "curl",
                "label": "使用 curl 测试流式输出",
                "source": """
curl -X POST /api/v1/text/generate \\
  -H "Content-Type: application/json" \\
  -H "Accept: text/event-stream" \\
  -H "Authorization: Bearer your-api-key" \\
  -d '{
    "model": "gpt-4",
    "messages": [{"role": "user", "content": "Hello"}],
    "stream": true
  }' \\
  --no-buffer
"""
            }
        ]
    }
    
    # 将示例添加到文本生成端点
    if ("paths" in openapi_schema and 
        "/api/v1/text/generate" in openapi_schema["paths"] and
        "post" in openapi_schema["paths"]["/api/v1/text/generate"]):
        
        openapi_schema["paths"]["/api/v1/text/generate"]["post"].update(streaming_examples)
    
    return openapi_schema


def create_streaming_test_operations(openapi_schema: Dict[str, Any]) -> Dict[str, Any]:
    """为流式输出创建测试操作"""
    
    # 添加专门的流式测试端点
    streaming_test_path = {
        "/api/v1/text/generate/stream-test": {
            "post": {
                "tags": ["文本生成", "测试"],
                "summary": "流式输出测试端点",
                "description": "专门用于测试流式输出功能的端点，返回预定义的测试数据",
                "operationId": "testStreamGeneration",
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "test_type": {
                                        "type": "string",
                                        "enum": ["simple", "long", "error", "heartbeat"],
                                        "description": "测试类型",
                                        "default": "simple"
                                    },
                                    "chunk_count": {
                                        "type": "integer",
                                        "minimum": 1,
                                        "maximum": 100,
                                        "description": "数据块数量",
                                        "default": 5
                                    },
                                    "delay_ms": {
                                        "type": "integer",
                                        "minimum": 0,
                                        "maximum": 5000,
                                        "description": "块之间的延迟（毫秒）",
                                        "default": 100
                                    }
                                }
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "流式测试响应",
                        "content": {
                            "text/event-stream": {
                                "schema": {
                                    "$ref": "#/components/schemas/StreamEvent"
                                },
                                "x-speakeasy-streaming": True,
                                "x-speakeasy-sse-sentinel": "[DONE]"
                            }
                        }
                    }
                }
            }
        }
    }
    
    # 添加到 paths
    if "paths" not in openapi_schema:
        openapi_schema["paths"] = {}
    
    openapi_schema["paths"].update(streaming_test_path)
    
    return openapi_schema


# === Swagger UI 自定义配置 ===
SWAGGER_UI_STREAMING_CONFIG = {
    "customCss": """
        .swagger-ui .response-content-type {
            display: block !important;
        }
        
        .swagger-ui .response[data-content-type="text/event-stream"] {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        
        .swagger-ui .response[data-content-type="text/event-stream"]::before {
            content: "🔄 流式输出";
            font-weight: bold;
            color: #007bff;
            display: block;
            margin-bottom: 10px;
        }
    """,
    "customJs": """
        // 添加流式输出测试功能
        window.addEventListener('DOMContentLoaded', function() {
            // 检测流式输出端点
            const streamingEndpoints = document.querySelectorAll('[data-content-type="text/event-stream"]');
            
            streamingEndpoints.forEach(endpoint => {
                const testButton = document.createElement('button');
                testButton.textContent = '测试流式输出';
                testButton.className = 'btn btn-primary btn-sm';
                testButton.onclick = function() {
                    testStreamingEndpoint(endpoint);
                };
                
                endpoint.appendChild(testButton);
            });
        });
        
        function testStreamingEndpoint(endpoint) {
            // 实现流式输出测试逻辑
            console.log('Testing streaming endpoint:', endpoint);
        }
    """
}


# === 使用示例 ===
def setup_streaming_openapi(app: FastAPI) -> None:
    """设置支持流式输出的 OpenAPI 文档"""
    
    def custom_openapi():
        if app.openapi_schema:
            return app.openapi_schema
        
        # 创建基础 OpenAPI 规范
        openapi_schema = create_streaming_openapi_schema(app)
        
        # 添加流式输出示例
        openapi_schema = add_streaming_examples_to_openapi(openapi_schema)
        
        # 添加测试操作
        openapi_schema = create_streaming_test_operations(openapi_schema)
        
        app.openapi_schema = openapi_schema
        return app.openapi_schema
    
    app.openapi = custom_openapi
