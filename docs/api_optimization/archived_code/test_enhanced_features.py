"""
优化版本功能测试套件

本文件包含了对所有新增功能的全面测试，确保代码的正确性和可靠性。
"""

import unittest
import json
from typing import Dict, Any

from optimized_text_generation_request import (
    OptimizedTextGenerationRequest,
    SimpleTextGenerationRequest,
    Message,
    MessageRole,
    GenerationConfig,
    StreamConfig,
    SafetyConfig
)


class TestLegacyMigration(unittest.TestCase):
    """测试传统格式迁移功能"""
    
    def test_basic_legacy_conversion(self):
        """测试基础的传统格式转换"""
        legacy_data = {
            "messages": [
                {"role": "user", "content": "Hello"}
            ],
            "model": "gpt-4",
            "temperature": 0.8,
            "max_tokens": 1000,
            "stream": True
        }
        
        optimized = OptimizedTextGenerationRequest.from_legacy_request(legacy_data)
        
        self.assertEqual(len(optimized.messages), 1)
        self.assertEqual(optimized.model, "gpt-4")
        self.assertEqual(optimized.generation.temperature, 0.8)
        self.assertEqual(optimized.generation.max_tokens, 1000)
        self.assertTrue(optimized.stream.enabled)
    
    def test_complex_legacy_conversion(self):
        """测试复杂的传统格式转换"""
        legacy_data = {
            "messages": [
                {"role": "system", "content": "You are helpful"},
                {"role": "user", "content": "Hello"}
            ],
            "model": "gpt-4",
            "temperature": 0.7,
            "top_p": 0.9,
            "frequency_penalty": 0.1,
            "presence_penalty": 0.2,
            "stop": ["END", "STOP"],
            "functions": [{"name": "test_func"}],
            "response_format": {"type": "json_object"},
            "thinking_budget": 1000,
            "provider_params": {"custom": "value"}
        }
        
        optimized = OptimizedTextGenerationRequest.from_legacy_request(legacy_data)
        
        self.assertEqual(optimized.generation.top_p, 0.9)
        self.assertEqual(optimized.generation.frequency_penalty, 0.1)
        self.assertEqual(optimized.generation.presence_penalty, 0.2)
        self.assertEqual(optimized.generation.stop, ["END", "STOP"])
        self.assertIsNotNone(optimized.functions)
        self.assertIsNotNone(optimized.response_format)
        self.assertIn("thinking_budget", optimized.provider_params)
        self.assertIn("custom", optimized.provider_params)
    
    def test_to_legacy_format(self):
        """测试转换回传统格式"""
        optimized = OptimizedTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="Test")],
            model="gpt-4",
            generation=GenerationConfig(temperature=0.8, max_tokens=500),
            stream=StreamConfig(enabled=True)
        )
        
        legacy = optimized.to_legacy_format()
        
        self.assertEqual(legacy["model"], "gpt-4")
        self.assertEqual(legacy["temperature"], 0.8)
        self.assertEqual(legacy["max_tokens"], 500)
        self.assertTrue(legacy["stream"])


class TestProviderCompatibility(unittest.TestCase):
    """测试供应商兼容性功能"""
    
    def test_openai_params(self):
        """测试OpenAI参数映射"""
        request = OptimizedTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="Test")],
            model="gpt-4",
            generation=GenerationConfig(temperature=0.7, max_tokens=1000),
            functions=[{"name": "test_func"}]
        )
        
        params = request.get_provider_specific_params("openai")
        
        self.assertEqual(params["model"], "gpt-4")
        self.assertEqual(params["temperature"], 0.7)
        self.assertEqual(params["max_tokens"], 1000)
        self.assertIn("functions", params)
        self.assertFalse(params["stream"])
    
    def test_anthropic_params(self):
        """测试Anthropic参数映射"""
        request = OptimizedTextGenerationRequest(
            messages=[
                Message(role=MessageRole.SYSTEM, content="System message"),
                Message(role=MessageRole.USER, content="User message")
            ],
            model="claude-3-sonnet",
            generation=GenerationConfig(temperature=0.7, max_tokens=1000, stop=["END"])
        )
        
        params = request.get_provider_specific_params("anthropic")
        
        self.assertEqual(params["model"], "claude-3-sonnet")
        self.assertEqual(params["max_tokens"], 1000)
        self.assertEqual(params["system"], "System message")
        self.assertEqual(len(params["messages"]), 1)  # 只有user消息
        self.assertEqual(params["stop_sequences"], ["END"])
    
    def test_google_params(self):
        """测试Google AI参数映射"""
        request = OptimizedTextGenerationRequest(
            messages=[
                Message(role=MessageRole.SYSTEM, content="System instruction"),
                Message(role=MessageRole.USER, content="User query")
            ],
            model="gemini-2.5-flash",
            generation=GenerationConfig(temperature=0.7, max_tokens=1000),
            safety=SafetyConfig(content_filter=True, safety_level="medium"),
            provider_params={"thinking_budget": 500}
        )
        
        params = request.get_provider_specific_params("google")
        
        self.assertIn("contents", params)
        self.assertIn("systemInstruction", params)
        self.assertIn("generationConfig", params)
        self.assertIn("safetySettings", params)
        self.assertIn("thinkingConfig", params)
        
        # 检查消息格式转换
        self.assertEqual(len(params["contents"]), 1)
        self.assertEqual(params["contents"][0]["role"], "user")
        self.assertIn("parts", params["contents"][0])
    
    def test_dashscope_params(self):
        """测试DashScope参数映射"""
        request = OptimizedTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="Test")],
            model="qwen-max",
            generation=GenerationConfig(temperature=0.7, max_tokens=1000),
            stream=StreamConfig(enabled=True)
        )
        
        params = request.get_provider_specific_params("dashscope")
        
        self.assertEqual(params["model"], "qwen-max")
        self.assertIn("input", params)
        self.assertIn("parameters", params)
        self.assertEqual(len(params["input"]["messages"]), 1)
        self.assertTrue(params["parameters"]["incremental_output"])


class TestProviderValidation(unittest.TestCase):
    """测试供应商验证功能"""
    
    def test_anthropic_max_tokens_required(self):
        """测试Anthropic要求max_tokens的验证"""
        request = OptimizedTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="Test")],
            model="claude-3-sonnet"
            # 故意不设置max_tokens
        )
        
        validation = request.validate_for_provider("anthropic")
        
        self.assertTrue(any("max_tokens" in error for error in validation["errors"]))
    
    def test_unsupported_features_warning(self):
        """测试不支持功能的警告"""
        request = OptimizedTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="Test")],
            model="claude-3-sonnet",
            functions=[{"name": "test_func"}],  # Anthropic不支持
            response_format={"type": "json_object"}  # Anthropic不支持
        )
        
        validation = request.validate_for_provider("anthropic")
        
        self.assertTrue(len(validation["warnings"]) >= 2)
        self.assertTrue(any("functions" in warning for warning in validation["warnings"]))
        self.assertTrue(any("response_format" in warning for warning in validation["warnings"]))
    
    def test_token_limit_warning(self):
        """测试token限制警告"""
        request = OptimizedTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="Test")],
            model="gemini-2.5-flash",
            generation=GenerationConfig(max_tokens=30000)  # 在验证范围内但超过Google限制
        )

        validation = request.validate_for_provider("google")

        self.assertTrue(any("超过供应商限制" in warning for warning in validation["warnings"]))


class TestSimpleRequestEnhancement(unittest.TestCase):
    """测试简化版本的增强功能"""
    
    def test_simple_to_optimized_conversion(self):
        """测试简化版本到优化版本的转换"""
        simple = SimpleTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="Test")],
            model="gpt-4",
            temperature=0.8,
            max_tokens=1000,
            stream=True
        )
        
        optimized = simple.to_optimized()
        
        self.assertIsInstance(optimized, OptimizedTextGenerationRequest)
        self.assertEqual(optimized.generation.temperature, 0.8)
        self.assertEqual(optimized.generation.max_tokens, 1000)
        self.assertTrue(optimized.stream.enabled)
    
    def test_simple_provider_validation(self):
        """测试简化版本的供应商验证"""
        simple = SimpleTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="Test")],
            model="claude-3-sonnet",
            temperature=0.7
            # 没有设置max_tokens
        )
        
        validation = simple.validate_for_provider("anthropic")
        
        # 应该检测到max_tokens缺失
        self.assertTrue(any("max_tokens" in error for error in validation["errors"]))
    
    def test_simple_provider_params(self):
        """测试简化版本的供应商参数获取"""
        simple = SimpleTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="Test")],
            model="gpt-4",
            temperature=0.7,
            max_tokens=1000
        )
        
        params = simple.get_provider_params("openai")
        
        self.assertEqual(params["model"], "gpt-4")
        self.assertEqual(params["temperature"], 0.7)
        self.assertEqual(params["max_tokens"], 1000)


class TestEdgeCases(unittest.TestCase):
    """测试边界情况"""
    
    def test_empty_messages_validation(self):
        """测试空消息列表的验证"""
        with self.assertRaises(Exception):
            OptimizedTextGenerationRequest(
                messages=[],
                model="gpt-4"
            )
    
    def test_invalid_provider_name(self):
        """测试无效的供应商名称"""
        request = OptimizedTextGenerationRequest(
            messages=[Message(role=MessageRole.USER, content="Test")],
            model="gpt-4"
        )
        
        with self.assertRaises(ValueError):
            request.get_provider_specific_params("invalid_provider")
    
    def test_provider_capabilities_unknown_provider(self):
        """测试未知供应商的能力查询"""
        capabilities = OptimizedTextGenerationRequest.get_provider_capabilities("unknown")
        
        self.assertEqual(capabilities, {})


def run_all_tests():
    """运行所有测试"""
    print("🧪 开始运行优化版本功能测试套件\n")
    
    # 创建测试套件
    test_classes = [
        TestLegacyMigration,
        TestProviderCompatibility,
        TestProviderValidation,
        TestSimpleRequestEnhancement,
        TestEdgeCases
    ]
    
    total_tests = 0
    passed_tests = 0
    
    for test_class in test_classes:
        print(f"--- 运行 {test_class.__name__} ---")
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=0, stream=open('/dev/null', 'w'))
        result = runner.run(suite)
        
        class_total = result.testsRun
        class_passed = class_total - len(result.failures) - len(result.errors)
        
        total_tests += class_total
        passed_tests += class_passed
        
        print(f"   ✅ 通过: {class_passed}/{class_total}")
        
        if result.failures:
            print(f"   ❌ 失败: {len(result.failures)}")
            for test, traceback in result.failures:
                print(f"      - {test}: {traceback.split('AssertionError: ')[-1].strip()}")
        
        if result.errors:
            print(f"   💥 错误: {len(result.errors)}")
            for test, traceback in result.errors:
                print(f"      - {test}: {traceback.split('Exception: ')[-1].strip()}")
    
    print(f"\n📊 测试总结: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查实现")


if __name__ == "__main__":
    run_all_tests()
