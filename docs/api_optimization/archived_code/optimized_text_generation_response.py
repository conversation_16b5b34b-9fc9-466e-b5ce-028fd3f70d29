"""
优化后的文本生成响应结构设计

主要改进：
1. 更清晰的响应结构层次
2. 标准化的错误处理格式
3. 增强的元数据信息
4. 更好的流式响应设计
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field


class FinishReason(str, Enum):
    """完成原因枚举"""
    STOP = "stop"                    # 自然停止
    LENGTH = "length"                # 达到最大长度
    CONTENT_FILTER = "content_filter"  # 内容过滤
    FUNCTION_CALL = "function_call"   # 函数调用
    TOOL_CALLS = "tool_calls"        # 工具调用
    ERROR = "error"                  # 发生错误


class UsageStats(BaseModel):
    """使用量统计"""
    prompt_tokens: int = Field(0, description="输入token数量")
    completion_tokens: int = Field(0, description="输出token数量")
    total_tokens: int = Field(0, description="总token数量")
    
    # 扩展统计信息
    thinking_tokens: Optional[int] = Field(None, description="思考过程token数量（某些模型支持）")
    cached_tokens: Optional[int] = Field(None, description="缓存命中的token数量")
    
    # 成本信息
    estimated_cost: Optional[float] = Field(None, description="预估成本（美元）")
    cost_breakdown: Optional[Dict[str, float]] = Field(None, description="成本明细")


class PerformanceMetrics(BaseModel):
    """性能指标"""
    processing_time: float = Field(..., description="总处理时间（秒）")
    queue_time: Optional[float] = Field(None, description="队列等待时间（秒）")
    inference_time: Optional[float] = Field(None, description="推理时间（秒）")
    
    # 流式输出相关指标
    first_token_time: Optional[float] = Field(None, description="首个token生成时间（秒）")
    tokens_per_second: Optional[float] = Field(None, description="生成速度（token/秒）")
    
    # 供应商相关指标
    provider_latency: Optional[float] = Field(None, description="供应商API延迟（秒）")
    retry_count: Optional[int] = Field(None, description="重试次数")


class ProviderInfo(BaseModel):
    """供应商信息"""
    name: str = Field(..., description="供应商名称")
    model: str = Field(..., description="实际使用的模型")
    version: Optional[str] = Field(None, description="模型版本")
    region: Optional[str] = Field(None, description="服务区域")
    endpoint: Optional[str] = Field(None, description="API端点")


class Choice(BaseModel):
    """生成选择"""
    index: int = Field(..., description="选择索引")
    message: Dict[str, Any] = Field(..., description="生成的消息")
    finish_reason: Optional[FinishReason] = Field(None, description="完成原因")
    
    # 扩展信息
    logprobs: Optional[Dict[str, Any]] = Field(None, description="对数概率信息")
    safety_scores: Optional[Dict[str, float]] = Field(None, description="安全评分")


class ErrorInfo(BaseModel):
    """错误信息"""
    error_code: str = Field(..., description="错误代码")
    message: str = Field(..., description="错误消息")
    details: Optional[str] = Field(None, description="详细错误信息")
    retryable: bool = Field(False, description="是否可重试")
    retry_after: Optional[int] = Field(None, description="建议重试间隔（秒）")


class OptimizedTextGenerationResponse(BaseModel):
    """优化后的文本生成响应模型
    
    主要改进：
    1. 结构化的响应组织
    2. 丰富的元数据信息
    3. 标准化的错误处理
    4. 更好的性能监控支持
    """
    
    # === 基础响应信息 ===
    id: str = Field(..., description="响应唯一标识")
    object: str = Field("chat.completion", description="对象类型")
    created: int = Field(..., description="创建时间戳")
    
    # === 核心响应数据 ===
    choices: List[Choice] = Field(..., description="生成选择列表")
    
    # === 使用量和性能信息 ===
    usage: Optional[UsageStats] = Field(None, description="使用量统计")
    performance: PerformanceMetrics = Field(..., description="性能指标")
    
    # === 供应商信息 ===
    provider: ProviderInfo = Field(..., description="供应商信息")
    
    # === 请求追踪信息 ===
    request_id: str = Field(..., description="请求ID")
    trace_id: Optional[str] = Field(None, description="链路追踪ID")
    
    # === 错误信息（可选）===
    error: Optional[ErrorInfo] = Field(None, description="错误信息")
    
    # === 元数据 ===
    metadata: Optional[Dict[str, Any]] = Field(None, description="响应元数据")
    
    @property
    def is_success(self) -> bool:
        """判断响应是否成功"""
        return self.error is None and len(self.choices) > 0
    
    @property
    def content(self) -> Optional[str]:
        """获取第一个选择的内容"""
        if self.choices and len(self.choices) > 0:
            message = self.choices[0].message
            return message.get("content")
        return None


class StreamChunk(BaseModel):
    """流式响应块"""
    id: str = Field(..., description="响应ID")
    object: str = Field("chat.completion.chunk", description="对象类型")
    created: int = Field(..., description="创建时间戳")
    
    # === 流式数据 ===
    choices: List[Dict[str, Any]] = Field(..., description="流式选择数据")
    
    # === 供应商信息 ===
    provider: ProviderInfo = Field(..., description="供应商信息")
    
    # === 请求追踪 ===
    request_id: str = Field(..., description="请求ID")
    
    # === 流式特有字段 ===
    chunk_index: int = Field(..., description="块索引")
    is_final: bool = Field(False, description="是否为最后一块")
    
    # === 使用量信息（仅在最后一块中包含）===
    usage: Optional[UsageStats] = Field(None, description="使用量统计")
    performance: Optional[PerformanceMetrics] = Field(None, description="性能指标")


class StreamEvent(BaseModel):
    """流式事件包装器"""
    event: str = Field(..., description="事件类型：chunk, error, done")
    data: Union[StreamChunk, ErrorInfo, str] = Field(..., description="事件数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="事件时间戳")


# === 错误响应模型 ===
class ErrorResponse(BaseModel):
    """标准化错误响应"""
    error: ErrorInfo = Field(..., description="错误信息")
    request_id: str = Field(..., description="请求ID")
    timestamp: int = Field(..., description="错误发生时间戳")
    
    # === 调试信息（仅开发环境）===
    debug_info: Optional[Dict[str, Any]] = Field(None, description="调试信息")


# === 批量响应模型 ===
class BatchTextGenerationResponse(BaseModel):
    """批量文本生成响应"""
    id: str = Field(..., description="批量请求ID")
    object: str = Field("batch.completion", description="对象类型")
    created: int = Field(..., description="创建时间戳")
    
    # === 批量结果 ===
    responses: List[Union[OptimizedTextGenerationResponse, ErrorResponse]] = Field(
        ..., description="批量响应结果"
    )
    
    # === 批量统计 ===
    total_requests: int = Field(..., description="总请求数")
    successful_requests: int = Field(..., description="成功请求数")
    failed_requests: int = Field(..., description="失败请求数")
    
    # === 批量性能 ===
    total_processing_time: float = Field(..., description="总处理时间")
    average_processing_time: float = Field(..., description="平均处理时间")


# === 响应构建器 ===
class ResponseBuilder:
    """响应构建器，简化响应对象的创建"""
    
    @staticmethod
    def success_response(
        request_id: str,
        choices: List[Choice],
        provider_info: ProviderInfo,
        performance: PerformanceMetrics,
        usage: Optional[UsageStats] = None
    ) -> OptimizedTextGenerationResponse:
        """构建成功响应"""
        import time
        
        return OptimizedTextGenerationResponse(
            id=f"chatcmpl-{request_id}",
            created=int(time.time()),
            choices=choices,
            usage=usage,
            performance=performance,
            provider=provider_info,
            request_id=request_id
        )
    
    @staticmethod
    def error_response(
        request_id: str,
        error_code: str,
        message: str,
        details: Optional[str] = None,
        retryable: bool = False
    ) -> ErrorResponse:
        """构建错误响应"""
        import time
        
        return ErrorResponse(
            error=ErrorInfo(
                error_code=error_code,
                message=message,
                details=details,
                retryable=retryable
            ),
            request_id=request_id,
            timestamp=int(time.time())
        )
    
    @staticmethod
    def stream_chunk(
        request_id: str,
        chunk_index: int,
        choices: List[Dict[str, Any]],
        provider_info: ProviderInfo,
        is_final: bool = False,
        usage: Optional[UsageStats] = None,
        performance: Optional[PerformanceMetrics] = None
    ) -> StreamChunk:
        """构建流式响应块"""
        import time
        
        return StreamChunk(
            id=f"chatcmpl-{request_id}",
            created=int(time.time()),
            choices=choices,
            provider=provider_info,
            request_id=request_id,
            chunk_index=chunk_index,
            is_final=is_final,
            usage=usage if is_final else None,
            performance=performance if is_final else None
        )
