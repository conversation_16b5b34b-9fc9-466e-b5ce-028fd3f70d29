"""
流式输出测试方案

包含：
1. 单元测试用例
2. 集成测试用例
3. 性能测试用例
4. 客户端测试示例
"""

import asyncio
import json
import time
from typing import AsyncIterator, List
import pytest
import httpx
from fastapi.testclient import TestClient
from sse_starlette.sse import EventSourceResponse


class StreamingTestSuite:
    """流式输出测试套件"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(base_url=base_url)
    
    async def test_basic_streaming(self):
        """基础流式输出测试"""
        request_data = {
            "model": "gpt-4",
            "messages": [
                {"role": "user", "content": "请写一首短诗"}
            ],
            "stream": {"enabled": True},
            "generation": {"max_tokens": 100}
        }
        
        chunks = []
        async with self.client.stream(
            "POST", 
            "/api/v1/text/generate",
            json=request_data,
            headers={"Accept": "text/event-stream"}
        ) as response:
            assert response.status_code == 200
            assert response.headers["content-type"] == "text/event-stream"
            
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    data = line[6:]  # 移除 "data: " 前缀
                    if data == "[DONE]":
                        break
                    
                    try:
                        chunk = json.loads(data)
                        chunks.append(chunk)
                    except json.JSONDecodeError:
                        continue
        
        # 验证流式响应
        assert len(chunks) > 0, "应该收到至少一个数据块"
        
        # 验证第一个块的结构
        first_chunk = chunks[0]
        assert "id" in first_chunk
        assert "object" in first_chunk
        assert first_chunk["object"] == "chat.completion.chunk"
        assert "choices" in first_chunk
        assert len(first_chunk["choices"]) > 0
        
        # 验证最后一个块包含完成信息
        last_chunk = chunks[-1]
        if last_chunk.get("is_final"):
            assert "usage" in last_chunk
            assert "performance" in last_chunk
        
        return chunks
    
    async def test_streaming_error_handling(self):
        """流式输出错误处理测试"""
        # 测试无效模型
        request_data = {
            "model": "invalid-model",
            "messages": [{"role": "user", "content": "test"}],
            "stream": {"enabled": True}
        }
        
        async with self.client.stream(
            "POST",
            "/api/v1/text/generate", 
            json=request_data
        ) as response:
            # 应该返回错误状态码或错误事件
            if response.status_code != 200:
                assert response.status_code in [400, 404, 500]
            else:
                # 检查是否收到错误事件
                async for line in response.aiter_lines():
                    if line.startswith("event: error"):
                        break
    
    async def test_streaming_interruption(self):
        """流式输出中断测试"""
        request_data = {
            "model": "gpt-4",
            "messages": [
                {"role": "user", "content": "请写一篇长文章"}
            ],
            "stream": {"enabled": True},
            "generation": {"max_tokens": 1000}
        }
        
        chunks_received = 0
        async with self.client.stream(
            "POST",
            "/api/v1/text/generate",
            json=request_data
        ) as response:
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    chunks_received += 1
                    # 模拟客户端中断
                    if chunks_received >= 3:
                        break
        
        assert chunks_received >= 3, "应该能够正常中断流式输出"
    
    async def test_streaming_performance(self):
        """流式输出性能测试"""
        request_data = {
            "model": "gpt-4",
            "messages": [
                {"role": "user", "content": "请计数从1到50"}
            ],
            "stream": {"enabled": True},
            "generation": {"max_tokens": 200}
        }
        
        start_time = time.time()
        first_chunk_time = None
        chunks = []
        
        async with self.client.stream(
            "POST",
            "/api/v1/text/generate",
            json=request_data
        ) as response:
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    if first_chunk_time is None:
                        first_chunk_time = time.time()
                    
                    data = line[6:]
                    if data == "[DONE]":
                        break
                    
                    try:
                        chunk = json.loads(data)
                        chunks.append(chunk)
                    except json.JSONDecodeError:
                        continue
        
        end_time = time.time()
        
        # 性能指标验证
        total_time = end_time - start_time
        first_token_latency = first_chunk_time - start_time if first_chunk_time else None
        
        assert total_time < 30, f"总响应时间过长: {total_time}秒"
        if first_token_latency:
            assert first_token_latency < 5, f"首个token延迟过高: {first_token_latency}秒"
        
        # 计算吞吐量
        if len(chunks) > 1:
            throughput = len(chunks) / total_time
            assert throughput > 0.5, f"吞吐量过低: {throughput} chunks/秒"
        
        return {
            "total_time": total_time,
            "first_token_latency": first_token_latency,
            "chunks_count": len(chunks),
            "throughput": len(chunks) / total_time if total_time > 0 else 0
        }


# === 单元测试用例 ===
@pytest.mark.asyncio
class TestStreamingUnit:
    """流式输出单元测试"""
    
    async def test_stream_chunk_creation(self):
        """测试流式响应块创建"""
        from docs.api_optimization.optimized_text_generation_response import (
            ResponseBuilder, ProviderInfo
        )
        
        provider_info = ProviderInfo(
            name="openai",
            model="gpt-4",
            version="2024-01-01"
        )
        
        chunk = ResponseBuilder.stream_chunk(
            request_id="test-123",
            chunk_index=0,
            choices=[{
                "index": 0,
                "delta": {"content": "Hello"},
                "finish_reason": None
            }],
            provider_info=provider_info
        )
        
        assert chunk.request_id == "test-123"
        assert chunk.chunk_index == 0
        assert len(chunk.choices) == 1
        assert chunk.choices[0]["delta"]["content"] == "Hello"
    
    async def test_stream_event_serialization(self):
        """测试流式事件序列化"""
        from docs.api_optimization.optimized_text_generation_response import (
            StreamEvent, StreamChunk, ProviderInfo
        )
        
        provider_info = ProviderInfo(name="test", model="test-model")
        
        chunk = StreamChunk(
            id="test-id",
            created=**********,
            choices=[{"index": 0, "delta": {"content": "test"}}],
            provider=provider_info,
            request_id="test-req",
            chunk_index=0
        )
        
        event = StreamEvent(event="chunk", data=chunk)
        
        # 测试JSON序列化
        json_str = event.json()
        assert "chunk" in json_str
        assert "test" in json_str


# === 集成测试用例 ===
@pytest.mark.integration
class TestStreamingIntegration:
    """流式输出集成测试"""
    
    def test_streaming_with_test_client(self, test_client: TestClient):
        """使用TestClient测试流式输出"""
        request_data = {
            "model": "test-model",
            "messages": [{"role": "user", "content": "test"}],
            "stream": True
        }
        
        with test_client.stream(
            "POST",
            "/api/v1/text/generate",
            json=request_data
        ) as response:
            assert response.status_code == 200
            
            # 读取流式响应
            content = b""
            for chunk in response.iter_content():
                content += chunk
            
            # 验证SSE格式
            content_str = content.decode()
            assert "data: " in content_str
            assert "[DONE]" in content_str
    
    @pytest.mark.asyncio
    async def test_websocket_streaming(self):
        """WebSocket流式输出测试"""
        import websockets
        
        uri = "ws://localhost:8000/ws/text/generate"
        
        async with websockets.connect(uri) as websocket:
            # 发送生成请求
            request = {
                "type": "generate",
                "request_id": "test-ws-123",
                "data": {
                    "model": "gpt-4",
                    "messages": [{"role": "user", "content": "Hello"}],
                    "stream": True
                }
            }
            
            await websocket.send(json.dumps(request))
            
            # 接收响应
            chunks = []
            while True:
                try:
                    message = await asyncio.wait_for(
                        websocket.recv(), 
                        timeout=10
                    )
                    data = json.loads(message)
                    
                    if data["type"] == "generation_chunk":
                        chunks.append(data)
                    elif data["type"] == "generation_complete":
                        break
                    elif data["type"] == "error":
                        pytest.fail(f"WebSocket错误: {data}")
                        
                except asyncio.TimeoutError:
                    pytest.fail("WebSocket响应超时")
            
            assert len(chunks) > 0, "应该收到至少一个数据块"


# === 客户端测试示例 ===
class StreamingClient:
    """流式输出客户端示例"""
    
    def __init__(self, base_url: str, api_key: str = None):
        self.base_url = base_url
        self.api_key = api_key
    
    async def generate_stream(
        self, 
        messages: List[dict], 
        model: str = "gpt-4",
        **kwargs
    ) -> AsyncIterator[dict]:
        """生成流式响应"""
        headers = {"Accept": "text/event-stream"}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        
        request_data = {
            "model": model,
            "messages": messages,
            "stream": {"enabled": True},
            **kwargs
        }
        
        async with httpx.AsyncClient() as client:
            async with client.stream(
                "POST",
                f"{self.base_url}/api/v1/text/generate",
                json=request_data,
                headers=headers
            ) as response:
                if response.status_code != 200:
                    raise Exception(f"API错误: {response.status_code}")
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]
                        if data == "[DONE]":
                            break
                        
                        try:
                            yield json.loads(data)
                        except json.JSONDecodeError:
                            continue
    
    async def example_usage(self):
        """使用示例"""
        messages = [
            {"role": "user", "content": "请写一首关于春天的诗"}
        ]
        
        print("开始生成...")
        full_content = ""
        
        async for chunk in self.generate_stream(messages):
            if chunk.get("choices"):
                delta = chunk["choices"][0].get("delta", {})
                content = delta.get("content", "")
                if content:
                    print(content, end="", flush=True)
                    full_content += content
        
        print(f"\n\n生成完成，总长度: {len(full_content)} 字符")
        return full_content


# === 性能基准测试 ===
class StreamingBenchmark:
    """流式输出性能基准测试"""
    
    async def benchmark_latency(self, num_requests: int = 10):
        """延迟基准测试"""
        client = StreamingClient("http://localhost:8000")
        
        latencies = []
        for i in range(num_requests):
            start_time = time.time()
            
            messages = [{"role": "user", "content": f"测试请求 {i+1}"}]
            
            first_chunk_time = None
            async for chunk in client.generate_stream(messages):
                if first_chunk_time is None:
                    first_chunk_time = time.time()
                    break
            
            if first_chunk_time:
                latency = first_chunk_time - start_time
                latencies.append(latency)
        
        avg_latency = sum(latencies) / len(latencies)
        max_latency = max(latencies)
        min_latency = min(latencies)
        
        return {
            "average_latency": avg_latency,
            "max_latency": max_latency,
            "min_latency": min_latency,
            "latencies": latencies
        }
    
    async def benchmark_throughput(self, duration: int = 60):
        """吞吐量基准测试"""
        client = StreamingClient("http://localhost:8000")
        
        start_time = time.time()
        total_chunks = 0
        total_requests = 0
        
        while time.time() - start_time < duration:
            messages = [{"role": "user", "content": "简短回复测试"}]
            
            chunks_count = 0
            async for chunk in client.generate_stream(messages):
                chunks_count += 1
            
            total_chunks += chunks_count
            total_requests += 1
        
        actual_duration = time.time() - start_time
        
        return {
            "duration": actual_duration,
            "total_requests": total_requests,
            "total_chunks": total_chunks,
            "requests_per_second": total_requests / actual_duration,
            "chunks_per_second": total_chunks / actual_duration
        }


# === 运行测试的主函数 ===
async def run_streaming_tests():
    """运行所有流式输出测试"""
    print("🚀 开始流式输出测试...")

    # 基础功能测试
    suite = StreamingTestSuite()

    print("\n1. 基础流式输出测试...")
    try:
        chunks = await suite.test_basic_streaming()
        print(f"✅ 基础测试通过，收到 {len(chunks)} 个数据块")
    except Exception as e:
        print(f"❌ 基础测试失败: {e}")

    print("\n2. 错误处理测试...")
    try:
        await suite.test_streaming_error_handling()
        print("✅ 错误处理测试通过")
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")

    print("\n3. 性能测试...")
    try:
        metrics = await suite.test_streaming_performance()
        print(f"✅ 性能测试通过")
        print(f"   - 总时间: {metrics['total_time']:.2f}秒")
        print(f"   - 首token延迟: {metrics['first_token_latency']:.2f}秒")
        print(f"   - 吞吐量: {metrics['throughput']:.2f} chunks/秒")
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")

    print("\n🎯 流式输出测试完成")


if __name__ == "__main__":
    asyncio.run(run_streaming_tests())
