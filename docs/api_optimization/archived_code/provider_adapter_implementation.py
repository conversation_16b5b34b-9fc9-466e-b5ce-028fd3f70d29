"""
供应商适配器实现示例

本文件展示了如何在现有的供应商实现中集成优化版本的请求处理，
提供了渐进式迁移的具体实现方案。
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union

from optimized_text_generation_request import (
    OptimizedTextGenerationRequest,
    SimpleTextGenerationRequest
)

logger = logging.getLogger(__name__)


class EnhancedProviderAdapter(ABC):
    """增强的供应商适配器基类
    
    这个基类展示了如何在现有的供应商实现中集成优化版本的请求处理，
    同时保持对传统格式的兼容性。
    """
    
    def __init__(self, provider_name: str):
        self.provider_name = provider_name
        self.logger = logging.getLogger(f"{__name__}.{provider_name}")
    
    def process_request(self, request: Union[Dict[str, Any], OptimizedTextGenerationRequest, SimpleTextGenerationRequest]) -> Dict[str, Any]:
        """处理请求的统一入口
        
        这个方法展示了如何在一个接口中同时支持三种不同的请求格式：
        1. 传统的字典格式
        2. 优化版本的OptimizedTextGenerationRequest
        3. 简化版本的SimpleTextGenerationRequest
        
        Args:
            request: 任意格式的请求
            
        Returns:
            Dict[str, Any]: 供应商特定的API参数
        """
        self.logger.info(f"开始处理 {type(request).__name__} 类型的请求")
        
        # 统一转换为OptimizedTextGenerationRequest
        if isinstance(request, dict):
            # 传统字典格式
            optimized_request = OptimizedTextGenerationRequest.from_legacy_request(request)
            self.logger.info("已将传统字典格式转换为优化版本")
        elif isinstance(request, SimpleTextGenerationRequest):
            # 简化版本
            optimized_request = request.to_optimized()
            self.logger.info("已将简化版本转换为优化版本")
        elif isinstance(request, OptimizedTextGenerationRequest):
            # 已经是优化版本
            optimized_request = request
            self.logger.info("请求已经是优化版本格式")
        else:
            raise ValueError(f"不支持的请求类型: {type(request)}")
        
        # 执行兼容性验证
        validation_result = optimized_request.validate_for_provider(self.provider_name)
        
        # 记录验证结果
        if validation_result["errors"]:
            error_msg = f"请求与 {self.provider_name} 不兼容: {validation_result['errors']}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        
        if validation_result["warnings"]:
            for warning in validation_result["warnings"]:
                self.logger.warning(f"兼容性警告: {warning}")
        
        if validation_result["info"]:
            for info in validation_result["info"]:
                self.logger.info(f"优化建议: {info}")
        
        # 获取供应商特定参数
        provider_params = optimized_request.get_provider_specific_params(self.provider_name)
        
        # 执行供应商特定的后处理
        final_params = self.post_process_params(provider_params, optimized_request)
        
        self.logger.info(f"请求处理完成，生成了 {len(final_params)} 个参数")
        return final_params
    
    @abstractmethod
    def post_process_params(self, params: Dict[str, Any], original_request: OptimizedTextGenerationRequest) -> Dict[str, Any]:
        """供应商特定的参数后处理
        
        子类可以重写这个方法来实现供应商特有的参数调整逻辑。
        """
        pass


class OpenAIAdapter(EnhancedProviderAdapter):
    """OpenAI供应商适配器实现"""
    
    def __init__(self):
        super().__init__("openai")
    
    def post_process_params(self, params: Dict[str, Any], original_request: OptimizedTextGenerationRequest) -> Dict[str, Any]:
        """OpenAI特定的参数后处理"""
        # OpenAI通常不需要特殊处理，但可以添加一些优化
        
        # 确保stream_options正确设置（OpenAI新特性）
        if params.get("stream", False):
            params["stream_options"] = {"include_usage": True}
        
        # 添加请求ID用于追踪
        if "metadata" not in params:
            params["metadata"] = {}
        params["metadata"]["request_source"] = "ai_gen_hub_optimized"
        
        self.logger.info("OpenAI参数后处理完成")
        return params


class AnthropicAdapter(EnhancedProviderAdapter):
    """Anthropic供应商适配器实现"""
    
    def __init__(self):
        super().__init__("anthropic")
    
    def post_process_params(self, params: Dict[str, Any], original_request: OptimizedTextGenerationRequest) -> Dict[str, Any]:
        """Anthropic特定的参数后处理"""
        
        # 确保max_tokens不为空（Anthropic要求）
        if "max_tokens" not in params or params["max_tokens"] is None:
            params["max_tokens"] = 4096
            self.logger.warning("为Anthropic自动设置max_tokens=4096")
        
        # 限制max_tokens在合理范围内
        if params["max_tokens"] > 200000:
            params["max_tokens"] = 200000
            self.logger.warning("max_tokens超出Anthropic限制，已调整为200000")
        
        # 移除不支持的参数
        unsupported_params = ["functions", "tools", "response_format"]
        for param in unsupported_params:
            if param in params:
                del params[param]
                self.logger.info(f"移除Anthropic不支持的参数: {param}")
        
        self.logger.info("Anthropic参数后处理完成")
        return params


class GoogleAIAdapter(EnhancedProviderAdapter):
    """Google AI供应商适配器实现"""
    
    def __init__(self):
        super().__init__("google")
    
    def post_process_params(self, params: Dict[str, Any], original_request: OptimizedTextGenerationRequest) -> Dict[str, Any]:
        """Google AI特定的参数后处理"""
        
        # 优化thinking配置
        if "thinkingConfig" in params:
            thinking_config = params["thinkingConfig"]
            # 确保thinking budget在合理范围内
            if thinking_config.get("thinkingBudget", 0) > 10000:
                thinking_config["thinkingBudget"] = 10000
                self.logger.warning("thinking budget超出建议值，已调整为10000")
        
        # 优化安全设置
        if "safetySettings" in params:
            # 确保安全设置完整
            required_categories = [
                "HARM_CATEGORY_HARASSMENT",
                "HARM_CATEGORY_HATE_SPEECH",
                "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                "HARM_CATEGORY_DANGEROUS_CONTENT"
            ]
            
            existing_categories = {setting["category"] for setting in params["safetySettings"]}
            for category in required_categories:
                if category not in existing_categories:
                    params["safetySettings"].append({
                        "category": category,
                        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                    })
        
        # 优化生成配置
        if "generationConfig" in params:
            gen_config = params["generationConfig"]
            
            # 确保maxOutputTokens在限制范围内
            if gen_config.get("maxOutputTokens", 0) > 8192:
                gen_config["maxOutputTokens"] = 8192
                self.logger.warning("maxOutputTokens超出Google限制，已调整为8192")
        
        self.logger.info("Google AI参数后处理完成")
        return params


class DashScopeAdapter(EnhancedProviderAdapter):
    """DashScope供应商适配器实现"""
    
    def __init__(self):
        super().__init__("dashscope")
    
    def post_process_params(self, params: Dict[str, Any], original_request: OptimizedTextGenerationRequest) -> Dict[str, Any]:
        """DashScope特定的参数后处理"""
        
        # 确保parameters结构正确
        if "parameters" in params:
            parameters = params["parameters"]
            
            # 设置DashScope特有的参数
            parameters["result_format"] = "message"
            
            # 优化流式输出设置
            if parameters.get("incremental_output", False):
                # DashScope的流式输出优化
                parameters["enable_search"] = False  # 流式时禁用搜索以提高速度
        
        # 确保模型名称映射正确
        model_mapping = {
            "gpt-4": "qwen-max",
            "gpt-3.5-turbo": "qwen-turbo",
            "claude-3-sonnet": "qwen-plus"
        }
        
        current_model = params.get("model", "")
        if current_model in model_mapping:
            params["model"] = model_mapping[current_model]
            self.logger.info(f"模型名称映射: {current_model} -> {params['model']}")
        
        self.logger.info("DashScope参数后处理完成")
        return params


def create_adapter(provider_name: str) -> EnhancedProviderAdapter:
    """工厂方法：创建对应的供应商适配器
    
    Args:
        provider_name: 供应商名称
        
    Returns:
        EnhancedProviderAdapter: 对应的适配器实例
    """
    adapters = {
        "openai": OpenAIAdapter,
        "anthropic": AnthropicAdapter,
        "google": GoogleAIAdapter,
        "dashscope": DashScopeAdapter
    }
    
    adapter_class = adapters.get(provider_name.lower())
    if not adapter_class:
        raise ValueError(f"不支持的供应商: {provider_name}")
    
    return adapter_class()


# 使用示例
def example_adapter_usage():
    """展示适配器的使用方法"""
    print("=== 供应商适配器使用示例 ===")
    
    # 创建不同格式的请求
    legacy_request = {
        "messages": [{"role": "user", "content": "Hello"}],
        "model": "gpt-4",
        "temperature": 0.7,
        "max_tokens": 1000
    }
    
    # 测试不同供应商的适配
    for provider_name in ["openai", "anthropic", "google", "dashscope"]:
        try:
            adapter = create_adapter(provider_name)
            result = adapter.process_request(legacy_request)
            print(f"✅ {provider_name}: 成功处理请求，生成 {len(result)} 个参数")
        except Exception as e:
            print(f"❌ {provider_name}: 处理失败 - {e}")


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    example_adapter_usage()
