"""
优化后的文本生成请求参数设计

主要改进：
1. 更清晰的参数分组和层次结构
2. 更合理的默认值设置
3. 增强的参数验证
4. 更好的文档说明
5. 渐进式迁移支持和供应商兼容性增强
"""

import logging
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, validator

# 设置日志记录器
logger = logging.getLogger(__name__)


class MessageRole(str, Enum):
    """消息角色枚举"""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    FUNCTION = "function"
    TOOL = "tool"


class Message(BaseModel):
    """聊天消息模型"""
    role: MessageRole = Field(..., description="消息角色")
    content: str = Field(..., min_length=1, description="消息内容，不能为空")
    name: Optional[str] = Field(None, description="消息发送者名称")
    function_call: Optional[Dict[str, Any]] = Field(None, description="函数调用信息")
    tool_calls: Optional[List[Dict[str, Any]]] = Field(None, description="工具调用信息")


class GenerationConfig(BaseModel):
    """生成配置参数组"""
    max_tokens: Optional[int] = Field(
        None, 
        ge=1, 
        le=32768, 
        description="最大生成token数，不设置则使用模型默认值"
    )
    temperature: float = Field(
        0.7, 
        ge=0.0, 
        le=2.0, 
        description="生成温度，控制随机性。0.0为确定性输出，2.0为最大随机性"
    )
    top_p: Optional[float] = Field(
        None, 
        ge=0.0, 
        le=1.0, 
        description="核采样参数，与temperature互斥使用"
    )
    top_k: Optional[int] = Field(
        None, 
        ge=1, 
        le=100, 
        description="Top-K采样参数，限制候选token数量"
    )
    frequency_penalty: float = Field(
        0.0, 
        ge=-2.0, 
        le=2.0, 
        description="频率惩罚，减少重复内容"
    )
    presence_penalty: float = Field(
        0.0, 
        ge=-2.0, 
        le=2.0, 
        description="存在惩罚，鼓励话题多样性"
    )
    stop: Optional[Union[str, List[str]]] = Field(
        None, 
        description="停止序列，遇到时停止生成"
    )

    @validator('stop')
    def validate_stop_sequences(cls, v):
        """验证停止序列"""
        if v is None:
            return v
        if isinstance(v, str):
            return [v] if v.strip() else None
        if isinstance(v, list):
            # 过滤空字符串并限制数量
            filtered = [s for s in v if isinstance(s, str) and s.strip()]
            if len(filtered) > 10:
                raise ValueError("停止序列数量不能超过10个")
            return filtered if filtered else None
        raise ValueError("停止序列必须是字符串或字符串列表")


class StreamConfig(BaseModel):
    """流式输出配置"""
    enabled: bool = Field(False, description="是否启用流式输出")
    chunk_size: Optional[int] = Field(
        None,
        ge=0,
        le=1000,
        description="流式输出块大小（字符数），0表示使用默认块大小"
    )
    include_usage: bool = Field(
        True, 
        description="是否在流式输出中包含使用量统计"
    )


class SafetyConfig(BaseModel):
    """安全配置参数"""
    content_filter: bool = Field(True, description="是否启用内容过滤")
    safety_level: str = Field(
        "medium",
        pattern="^(low|medium|high|strict)$",
        description="安全级别：low, medium, high, strict"
    )
    custom_filters: Optional[List[str]] = Field(
        None, 
        description="自定义过滤规则"
    )


class OptimizedTextGenerationRequest(BaseModel):
    """优化后的文本生成请求模型
    
    主要改进：
    1. 参数分组：将相关参数组织到子配置中
    2. 更好的默认值：基于最佳实践设置合理默认值
    3. 增强验证：添加更严格的参数验证
    4. 清晰文档：提供详细的参数说明
    """
    
    # === 核心必需参数 ===
    messages: List[Message] = Field(
        ..., 
        min_items=1, 
        description="对话消息列表，至少包含一条消息"
    )
    model: str = Field(
        ..., 
        min_length=1, 
        description="使用的模型名称，如 'gpt-4', 'claude-3-sonnet'"
    )
    
    # === 生成配置 ===
    generation: GenerationConfig = Field(
        default_factory=GenerationConfig,
        description="文本生成配置参数"
    )
    
    # === 流式配置 ===
    stream: StreamConfig = Field(
        default_factory=StreamConfig,
        description="流式输出配置"
    )
    
    # === 安全配置 ===
    safety: Optional[SafetyConfig] = Field(
        None,
        description="安全配置，不设置则使用默认安全策略"
    )
    
    # === 高级功能参数 ===
    functions: Optional[List[Dict[str, Any]]] = Field(
        None, 
        description="可用函数列表，用于函数调用功能"
    )
    function_call: Optional[Union[str, Dict[str, str]]] = Field(
        None, 
        description="函数调用设置：'auto', 'none', 或指定函数"
    )
    tools: Optional[List[Dict[str, Any]]] = Field(
        None, 
        description="可用工具列表"
    )
    tool_choice: Optional[Union[str, Dict[str, Any]]] = Field(
        None, 
        description="工具选择设置"
    )
    
    # === 结构化输出参数 ===
    response_format: Optional[Dict[str, Any]] = Field(
        None, 
        description="响应格式配置，支持JSON Schema约束"
    )
    response_schema: Optional[Dict[str, Any]] = Field(
        None, 
        description="JSON Schema约束"
    )
    
    # === 元数据参数 ===
    user: Optional[str] = Field(
        None, 
        description="用户标识，用于追踪和分析"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None, 
        description="请求元数据，用于日志和分析"
    )
    
    # === 供应商特定参数 ===
    provider_params: Dict[str, Any] = Field(
        default_factory=dict, 
        description="供应商特定参数，透传给底层API"
    )

    @validator('messages')
    def validate_messages(cls, v):
        """验证消息列表"""
        if not v:
            raise ValueError("消息列表不能为空")
        
        # 检查消息角色顺序的合理性
        roles = [msg.role for msg in v]
        
        # 第一条消息通常应该是system或user
        if roles[0] not in [MessageRole.SYSTEM, MessageRole.USER]:
            raise ValueError("第一条消息应该是system或user角色")
        
        # 检查是否有连续的相同角色（除了system）
        for i in range(1, len(roles)):
            if roles[i] == roles[i-1] and roles[i] != MessageRole.SYSTEM:
                # 允许连续的system消息，但警告其他情况
                pass
        
        return v

    @validator('model')
    def validate_model(cls, v):
        """验证模型名称"""
        if not v or not v.strip():
            raise ValueError("模型名称不能为空")
        
        # 可以添加支持的模型列表验证
        # supported_models = ["gpt-4", "gpt-3.5-turbo", "claude-3-sonnet", ...]
        # if v not in supported_models:
        #     raise ValueError(f"不支持的模型: {v}")
        
        return v.strip()

    @validator('generation')
    def validate_generation_config(cls, v):
        """验证生成配置"""
        # 检查temperature和top_p是否同时设置
        if v.temperature != 0.7 and v.top_p is not None:
            # 警告：建议只使用其中一个
            pass
        
        return v

    class Config:
        """Pydantic配置"""
        # 允许额外字段，但会发出警告
        extra = "forbid"
        # 使用枚举值而不是枚举名称
        use_enum_values = True
        # 验证赋值
        validate_assignment = True
        # JSON编码器
        json_encoders = {
            # 可以添加自定义编码器
        }

    # ========================================================================
    # 渐进式迁移支持方法
    # ========================================================================

    @classmethod
    def from_legacy_request(cls, legacy_request: Dict[str, Any]) -> "OptimizedTextGenerationRequest":
        """从传统的TextGenerationRequest格式转换为优化版本

        这个方法实现了向后兼容性，允许现有代码逐步迁移到优化版本。
        它会智能地将扁平化的参数重新组织到相应的配置组中。

        Args:
            legacy_request: 传统格式的请求参数字典

        Returns:
            OptimizedTextGenerationRequest: 转换后的优化版本请求

        Example:
            >>> legacy_data = {
            ...     "messages": [{"role": "user", "content": "Hello"}],
            ...     "model": "gpt-4",
            ...     "temperature": 0.8,
            ...     "max_tokens": 1000,
            ...     "stream": True
            ... }
            >>> optimized_request = OptimizedTextGenerationRequest.from_legacy_request(legacy_data)
        """
        logger.info("正在将传统请求格式转换为优化版本")

        # 提取核心必需参数
        messages = legacy_request.get("messages", [])
        model = legacy_request.get("model", "")

        # 构建生成配置
        generation_params = {}
        for param in ["max_tokens", "temperature", "top_p", "top_k",
                     "frequency_penalty", "presence_penalty", "stop"]:
            if param in legacy_request:
                generation_params[param] = legacy_request[param]

        generation_config = GenerationConfig(**generation_params)

        # 构建流式配置
        stream_enabled = legacy_request.get("stream", False)
        stream_config = StreamConfig(enabled=stream_enabled)

        # 构建安全配置（如果有安全相关参数）
        safety_config = None
        if "safety_settings" in legacy_request:
            safety_config = SafetyConfig(
                content_filter=True,
                safety_level="medium"  # 默认安全级别
            )

        # 提取高级功能参数
        functions = legacy_request.get("functions")
        function_call = legacy_request.get("function_call")
        tools = legacy_request.get("tools")
        tool_choice = legacy_request.get("tool_choice")
        response_format = legacy_request.get("response_format")
        response_schema = legacy_request.get("response_schema")
        user = legacy_request.get("user")

        # 提取供应商特定参数
        provider_params = legacy_request.get("provider_params", {})

        # 添加thinking相关参数到provider_params（如果存在）
        if "thinking_budget" in legacy_request:
            provider_params["thinking_budget"] = legacy_request["thinking_budget"]
        if "thinking_config" in legacy_request:
            provider_params["thinking_config"] = legacy_request["thinking_config"]

        # 构建优化版本请求
        return cls(
            messages=messages,
            model=model,
            generation=generation_config,
            stream=stream_config,
            safety=safety_config,
            functions=functions,
            function_call=function_call,
            tools=tools,
            tool_choice=tool_choice,
            response_format=response_format,
            response_schema=response_schema,
            user=user,
            provider_params=provider_params
        )

    def to_legacy_format(self) -> Dict[str, Any]:
        """转换为传统的扁平化格式

        这个方法确保优化版本可以与现有的供应商适配器兼容，
        在迁移期间提供双向转换能力。

        Returns:
            Dict[str, Any]: 传统格式的请求参数
        """
        logger.info("正在将优化版本转换为传统格式")

        # 基础参数
        legacy_format = {
            "messages": self.messages,
            "model": self.model,
        }

        # 展开生成配置
        if self.generation.max_tokens is not None:
            legacy_format["max_tokens"] = self.generation.max_tokens
        legacy_format["temperature"] = self.generation.temperature
        if self.generation.top_p is not None:
            legacy_format["top_p"] = self.generation.top_p
        if self.generation.top_k is not None:
            legacy_format["top_k"] = self.generation.top_k
        legacy_format["frequency_penalty"] = self.generation.frequency_penalty
        legacy_format["presence_penalty"] = self.generation.presence_penalty
        if self.generation.stop is not None:
            legacy_format["stop"] = self.generation.stop

        # 流式配置
        legacy_format["stream"] = self.stream.enabled

        # 高级功能参数
        if self.functions is not None:
            legacy_format["functions"] = self.functions
        if self.function_call is not None:
            legacy_format["function_call"] = self.function_call
        if self.tools is not None:
            legacy_format["tools"] = self.tools
        if self.tool_choice is not None:
            legacy_format["tool_choice"] = self.tool_choice
        if self.response_format is not None:
            legacy_format["response_format"] = self.response_format
        if self.response_schema is not None:
            legacy_format["response_schema"] = self.response_schema
        if self.user is not None:
            legacy_format["user"] = self.user

        # 安全设置
        if self.safety is not None:
            legacy_format["safety_settings"] = [
                {
                    "category": "HARM_CATEGORY_HARASSMENT",
                    "threshold": self.safety.safety_level.upper()
                }
            ]

        # 供应商特定参数
        legacy_format["provider_params"] = self.provider_params.copy()

        return legacy_format

    # ========================================================================
    # 供应商兼容性增强方法
    # ========================================================================

    def get_provider_specific_params(self, provider_name: str) -> Dict[str, Any]:
        """根据供应商类型获取特定的参数映射

        这个方法实现了智能的供应商适配，根据不同供应商的API规范
        和能力限制，自动调整和映射参数。

        Args:
            provider_name: 供应商名称 ("openai", "anthropic", "google", "dashscope")

        Returns:
            Dict[str, Any]: 适配后的供应商特定参数

        Raises:
            ValueError: 不支持的供应商名称
        """
        provider_name = provider_name.lower()
        logger.info(f"正在为供应商 {provider_name} 生成特定参数")

        if provider_name == "openai":
            return self._get_openai_params()
        elif provider_name == "anthropic":
            return self._get_anthropic_params()
        elif provider_name == "google":
            return self._get_google_params()
        elif provider_name == "dashscope":
            return self._get_dashscope_params()
        else:
            raise ValueError(f"不支持的供应商: {provider_name}")

    def _get_openai_params(self) -> Dict[str, Any]:
        """获取OpenAI格式的参数

        OpenAI API与我们的标准格式最为接近，几乎可以直接映射。
        """
        params = {
            "model": self.model,
            "messages": [
                {
                    "role": msg.role.value,
                    "content": msg.content,
                    **({"name": msg.name} if msg.name else {}),
                    **({"function_call": msg.function_call} if msg.function_call else {}),
                    **({"tool_calls": msg.tool_calls} if msg.tool_calls else {})
                }
                for msg in self.messages
            ],
            "stream": self.stream.enabled,
        }

        # 生成参数
        if self.generation.max_tokens is not None:
            params["max_tokens"] = self.generation.max_tokens
        params["temperature"] = self.generation.temperature
        if self.generation.top_p is not None:
            params["top_p"] = self.generation.top_p
        params["frequency_penalty"] = self.generation.frequency_penalty
        params["presence_penalty"] = self.generation.presence_penalty
        if self.generation.stop is not None:
            params["stop"] = self.generation.stop

        # 高级功能
        if self.functions is not None:
            params["functions"] = self.functions
        if self.function_call is not None:
            params["function_call"] = self.function_call
        if self.tools is not None:
            params["tools"] = self.tools
        if self.tool_choice is not None:
            params["tool_choice"] = self.tool_choice
        if self.response_format is not None:
            params["response_format"] = self.response_format
        if self.user is not None:
            params["user"] = self.user

        # 合并供应商特定参数
        params.update(self.provider_params)

        return params

    def _get_anthropic_params(self) -> Dict[str, Any]:
        """获取Anthropic格式的参数

        Anthropic API有一些特殊要求：
        1. system消息需要单独处理
        2. max_tokens是必需参数
        3. 不支持某些OpenAI特有的功能
        """
        # 分离system消息和对话消息
        system_messages = []
        conversation_messages = []

        for msg in self.messages:
            if msg.role == MessageRole.SYSTEM:
                system_messages.append(msg.content)
            elif msg.role in [MessageRole.USER, MessageRole.ASSISTANT]:
                conversation_messages.append({
                    "role": msg.role.value,
                    "content": msg.content
                })

        params = {
            "model": self.model,
            "messages": conversation_messages,
            "max_tokens": self.generation.max_tokens or 4096,  # Anthropic要求必须指定
            "stream": self.stream.enabled,
        }

        # 添加system消息（如果有）
        if system_messages:
            params["system"] = "\n".join(system_messages)

        # 生成参数（只添加Anthropic支持的）
        params["temperature"] = self.generation.temperature
        if self.generation.top_p is not None:
            params["top_p"] = self.generation.top_p
        if self.generation.top_k is not None:
            params["top_k"] = self.generation.top_k

        # 停止序列（Anthropic使用不同的字段名）
        if self.generation.stop is not None:
            stop_sequences = (
                [self.generation.stop] if isinstance(self.generation.stop, str)
                else self.generation.stop
            )
            params["stop_sequences"] = stop_sequences

        # 过滤不支持的功能并记录警告
        unsupported_features = []
        if self.functions is not None:
            unsupported_features.append("functions")
        if self.tools is not None:
            unsupported_features.append("tools")
        if self.response_format is not None:
            unsupported_features.append("response_format")

        if unsupported_features:
            logger.warning(f"Anthropic不支持以下功能，将被忽略: {unsupported_features}")

        # 合并供应商特定参数
        params.update(self.provider_params)

        return params

    def _get_google_params(self) -> Dict[str, Any]:
        """获取Google AI格式的参数

        Google AI API使用完全不同的格式：
        1. 消息格式为contents数组，每个消息包含parts
        2. system消息转换为systemInstruction
        3. 生成参数包装在generationConfig中
        4. 支持thinking功能和安全设置
        """
        # 分离system消息和对话消息
        system_messages = []
        conversation_contents = []

        for msg in self.messages:
            if msg.role == MessageRole.SYSTEM:
                system_messages.append(msg.content)
            else:
                # 转换为Google格式
                role = "user" if msg.role == MessageRole.USER else "model"
                conversation_contents.append({
                    "role": role,
                    "parts": [{"text": msg.content}]
                })

        params = {
            "contents": conversation_contents,
            "generationConfig": {}
        }

        # 添加system指令（如果有）
        if system_messages:
            params["systemInstruction"] = {
                "parts": [{"text": "\n".join(system_messages)}]
            }

        # 生成配置
        generation_config = {}
        if self.generation.max_tokens is not None:
            generation_config["maxOutputTokens"] = self.generation.max_tokens
        generation_config["temperature"] = self.generation.temperature
        if self.generation.top_p is not None:
            generation_config["topP"] = self.generation.top_p
        if self.generation.top_k is not None:
            generation_config["topK"] = self.generation.top_k
        if self.generation.stop is not None:
            stop_sequences = (
                [self.generation.stop] if isinstance(self.generation.stop, str)
                else self.generation.stop
            )
            generation_config["stopSequences"] = stop_sequences

        params["generationConfig"] = generation_config

        # 安全设置
        if self.safety is not None:
            safety_settings = []
            # Google AI的安全类别
            safety_categories = [
                "HARM_CATEGORY_HARASSMENT",
                "HARM_CATEGORY_HATE_SPEECH",
                "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                "HARM_CATEGORY_DANGEROUS_CONTENT"
            ]

            # 映射安全级别
            threshold_mapping = {
                "low": "BLOCK_ONLY_HIGH",
                "medium": "BLOCK_MEDIUM_AND_ABOVE",
                "high": "BLOCK_LOW_AND_ABOVE",
                "strict": "BLOCK_LOW_AND_ABOVE"
            }

            threshold = threshold_mapping.get(self.safety.safety_level, "BLOCK_MEDIUM_AND_ABOVE")

            for category in safety_categories:
                safety_settings.append({
                    "category": category,
                    "threshold": threshold
                })

            params["safetySettings"] = safety_settings

        # Thinking配置（Google特有功能）
        if "thinking_budget" in self.provider_params:
            params["thinkingConfig"] = {
                "thinkingBudget": self.provider_params["thinking_budget"]
            }

        # 工具配置（如果有）
        if self.tools is not None:
            tools_config = []
            for tool in self.tools:
                if "function" in tool:
                    # 转换函数调用格式
                    tools_config.append({
                        "functionDeclarations": [tool["function"]]
                    })
            if tools_config:
                params["tools"] = tools_config

        # 合并其他供应商特定参数
        for key, value in self.provider_params.items():
            if key not in ["thinking_budget"]:  # 避免重复添加
                params[key] = value

        return params

    def _get_dashscope_params(self) -> Dict[str, Any]:
        """获取DashScope格式的参数

        DashScope API使用嵌套的input结构：
        1. 消息放在input.messages中
        2. 生成参数放在parameters中
        3. 基本兼容OpenAI格式但有细微差异
        """
        # 转换消息格式
        messages = []
        for msg in self.messages:
            messages.append({
                "role": msg.role.value,
                "content": msg.content
            })

        params = {
            "model": self.model,
            "input": {
                "messages": messages
            },
            "parameters": {
                "result_format": "message"  # DashScope特有设置
            }
        }

        # 生成参数
        parameters = params["parameters"]
        if self.generation.max_tokens is not None:
            parameters["max_tokens"] = self.generation.max_tokens
        parameters["temperature"] = self.generation.temperature
        if self.generation.top_p is not None:
            parameters["top_p"] = self.generation.top_p
        if self.generation.top_k is not None:
            parameters["top_k"] = self.generation.top_k
        if self.generation.stop is not None:
            parameters["stop"] = self.generation.stop

        # 流式输出
        if self.stream.enabled:
            parameters["incremental_output"] = True

        # 工具调用支持
        if self.tools is not None:
            parameters["tools"] = self.tools
        if self.tool_choice is not None:
            parameters["tool_choice"] = self.tool_choice

        # 合并供应商特定参数
        if self.provider_params:
            # DashScope的特定参数通常放在parameters中
            parameters.update(self.provider_params)

        return params

    # ========================================================================
    # 供应商能力检测和参数验证
    # ========================================================================

    @staticmethod
    def get_provider_capabilities(provider_name: str) -> Dict[str, Any]:
        """获取供应商能力信息

        返回指定供应商支持的功能和限制，用于参数验证和智能适配。

        Args:
            provider_name: 供应商名称

        Returns:
            Dict[str, Any]: 供应商能力信息
        """
        capabilities = {
            "openai": {
                "supports_streaming": True,
                "supports_functions": True,
                "supports_tools": True,
                "supports_structured_output": True,
                "supports_safety_settings": False,
                "supports_thinking": False,
                "max_tokens_limit": 128000,
                "supported_parameters": [
                    "temperature", "top_p", "frequency_penalty",
                    "presence_penalty", "stop", "max_tokens"
                ],
                "required_parameters": ["model", "messages"],
                "message_roles": ["system", "user", "assistant", "function", "tool"]
            },
            "anthropic": {
                "supports_streaming": True,
                "supports_functions": False,
                "supports_tools": False,
                "supports_structured_output": False,
                "supports_safety_settings": False,
                "supports_thinking": False,
                "max_tokens_limit": 200000,
                "supported_parameters": [
                    "temperature", "top_p", "top_k", "stop_sequences", "max_tokens"
                ],
                "required_parameters": ["model", "messages", "max_tokens"],
                "message_roles": ["system", "user", "assistant"],
                "special_handling": {
                    "system_message": "separate_field",
                    "stop_parameter": "stop_sequences"
                }
            },
            "google": {
                "supports_streaming": True,
                "supports_functions": True,
                "supports_tools": True,
                "supports_structured_output": True,
                "supports_safety_settings": True,
                "supports_thinking": True,
                "max_tokens_limit": 8192,
                "supported_parameters": [
                    "temperature", "top_p", "top_k", "stop_sequences", "max_output_tokens"
                ],
                "required_parameters": ["contents"],
                "message_roles": ["user", "model"],
                "special_handling": {
                    "system_message": "system_instruction",
                    "message_format": "contents_parts",
                    "generation_config": "nested"
                }
            },
            "dashscope": {
                "supports_streaming": True,
                "supports_functions": False,
                "supports_tools": True,
                "supports_structured_output": False,
                "supports_safety_settings": False,
                "supports_thinking": False,
                "max_tokens_limit": 32000,
                "supported_parameters": [
                    "temperature", "top_p", "top_k", "stop", "max_tokens"
                ],
                "required_parameters": ["model", "input"],
                "message_roles": ["system", "user", "assistant"],
                "special_handling": {
                    "message_format": "input_messages",
                    "parameters": "nested"
                }
            }
        }

        return capabilities.get(provider_name.lower(), {})

    def validate_for_provider(self, provider_name: str) -> Dict[str, List[str]]:
        """验证请求是否与指定供应商兼容

        检查请求参数是否符合供应商的能力和限制，返回验证结果。

        Args:
            provider_name: 供应商名称

        Returns:
            Dict[str, List[str]]: 验证结果，包含errors、warnings、info三个列表
        """
        result = {
            "errors": [],
            "warnings": [],
            "info": []
        }

        capabilities = self.get_provider_capabilities(provider_name)
        if not capabilities:
            result["errors"].append(f"不支持的供应商: {provider_name}")
            return result

        # 检查token限制
        if self.generation.max_tokens is not None:
            max_limit = capabilities.get("max_tokens_limit", float('inf'))
            if self.generation.max_tokens > max_limit:
                result["warnings"].append(
                    f"max_tokens ({self.generation.max_tokens}) 超过供应商限制 ({max_limit})，将被调整"
                )

        # 检查不支持的功能
        if self.functions is not None and not capabilities.get("supports_functions", False):
            result["warnings"].append(f"{provider_name} 不支持函数调用功能，functions参数将被忽略")

        if self.tools is not None and not capabilities.get("supports_tools", False):
            result["warnings"].append(f"{provider_name} 不支持工具调用功能，tools参数将被忽略")

        if self.response_format is not None and not capabilities.get("supports_structured_output", False):
            result["warnings"].append(f"{provider_name} 不支持结构化输出，response_format参数将被忽略")

        if self.safety is not None and not capabilities.get("supports_safety_settings", False):
            result["warnings"].append(f"{provider_name} 不支持详细安全设置，将使用默认安全策略")

        # 检查thinking功能
        thinking_params = ["thinking_budget", "thinking_config"]
        has_thinking = any(param in self.provider_params for param in thinking_params)
        if has_thinking and not capabilities.get("supports_thinking", False):
            result["warnings"].append(f"{provider_name} 不支持thinking功能，相关参数将被忽略")

        # 检查消息角色
        supported_roles = capabilities.get("message_roles", [])
        for msg in self.messages:
            if msg.role.value not in supported_roles:
                result["warnings"].append(
                    f"{provider_name} 不支持 {msg.role.value} 角色，消息可能被跳过或转换"
                )

        # Anthropic特殊检查
        if provider_name.lower() == "anthropic":
            if self.generation.max_tokens is None:
                result["errors"].append("Anthropic要求必须指定max_tokens参数")

        # 添加优化建议
        if provider_name.lower() == "google" and self.generation.max_tokens is not None:
            if self.generation.max_tokens > 8192:
                result["info"].append("建议使用Google的thinking功能来处理长文本生成需求")

        return result


# === 向后兼容的简化版本 ===
class SimpleTextGenerationRequest(BaseModel):
    """简化版本的文本生成请求，保持向后兼容性"""
    
    messages: List[Message] = Field(..., description="对话消息列表")
    model: str = Field(..., description="使用的模型名称")
    max_tokens: Optional[int] = Field(None, description="最大生成token数")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="生成温度")
    stream: bool = Field(False, description="是否流式输出")
    
    def to_optimized(self) -> OptimizedTextGenerationRequest:
        """转换为优化版本的请求

        这个方法提供了从简化版本到优化版本的无缝转换，
        确保现有代码可以逐步迁移到新的API设计。
        """
        generation_config = GenerationConfig(
            max_tokens=self.max_tokens,
            temperature=self.temperature or 0.7
        )

        stream_config = StreamConfig(enabled=self.stream)

        return OptimizedTextGenerationRequest(
            messages=self.messages,
            model=self.model,
            generation=generation_config,
            stream=stream_config
        )

    def validate_for_provider(self, provider_name: str) -> Dict[str, List[str]]:
        """验证简化请求是否与指定供应商兼容

        为简化版本提供同样的供应商兼容性检查功能。
        """
        # 先转换为优化版本，然后进行验证
        optimized = self.to_optimized()
        return optimized.validate_for_provider(provider_name)

    def get_provider_params(self, provider_name: str) -> Dict[str, Any]:
        """获取供应商特定参数

        为简化版本提供供应商适配功能。
        """
        optimized = self.to_optimized()
        return optimized.get_provider_specific_params(provider_name)
