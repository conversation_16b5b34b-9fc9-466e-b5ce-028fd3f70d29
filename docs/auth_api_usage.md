# AI Gen Hub 认证API使用指南

本指南详细介绍如何使用 AI Gen Hub 的认证和授权API。

## 📋 目录

- [API概览](#api概览)
- [认证流程](#认证流程)
- [API Token管理](#api-token管理)
- [权限系统](#权限系统)
- [代码示例](#代码示例)
- [错误处理](#错误处理)
- [最佳实践](#最佳实践)

## 🔍 API概览

### 基础信息

- **Base URL**: `https://api.your-domain.com`
- **API版本**: `v1`
- **认证方式**: 
  - 控制台访问: OAuth2/OIDC
  - API访问: Bearer <PERSON>ken (JWT或API Key)

### 支持的认证方式

1. **OAuth2/OIDC登录** - 用于控制台用户
2. **API Token** - 用于程序化访问
3. **JWT Token** - 短期访问令牌

## 🔐 认证流程

### 1. OAuth2/OIDC 登录流程

#### 步骤1: 发起登录

```http
POST /auth/login
Content-Type: application/json

{
  "idp_name": "auth0",
  "redirect_url": "https://console.your-domain.com/dashboard"
}
```

**响应**:
```json
{
  "authorization_url": "https://your-domain.auth0.com/authorize?client_id=...",
  "state": "random-state-string"
}
```

#### 步骤2: 用户授权

用户被重定向到IdP进行身份验证，授权后返回到回调URL。

#### 步骤3: 处理回调

```http
GET /auth/callback/auth0?code=auth_code&state=state_string
```

**响应**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

### 2. 刷新访问令牌

```http
POST /auth/refresh
Content-Type: application/json
Authorization: Bearer <current_access_token>

{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

### 3. 用户登出

```http
POST /auth/logout
Authorization: Bearer <access_token>

{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## 🔑 API Token管理

### 创建API Token

```http
POST /auth/tokens
Content-Type: application/json
Authorization: Bearer <access_token>

{
  "name": "我的API令牌",
  "scopes": ["api_access"],
  "permissions": ["api:text:generate", "api:image:generate"],
  "expires_in_days": 90,
  "rate_limit": 1000
}
```

**响应**:
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "name": "我的API令牌",
  "token": "ak_1234567890abcdef1234567890abcdef",
  "token_prefix": "ak_12345...",
  "scopes": ["api_access"],
  "permissions": ["api:text:generate", "api:image:generate"],
  "expires_at": "2024-06-01T00:00:00Z",
  "created_at": "2024-03-01T00:00:00Z"
}
```

### 列出API Token

```http
GET /auth/tokens
Authorization: Bearer <access_token>
```

**响应**:
```json
[
  {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "name": "我的API令牌",
    "token_prefix": "ak_12345...",
    "scopes": ["api_access"],
    "permissions": ["api:text:generate"],
    "is_active": true,
    "last_used_at": "2024-03-15T10:30:00Z",
    "expires_at": "2024-06-01T00:00:00Z",
    "created_at": "2024-03-01T00:00:00Z"
  }
]
```

### 撤销API Token

```http
DELETE /auth/tokens/{token_id}
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "message": "令牌撤销成功"
}
```

## 👤 用户信息

### 获取当前用户信息

```http
GET /auth/me
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "username": "john_doe",
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "role": "developer",
  "permissions": [
    "api:text:generate",
    "api:image:generate",
    "console:access"
  ],
  "status": "active",
  "created_at": "2024-01-01T00:00:00Z",
  "last_login_at": "2024-03-15T09:00:00Z"
}
```

## 🛡️ 权限系统

### 权限级别

1. **系统权限**:
   - `system:admin` - 系统管理员
   - `system:config` - 系统配置
   - `system:monitor` - 系统监控

2. **用户管理权限**:
   - `user:create` - 创建用户
   - `user:read` - 查看用户
   - `user:update` - 更新用户
   - `user:delete` - 删除用户
   - `user:manage` - 用户管理

3. **API权限**:
   - `api:text:generate` - 文本生成
   - `api:image:generate` - 图像生成
   - `api:provider:manage` - 供应商管理
   - `api:token:manage` - 令牌管理

4. **控制台权限**:
   - `console:access` - 控制台访问
   - `console:analytics` - 分析功能
   - `console:settings` - 设置管理

### 角色定义

- **super_admin**: 所有权限
- **admin**: 管理权限（除系统管理员权限外）
- **developer**: 开发者权限（API访问、令牌管理等）
- **user**: 基础用户权限（基本API访问）
- **readonly**: 只读权限

## 💻 代码示例

### Python 客户端示例

```python
import requests
import json
from typing import Optional, Dict, Any

class AIGenHubClient:
    """AI Gen Hub API 客户端"""
    
    def __init__(self, base_url: str, api_token: Optional[str] = None):
        self.base_url = base_url.rstrip('/')
        self.api_token = api_token
        self.session = requests.Session()
        
        if api_token:
            self.session.headers.update({
                'Authorization': f'Bearer {api_token}',
                'Content-Type': 'application/json'
            })
    
    def login_with_oauth(self, idp_name: str = "auth0") -> Dict[str, str]:
        """发起OAuth登录流程"""
        response = self.session.post(
            f"{self.base_url}/auth/login",
            json={"idp_name": idp_name}
        )
        response.raise_for_status()
        return response.json()
    
    def create_api_token(
        self, 
        name: str, 
        scopes: list = None, 
        permissions: list = None,
        expires_in_days: int = 90
    ) -> Dict[str, Any]:
        """创建API令牌"""
        data = {
            "name": name,
            "scopes": scopes or ["api_access"],
            "permissions": permissions or [],
            "expires_in_days": expires_in_days
        }
        
        response = self.session.post(
            f"{self.base_url}/auth/tokens",
            json=data
        )
        response.raise_for_status()
        return response.json()
    
    def get_user_info(self) -> Dict[str, Any]:
        """获取当前用户信息"""
        response = self.session.get(f"{self.base_url}/auth/me")
        response.raise_for_status()
        return response.json()
    
    def generate_text(self, prompt: str, model: str = "gpt-3.5-turbo") -> str:
        """生成文本（示例API调用）"""
        data = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}]
        }
        
        response = self.session.post(
            f"{self.base_url}/api/v1/text/generate",
            json=data
        )
        response.raise_for_status()
        return response.json()["choices"][0]["message"]["content"]

# 使用示例
if __name__ == "__main__":
    # 使用API Token初始化客户端
    client = AIGenHubClient(
        base_url="https://api.your-domain.com",
        api_token="ak_your_api_token_here"
    )
    
    # 获取用户信息
    user_info = client.get_user_info()
    print(f"当前用户: {user_info['username']}")
    
    # 生成文本
    result = client.generate_text("写一首关于春天的诗")
    print(f"生成结果: {result}")
```

### JavaScript 客户端示例

```javascript
class AIGenHubClient {
    constructor(baseUrl, apiToken = null) {
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.apiToken = apiToken;
    }
    
    async request(method, endpoint, data = null) {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (this.apiToken) {
            headers['Authorization'] = `Bearer ${this.apiToken}`;
        }
        
        const config = {
            method,
            headers
        };
        
        if (data) {
            config.body = JSON.stringify(data);
        }
        
        const response = await fetch(`${this.baseUrl}${endpoint}`, config);
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || `HTTP ${response.status}`);
        }
        
        return response.json();
    }
    
    async loginWithOAuth(idpName = 'auth0') {
        return this.request('POST', '/auth/login', { idp_name: idpName });
    }
    
    async createApiToken(name, scopes = ['api_access'], permissions = [], expiresInDays = 90) {
        return this.request('POST', '/auth/tokens', {
            name,
            scopes,
            permissions,
            expires_in_days: expiresInDays
        });
    }
    
    async getUserInfo() {
        return this.request('GET', '/auth/me');
    }
    
    async generateText(prompt, model = 'gpt-3.5-turbo') {
        const response = await this.request('POST', '/api/v1/text/generate', {
            model,
            messages: [{ role: 'user', content: prompt }]
        });
        return response.choices[0].message.content;
    }
}

// 使用示例
const client = new AIGenHubClient(
    'https://api.your-domain.com',
    'ak_your_api_token_here'
);

// 获取用户信息
client.getUserInfo()
    .then(userInfo => console.log('当前用户:', userInfo.username))
    .catch(error => console.error('错误:', error));

// 生成文本
client.generateText('写一首关于春天的诗')
    .then(result => console.log('生成结果:', result))
    .catch(error => console.error('错误:', error));
```

### cURL 示例

```bash
#!/bin/bash

# 设置变量
BASE_URL="https://api.your-domain.com"
API_TOKEN="ak_your_api_token_here"

# 获取用户信息
curl -X GET "$BASE_URL/auth/me" \
  -H "Authorization: Bearer $API_TOKEN" \
  -H "Content-Type: application/json"

# 创建API令牌
curl -X POST "$BASE_URL/auth/tokens" \
  -H "Authorization: Bearer $API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试令牌",
    "scopes": ["api_access"],
    "permissions": ["api:text:generate"],
    "expires_in_days": 30
  }'

# 生成文本
curl -X POST "$BASE_URL/api/v1/text/generate" \
  -H "Authorization: Bearer $API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "写一首关于春天的诗"}
    ]
  }'
```

## ❌ 错误处理

### 常见错误码

| 状态码 | 错误类型 | 描述 |
|--------|----------|------|
| 401 | `authentication_failed` | 认证失败 |
| 403 | `authorization_failed` | 权限不足 |
| 429 | `rate_limit_exceeded` | 频率限制超出 |
| 400 | `invalid_request` | 请求格式错误 |
| 404 | `not_found` | 资源不存在 |
| 500 | `internal_error` | 内部服务器错误 |

### 错误响应格式

```json
{
  "error": "authentication_failed",
  "message": "令牌已过期",
  "error_code": "TokenExpiredError",
  "details": {
    "expired_at": "2024-03-15T10:00:00Z"
  }
}
```

### 错误处理示例

```python
import requests
from requests.exceptions import HTTPError

def handle_api_error(response):
    """处理API错误响应"""
    try:
        error_data = response.json()
        error_type = error_data.get('error', 'unknown_error')
        message = error_data.get('message', 'Unknown error occurred')
        
        if error_type == 'authentication_failed':
            print(f"认证失败: {message}")
            # 重新获取令牌或提示用户登录
        elif error_type == 'authorization_failed':
            print(f"权限不足: {message}")
            # 提示用户权限不足
        elif error_type == 'rate_limit_exceeded':
            retry_after = error_data.get('retry_after', 60)
            print(f"频率限制: {message}，请在 {retry_after} 秒后重试")
            # 实现重试逻辑
        else:
            print(f"API错误: {message}")
            
    except ValueError:
        print(f"HTTP错误: {response.status_code}")

# 使用示例
try:
    response = requests.get(
        "https://api.your-domain.com/auth/me",
        headers={"Authorization": "Bearer invalid_token"}
    )
    response.raise_for_status()
except HTTPError:
    handle_api_error(response)
```

## 🎯 最佳实践

### 1. 令牌管理

- **安全存储**: 将API令牌存储在安全的地方，避免硬编码
- **定期轮换**: 定期更新API令牌，设置合理的过期时间
- **最小权限**: 只授予必要的权限和范围
- **监控使用**: 定期检查令牌使用情况和异常活动

### 2. 错误处理

- **优雅降级**: 实现适当的错误处理和重试机制
- **用户友好**: 提供清晰的错误信息给最终用户
- **日志记录**: 记录API调用和错误信息用于调试

### 3. 性能优化

- **连接复用**: 使用HTTP连接池减少连接开销
- **请求缓存**: 缓存不经常变化的数据
- **并发控制**: 合理控制并发请求数量

### 4. 安全考虑

- **HTTPS**: 始终使用HTTPS进行API通信
- **输入验证**: 验证所有输入数据
- **敏感信息**: 避免在日志中记录敏感信息

---

## 📚 相关文档

- [认证系统部署指南](auth_deployment_guide.md)
- [安全最佳实践](security_best_practices.md)
- [API参考文档](api_reference.md)

## 🆘 获取帮助

如果您在使用API时遇到问题：

1. 查看[错误处理](#错误处理)部分
2. 检查API文档和示例代码
3. 在GitHub Issues中搜索相关问题
4. 联系技术支持团队
