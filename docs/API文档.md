# AI Gen Hub API 文档

AI Gen Hub 提供了完整的 RESTful API，支持多种 AI 服务和企业级功能。

## 🚀 快速开始

### 基础URL
```
https://api.ai-gen-hub.com/v1
```

### 认证
所有API请求都需要在请求头中包含API密钥：
```http
Authorization: Bearer YOUR_API_KEY
```

### 响应格式
所有API响应都遵循统一的格式：
```json
{
  "success": true,
  "data": {...},
  "message": "操作成功",
  "request_id": "req_12345",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 📝 文本生成 API

### 生成文本
```http
POST /text/generate
Content-Type: application/json

{
  "messages": [
    {
      "role": "user",
      "content": "写一首关于春天的诗"
    }
  ],
  "model": "gpt-3.5-turbo",
  "config": {
    "max_tokens": 500,
    "temperature": 0.7,
    "top_p": 0.9
  },
  "stream": {
    "enabled": false
  }
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": "gen_12345",
    "content": "春风轻拂柳絮飞，\n桃花满树映朝晖。\n...",
    "model": "gpt-3.5-turbo",
    "usage": {
      "prompt_tokens": 15,
      "completion_tokens": 120,
      "total_tokens": 135
    },
    "finish_reason": "stop"
  }
}
```

### 流式生成
```http
POST /text/generate
Content-Type: application/json

{
  "messages": [...],
  "model": "gpt-3.5-turbo",
  "stream": {
    "enabled": true,
    "chunk_size": 50
  }
}
```

## 🎨 图像生成 API

### 生成图像
```http
POST /image/generate
Content-Type: application/json

{
  "prompt": "一只可爱的小猫坐在花园里",
  "model": "dall-e-3",
  "config": {
    "size": "1024x1024",
    "quality": "standard",
    "style": "vivid",
    "n": 1
  }
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": "img_12345",
    "created": **********,
    "data": [
      {
        "url": "https://example.com/generated-image.png",
        "revised_prompt": "一只橘色的小猫咪坐在充满鲜花的花园里"
      }
    ],
    "provider": "openai"
  }
}
```

## 🔄 工作流 API

### 创建工作流
```http
POST /workflow/create
Content-Type: application/json

{
  "name": "内容创作工作流",
  "description": "自动化内容创作流程",
  "nodes": [
    {
      "id": "node_1",
      "type": "text_generation",
      "config": {
        "model": "gpt-4",
        "prompt": "写一篇关于{{topic}}的文章大纲"
      }
    },
    {
      "id": "node_2",
      "type": "text_generation",
      "config": {
        "model": "gpt-4",
        "prompt": "基于以下大纲写一篇完整文章：{{node_1.output}}"
      }
    }
  ],
  "connections": [
    {
      "from": "node_1",
      "to": "node_2"
    }
  ]
}
```

### 执行工作流
```http
POST /workflow/{workflow_id}/execute
Content-Type: application/json

{
  "inputs": {
    "topic": "人工智能的发展趋势"
  }
}
```

## 👥 协作 API

### 创建协作空间
```http
POST /collaboration/spaces
Content-Type: application/json

{
  "name": "AI内容创作团队",
  "description": "团队协作创作AI内容",
  "settings": {
    "public": false,
    "max_members": 10,
    "permissions": {
      "can_edit": true,
      "can_comment": true,
      "can_share": false
    }
  }
}
```

### 邀请成员
```http
POST /collaboration/spaces/{space_id}/invite
Content-Type: application/json

{
  "email": "<EMAIL>",
  "role": "editor",
  "message": "邀请您加入我们的AI内容创作团队"
}
```

## 🎯 推荐 API

### 获取个性化推荐
```http
GET /recommendations/personalized?user_id=user_123&type=content&limit=10
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "recommendations": [
      {
        "id": "rec_1",
        "type": "template",
        "title": "营销文案模板",
        "description": "基于您的使用历史推荐",
        "score": 0.95,
        "reason": "您经常使用营销相关的提示词"
      }
    ],
    "total": 25,
    "has_more": true
  }
}
```

## 🏢 企业 API

### 创建企业配置
```http
POST /enterprise/config
Content-Type: application/json
Authorization: Bearer ADMIN_TOKEN

{
  "organization_name": "科技有限公司",
  "deployment_type": "cloud",
  "domain": "company.com",
  "contact_email": "<EMAIL>",
  "security_level": "high"
}
```

### SSO登录
```http
GET /enterprise/sso/login/{organization_id}?redirect_url=https://app.company.com
```

### 查询审计日志
```http
GET /enterprise/audit/logs?organization_id=org_123&start_time=2024-01-01T00:00:00Z&end_time=2024-01-31T23:59:59Z&limit=100
```

## 📊 监控 API

### 获取系统状态
```http
GET /system/health
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "2.1.0",
    "uptime": 86400,
    "services": {
      "api": "healthy",
      "database": "healthy",
      "cache": "healthy",
      "ai_providers": "healthy"
    },
    "metrics": {
      "requests_per_minute": 150,
      "average_response_time": 245,
      "error_rate": 0.01
    }
  }
}
```

### 获取使用统计
```http
GET /system/stats?period=7d
```

## 🔧 管理 API

### 用户管理
```http
GET /admin/users?page=1&limit=20
POST /admin/users
PUT /admin/users/{user_id}
DELETE /admin/users/{user_id}
```

### 供应商管理
```http
GET /admin/providers
POST /admin/providers/{provider_id}/configure
PUT /admin/providers/{provider_id}/status
```

## 📚 SDK 示例

### Python SDK
```python
from ai_gen_hub import AIGenHub

# 初始化客户端
client = AIGenHub(api_key="your_api_key")

# 生成文本
response = client.text.generate(
    messages=[{"role": "user", "content": "Hello, world!"}],
    model="gpt-3.5-turbo"
)

# 生成图像
image = client.image.generate(
    prompt="一只可爱的小猫",
    model="dall-e-3"
)

# 执行工作流
result = client.workflow.execute(
    workflow_id="workflow_123",
    inputs={"topic": "AI技术"}
)
```

### JavaScript SDK
```javascript
import { AIGenHub } from '@ai-gen-hub/sdk';

const client = new AIGenHub({
  apiKey: 'your_api_key'
});

// 生成文本
const response = await client.text.generate({
  messages: [{ role: 'user', content: 'Hello, world!' }],
  model: 'gpt-3.5-turbo'
});

// 生成图像
const image = await client.image.generate({
  prompt: '一只可爱的小猫',
  model: 'dall-e-3'
});
```

## 🚨 错误处理

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "请求参数无效",
    "details": {
      "field": "model",
      "reason": "不支持的模型类型"
    }
  },
  "request_id": "req_12345",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 常见错误码
- `INVALID_REQUEST`: 请求参数无效
- `UNAUTHORIZED`: 认证失败
- `FORBIDDEN`: 权限不足
- `NOT_FOUND`: 资源不存在
- `RATE_LIMIT_EXCEEDED`: 请求频率超限
- `INTERNAL_ERROR`: 服务器内部错误
- `SERVICE_UNAVAILABLE`: 服务暂时不可用

## 📈 限制和配额

### 请求限制
- 免费用户: 100 请求/小时
- 基础用户: 1,000 请求/小时
- 专业用户: 10,000 请求/小时
- 企业用户: 无限制

### 内容限制
- 文本输入: 最大 32,000 字符
- 图像生成: 最大 1024x1024 像素
- 工作流节点: 最大 50 个节点

## 🔗 相关链接

- [官方网站](https://ai-gen-hub.com)
- [开发者文档](https://docs.ai-gen-hub.com)
- [SDK下载](https://github.com/ai-gen-hub/sdk)
- [社区论坛](https://community.ai-gen-hub.com)
- [技术支持](mailto:<EMAIL>)
