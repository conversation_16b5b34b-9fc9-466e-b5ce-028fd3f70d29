# AI Gen Hub 部署指南

本指南将帮助您在不同环境中部署 AI Gen Hub 系统。

## 🚀 快速部署

### 使用 Docker Compose (推荐)

1. **克隆项目**
```bash
git clone https://github.com/your-org/ai-gen-hub.git
cd ai-gen-hub
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置必要的环境变量
```

3. **启动服务**
```bash
docker-compose up -d
```

4. **验证部署**
```bash
curl http://localhost:8000/health
```

## 🐳 Docker 部署

### 构建镜像
```bash
# 构建应用镜像
docker build -t ai-gen-hub:latest .

# 构建前端镜像
docker build -t ai-gen-hub-web:latest -f web/Dockerfile web/
```

### 运行容器
```bash
# 启动数据库
docker run -d --name postgres \
  -e POSTGRES_DB=ai_gen_hub \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  postgres:15

# 启动 Redis
docker run -d --name redis \
  -p 6379:6379 \
  redis:7-alpine

# 启动应用
docker run -d --name ai-gen-hub \
  -p 8000:8000 \
  -e DATABASE_URL=postgresql://postgres:password@localhost:5432/ai_gen_hub \
  -e REDIS_URL=redis://localhost:6379 \
  --link postgres \
  --link redis \
  ai-gen-hub:latest
```

## ☸️ Kubernetes 部署

### 1. 创建命名空间
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: ai-gen-hub
```

### 2. 配置 ConfigMap
```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-gen-hub-config
  namespace: ai-gen-hub
data:
  DATABASE_HOST: "postgres-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "ai_gen_hub"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  LOG_LEVEL: "INFO"
```

### 3. 创建 Secret
```yaml
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: ai-gen-hub-secret
  namespace: ai-gen-hub
type: Opaque
data:
  DATABASE_PASSWORD: <base64-encoded-password>
  OPENAI_API_KEY: <base64-encoded-api-key>
  GOOGLE_AI_API_KEY: <base64-encoded-api-key>
  JWT_SECRET: <base64-encoded-jwt-secret>
```

### 4. 部署数据库
```yaml
# postgres.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: ai-gen-hub
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15
        env:
        - name: POSTGRES_DB
          value: "ai_gen_hub"
        - name: POSTGRES_USER
          value: "postgres"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-gen-hub-secret
              key: DATABASE_PASSWORD
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: ai-gen-hub
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
```

### 5. 部署应用
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-gen-hub
  namespace: ai-gen-hub
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-gen-hub
  template:
    metadata:
      labels:
        app: ai-gen-hub
    spec:
      containers:
      - name: ai-gen-hub
        image: ai-gen-hub:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          value: "****************************************************************/ai_gen_hub"
        - name: REDIS_URL
          value: "redis://redis-service:6379"
        envFrom:
        - configMapRef:
            name: ai-gen-hub-config
        - secretRef:
            name: ai-gen-hub-secret
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: ai-gen-hub-service
  namespace: ai-gen-hub
spec:
  selector:
    app: ai-gen-hub
  ports:
  - port: 80
    targetPort: 8000
  type: LoadBalancer
```

### 6. 配置 Ingress
```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-gen-hub-ingress
  namespace: ai-gen-hub
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - api.ai-gen-hub.com
    secretName: ai-gen-hub-tls
  rules:
  - host: api.ai-gen-hub.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-gen-hub-service
            port:
              number: 80
```

## 🌐 云平台部署

### AWS 部署

#### 使用 ECS
```bash
# 创建 ECS 集群
aws ecs create-cluster --cluster-name ai-gen-hub

# 创建任务定义
aws ecs register-task-definition --cli-input-json file://task-definition.json

# 创建服务
aws ecs create-service \
  --cluster ai-gen-hub \
  --service-name ai-gen-hub-service \
  --task-definition ai-gen-hub:1 \
  --desired-count 2
```

#### 使用 EKS
```bash
# 创建 EKS 集群
eksctl create cluster --name ai-gen-hub --region us-west-2

# 部署应用
kubectl apply -f k8s/
```

### Azure 部署

#### 使用 Container Instances
```bash
# 创建资源组
az group create --name ai-gen-hub-rg --location eastus

# 部署容器
az container create \
  --resource-group ai-gen-hub-rg \
  --name ai-gen-hub \
  --image ai-gen-hub:latest \
  --ports 8000 \
  --environment-variables \
    DATABASE_URL=postgresql://... \
    REDIS_URL=redis://...
```

### Google Cloud 部署

#### 使用 Cloud Run
```bash
# 构建并推送镜像
gcloud builds submit --tag gcr.io/PROJECT_ID/ai-gen-hub

# 部署到 Cloud Run
gcloud run deploy ai-gen-hub \
  --image gcr.io/PROJECT_ID/ai-gen-hub \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

## 🔧 配置说明

### 环境变量
```bash
# 数据库配置
DATABASE_URL=postgresql://user:password@host:port/database
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis 配置
REDIS_URL=redis://host:port/db
REDIS_POOL_SIZE=10

# AI 供应商 API 密钥
OPENAI_API_KEY=sk-...
GOOGLE_AI_API_KEY=...
ANTHROPIC_API_KEY=...

# 认证配置
JWT_SECRET=your-secret-key
JWT_EXPIRE_HOURS=24

# 应用配置
APP_NAME=AI Gen Hub
APP_VERSION=2.1.0
DEBUG=false
LOG_LEVEL=INFO

# 监控配置
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
GRAFANA_ENABLED=true
```

### 数据库迁移
```bash
# 运行数据库迁移
alembic upgrade head

# 创建初始数据
python scripts/init_data.py
```

## 📊 监控和日志

### Prometheus 配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ai-gen-hub'
    static_configs:
      - targets: ['ai-gen-hub-service:8000']
    metrics_path: /metrics
```

### Grafana 仪表板
```json
{
  "dashboard": {
    "title": "AI Gen Hub Metrics",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])"
          }
        ]
      }
    ]
  }
}
```

### 日志配置
```yaml
# logging.yaml
version: 1
formatters:
  default:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
handlers:
  console:
    class: logging.StreamHandler
    formatter: default
  file:
    class: logging.FileHandler
    filename: /var/log/ai-gen-hub.log
    formatter: default
loggers:
  ai_gen_hub:
    level: INFO
    handlers: [console, file]
```

## 🔒 安全配置

### SSL/TLS 配置
```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name api.ai-gen-hub.com;
    
    ssl_certificate /etc/ssl/certs/ai-gen-hub.crt;
    ssl_certificate_key /etc/ssl/private/ai-gen-hub.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    location / {
        proxy_pass http://ai-gen-hub-service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 防火墙规则
```bash
# 允许 HTTP/HTTPS 流量
ufw allow 80/tcp
ufw allow 443/tcp

# 限制数据库访问
ufw allow from 10.0.0.0/8 to any port 5432

# 启用防火墙
ufw enable
```

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
```bash
# 检查数据库状态
kubectl logs deployment/postgres -n ai-gen-hub

# 测试连接
psql -h postgres-service -U postgres -d ai_gen_hub
```

2. **API 密钥无效**
```bash
# 检查 Secret 配置
kubectl get secret ai-gen-hub-secret -n ai-gen-hub -o yaml

# 更新 API 密钥
kubectl patch secret ai-gen-hub-secret -n ai-gen-hub \
  --patch='{"data":{"OPENAI_API_KEY":"<new-base64-key>"}}'
```

3. **内存不足**
```bash
# 检查资源使用
kubectl top pods -n ai-gen-hub

# 调整资源限制
kubectl patch deployment ai-gen-hub -n ai-gen-hub \
  --patch='{"spec":{"template":{"spec":{"containers":[{"name":"ai-gen-hub","resources":{"limits":{"memory":"2Gi"}}}]}}}}'
```

## 📞 技术支持

如有部署问题，请联系：
- 📧 邮箱: <EMAIL>
- 📚 文档: https://docs.ai-gen-hub.com
- 💬 社区: https://community.ai-gen-hub.com
