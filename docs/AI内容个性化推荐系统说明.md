# AI内容个性化推荐系统说明

AI Gen Hub 个性化推荐系统基于用户行为分析和机器学习算法，为每个用户提供精准的内容推荐，提升用户体验和内容发现效率。

## 🚀 功能概览

### 1. 智能用户画像
- **行为分析**: 深度分析用户浏览、点赞、分享等行为模式
- **偏好学习**: 自动学习用户的内容类别、标签、复杂度偏好
- **动态更新**: 实时更新用户画像，适应偏好变化
- **置信度评估**: 评估偏好数据的可靠性和准确性

### 2. 多算法推荐引擎
- **基于内容推荐**: 根据内容特征和用户偏好匹配
- **协同过滤**: 基于相似用户的行为模式推荐
- **热门推荐**: 发现和推荐当前热门内容
- **混合推荐**: 融合多种算法的优势

### 3. 个性化推荐服务
- **实时推荐**: 毫秒级响应的个性化推荐
- **多样性控制**: 平衡推荐结果的相关性和多样性
- **推荐解释**: 提供推荐理由，增强用户信任
- **反馈学习**: 基于用户反馈持续优化推荐效果

## 📋 核心组件

### 用户行为模型
```python
@dataclass
class UserBehavior:
    behavior_id: str
    user_id: str
    content_id: str
    action_type: UserActionType  # VIEW, LIKE, SHARE, BOOKMARK等
    duration: Optional[float]    # 行为持续时间
    timestamp: datetime
    metadata: Dict[str, Any]     # 上下文信息
```

### 内容特征模型
```python
@dataclass
class ContentFeature:
    content_id: str
    title: str
    category: ContentCategory
    tags: List[str]
    quality_score: float         # 质量评分 0-10
    complexity: float           # 复杂度 0-1
    author_id: str
    view_count: int
    like_count: int
    embedding_vector: List[float]  # 内容向量表示
```

### 用户偏好模型
```python
@dataclass
class UserPreference:
    user_id: str
    category_preferences: Dict[ContentCategory, float]
    tag_preferences: Dict[str, float]
    complexity_preference: float
    quality_threshold: float
    novelty_preference: float    # 新颖性偏好
    diversity_preference: float  # 多样性偏好
    confidence: float           # 偏好置信度
```

## 🔧 API接口

### 记录用户行为

#### 记录行为
```http
POST /api/v1/recommendation/behaviors
Content-Type: application/json

{
  "user_id": "user_12345",
  "content_id": "content_67890",
  "action_type": "like",
  "duration": 120.5,
  "metadata": {
    "device": "mobile",
    "source": "search",
    "session_id": "session_abc123"
  }
}
```

#### 批量记录行为
```http
POST /api/v1/recommendation/behaviors/batch
Content-Type: application/json

{
  "behaviors": [
    {
      "user_id": "user_12345",
      "content_id": "content_001",
      "action_type": "view",
      "duration": 45.2
    },
    {
      "user_id": "user_12345", 
      "content_id": "content_002",
      "action_type": "like"
    }
  ]
}
```

### 获取个性化推荐

#### 基础推荐
```http
GET /api/v1/recommendation/recommend?user_id=user_12345&count=10
```

#### 高级推荐
```http
POST /api/v1/recommendation/recommend
Content-Type: application/json

{
  "user_id": "user_12345",
  "count": 20,
  "recommendation_types": ["content_based", "collaborative", "trending"],
  "categories": ["text", "image"],
  "context": {
    "device": "mobile",
    "time_of_day": "evening",
    "location": "home"
  },
  "diversity_factor": 0.3,
  "novelty_factor": 0.2
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "user_id": "user_12345",
    "request_id": "req_abc123",
    "recommendations": [
      {
        "content_id": "content_001",
        "score": 0.95,
        "rank": 1,
        "recommendation_type": "content_based",
        "reason": "基于您对机器学习内容的偏好",
        "reason_details": {
          "category_match": 0.9,
          "tag_match": 0.85,
          "quality_match": 0.8
        },
        "content_feature": {
          "title": "深度学习实战指南",
          "category": "document",
          "tags": ["深度学习", "实战", "神经网络"],
          "quality_score": 9.2,
          "complexity": 0.8,
          "author_name": "AI专家"
        },
        "predicted_engagement": 0.78
      }
    ],
    "total_candidates": 1500,
    "filtered_count": 20,
    "algorithms_used": ["content_based", "collaborative"],
    "computation_time": 0.045
  }
}
```

### 用户画像管理

#### 获取用户画像
```http
GET /api/v1/recommendation/profile/{user_id}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "user_id": "user_12345",
    "username": "Alice",
    "total_actions": 156,
    "avg_session_duration": 320.5,
    "usage_frequency": "high",
    "active_hours": [9, 10, 14, 15, 20, 21],
    "preferences": {
      "category_preferences": {
        "text": 0.8,
        "image": 0.6,
        "code": 0.9,
        "document": 0.7
      },
      "tag_preferences": {
        "机器学习": 0.95,
        "Python": 0.88,
        "数据科学": 0.82
      },
      "complexity_preference": 0.75,
      "quality_threshold": 7.5,
      "novelty_preference": 0.6,
      "diversity_preference": 0.4,
      "confidence": 0.85
    },
    "similar_users": ["user_456", "user_789"],
    "updated_at": "2024-01-15T14:30:00Z"
  }
}
```

#### 更新用户偏好
```http
PUT /api/v1/recommendation/preferences
Content-Type: application/json

{
  "user_id": "user_12345",
  "category_preferences": {
    "text": 0.9,
    "image": 0.7
  },
  "tag_preferences": {
    "机器学习": 0.95,
    "深度学习": 0.88
  },
  "complexity_preference": 0.8,
  "quality_threshold": 8.0
}
```

### 内容管理

#### 注册内容特征
```http
POST /api/v1/recommendation/content
Content-Type: application/json

{
  "content_id": "content_12345",
  "title": "Python机器学习实战",
  "category": "document",
  "tags": ["Python", "机器学习", "实战", "数据科学"],
  "quality_score": 8.5,
  "complexity": 0.7,
  "author_id": "author_123",
  "author_name": "数据科学家",
  "description": "从零开始学习Python机器学习",
  "language": "zh",
  "length": 15000,
  "metadata": {
    "difficulty": "intermediate",
    "estimated_time": "2小时"
  }
}
```

#### 获取相似内容
```http
GET /api/v1/recommendation/similar/{content_id}?count=10
```

### 热门内容

#### 获取热门内容
```http
GET /api/v1/recommendation/trending?time_window=24h&category=text&count=20
```

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "content_id": "content_001",
      "rank": 1,
      "trending_score": 95.8,
      "velocity": 12.5,
      "recent_views": 1250,
      "recent_likes": 89,
      "recent_shares": 34,
      "rank_change": 2,
      "content_feature": {
        "title": "ChatGPT使用技巧大全",
        "category": "document",
        "quality_score": 9.1
      }
    }
  ]
}
```

## 🎯 推荐算法详解

### 1. 基于内容的推荐 (Content-Based)

#### 算法原理
基于内容特征和用户历史偏好进行匹配推荐。

#### 特征匹配
```python
def calculate_content_score(content, user_preferences):
    score = 0.0
    
    # 类别匹配 (30%)
    category_score = user_preferences.category_preferences.get(content.category, 0)
    score += category_score * 0.3
    
    # 标签匹配 (30%)
    tag_scores = [user_preferences.tag_preferences.get(tag, 0) for tag in content.tags]
    tag_score = sum(tag_scores) / len(content.tags) if content.tags else 0
    score += tag_score * 0.3
    
    # 质量匹配 (20%)
    if content.quality_score >= user_preferences.quality_threshold:
        quality_score = min(content.quality_score / 10.0, 1.0)
        score += quality_score * 0.2
    
    # 复杂度匹配 (20%)
    complexity_diff = abs(content.complexity - user_preferences.complexity_preference)
    complexity_score = 1.0 - complexity_diff
    score += complexity_score * 0.2
    
    return min(score, 1.0)
```

### 2. 协同过滤推荐 (Collaborative Filtering)

#### 用户相似度计算
```python
def cosine_similarity(user1_items, user2_items):
    common_items = set(user1_items.keys()) & set(user2_items.keys())
    
    if not common_items:
        return 0.0
    
    # 计算余弦相似度
    dot_product = sum(user1_items[item] * user2_items[item] for item in common_items)
    norm1 = sqrt(sum(score ** 2 for score in user1_items.values()))
    norm2 = sqrt(sum(score ** 2 for score in user2_items.values()))
    
    return dot_product / (norm1 * norm2) if norm1 > 0 and norm2 > 0 else 0.0
```

#### 推荐分数计算
```python
def collaborative_score(target_user, similar_users, user_item_matrix):
    item_scores = defaultdict(float)
    item_weights = defaultdict(float)
    
    for user_id, similarity in similar_users:
        user_items = user_item_matrix[user_id]
        
        for item_id, rating in user_items.items():
            if item_id not in target_user_items:
                item_scores[item_id] += similarity * rating
                item_weights[item_id] += similarity
    
    # 标准化分数
    return {item_id: score / item_weights[item_id] 
            for item_id, score in item_scores.items() 
            if item_weights[item_id] > 0}
```

### 3. 混合推荐策略

#### 算法融合
```python
def hybrid_recommend(content_recs, collab_recs, trending_recs, weights):
    # 权重配置
    algorithm_weights = {
        'content_based': 0.4,
        'collaborative': 0.4, 
        'trending': 0.2
    }
    
    # 分数融合
    merged_scores = defaultdict(float)
    for rec in content_recs:
        merged_scores[rec.content_id] += rec.score * algorithm_weights['content_based']
    
    for rec in collab_recs:
        merged_scores[rec.content_id] += rec.score * algorithm_weights['collaborative']
    
    for rec in trending_recs:
        merged_scores[rec.content_id] += rec.score * algorithm_weights['trending']
    
    return sorted(merged_scores.items(), key=lambda x: x[1], reverse=True)
```

## 📊 用户行为分析

### 行为权重配置
```python
ACTION_WEIGHTS = {
    'view': 1.0,        # 浏览
    'like': 5.0,        # 点赞
    'dislike': -3.0,    # 不喜欢
    'share': 4.0,       # 分享
    'comment': 3.0,     # 评论
    'bookmark': 6.0,    # 收藏
    'download': 3.5,    # 下载
    'copy': 2.0,        # 复制
    'edit': 4.5,        # 编辑
    'search': 1.5       # 搜索
}
```

### 时间衰减模型
```python
def time_decay_factor(timestamp, decay_days=30):
    """计算时间衰减因子"""
    days_ago = (datetime.now() - timestamp).days
    return math.exp(-days_ago / decay_days)
```

### 偏好置信度计算
```python
def calculate_confidence(user_behaviors):
    """计算偏好置信度"""
    # 基于行为数量
    behavior_count_score = min(len(user_behaviors) / 100.0, 1.0)
    
    # 基于行为多样性
    action_types = set(b.action_type for b in user_behaviors)
    diversity_score = len(action_types) / len(UserActionType)
    
    # 基于时间跨度
    time_span_days = (max(b.timestamp for b in user_behaviors) - 
                     min(b.timestamp for b in user_behaviors)).days
    time_score = min(time_span_days / 30.0, 1.0)
    
    # 综合置信度
    return (behavior_count_score * 0.5 + 
            diversity_score * 0.3 + 
            time_score * 0.2)
```

## 🎯 使用场景

### 1. 内容发现
```python
# 为新用户推荐热门内容
recommendations = await recommendation_service.get_recommendations(
    user_id="new_user",
    count=10,
    recommendation_types=["trending", "content_based"]
)
```

### 2. 个性化首页
```python
# 生成个性化首页推荐
recommendations = await recommendation_service.get_recommendations(
    user_id="active_user",
    count=20,
    recommendation_types=["hybrid"],
    diversity_factor=0.3,
    context={"page": "homepage", "time": "morning"}
)
```

### 3. 相关内容推荐
```python
# 在内容详情页推荐相关内容
similar_content = await recommendation_service.get_similar_content(
    content_id="current_content",
    count=5
)
```

### 4. 搜索结果个性化
```python
# 基于用户偏好重排搜索结果
personalized_results = await recommendation_service.personalize_search_results(
    user_id="user_123",
    search_results=search_results,
    query="机器学习"
)
```

## 📈 效果评估

### 推荐质量指标
- **准确率 (Precision)**: 推荐内容中用户感兴趣的比例
- **召回率 (Recall)**: 用户感兴趣内容中被推荐的比例
- **F1分数**: 准确率和召回率的调和平均
- **多样性 (Diversity)**: 推荐结果的多样性程度
- **新颖性 (Novelty)**: 推荐新内容的能力

### 业务指标
- **点击率 (CTR)**: 推荐内容的点击率
- **转化率**: 从推荐到目标行为的转化率
- **用户满意度**: 用户对推荐结果的满意程度
- **停留时间**: 用户在推荐内容上的停留时间
- **回访率**: 用户的回访频率

### A/B测试框架
```python
# 推荐算法A/B测试
ab_test = await ab_test_service.create_test(
    name="推荐算法优化测试",
    control_group={
        "algorithm": "baseline_hybrid",
        "traffic_percentage": 50.0
    },
    treatment_groups=[{
        "algorithm": "enhanced_hybrid", 
        "traffic_percentage": 50.0
    }],
    target_metrics=["ctr", "conversion_rate", "user_satisfaction"],
    duration_days=14
)
```

## 🔒 隐私保护

### 数据脱敏
- **用户ID哈希**: 使用哈希算法保护用户身份
- **行为聚合**: 聚合统计避免个人行为暴露
- **差分隐私**: 在统计中添加噪声保护隐私

### 权限控制
- **数据访问控制**: 严格控制推荐数据的访问权限
- **用户同意**: 获得用户明确同意后收集行为数据
- **数据删除**: 支持用户删除个人推荐数据

## 📞 技术支持

如有问题或建议，请联系：
- 📧 邮箱: <EMAIL>
- 📱 微信: ai-recommendation-support
- 🌐 官网: https://ai-gen-hub.com/recommendation
- 📚 文档: https://docs.ai-gen-hub.com/recommendation
