#!/usr/bin/env python3
"""
测试参数验证功能

验证文本生成API的参数预验证功能，特别是functions和tools字段的验证。
"""

import asyncio
import json
import logging
import httpx

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_invalid_function_name():
    """测试无效函数名称的验证"""
    logger.info("🧪 测试无效函数名称验证...")
    
    base_url = "http://localhost:8001"
    
    # 测试用例：包含无效函数名称的请求
    test_request = {
        "messages": [
            {
                "role": "user",
                "content": "测试函数调用"
            }
        ],
        "model": "gemini-2.5-flash",
        "functions": [
            {
                "name": "",  # 空函数名
                "description": "测试函数"
            },
            {
                "name": "123invalid",  # 以数字开头
                "description": "无效函数名"
            },
            {
                "name": "valid_function_name",  # 有效函数名
                "description": "有效函数"
            }
        ]
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            logger.info("📤 发送包含无效函数名的请求...")
            response = await client.post(
                f"{base_url}/api/v1/text/v2/generate",
                json=test_request,
                headers={"Content-Type": "application/json"}
            )
            
            logger.info(f"📥 收到响应: status={response.status_code}")
            
            if response.status_code == 400:
                try:
                    error_data = response.json()
                    if "PARAMETER_VALIDATION_ERROR" in str(error_data):
                        logger.info("✅ 参数验证错误被正确捕获")
                        logger.info(f"错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                        return True
                    else:
                        logger.warning(f"⚠️  收到400错误但不是参数验证错误: {error_data}")
                        return False
                except Exception as e:
                    logger.error(f"❌ 解析错误响应失败: {e}")
                    return False
            else:
                logger.error(f"❌ 预期400错误，但收到: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 请求异常: {e}")
            return False

async def test_empty_placeholder_functions():
    """测试空占位符函数的过滤"""
    logger.info("\n🧪 测试空占位符函数过滤...")
    
    base_url = "http://localhost:8001"
    
    # 测试用例：包含空占位符的请求
    test_request = {
        "messages": [
            {
                "role": "user",
                "content": "测试函数调用"
            }
        ],
        "model": "gemini-2.5-flash",
        "functions": [
            {
                "additionalProp1": {}  # 空占位符
            },
            {},  # 空字典
            {
                "name": "valid_function",
                "description": "有效函数"
            }
        ],
        "tools": [
            {
                "additionalProp1": {}  # 空占位符
            },
            {
                "type": "function",
                "function": {
                    "name": "valid_tool_function",
                    "description": "有效工具函数"
                }
            }
        ]
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            logger.info("📤 发送包含空占位符的请求...")
            response = await client.post(
                f"{base_url}/api/v1/text/v2/generate",
                json=test_request,
                headers={"Content-Type": "application/json"}
            )
            
            logger.info(f"📥 收到响应: status={response.status_code}")
            
            if response.status_code in [200, 400]:  # 200=成功, 400=业务错误但验证通过
                logger.info("✅ 空占位符被正确过滤，请求通过验证")
                return True
            elif response.status_code == 400:
                error_data = response.json()
                if "PARAMETER_VALIDATION_ERROR" in str(error_data):
                    logger.error("❌ 空占位符过滤失败，仍然触发验证错误")
                    logger.error(f"错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                    return False
                else:
                    logger.info("✅ 验证通过，只是业务逻辑错误")
                    return True
            else:
                logger.error(f"❌ 意外的响应状态码: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 请求异常: {e}")
            return False

async def test_valid_functions():
    """测试有效函数的正常处理"""
    logger.info("\n🧪 测试有效函数的正常处理...")
    
    base_url = "http://localhost:8001"
    
    # 测试用例：包含有效函数的请求
    test_request = {
        "messages": [
            {
                "role": "user",
                "content": "测试函数调用"
            }
        ],
        "model": "gemini-2.5-flash",
        "functions": [
            {
                "name": "get_weather",
                "description": "获取天气信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "城市名称"
                        }
                    },
                    "required": ["location"]
                }
            }
        ]
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            logger.info("📤 发送包含有效函数的请求...")
            response = await client.post(
                f"{base_url}/api/v1/text/v2/generate",
                json=test_request,
                headers={"Content-Type": "application/json"}
            )
            
            logger.info(f"📥 收到响应: status={response.status_code}")
            
            if response.status_code in [200, 400]:  # 200=成功, 400=业务错误但验证通过
                logger.info("✅ 有效函数通过验证")
                return True
            elif response.status_code == 400:
                error_data = response.json()
                if "PARAMETER_VALIDATION_ERROR" in str(error_data):
                    logger.error("❌ 有效函数被错误拒绝")
                    logger.error(f"错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                    return False
                else:
                    logger.info("✅ 验证通过，只是业务逻辑错误")
                    return True
            else:
                logger.error(f"❌ 意外的响应状态码: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 请求异常: {e}")
            return False

async def test_function_name_edge_cases():
    """测试函数名称边界情况"""
    logger.info("\n🧪 测试函数名称边界情况...")
    
    base_url = "http://localhost:8001"
    
    test_cases = [
        {
            "name": "长函数名测试",
            "function_name": "a" * 65,  # 超过64字符限制
            "should_fail": True
        },
        {
            "name": "特殊字符测试",
            "function_name": "func@name",  # 包含不允许的字符
            "should_fail": True
        },
        {
            "name": "下划线开头测试",
            "function_name": "_valid_function",  # 下划线开头（应该有效）
            "should_fail": False
        },
        {
            "name": "点和破折号测试",
            "function_name": "valid.function-name",  # 包含点和破折号（应该有效）
            "should_fail": False
        }
    ]
    
    results = {}
    
    for test_case in test_cases:
        name = test_case["name"]
        function_name = test_case["function_name"]
        should_fail = test_case["should_fail"]
        
        logger.info(f"  测试 {name}: '{function_name}'")
        
        test_request = {
            "messages": [{"role": "user", "content": "测试"}],
            "model": "gemini-2.5-flash",
            "functions": [
                {
                    "name": function_name,
                    "description": "测试函数"
                }
            ]
        }
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.post(
                    f"{base_url}/api/v1/text/v2/generate",
                    json=test_request,
                    headers={"Content-Type": "application/json"}
                )
                
                is_validation_error = (
                    response.status_code == 400 and 
                    "PARAMETER_VALIDATION_ERROR" in str(response.json())
                )
                
                if should_fail:
                    if is_validation_error:
                        results[name] = "✅ 正确拒绝"
                    else:
                        results[name] = "❌ 应该拒绝但通过了"
                else:
                    if is_validation_error:
                        results[name] = "❌ 应该通过但被拒绝"
                    else:
                        results[name] = "✅ 正确通过"
                        
            except Exception as e:
                results[name] = f"❌ 异常: {str(e)}"
    
    logger.info("\n📊 边界情况测试结果:")
    for name, result in results.items():
        logger.info(f"  {name}: {result}")
    
    return all("✅" in result for result in results.values())

async def main():
    """主函数"""
    logger.info("🚀 开始参数验证功能测试...")
    
    # 等待服务器启动
    await asyncio.sleep(10)
    
    # 执行各项测试
    test1 = await test_invalid_function_name()
    test2 = await test_empty_placeholder_functions()
    test3 = await test_valid_functions()
    test4 = await test_function_name_edge_cases()
    
    # 总结
    logger.info("\n" + "="*60)
    logger.info("📋 测试总结:")
    logger.info(f"  无效函数名验证: {'✅ 通过' if test1 else '❌ 失败'}")
    logger.info(f"  空占位符过滤: {'✅ 通过' if test2 else '❌ 失败'}")
    logger.info(f"  有效函数处理: {'✅ 通过' if test3 else '❌ 失败'}")
    logger.info(f"  边界情况测试: {'✅ 通过' if test4 else '❌ 失败'}")
    
    all_passed = all([test1, test2, test3, test4])
    logger.info(f"  整体结果: {'✅ 所有测试通过' if all_passed else '❌ 部分测试失败'}")
    
    if all_passed:
        logger.info("🎉 参数验证功能工作正常！")
    else:
        logger.error("💥 参数验证功能需要进一步调试")

if __name__ == "__main__":
    asyncio.run(main())
