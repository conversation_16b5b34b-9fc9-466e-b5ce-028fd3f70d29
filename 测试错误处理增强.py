#!/usr/bin/env python3
"""
错误处理增强功能测试脚本

测试增强的错误处理系统，包括错误分类、用户友好消息、修复建议等功能
"""

import asyncio
import sys
import time
from pathlib import Path
from typing import Dict, Any

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

try:
    from ai_gen_hub.core.error_handler import (
        EnhancedErrorHandler,
        ErrorContext,
        ErrorCategory,
        ErrorSeverity
    )
    from ai_gen_hub.core.exceptions import (
        AIGenHubException,
        AuthenticationError,
        AuthorizationError,
        RateLimitError,
        QuotaExceededError,
        ProviderUnavailableError,
        TimeoutError,
        InvalidRequestError,
        ModelNotSupportedError,
        ConfigurationError,
        CacheError
    )
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)


class ErrorHandlerTester:
    """错误处理器测试类"""
    
    def __init__(self):
        self.handler = EnhancedErrorHandler()
        self.test_results = []
    
    def print_header(self):
        """打印标题"""
        print("=" * 60)
        print("🔧 错误处理增强功能测试")
        print("=" * 60)
        print()
    
    def print_section(self, title: str):
        """打印章节标题"""
        print(f"\n📋 {title}")
        print("-" * 40)
    
    def print_success(self, message: str):
        """打印成功信息"""
        print(f"✅ {message}")
    
    def print_warning(self, message: str):
        """打印警告信息"""
        print(f"⚠️  {message}")
    
    def print_error(self, message: str):
        """打印错误信息"""
        print(f"❌ {message}")
    
    def print_info(self, message: str):
        """打印信息"""
        print(f"ℹ️  {message}")
    
    def test_error_classification(self):
        """测试错误分类功能"""
        self.print_section("错误分类测试")
        
        test_cases = [
            (AuthenticationError("API密钥无效"), ErrorCategory.AUTHENTICATION, ErrorSeverity.MEDIUM),
            (AuthorizationError("权限不足"), ErrorCategory.AUTHORIZATION, ErrorSeverity.MEDIUM),
            (RateLimitError("请求频率超限"), ErrorCategory.RATE_LIMIT, ErrorSeverity.MEDIUM),
            (QuotaExceededError("配额已用完"), ErrorCategory.QUOTA, ErrorSeverity.HIGH),
            (ProviderUnavailableError("OpenAI", "服务维护中"), ErrorCategory.PROVIDER, ErrorSeverity.HIGH),
            (TimeoutError("请求超时"), ErrorCategory.TIMEOUT, ErrorSeverity.MEDIUM),
            (InvalidRequestError("参数格式错误"), ErrorCategory.VALIDATION, ErrorSeverity.LOW),
            (ModelNotSupportedError("gpt-5", "openai"), ErrorCategory.VALIDATION, ErrorSeverity.LOW),
            (ConfigurationError("缺少API密钥配置"), ErrorCategory.CONFIGURATION, ErrorSeverity.HIGH),
            (CacheError("Redis连接失败"), ErrorCategory.CACHE, ErrorSeverity.LOW),
            (ValueError("无效的参数值"), ErrorCategory.VALIDATION, ErrorSeverity.LOW),
            (ConnectionError("网络连接失败"), ErrorCategory.NETWORK, ErrorSeverity.HIGH),
            (Exception("未知错误"), ErrorCategory.UNKNOWN, ErrorSeverity.CRITICAL),
        ]
        
        success_count = 0
        for exception, expected_category, expected_severity in test_cases:
            try:
                category, severity = self.handler.classify_error(exception)
                
                if category == expected_category and severity == expected_severity:
                    self.print_success(f"{type(exception).__name__}: {category.value}/{severity.value}")
                    success_count += 1
                else:
                    self.print_error(
                        f"{type(exception).__name__}: 期望 {expected_category.value}/{expected_severity.value}, "
                        f"实际 {category.value}/{severity.value}"
                    )
            except Exception as e:
                self.print_error(f"{type(exception).__name__}: 分类失败 - {e}")
        
        self.print_info(f"分类测试通过率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
        return success_count == len(test_cases)
    
    def test_suggestion_generation(self):
        """测试修复建议生成"""
        self.print_section("修复建议生成测试")
        
        test_cases = [
            AuthenticationError("API密钥无效"),
            AuthorizationError("权限不足"),
            RateLimitError("请求频率超限"),
            QuotaExceededError("配额已用完"),
            ProviderUnavailableError("OpenAI", "服务维护中"),
            TimeoutError("请求超时"),
            InvalidRequestError("参数格式错误"),
            ModelNotSupportedError("gpt-5", "openai"),
            ConfigurationError("缺少API密钥配置"),
            CacheError("Redis连接失败"),
        ]
        
        success_count = 0
        for exception in test_cases:
            try:
                suggestions = self.handler.generate_suggestions(exception)
                
                if suggestions and len(suggestions) > 0:
                    self.print_success(f"{type(exception).__name__}: {len(suggestions)} 条建议")
                    for i, suggestion in enumerate(suggestions[:2], 1):  # 只显示前2条
                        self.print_info(f"  {i}. {suggestion}")
                    success_count += 1
                else:
                    self.print_warning(f"{type(exception).__name__}: 没有生成建议")
            except Exception as e:
                self.print_error(f"{type(exception).__name__}: 建议生成失败 - {e}")
        
        self.print_info(f"建议生成测试通过率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
        return success_count == len(test_cases)
    
    def test_error_info_creation(self):
        """测试错误信息创建"""
        self.print_section("错误信息创建测试")
        
        # 创建测试上下文
        context = ErrorContext(
            request_id="test-req-123",
            user_id="test-user",
            provider="openai",
            model="gpt-3.5-turbo",
            endpoint="/api/v1/text/generate"
        )
        
        test_cases = [
            AuthenticationError("API密钥无效"),
            RateLimitError("请求频率超限", retry_after=60),
            ProviderUnavailableError("OpenAI", "服务维护中"),
            ValueError("无效的参数值"),
        ]
        
        success_count = 0
        for exception in test_cases:
            try:
                error_info = self.handler.create_error_info(exception, context)
                
                # 验证错误信息的完整性
                required_fields = ["error_code", "message", "category", "severity", "retryable"]
                error_dict = error_info.to_dict()
                
                missing_fields = [field for field in required_fields if field not in error_dict]
                
                if not missing_fields:
                    self.print_success(f"{type(exception).__name__}: 错误信息完整")
                    self.print_info(f"  错误码: {error_dict['error_code']}")
                    self.print_info(f"  分类: {error_dict['category']}")
                    self.print_info(f"  严重程度: {error_dict['severity']}")
                    self.print_info(f"  可重试: {error_dict['retryable']}")
                    success_count += 1
                else:
                    self.print_error(f"{type(exception).__name__}: 缺少字段 {missing_fields}")
            except Exception as e:
                self.print_error(f"{type(exception).__name__}: 错误信息创建失败 - {e}")
        
        self.print_info(f"错误信息创建测试通过率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
        return success_count == len(test_cases)
    
    def test_http_status_mapping(self):
        """测试HTTP状态码映射"""
        self.print_section("HTTP状态码映射测试")
        
        test_cases = [
            (AuthenticationError("API密钥无效"), 401),
            (AuthorizationError("权限不足"), 403),
            (InvalidRequestError("参数错误"), 400),
            (RateLimitError("请求频率超限"), 429),
            (QuotaExceededError("配额已用完"), 429),
            (TimeoutError("请求超时"), 504),
            (ProviderUnavailableError("OpenAI", "维护中"), 502),
            (ConfigurationError("配置错误"), 500),
            (Exception("未知错误"), 500),
        ]
        
        success_count = 0
        for exception, expected_status in test_cases:
            try:
                category, _ = self.handler.classify_error(exception)
                actual_status = self.handler._get_http_status_code(exception, category)
                
                if actual_status == expected_status:
                    self.print_success(f"{type(exception).__name__}: {actual_status}")
                    success_count += 1
                else:
                    self.print_error(
                        f"{type(exception).__name__}: 期望 {expected_status}, 实际 {actual_status}"
                    )
            except Exception as e:
                self.print_error(f"{type(exception).__name__}: 状态码映射失败 - {e}")
        
        self.print_info(f"状态码映射测试通过率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
        return success_count == len(test_cases)
    
    def test_error_stats(self):
        """测试错误统计功能"""
        self.print_section("错误统计测试")
        
        # 模拟一些错误
        test_exceptions = [
            AuthenticationError("API密钥无效"),
            AuthenticationError("API密钥过期"),
            RateLimitError("请求频率超限"),
            ProviderUnavailableError("OpenAI", "维护中"),
            ProviderUnavailableError("Google", "超时"),
            ValueError("参数错误"),
        ]
        
        try:
            # 处理错误以生成统计
            for exception in test_exceptions:
                error_info = self.handler.create_error_info(exception)
                self.handler._update_error_stats(error_info)
            
            # 获取统计信息
            stats = self.handler.get_error_stats()
            
            expected_total = len(test_exceptions)
            actual_total = stats.get("total_errors", 0)
            
            if actual_total == expected_total:
                self.print_success(f"错误统计正确: {actual_total} 个错误")
                self.print_info(f"错误类型数: {stats.get('error_types', 0)}")
                
                # 显示部分统计详情
                errors_by_category = stats.get("errors_by_category", {})
                for category, info in list(errors_by_category.items())[:3]:
                    self.print_info(f"  {category}: {info['count']} 次")
                
                return True
            else:
                self.print_error(f"错误统计不正确: 期望 {expected_total}, 实际 {actual_total}")
                return False
        except Exception as e:
            self.print_error(f"错误统计测试失败: {e}")
            return False
    
    def test_json_response_format(self):
        """测试JSON响应格式"""
        self.print_section("JSON响应格式测试")
        
        try:
            # 创建一个测试异常
            exception = RateLimitError("请求频率超限", retry_after=60)
            context = ErrorContext(
                request_id="test-123",
                endpoint="/api/v1/test"
            )
            
            # 生成JSON响应（模拟）
            error_info = self.handler.create_error_info(exception, context)
            response_data = {
                "error": error_info.to_dict(),
                "timestamp": time.time(),
                "request_id": context.request_id
            }
            
            # 验证响应格式
            required_fields = ["error", "timestamp", "request_id"]
            missing_fields = [field for field in required_fields if field not in response_data]
            
            if not missing_fields:
                self.print_success("JSON响应格式正确")
                self.print_info(f"包含字段: {list(response_data.keys())}")
                
                # 验证错误对象结构
                error_obj = response_data["error"]
                error_required_fields = ["error_code", "message", "category", "severity"]
                error_missing_fields = [field for field in error_required_fields if field not in error_obj]
                
                if not error_missing_fields:
                    self.print_success("错误对象结构正确")
                    return True
                else:
                    self.print_error(f"错误对象缺少字段: {error_missing_fields}")
                    return False
            else:
                self.print_error(f"响应缺少字段: {missing_fields}")
                return False
        except Exception as e:
            self.print_error(f"JSON响应格式测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        self.print_header()
        
        tests = [
            ("错误分类", self.test_error_classification),
            ("修复建议生成", self.test_suggestion_generation),
            ("错误信息创建", self.test_error_info_creation),
            ("HTTP状态码映射", self.test_http_status_mapping),
            ("错误统计", self.test_error_stats),
            ("JSON响应格式", self.test_json_response_format),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                if result:
                    passed_tests += 1
                    self.test_results.append((test_name, True, None))
                else:
                    self.test_results.append((test_name, False, "测试失败"))
            except Exception as e:
                self.print_error(f"{test_name} 测试异常: {e}")
                self.test_results.append((test_name, False, str(e)))
        
        # 打印测试总结
        self.print_section("测试总结")
        
        for test_name, passed, error in self.test_results:
            status = "✅ 通过" if passed else f"❌ 失败 ({error})" if error else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        print(f"\n📊 测试结果: {passed_tests}/{total_tests} 通过 ({passed_tests/total_tests*100:.1f}%)")
        
        if passed_tests == total_tests:
            self.print_success("🎉 所有错误处理增强功能测试通过！")
            return True
        else:
            self.print_warning("⚠️ 部分测试失败，请检查错误处理实现")
            return False


def main():
    """主函数"""
    tester = ErrorHandlerTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🚀 错误处理增强系统已准备就绪！")
        print("\n主要改进:")
        print("  • 智能错误分类和严重程度评估")
        print("  • 用户友好的错误消息")
        print("  • 详细的修复建议")
        print("  • 完整的错误统计和监控")
        print("  • 统一的JSON响应格式")
    else:
        print("\n🔧 请根据测试结果修复错误处理相关问题")
    
    return success


if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
