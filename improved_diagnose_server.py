#!/usr/bin/env python3
"""
改进的AI Gen Hub服务器诊断脚本
修复了流式请求检测逻辑，能正确识别SSE流中的错误信息
"""

import asyncio
import httpx
import json
import time
from typing import Dict, Any, List

class ImprovedServerDiagnostics:
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def print_status(self, test_name: str, status: str, details: str = ""):
        """打印测试状态"""
        status_emoji = "✅" if status == "正常" else "❌"
        print(f"{test_name}: {status_emoji} {status}")
        if details:
            print(f"  详情: {details}")
    
    async def check_health(self) -> Dict[str, Any]:
        """检查服务器健康状态"""
        try:
            response = await self.client.get(f"{self.base_url}/health")
            if response.status_code == 200:
                return {"status": "正常", "data": response.json()}
            else:
                return {"status": "异常", "error": f"状态码: {response.status_code}"}
        except Exception as e:
            return {"status": "异常", "error": str(e)}
    
    async def check_sync_text_generation(self) -> Dict[str, Any]:
        """检查同步文本生成"""
        try:
            payload = {
                "messages": [{"role": "user", "content": "Hello"}],
                "model": "gemini-2.5-flash",
                "stream": False,
                "max_tokens": 50
            }
            
            response = await self.client.post(
                f"{self.base_url}/api/v1/text/generate",
                json=payload
            )
            
            if response.status_code == 200:
                return {"status": "正常", "data": response.json()}
            else:
                error_data = response.text
                try:
                    error_json = response.json()
                    error_msg = error_json.get("detail", error_data)
                except:
                    error_msg = error_data
                return {"status": "异常", "error": f"状态码: {response.status_code}, 错误: {error_msg}"}
                
        except Exception as e:
            return {"status": "异常", "error": str(e)}
    
    async def check_stream_text_generation(self) -> Dict[str, Any]:
        """检查流式文本生成（改进版）"""
        try:
            payload = {
                "messages": [{"role": "user", "content": "Hello"}],
                "model": "gemini-2.5-flash", 
                "stream": True,
                "max_tokens": 50
            }
            
            async with self.client.stream(
                "POST",
                f"{self.base_url}/api/v1/text/generate",
                json=payload
            ) as response:
                
                if response.status_code != 200:
                    error_data = await response.aread()
                    return {"status": "异常", "error": f"状态码: {response.status_code}, 错误: {error_data.decode()}"}
                
                # 检查SSE流内容
                chunks_received = 0
                error_found = False
                error_message = ""
                success_chunks = 0
                
                async for chunk in response.aiter_text():
                    if chunk.strip():
                        chunks_received += 1
                        
                        # 检查是否包含错误事件
                        if "event: error" in chunk:
                            error_found = True
                            # 提取错误信息
                            lines = chunk.split('\n')
                            for line in lines:
                                if line.startswith('data: '):
                                    try:
                                        error_data = json.loads(line[6:])
                                        error_message = error_data.get("error", "未知错误")
                                    except:
                                        error_message = line[6:]
                                    break
                        
                        # 检查是否包含成功的数据块
                        elif "event: data" in chunk or '"delta"' in chunk:
                            success_chunks += 1
                        
                        # 限制检查的块数量，避免无限等待
                        if chunks_received >= 10:
                            break
                
                if error_found:
                    return {"status": "异常", "error": f"SSE流中发现错误: {error_message}"}
                elif success_chunks > 0:
                    return {"status": "正常", "details": f"成功接收 {success_chunks} 个数据块"}
                elif chunks_received > 0:
                    return {"status": "异常", "error": f"接收到 {chunks_received} 个块，但没有有效数据"}
                else:
                    return {"status": "异常", "error": "没有接收到任何数据"}
                    
        except Exception as e:
            return {"status": "异常", "error": str(e)}
    
    async def check_models_list(self) -> Dict[str, Any]:
        """检查模型列表"""
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/models")
            if response.status_code == 200:
                models = response.json()
                return {"status": "正常", "data": models, "count": len(models.get("data", []))}
            else:
                return {"status": "异常", "error": f"状态码: {response.status_code}"}
        except Exception as e:
            return {"status": "异常", "error": str(e)}
    
    async def run_diagnostics(self):
        """运行完整的诊断"""
        print("🔍 开始AI Gen Hub服务器诊断（改进版）...")
        print("=" * 60)
        
        # 1. 健康检查
        print("\n1. 服务器健康检查")
        health_result = await self.check_health()
        self.print_status("健康检查", health_result["status"], 
                         health_result.get("error", "服务器运行正常"))
        
        # 2. 同步文本生成
        print("\n2. 同步文本生成测试")
        sync_result = await self.check_sync_text_generation()
        self.print_status("同步文本生成", sync_result["status"], 
                         sync_result.get("error", "请求处理成功"))
        
        # 3. 流式文本生成（改进版检测）
        print("\n3. 流式文本生成测试（改进版）")
        stream_result = await self.check_stream_text_generation()
        self.print_status("流式文本生成", stream_result["status"], 
                         stream_result.get("error") or stream_result.get("details", ""))
        
        # 4. 模型列表
        print("\n4. 模型列表测试")
        models_result = await self.check_models_list()
        self.print_status("模型列表", models_result["status"], 
                         models_result.get("error") or f"共 {models_result.get('count', 0)} 个模型")
        
        # 总结
        print("\n" + "=" * 60)
        print("📊 诊断总结:")
        
        all_results = [health_result, sync_result, stream_result, models_result]
        normal_count = sum(1 for r in all_results if r["status"] == "正常")
        total_count = len(all_results)
        
        print(f"✅ 正常: {normal_count}/{total_count}")
        print(f"❌ 异常: {total_count - normal_count}/{total_count}")
        
        if normal_count == total_count:
            print("🎉 所有测试通过！服务器运行正常。")
        else:
            print("⚠️  部分测试失败，请检查上述错误信息。")
        
        # 详细错误分析
        if normal_count < total_count:
            print("\n🔧 错误分析:")
            test_names = ["健康检查", "同步文本生成", "流式文本生成", "模型列表"]
            for i, (name, result) in enumerate(zip(test_names, all_results)):
                if result["status"] == "异常":
                    print(f"  • {name}: {result.get('error', '未知错误')}")

async def main():
    async with ImprovedServerDiagnostics() as diagnostics:
        await diagnostics.run_diagnostics()

if __name__ == "__main__":
    asyncio.run(main())
